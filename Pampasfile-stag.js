﻿const _ = require("lodash")
const opts = {
  // backendUrl: "**************:10081" localhost:10081  吴联磊************** 彦纪鹏************** http://mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/backend
  backendUrl: "http://mall-stag.yang800.cn/backend",
  newBackendUrl: "http://mall-stag.yang800.cn/backend/mall-admin"
  // backendUrl: "***********:10081"
  // backendUrl: "http://web.mall.c0d5363253401480d8190afc3e7977570.cn-hangzhou.alicontainer.com/backend"
}
const config = {
  port: 10091,
  designer: {
    mysql: { // 如果不使用装修，那么 mysql 是不需要配的
      // database: 'parana',
      // username: 'root',
      // password: 'Dd123456$',
      // host: '*************',
      // port: '3306',// default 3306
      // pool: {  // connection pool
      //  max: 10,  //max connections
      //  min: 0,
      //  idle: 10000 //idle time(ms),that a connection can be idle before being released
      // }
    }
  },
  session: {
    store: "redis",// 模式支持redis、cookie
    cookieDomain: "",
    prefix: "afsession",
    maxAge: 1000 * 60 * 30,
    user: {
      idKey: "userId",
      getService: "getUserById",
    },
  },
  redis: {
    host: "127.0.0.1",
    port: 6379,
	  //password: 'dd654321'
  }
}

const defaultOptions = require("./Pampasfile-default")(opts)

module.exports = _.defaultsDeep(config, defaultOptions)

const config = {
  backendUrl: "parana-web-svc:80",
  newBackendUrl: "mall-admin-starter-svc:8080",
  payBackendUrl: "danding-pay-svc:8080",
  trackBackendUrl: "mall-track-point-svc:8080",
  dashboardBackendUrl: "danding-data-dashboard-svc:8080",
  localUrl: process.env.LOCAL_URL || "http://mall-stag.yang800.cn",
  memberUrl: process.env.MEMBER_URL || "http://localhost",
  port: 8080,
  designer: {
    enable: true, // default false
    siteManageUrl: "system/sites",
    mobileBaseUrl: "",
    mysql: { // 如果不使用装修，那么 mysql 是不需要配的
      database: "parana_stag",
      username: "parana_front",
      password: "b80c157@c3791",
      host: "rm-8vbk1bu7a2p3o9l20.mysql.zhangbei.rds.aliyuncs.com",
      port: "3306", // default 3306
      pool: {  // connection pool
        max: 2,  // max connections
        min: 0,
        idle: 10000 // idle time(ms),that a connection can be idle before being released
      }
    },
    designer_layouts: { // 装修需要用到的 layouts
      editorLayout: "eevee/design.hbs",
      templateLayout: "eevee/template.hbs",  //
      mTemplateLayout: "",  // 移动端的模板
      layouts: {
        "PC-DEFAULT": {
          type: "SITE",
          root: "eevee",
          name: "默认 PC 端模版",
          pageLayouts: { // 如果有页面需要定制布局，需要配置此项  需要在 body 上加 data-page-layouts="{{pageLayouts}}"
            无头布局: "no_header_layout.hbs",
          },
          desc: "默认主题的 PC 端模版"
        },
        "PC-SHOP": {
          type: "SHOP",
          root: "eevee",
          name: "默认 PC 端店铺模版",
          desc: "默认主题的 PC 端模版"
        },
        "M-SHOP": {
          type: "SHOP",
          app: "MOBILE",
          root: "/layouts/shop",
          name: "默认 MOBILE 端店铺模版",
          desc: "默认主题的 MOBILE 端模版"
        },
        "M-SITE": {
          type: "SITE",
          app: "MOBILE",
          root: "eevee",
          appType: "react",
          name: "默认 MOBILE 端模版",
          desc: "默认主题的 MOBILE 端模版"
        }
      }
    },
    shopId: 1,
    itemId: 5002
  },
  session: {
    store: "redis",
    cookieDomain: "",
    prefix: "afsession",
    maxAge: 1000 * 60 * 30,
    user: {
      idKey: "userId",
      getService: "getUserById",
    },
  },
  redis: {
    host: "redis-svc",
    port: 6379,
    password: "dd654321"
  },
  middlewareOpts: {
    bodyParser: {
      formLimit: "1mb"
    }
  },
  featureConfig: {
    platform: "danding"
  }
}
module.exports = config

{{#component "carousel-container js-comp"}}
  <div class="carousel{{#equals isSuper "1"}} carousel-super{{/equals}}{{#equals type "round"}} carousel-rounded{{/equals}}{{#equals type "square"}} carousel-squared{{/equals}}{{#equals type "round-rt"}} carousel-rounded-rt{{/equals}}{{#equals type "square-lb"}} carousel-squared-lb{{/equals}}" data-ride="carousel" data-type="switchable" data-base-index="{{#if baseIndex}}{{baseIndex}}{{else}}-1{{/if}}" data-interval="{{#if interval}}{{interval}}{{else}}3000{{/if}}" style="height:{{#if height}}{{height}}{{else}}100{{/if}}px;">
    <!-- Wrapper for slides -->
    <div class="carousel-contents" data-carousels="{{json images}}">
      {{#if images}}
        {{#each images}}
        <!--div data-role="content" class="carousel-content {{#unless @first}}hide{{/unless}}" data-id="1" style="height:{{#if ../height}}{{../height}}{{else}}100{{/if}}px;background:url('https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/685eb2c7664270ed1155be1a6739e77d.jpg') no-repeat center;{{#if ../bgColor}}background-color:{{../bgColor}};{{/if}}">
          {{#if href}}
            <a href="{{href}}" target="_blank"></a>
          {{/if}}
        </div-->
        {{inject "common/advertisement"}}
        {{/each}}
      {{/if}}
    </div>
    <div class="carousel-navs" data-role="link">
      <!-- Indicators -->
      {{#equals isIndicator "1"}}
      <ol class="carousel-indicators">
        {{#if images}}
          {{#each images}}
          <li data-role="nav" {{#if @first}}class="active"{{/if}}>{{#if ../type}}{{#equals ../type "auto"}}{{else}}<span>{{add @index 1}}</span>{{/equals}}{{/if}}</li>
          {{/each}}
        {{/if}}
      </ol>
      {{/equals}}

        <!-- Controls -->
      {{#equals isControl "1"}}
      <a class="left carousel-control" data-role="prev">
        <i class="icon-carousel-left"></i>
      </a>
      <a class="right carousel-control" data-role="next">
        <i class="icon-carousel-right"></i>
      </a>
      {{/equals}}
    </div>
  </div>
{{/component}}

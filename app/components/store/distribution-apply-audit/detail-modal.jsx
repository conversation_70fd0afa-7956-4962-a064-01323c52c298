import AccountDetail from "./account-detail";
import ComponyDetail from "./compony-detail"
const {Button, Table,Switch,Modal,message,Badge,Form,Input,Select,Alert,Space,Tag,Tabs,Radio} = antd;
const FormItem = Form.Item;
const {SearchList, Spa, SpaConfigProvider,DTEditModal,DTUploaderFile} = dtComponents
const {useState,useEffect,useRef,Fragment} = React;
const {InfoCircleOutlined} = icons;
export default ({open,detail,close,reload})=>{
    const [person,setPerson] = useState({});
    const [company,setCompany] = useState({});
    const [activeKey,setActiveKey] = useState('1');
    const [showError,setShowError] = useState(false);
    const isEnabled = useRef();
    const [form] = Form.useForm()
    useEffect(()=>{
        if(open && detail){
            load();
        }
    },[detail,open])

    const load = () => {
        $.ajax({
            url:`/api/allinPay/allinPayAccountDetail`,
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({ shopId: sessionStorage.shopId, subUserId: detail.userId }),
            success: (data) => {
                console.log("data.data:",data.data)
                if(data.success){
                    setPerson(data.data.person);
                    setCompany(data.data.company);
                    if(data.data.activeBizUserId){
                        let value = '';
                        if(data.data.activeBizUserId === data.data.person.bizUserId){
                            value = 1
                        }
                        if(data.data.activeBizUserId === data.data.company.bizUserId){
                            value = 2
                        }
                        isEnabled.current = value;
                        form.setFieldsValue({
                            isEnabled: value
                        })
                    }
                } else {
                    message.error(data.errorMsg)
                }
                return $("body").spin(false)
            },
            error: (data) => {
                $("body").spin(false)
                message.error(data.responseJSON.message)
                
            }
        })
    }


    const loopQuery = ()=>{
            $.ajax({
                url:`/api/allinPay/allinPayAccountDetail`,
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({ shopId: sessionStorage.shopId, subUserId: detail.userId }),
                success: (data) => {
                    console.log("data:",data.data);
                    if(data.success){
                        setPerson(data.data.person);
                        setCompany(data.data.company);
                        if(data.data.activeBizUserId){
                            let value = '';
                            if(data.data.activeBizUserId === data.data.person.bizUserId){
                                value = 1
                            }
                            if(data.data.activeBizUserId === data.data.company.bizUserId){
                                value = 2
                            }
                            isEnabled.current = value;
                            form.setFieldsValue({
                                isEnabled:value
                            })
                        }
                        message.success('更新成功') 
                    } else {
                        message.error(data.errorMsg)
                    }
                    $("body").spin(false)
                },
                error: (data) => {
                    message.error(data.responseJSON.message)
                }
            })
    }

    const changeSelectStatus = (val) => {
        const bizUserId = val === 1? person.bizUserId: company.bizUserId
        $.ajax({
            url:`/api/allinPay/switchAvailability`,
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({ 
                shopId: sessionStorage.shopId, 
                subUserId: detail.userId,
                bizUserId: bizUserId
            }),
            success: (data) => {
                if(data.success){
                    reload();
                    message.success('切换成功')
                    setShowError(false)
                } else {
                    message.error(data.errorMsg)
                    setShowError(data.errorMsg)
                    form.setFieldsValue({
                        isEnabled:isEnabled.current
                    })
                }
                $("body").spin(false)
            },
            error: (data) => {
                message.error(data.responseJSON.message)
            }
        })
    }

    return (<Fragment>
        <Modal
            open={open}
            title={'通联账户'}
            onCancel={()=>{
                close && close();
                setActiveKey('1')
                setShowError(false)
            }}
            onOk={()=>{
                close && close();
                setActiveKey('1')
                setShowError(false)
            }}
        >
            
            <Tabs
                activeKey={activeKey}
                animated={false}
                centered
                onTabClick={(e)=>{
                    console.log(e)
                    setActiveKey(e)
                }}
            >
                <Tabs.TabPane tab="个人会员" key="1">
                </Tabs.TabPane>
                <Tabs.TabPane tab="企业会员" key="2">
                </Tabs.TabPane>
            </Tabs>
            {
                activeKey === '1' && 
                <AccountDetail person={person} pageLoad={reload} detail={detail}  />
            }
            {
                activeKey === '2' && 
                <ComponyDetail company={company} pageLoad={reload} detail={detail} 
                    load={loopQuery}
                    close={()=>{
                        close && close()
                        setActiveKey('1')
                        setShowError(false)
                        setActiveId('')
                    }}
                /> 
            }
            {
                (person.bizUserId || company.bizUserId) && 
                <Form form={form}>
                    <FormItem
                        label={'会员类型'}
                        name={'isEnabled'}
                        style={{borderTop: '1px solid #ccc',marginLeft:'20px'}}
                        wrapperCol={{span:18}}
                        // valuePropName={'checked'}
                        extra={"启用/关闭企业账号后，新的订单提现金额将进入新的账户，提现时请点击【同步账户】避免提现失败"}
                        help={showError && <span style={{color:'red'}}>{showError}</span>}
                    >
                        <Radio.Group
                            onChange={(e)=>{
                                changeSelectStatus(e.target.value)
                            }}
                        >
                            {person.bizUserId && <Radio value={1}>个人会员</Radio>}
                            {/* <Radio value={1}>个人会员</Radio> */}
                            {/* <Radio value={2}>企业会员</Radio> */}
                            {company.bizUserId && <Radio value={2}>企业会员</Radio>}
                        </Radio.Group>
                    </FormItem>  
                </Form>
            }
        </Modal>
    </Fragment>)
}
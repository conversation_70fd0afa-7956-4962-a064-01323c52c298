/**
 * @description 条款设置
 * <AUTHOR>
 */
const { useState } = React
import TextAreaModal from "./textArea-modal";
import {lib} from "../../common/react/utils/lib";
function Page() {
    const [openTextArea, setOpenTextArea] = useState(false)

    // 社群运营
    const isCommunityOperation = lib.isCommunityOperation()
    const [status, setStatus] = useState(null)
    let baseInfo = [{
        name: "隐私条款",
        value: "编辑隐私条款",
        status: 1,
    },
    {
        name: "服务协议",
        value: "编辑服务协议",
        status: 2,
    },
    {
        name: "消费者服务条款",
        value: "编辑消费者服务条款",
        status: 3,
    },
    {
        name: "权益说明",
        value: "编辑权益说明",
        status: 4,
    },
    {
        name: "积分规则",
        value: "编辑积分规则",
        status: 5,
    },
    {
        name: "门店推广服务协议",
        value: "门店推广服务协议",
        status: 15,
    },
    ]

    const communityInfo = [
        {
            name: "拼团协议",
            value: "拼团协议",
            status: 14,
        }
    ]
    
    return (
        <div>
            {
                [...baseInfo, ...(isCommunityOperation ? communityInfo : [])].map((item, index) => {
                    return (
                        <div key={index} style={{ marginBottom: 24, display: "flex", alignItems: "center" }}>
                            <div style={{ width: 204 }}>{item.name}:</div>
                            <a onClick={() => {
                                setStatus(item.status)
                                setOpenTextArea(true);
                                // getDetail(item.status)
                            }}>{item.value}</a>
                        </div>
                    )
                })
            }

            <TextAreaModal
                open={openTextArea}
                onClose={() => { setOpenTextArea(false) }}
                status={status}
            />
        </div>
    )
}

ReactDOM.render(
    <Page />, document.getElementById("new-integral-rule-set")
);

<div class="p-wrapper refund-express-info">
  <div class="p-title">退货物流信息</div>
  <div class="order-return-logistic">
    <div class="p-content">
      <span>{{i18n "Express company" bundle="trade"}}：{{refundExpressInfo.companyName}}</span><br>
      <span>{{i18n "Express no" bundle="trade"}}：{{refundExpressInfo.expressNo}}</span><br>
      <span>{{formatDate refundExpressInfo.shipAt}}</span><br>
    </div>
    {{#with returnExpressTrack}}
      <div class="logistic-info" style="overflow: auto;">
        <ul class="logistic-ul" style="height: 300px;">
          {{#if steps}}
            {{#each steps}}
              <li class="logistic-li-node {{#if @last}}end{{else}}{{#if @first}}start{{/if}}{{/if}}"><span class="node"></span><span class="node-time">{{time}}</span><span class="node-info">{{desc}}</span></li>
            {{/each}}
          {{else}}
            <span>暂时没有物流信息</span>
          {{/if}}
        </ul>
      </div>
    {{/with}}
  </div>
</div>

{{#component "component-site-header js-comp"}}
  <form class="form form-search" type="get" id="form-search" action="{{_HREF_.main}}/search" data-hrefbase="{{_HREF_.main}}">
    {{#equals position "up"}}
      {{> component:common/header/backend_templates/hotwords}}
    {{/equals}}
    <fieldset>
      <div class="input-group input-group-append input-search"  style="display: none">
        <div class="search-type-group"  style="margin-top: 1px;">
          <ul class="search-type">
            <li class="search-type-li search-type-items active" data-type="1">
              {{i18n "Product" bundle="common"}}
            </li>
            <li class="search-type-li search-type-shops" data-type="2">
              {{i18n "Store" bundle="common"}}
            </li>
          </ul>
          <i class="icon-feebas icon-feebas-xiangxiazhedie search-tab-icon"></i>
        </div>
        <input type="text" name="q" class="search-input items-suggest active" value="{{q}}" placeholder="{{placeholder}}" autocomplete="off">
        <input type="text" name="q" class="search-input shops-suggest" value="{{q}}" autocomplete="off">
        <span class="input-group-btn">
          <button class="btn btn-primary btn-medium" id="search-button" type="submit">搜&nbsp;索</button>
        </span>
      </div>
    </fieldset>
    {{#ifCond position "!=" "up"}}
      {{> component:common/header/backend_templates/hotwords}}
    {{/ifCond}}
  </form>
  {{> component:common/header/backend_templates/float_search}}
{{/component}}

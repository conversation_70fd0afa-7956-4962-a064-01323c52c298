
const {
    LockOutlined,
    MobileOutlined,
    SafetyCertificateOutlined,
    UserOutlined,
} = icons;
const {Button, Table,Switch,Modal,message,Menu,Badge,Alert,Form,Input,Span} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents
const {useState,useEffect,useRef,Fragment} = React;
// const {Link} = React
import request from "../../../utils/plugins/axios/request";
import cookie from "../../utils/plugins/cookie";
import PwModal from "./Pw-modal";
import {getParam} from "../react/utils/common"
console.log("login-new")
function Login() {
    const [pwVisible,setPwVisible] = useState(false)
    const [lastBol,setLastBol] = useState(false);
    const [form] = Form.useForm();

    const redirectUrl = useRef();
    function login(type, formData) {
        form.validateFields()
        .then((values)=>{
            console.log("values:",values)
            let url = "";
            if(values.loginBy.indexOf(":") !== -1){
                url = "/api/seller/login"
                values.username = values.loginBy
            }else{
                url = "/api/user/login"
            }
            // let type = ''
            if(/^1[3|4|5|7|8][0-9]{9}$/.test(values.loginBy)){
                values.type = "3";
            }else if (/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.test(values.loginBy)){
                values.type = "2";
            } else{
                values.type = "1";
            }
            values.target = getParam('target')

            $.ajax({
                url:url,
                // data:{...formData},
                needMask: true,
                method: "POST",
                data: JSON.stringify(values),
                contentType: "application/json",
                dataType: "json",
                success: data => {
                    console.log("res:",data)
                    if("success" in data && !data.success){
                       return message.warn(data.errorMsg)
                    }
                    // #登录去掉session
                    sessionStorage.clear()
                    // #把shopId写入sessionStorage,用于订单列表获取店铺信息
                    cookie.set("shopId", data.shopId, 30, document.domain);
                    sessionStorage.shopId=data.shopId
                    if(data.shopId){
                        getShopInfo()
                    }
                    if(data.passwordStatus === -1){
                        if(data.redirect){
                            window.location.href = data.redirect;
                            redirectUrl.current = data.redirect;
                        }
                    } else{
                        // 展示修改密码弹框
                        setPwVisible(true);

                        if(data.redirect){
                            redirectUrl.current = data.redirect;
                        }
                        setLastBol(data.passwordStatus===1);

                    }
                },
                fail(err) {
                    console.log("err:",err)
                },
            });
        })

    }



    function getShopInfo(){
        $.ajax({
            url: "/api/shop/"+sessionStorage.shopId,
            type: "GET",
            async: false,
            success: (data)=>{
                sessionStorage.shopLogo=data.imageUrl;
                cookie.set("shopLogo", data.imageUrl, 30, document.domain);
                if (data.extra&&data.extra.salesPattern){
                    sessionStorage.salesPattern=data.extra.salesPattern;
                    sessionStorage.shopInfo=JSON.stringify(data);
                    cookie.set("salesPattern", data.extra.salesPattern, 30, document.domain);
                    cookie.set("shopInfo", JSON.stringify(data), 30, document.domain);
                    cookie.set("openFans", data.extra.openFans, 30, document.domain);
                }else {
                    sessionStorage.removeItem("salesPattern");
                    sessionStorage.removeItem("shopInfo");
                    cookie.set("salesPattern", '', 30, document.domain);
                    cookie.set("shopInfo", '', 30, document.domain);
                    cookie.set("openFans", '', 30, document.domain);
                  if (data){
                    sessionStorage.salesPattern='commonShop';
                    cookie.set("salesPattern", 'commonShop', 30, document.domain);
                    sessionStorage.shopInfo=JSON.stringify(data) ;
                  }
                }

            }

        })


    }

    return (
        <div className={'login-page'}>
                <div className="login-wrap">
                    <h2 style={{textAlign:'left',marginBottom:'20px',fontWeight:'bold'}}>登录</h2>
                    <Form form={form} onFinish={data => login("login", data)}>
                        <Form.Item
                            name="loginBy"
                            rules={[{ required: true, message: "请输入手机号" }]}>
                            <Input
                                size="large"
                                addonBefore="+86"
                                placeholder="手机号"
                                prefix={<UserOutlined style={{ color: "#999" }} />}
                            />
                        </Form.Item>
                        <Form.Item name="password" rules={[{ required: true, message: "请输入密码" }]}>
                            <Input.Password
                                size="large"
                                placeholder="密码"
                                type=""
                                prefix={<LockOutlined style={{ color: "#999" }} />}
                            />
                        </Form.Item>
                        <Form.Item>
                           <a
                            style={{ float: "right" }}
                            href={`/forget-password`}
                            className={"clearfix"}>
                            忘记密码？
                        </a>
                        </Form.Item>

                        <Form.Item>
                            <Button type="primary" onClick={()=>{
                                login()
                            }}  style={{width:'100%'}} size="large" >
                                立即登录
                            </Button>
                        </Form.Item>
                        <div className="privacy-div">
                            登录即代表你已阅读并同意
                            <a href={`/agreement/service`}>服务协议</a>
                            {/* 和 */}
                            {/* <a href={`/privacy`}>隐私条款</a> */}
                        </div>
                    </Form>
                </div>
            <PwModal
                pwVisible={pwVisible}
                setPwVisible={setPwVisible}
                fetchDetail ={()=>{
                    window.location.href = redirectUrl.current;
                }}
                disabled={lastBol}
            />
        </div>

    );
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        // params.data.shopid = sessionStorage.shopid
        params.url = params.url + "?shopId="+sessionStorage.shopId
        const obj = Object.assign(params, {
          method: 'POST',
        })
        request(obj);
      }
    }
  })}>
    <Login />
  </SpaConfigProvider>, document.getElementById("login")
);

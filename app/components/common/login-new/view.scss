@font-face {
    font-family: "login-iconfont"; /* project id 1257720 */
    src: url("//at.alicdn.com/t/font_1257720_fr77ls7dp5j.eot");
    src: url("//at.alicdn.com/t/font_1257720_fr77ls7dp5j.eot?#iefix") format("embedded-opentype"),
        url("//at.alicdn.com/t/font_1257720_fr77ls7dp5j.woff2") format("woff2"),
        url("//at.alicdn.com/t/font_1257720_fr77ls7dp5j.woff") format("woff"),
        url("//at.alicdn.com/t/font_1257720_fr77ls7dp5j.ttf") format("truetype"),
        url("//at.alicdn.com/t/font_1257720_fr77ls7dp5j.svg#login-iconfont") format("svg");
}
.react-single-app-personal-center {
    width: 100%;
    padding: 98px 24px 0;
    box-sizing: border-box;
    background: #fff;
    .welcome {
        padding-left: 24px;
        padding-bottom: 24px;
        box-sizing: border-box;
        font-size: 24px;
        line-height: 32px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.85);
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }
    .item {
        padding: 24px;
        box-sizing: border-box;
        line-height: 24px;
        font-size: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        .item_head {
            display: inline-block;
            width: 105px;
            margin-right: 12px;
        }
        .item_body {
            color: rgba(0, 0, 0, 0.65);
        }
        .item_operation {
            float: right;
            font-size: 14px;
            color: #1890ff;
            cursor: pointer;
        }
    }
    &-check {
        color: #666;

        &:before {
            font-family: login-iconfont;
            content: "\e668";
            color: #999;
            margin-right: 5px;
            font-size: 16px;
        }

        &.true:before {
            content: "\e66b";
            color: rgb(141, 206, 85);
        }
    }
}
.component-standard-container{
    // width: 300px;
    height: 100vh;
}
// .login-page{
.login-container {
    display: flex;
    // top: 50%;
    // left: 50%;
    // transform: translate(-50%, -50%);
    justify-content: flex-start;
    position: relative;
    z-index: 100;
    font-size: 14px;
    
   

    .left-bg {
        margin-right: 120px;
    }

    button[type="submit"] {
        margin-top: 20px;
    }
}
.outer-login{
     .login-wrap {
        width: 448px;
        // height: 452px;
        background: #ffffff;
        padding: 26px 40px;
        box-shadow: 0 2px 16px 0 rgba(224, 224, 224, 0.5);
        border-radius: 16px;
        // margin-right: calc(50vw - 560px);
        position: relative;
        float: right;

        .login-switch {
            display: flex;
            align-items: center;
            justify-content: space-between;
            // padding-left: ;
            // padding: 0 36px;
            img {
                width: 12px;
                height: 11px;
                margin-right: 2px;
            }

            .role-name {
                font-size: 20px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #333333;
                line-height: 28px;
                padding-top: 30px;
            }

            .switch-btn {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 20px;
                margin-bottom: 30px;
                width: 88px;
                height: 24px;
            }

            .switch-btn:hover {
                cursor: pointer;
                width: 88px;
                height: 24px;
                background: #f6f7fa;
                border-radius: 2px;
                color: #666;
                margin-bottom: 30px;
            }
        }

        .lpr-content {
            padding: 0;
            .login-type {
                width: 100%;
                justify-content: center;
                .link {
                    color: #1890ff;
                    text-decoration: none;
                    cursor: pointer;
                    &.cursor-auto {
                        cursor: auto;
                    }
                }

                .link:hover {
                    color: #40a9ff;
                }
            }
            .oauth-login {
                padding: 85px 0;
            }
            .oauth-btn-login {
                width: 100%;
                .wechat-icon {
                    margin: auto;
                    vertical-align: -5px;
                }
            }
        }
        .privacy-div{
            text-align: center;
        }
    }
}
.main-login{
    height: calc(100% - 130px);
}
// }

const {<PERSON><PERSON>, <PERSON>,Switch,<PERSON><PERSON>,message,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,Input,Tooltip,Row,Col} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents
const {useState,useEffect,useRef,Fragment,useMemo} = React;
const {BellOutlined}  = icons;
function Check({ status = false, children, style = {} }) {
    return (
        <div className={`react-single-app-personal-center-check ${status}`} style={style}>
            {children}
        </div>
    );
}
const FormLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
};

export default function pwModal({ pwVisible, setPwVisible, fetchDetail,disabled }) {
    let [form] = Form.useForm();
    let [password, setPassword] = useState("");
    function onOk() {
        form.validateFields().then(values => {
            console.log("values",values)
            // $.ajax
            // url: "/api/user/change_password"
            // type: "POST"
            verifyPassword(values.oldPassword,()=>{
                $.ajax({
                    url: "/api/user/change_password",
                    data: {
                        oldPassword: values.oldPassword,
                        newPassword: values.password,
                    },
                    type: "POST",
                    needMask: true,
                    success: res => {
                        if("success" in res && !res.success){
                            return message.warn(res.errorMsg)
                        }
                        setPwVisible(false);
                        fetchDetail();
                        setPassword("");
                        form.resetFields();
                    },
                });
            })
            
        });
    }
    function verifyPassword(password,fn){
        // /api/user/validate-password
        $.ajax({
            url:"/api/user/validate-password",
            data:JSON.stringify({
                password:password
            }),
            method: "POST",
            // data: JSON.stringify(values),
            contentType: "application/json",
            dataType: "json",
            success(data){
                if(!data.data){
                    message.error("原密码错误")
                    return;
                }
                fn &&fn()
            }
        })
    }
    const passwordTooltip = useMemo(
        () => () => {
            return (
                <Fragment>
                    <Check
                        style={{ marginTop: "10px" }}
                        status={(() => {
                            return password.length > 7 && password.length < 21;
                        })()}>
                        密码长度为8-20位
                    </Check>
                    <Check
                        status={(function () {
                            return /^([\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]|\w)+$/.test(password);
                        })()}>
                        只能包含大小写字母、数字以及英文符号（不含空格）
                    </Check>
                    <Check
                        style={{ marginBottom: "10px" }}
                        status={(function () {
                            return (
                                /[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]+/.test(password) +
                                    /\d+/.test(password) +
                                    /[a-z]+/.test(password) +
                                    /[A-Z]+/.test(password) >
                                2
                            );
                        })()}>
                        大写字母、小写字母、数字和英文符号至少包含3种
                    </Check>
                </Fragment>
            );
        },
        [password],
    );
    return (
        <Modal
            title="修改密码"
            open={pwVisible}
            // onOk={() => onOk()}
            // // okText="去"
            onCancel={() => {
                setPassword("");
                form.resetFields();
                setPwVisible(false);
                // fetchDetail()
                $.ajax({
                    type: "POST",
                    url: "/api/user/logout",
                    success: ()=>{
                        // window.location.href = "/"   
                    }
                })
                
            }}
            footer={[
                <Tooltip  placement="top" title={disabled?"你的账号存在安全风险，必须修改":null} >
                    <Button style={{marginRight:'20px'}} disabled={disabled} 
                    
                        onClick={()=>{
                            fetchDetail();
                        setPassword("");
                        form.resetFields();
                        setPwVisible(false); 
                    }}>暂不修改</Button>
                </Tooltip>
                ,
                <Button onClick={onOk}type="primary">确定</Button>
            ]}
        >
            <Alert type="ss"
               style={{backgroundColor: "#f5f5f5",
                "borderRadius":"6px",
                marginBottom: "20px",}}
            message={
                disabled?"您的账号暂不可登录，请您修改您的密码，如有问题请联系技术"
                :
                "您的账号存在安全风险，建议您尽快修改，五次后将影响登录"} 
                showIcon={true}
                icon={<BellOutlined style={{color:'#e48665',fontSize:'20px'}} />}
            />
            <Form
                form={form}
                {...FormLayout}
                onValuesChange={(changedValues, values) => {
                    if (changedValues.hasOwnProperty("password")) {
                        setPassword(changedValues.password);
                    }
                }}>
                <Form.Item label="旧密码" name="oldPassword" rules={[{ required: true,message:'旧密码是必填的' }]}>
                    <Input type="password" 
                        autoComplete={"new-password"}
                        onBlur={()=>{

                        }}
                    />
                </Form.Item>
                <Form.Item
                    label="新密码"
                    name="password"
                    autoComplete={"new-password"}
                    rules={[
                        { required: true,message:'新密码是必填的'},
                        {
                            validator: (rule, value) => {
                                return new Promise((resolve, reject) => {
                                    if(!value){
                                        resolve();
                                    }
                                    if (
                                        !/^([\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]|\w){8,20}$/.test(value) ||
                                        /[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]+/.test(value) +
                                            /\d+/.test(value) +
                                            /[a-z]+/.test(value) +
                                            /[A-Z]+/.test(value) <
                                            3
                                    ) {
                                        reject("密码格式不正确");
                                    } else {
                                        resolve();
                                    }
                                });
                            },
                            validateTrigger: "onSubmit",
                        },
                    ]}
                   >
                    <Input type="password" maxLength={20} autoComplete={"new-password"} />
                </Form.Item>
                <Form.Item
                    label="确认密码"
                    name="password2"
                    // help={passwordTooltip()}
                    rules={[
                        {
                            required: true,
                            message: "请输入确认密码",
                        },
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (!value || getFieldValue("password") === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject("两次输入密码不相同");
                            },
                        }),
                    ]}>
                    <Input type="password" maxLength={20} autoComplete={"new-password"} />
                </Form.Item>
                <Row>
                    <Col span={6}></Col>
                    <Col span={16}>{passwordTooltip()}</Col>
                </Row>
            </Form>
        </Modal>
    );
}


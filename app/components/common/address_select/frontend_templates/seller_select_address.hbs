<div class="modal">
  <div class="modal-header">
    <h2>{{i18n "Recipient Address" bundle="common"}}</h2>
  </div>
  <form class="form form-aligned address-form {{#if properties.resource.address.tradeInfoLevel}}{{#gt properties.resource.address.tradeInfoLevel 3}}has-street{{/gt}}{{/if}}" id="address-form" data-id="{{data.id}}">
    <fieldset>
      <div class="address-edit-form modal-body" data-address="{{json this}}">
        <div class="control-group">
          <label for="name"><span class="required">*</span>{{i18n "Recipient" bundle="common"}}:</label>
          {{#if data.id}}
          <input name="id" type="text" style="display:none;" id="addressId" value="{{data.id}}">
          {{/if}}
          {{#if userId}}
          <input name="userId" type="text" style="display:none;" id="userId" value="{{userId}}">
          {{/if}}
          <input id="sites-id" name="receiveUserName" type="text" placeholder="{{i18n "Please enter recipient name" bundle="common"}}" value="{{data.receiveUserName}}" maxLength="25" pattern="^.{2,25}$" required>
          <span class="note"><i>&times;</i> {{i18n "Name must be in between 2-25 characters" bundle="common"}}</span>
          <span class="note-error"><i>&times;</i> {{i18n "Name must be in between 2-25 characters" bundle="common"}}</span>
          <span class="note-error-empty"><i>&times;</i> {{i18n "Name must be in between 2-25 characters" bundle="common"}}</span>
        </div>
        <div class="control-group">
          <label for="mobile-phone"><span class="required">*</span>{{i18n "Phone no" bundle="common"}}:</label>
          <input id="mobile-phone" name="mobile" type="text" pattern="(^(1\d{10})$)|(^1\d{2}\*{4}\d{4}$)"  class="control" placeholder="{{i18n "Please enter phone number" bundle="common"}}" value="{{data.mobile}}" required>
          <span class="note"><i>&times;</i> {{i18n "Required, cannot be empty" bundle="common"}}</span>
          <span class="note-error"><i>&times;</i> {{i18n "Please enter only numeric values" bundle="common"}}</span>
          <span class="note-error-empty"><i>&times;</i> {{i18n "Mobile number cannot be empty" bundle="common"}}</span>
        </div>

        <input type="hidden" name="type" value="1">
        <div class="control-group">
          <label for="province"><span class="required">*</span>{{i18n "Recipient Address" bundle="common"}}:</label>
          <div class="address-select-group">
            <select name="provinceId" class="address-select" data-level="1">
              <option value="{{data.provinceId}}">请选择</option>
            </select>
            <select name="cityId" class="address-select" data-level="2">
              <option value="{{data.cityId}}">请选择</option>
            </select>
            <select name="regionId" class="address-select" data-level="3">
              <option value="{{data.regionId}}">请选择</option>
            </select>
            {{#if properties.resource.address.tradeInfoLevel}}
              {{#gt properties.resource.address.tradeInfoLevel 3}}
                <select name="streetId" class="address-select" data-level="4">
                  <option value="{{data.streetId}}">请选择</option>
                </select>
              {{/gt}}
            {{/if}}
          </div>
          {{#if data.isCookie}}
            <input type="hidden" name="provinceCode" value="{{data.provinceCode}}">
            <input type="hidden" name="cityCode" value="{{data.cityCode}}">
            <input type="hidden" name="districtCode" value="{{data.districtCode}}">
            <input type="hidden" name="streetCode" value="{{data.streetCode}}">
          {{/if}}
        </div>
        <div class="control-group">
          <label for="street"><span class="required">*</span>{{i18n "Street Address" bundle="common"}}:</label>
          <textarea id="street-address" name="detail" type="text" placeholder="{{i18n "Please enter street address" bundle="common"}}" pattern="^[\s\S]{5,120}$" maxLength="120" required>{{data.detail}}</textarea>
          <span class="note-error"><i>&times;</i> {{i18n "Address must be in 5-120 characters" bundle="common"}}</span>
          <span class="note-error-empty"><i>&times;</i> {{i18n "Required, cannot be empty" bundle="common"}}</span>
        </div>
        <input type="hidden" name="isDefault" value="{{data.isDefault}}">

      </div>
      <div class="modal-footer">
        <button class="btn btn-success btn-primary btn-medium" type="submit">{{i18n "Confirm" bundle="common"}}</button>
        <button class="btn btn-info btn-medium close">{{i18n "Cancel" bundle="common"}}</button>
      </div>
    </fieldset>
  </form>
</div>

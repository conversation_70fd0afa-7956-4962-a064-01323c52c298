<style>
  /*显示\隐藏密码图片*/
  .user-login-form #login-password #eye_img{
    /*width: 40px;*/
    height: 25px;
    position: absolute;
    right: 0px;
    margin: 7px;
    z-index: 5;
  }
  /*录手机号的地方增加国家代码*/
  .selectric {
    border: 1px solid #dfdfdf;
    background: #fff;
    position: relative;
    width: 114px;
    padding: 2px 2px;
    border-radius: 0;
  }
  .error-tips{
    color: red;
  }

</style>
<form class="form form-aligned user-login-form" method="post" action="/api/user/login" data-site-type="{{#if copartnerId}}{{copartnerId}}{{else}}7{{/if}}">
  <div class="control-group input-wrap" id="login-name" style="display: flex;
    align-items: center;
    justify-content: center;position: relative;">
    <input type="hidden" name="target" value="{{target}}">
    <input id="login-type" type="hidden" name="type" value="1">
    <input type="hidden" value="" name="countryCode">
    <div>
      <img class="input-icon"  src="/assets/images/other-images/mobile.png" alt="" width="20" height="20">
      <input id="loginId" type="text" class="form-control " style="width: 400px;
      height: 50px;
      background: rgb(255, 255, 255);
      border: 1px solid rgb(221, 221, 221);
      border-radius: 4px;
      padding: 7px 10px 5px 30px;" name="loginBy" placeholder="请输入手机号码">
    </div>
  </div>
  <div class="control-group input-wrap" id="login-password" style="display: flex;
    align-items: center;
    justify-content: center;position: relative;">
    <div>
      <img class="input-icon"  src="/assets/images/other-images/password.png" alt="" width="20" height="20">
      <input id="password" type="password" class="form-control " name="password" placeholder="{{i18n "Please enter your password" bundle="common"}}" style="width: 400px;
      height: 50px;
      background: rgb(255, 255, 255);
      border: 1px solid rgb(221, 221, 221);
      border-radius: 4px;
      padding: 7px 10px 5px 30px;">
      <img id="eye_img" onclick="hideShowPsw()" src="/assets/images/other-images/invisible.png" style="float: right;z-index: 5;margin-top: -35px;">
    </div>
  </div>
  <div style="
    padding-top: 15px;
    text-align: right;
    padding-bottom: 29px;">
  <!--  <a class="keep" href="/forget-password" style="font-family: PingFangSC-Semibold;
font-size: 14px;
color: #4A90E2;">{{i18n "Forgot password" bundle="common"}}?</a>-->
    {{!-- <a class="keep" href="/user/bind-card">{{i18n "Binding vip card" bundle="common"}}</a> --}}
    <span class="email-active hide">╭(╯3╰)╮{{i18n "Dear,your email has not been activated" bundle="common"}}</span>
    <a class="email-active pull-right toreg emailActive hide" href="javascript:void(0);">{{i18n "Immediately to activate" bundle="common"}}！</a>
  </div>
  <div class="control-group" id="login-submit" style="background: #3385D0;
    border-radius: 4px;
    border-radius: 4px;
    width: 400px;
    height: 47px;">
    <input type="submit" class="form-control btn btn-primary" disabled value="立即登录" style="
    background: #3385D0;
    border-radius: 4px;
    border-radius: 4px;
    height: 47px;
">
  </div>
</form>
<script type="text/javascript">
  // 这里使用最原始的js语法实现，可对应换成jquery语法进行逻辑控制
  var eyeImg = document.getElementById("eye_img");
  var demoInput = document.getElementById("password");
  //隐藏text block，显示password block
  function hideShowPsw(){
    if (demoInput.type == "password") {
      demoInput.type = "text";
      eyeImg.src = "/assets/images/other-images/visible.png";
    }else {
      demoInput.type = "password";
      eyeImg.src = "/assets/images/other-images/invisible.png";
    }
  }
</script>

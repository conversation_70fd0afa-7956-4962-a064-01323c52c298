@import "compass/css3/inline-block";
@import "compass/css3/box-shadow";
@import "compass/css3/border-radius";
@import "compass/css3/transition";
@import "compass/css3/opacity";
@import "pokeball/theme";

.component-login{
  width: 360px;
  padding: 50px 40px;
  background-color: white;
  border: 1px solid #DADADA;
  &.modal {
    padding: 30px;
    position: absolute;
  }

  @include box-shadow(0px 0px 30px darken($color-background, 20%));

  .user-login-form{
    font-size: 14px;
    margin: 0 50px;
    text-align: left;
    legend {
      margin-bottom: 50px;
      font-size: 14px;
    }
    .control-group {
      margin-bottom: 20px;
    }
    .keep-group {
      margin-bottom: 0;
      margin-top: 30px;
    }
    input {
      height: 40px;
      padding-left: 10px;
      padding-right: 10px;
      position: relative;
      @include border-radius(5px);
      vertical-align: middle;
      &.btn {
        font-size: 14px;
      }
    }
    #login-name, #login-password {
      background-color: #fff;
      position: relative;
      zoom: 1;
      margin-bottom: 20px;
      border: $color-border solid 1px;
      @include transition(all .5s 0 ease);
      &:hover {
        border-color: #129fea;
        z-index: 3;
      }
      &.error {
        z-index: 2;
        border-color: $color-danger;
        input {
          color: $color-secondary;
        }
      }
      .input-icon{
        position: absolute;
        top: 12px;
        left: 10px;
        font-size: 24px;
        z-index: 4;
        color: $color-text-note;
      }
      input {
        border: none;
        font-size: 12px;
        padding-left: 60px;
        @include transition(color .5s 0 ease);
      }
      .error-tips {
        color: $color-danger;
        font-size: 12px;
        height: 16px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: absolute;
        right: 3px;
        bottom: 25px;
        z-index: 3;
        @include opacity(0);
      }
    }
    .error-wall {
      color: $color-danger;
      margin-top: -10px;
      margin-bottom: 10px;
      height: 0;
      font-size: 12px;
      overflow: hidden;
      ul {
      }
    }
    .register-control {
      font-size: 14px;
      padding-top: 20px;
      border-top: $color-secondary dashed 1px;
    }
    .error {
      input {
        z-index: 1;
      }
    }
  }

  legend, a {
    color: $color-text;
  }
  .form {
    margin: 0;
  }
  .keep-group {
    padding-bottom: 20px;
    font-size: 12px;
    .toreg {
      color: $color-danger;
    }
    .register-a {
      color: $color-link;
    }
  }
  .login-type {
    line-height: 1.5em;
    li {
      margin-bottom: 10px;
      padding-left: 20px;
      list-style-type: none;
      position: relative;
      zoom: 1;
      a {
        text-decoration: underline;
      }
      i {
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -9px;
      }
    }
  }
}

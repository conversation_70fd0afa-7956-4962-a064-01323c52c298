<style>
  /*显示\隐藏密码图片*/
  .user-login-form #login-password img{
    /*width: 40px;*/
    height: 25px;
    position: absolute;
    right: 0px;
    margin: 7px;
    z-index: 5;
  }
  /*录手机号的地方增加国家代码*/
  .selectric {
    border: 1px solid #dfdfdf;
    background: #fff;
    position: relative;
    width: 120px;
    border-radius: 0;
    height: 40px
  }

  .selectric .button {
      display: block;
      position: absolute;
      right: 0;
      top: 5px;
      width: 28px;
      height: 25px;
      color: #F7F7F7;
      text-align: center;
      font: 0/0 a;
  }
  .selectric-items {
    display: none;
    position: absolute;
    top: 39px;
    left: 0;
    background-color: #fff;
    border: 1px solid #dfdfdf;
    z-index: -1;
    box-shadow: 0 0 10px -6px;
  }
</style>
<form class="form form-aligned user-login-form" method="post" action="/api/user/login" data-site-type="{{#if copartnerId}}{{copartnerId}}{{else}}7{{/if}}">
  <fieldset>
    <legend>{{i18n "Sign in" bundle="common"}}</legend>
    <div class="control-group input-wrap" id="login-name" style="display: flex; align-items: center;justify-content: center;border:hidden">
      <input type="hidden" name="target" value="{{target}}">
      <input id="login-type" type="hidden" name="type" value="1">
      <div style="width: 124px;display: inline-block;padding-left:2px;height:40px;">
        <select id="countryCode" name="countryCode" style="width:120px; height:40px;padding-top:10px">
          <option selected="true" value="">+86 大陆地区</option>
          <option value="0061">+61 澳大利亚</option>
        </select>
      </div>
      <div style="border: 1px solid #dfdfdf;margin-left:2px">
        <input id="loginId" type="text" class="form-control" style="width: 161px; padding-left: 2px" name="loginBy" placeholder="请输入手机号码">
      </div>
    </div>
    <div class="control-group input-wrap" id="login-password">
      <img id="eye_img" onclick="hideShowPsw()" src="/assets/images/other-images/invisible.png">
      <input id="password" type="password" class="form-control" name="password" placeholder="{{i18n "Please enter your password" bundle="common"}}">
      <i class="icon-feebas input-icon icon-feebas-lock" style="z-index: 1;"></i>
   </div>
    <div class="control-group keep-group">
      <a class="keep" href="/forget-password">{{i18n "Forgot password" bundle="common"}}?</a>
      {{!-- <a class="keep" href="/user/bind-card">{{i18n "Binding vip card" bundle="common"}}</a> --}}
      <a class="keep pull-right register-a" href="/register">{{i18n "Register now" bundle="common"}}</a>
      <span class="keeped hide">╭(╯3╰)╮{{i18n "Your account has not been registered" bundle="common"}}</span>
      <a class="keeped pull-right toreg hide" href="/register">{{i18n "Go to registration" bundle="common"}}！</a>
      <span class="email-active hide">╭(╯3╰)╮{{i18n "Dear,your email has not been activated" bundle="common"}}</span>
      <a class="email-active pull-right toreg emailActive hide" href="javascript:void(0);">{{i18n "Immediately to activate" bundle="common"}}！</a>
    </div>
    <div class="control-group" id="login-submit">
      <input type="submit" class="form-control btn btn-primary" disabled value="立即登录">
    </div>

  </fieldset>
</form>
<script type="text/javascript">
  // 这里使用最原始的js语法实现，可对应换成jquery语法进行逻辑控制
  var eyeImg = document.getElementById("eye_img");
  var demoInput = document.getElementById("password");
  //隐藏text block，显示password block
  function hideShowPsw(){
    if (demoInput.type == "password") {
      demoInput.type = "text";
      eyeImg.src = "/assets/images/other-images/visible.png";
    }else {
      demoInput.type = "password";
      eyeImg.src = "/assets/images/other-images/invisible.png";
    }
  }
</script>

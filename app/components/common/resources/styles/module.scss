@import "pokeball/theme";
@import "compass/css3";
@import "common/resources/styles/components";
@import "common/resources/styles/error";
@import "common/resources/styles/static";
@import "common/resources/styles/layout";

$header-height: 60px;
$foot-height: 41px;
$sidebar-width: 200px;
$sidebar-width-mini: 36px;

.body {
  background-color: #edeeef;
}
//.component-standard-container {
//  padding: 16px 0 0;
//}

.user-center-wrapper {
  padding-top: 100px;
  width: 100%;
  display: block;

  .user-center-container {
    width: 1200px;
    margin: 0 auto;

    .component-standard-tab-container {
      min-width: auto;

      .tab-contents {
        padding: 20px 0 0;
      }
    }

    .component-standard-container {
      padding: 0;
      min-width: auto;
    }
  }
}

.top-ceiling {
  &.user-center {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 4;
    display: block;
  }
}

.main-top {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 400;
  display: block;

  &.user-center {
    padding-top: 40px;
    background-color: $color-primary;
  }
}

.main-left {
  position: fixed;
  left: 0;
  width: 180px;
  top: 0;
  bottom: 0;
  z-index: 2;
  display: block;
  background-color: $color-sidebar-bg;

  &.user-center {
    width: 180px;
    padding-top: 0px;
    position: relative;
    float: left;
    background: none;

    .component-sidebar-container {
      .sidebar {
        padding-top: 0;
        background: none;
      }
    }

    .nav-bg-box {
      position: fixed;
      right: 400px;
      top: 0;
      width: 100000px;
      height: 100000px;
      background-color: $color-buyer-sidebar-bg;
      margin-right: 50%;
    }
  }
}

.main-left-mini {
  width: $sidebar-width-mini;
}

.white-space-normal {
  white-space: normal !important;
}

.main-right {
  margin-top: 0;
  margin-bottom: 0;
  padding: 60px 16px 0 16px;
  overflow-y: auto;
  display: block;
  position:static;
  background-color: #edeeef;

  &.user-center {
    background-color: #ffffff;
    margin-left: 200px;
    padding: 0 0 0 24px;
    margin-bottom: 24px;
  }
}

.main-right-mini {
  margin-left: $sidebar-width-mini;
}

.main-foot {
  position: fixed;
  margin-left: $sidebar-width;
  display: block;
  z-index: 2;
  height: $foot-height;
  background-color: #fff;
  border-top: 1px solid #e4eaec;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
}

.hide {
  display: none;
}

ul,
ol {
  padding: 0;
  margin: 0;
  list-style: none;
}


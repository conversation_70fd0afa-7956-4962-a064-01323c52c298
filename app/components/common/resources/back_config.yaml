login:
  type: 'http'
  url: "/api/user/login"
  query:
    - key: username
    - key: password
getUserById:
  type: 'http'
  url: "/api/user/{userId}"
getRolesByUserId:
  type: 'http'
  url: "/api/user/{userId}/roles"

getMemberIdByUserId:
  type: 'http'
  url: "/api/member/profile/user/{userId}"

getShopByshopId:
  type: 'http'
  url: "/api/shop/{shopId}"

getCurrentUser:
  type: 'http'
  url: "/api/user/current"

getRequestToken:
  type: 'http'
  url: "/api/token/request"

@import "compass/css3";
@import "pokeball/theme";

.shop-choose {
  .cutwords {
    overflow: visible;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .shop-container {
    width: 600px;
    margin-bottom: 20px;
    table.table {
      th {
        border: none;
      }
      td {
        border-left: none;
        border-right: none;
        height: 60px;
      }
      tbody:last-child tr {
        background-color: #ffffff;
        vertical-align: middle;
      }
      .top-text {
        vertical-align: top;
        display: inline-block;
        line-height: 24px;
        max-width: 230px;
      }
      .shop-choose-pagination label.total {
        display: none;
      }
      .shop-list-pagination label.total {
        padding-right: 10px;
      }
    }
  }
}

.shop-choose-dialog {
  width: 1000px !important;
  .table-wrapper {
    max-height: 350px !important;
    overflow-y: auto;
  }
  .control-group.sp {
    .control-label.menu {
      width: 108px;
      text-align: left;
      ul {
        height: 26px;
        display: inline-block;
        overflow-y: visible;
        border: none;
        li {
          padding: 4px 0;
          background: none;
          span.caret {
            color: #999999;
          }
        }
      }
    }
    input {
      vertical-align: top;
    }
    a.searchBtn {
      line-height: 32px;
      display: inline-block;
      vertical-align: top;
      margin-left: 10px;
    }
  }
  .jump-to-shop {
    max-width: 190px;
    display: inline-block;
    vertical-align: middle;
    white-space: normal;
    overflow: visible;
  }
  table.table {
    th {
      border: none;
    }
    td {
      border-left: none;
      border-right: none;
    }
    tbody:last-child tr {
      background-color: #ffffff;
      td {
        border-bottom: none;
        height: 60px;
      }
    }
  }
  .shop-choose-pagination label.total {
    display: none;
  }
  .pagination {
    margin-top: 0;
  }
  .choose-all,
  .cancel-all {
    vertical-align: middle;
  }
  .shop-search-type {
    width: 115px;
    height: 40px;
    margin: 10px 100px 10px 10px;
  }
  .search_pruduct {
    width: 150px;
    height: 40px;
    margin: 10px 100px 10px 100px;
  }
  .searchBtn_pruduct {
    margin: 10px 0px;
  }
}

.sku-choose-dialog {
  width: 600px;
  .h20 {
    height: 20px !important;
    line-height: 18px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .table-wrapper {
    max-height: 350px;
    overflow-y: auto;
  }
  label {
    width: auto !important;
    margin-right: 5px !important;
  }
  .skuName {
    max-width: 100%;
    display: inline-block;
  }
  table.table {
    th {
      border: none;
    }
    td {
      border-left: none;
      border-right: none;
    }
    tbody tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
  .form-choose-dialog {
    padding: 15px 0;
  }
}

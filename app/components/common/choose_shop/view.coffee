###
  选择商品及sku组件
  author by terminus.io (zl)
###
Tip = require "common/tip_and_alert/view"
Modal = require("pokeball").Modal
Pagination = require("pokeball").Pagination
chooseShopTemplate = Handlebars.templates["common/choose_shop/frontend_templates/choose_shop"]
shopChooseListTemplate = Handlebars.templates["common/choose_shop/frontend_templates/shop_choose_list"]
shopListTemplate = Handlebars.templates["common/choose_shop/frontend_templates/shop_list"]

class chooseShop
  constructor: ($)->
    @chooseShopForm = "#shop-choose-form"
    @shopSearchForm = $(".form-choose-dialog");
    @createBtn = $(".js-shop-choose")
    @shopIds = $("[name=shopids]")
    @shopIdsTemp = @shopIds.data("shopids")
    @shopNameEle = $("input[name=shopSearchNameKey]");
    @shopAddresEle = $("input[name=shopSearchAddresKey]");
    @bindEvent()


  bindEvent: ()->
    @createBtn.on "click", @createItemFn
    @itemListInit()

  # 搜索服务商
  searchShop: () =>
    @shopNameVal = $("[name=shopSearchNameKey]").val()
    @shopAddresVal = $("[name=shopSearchAddresKey]").val()
    @renderItems(1, "true");


  itemListInit: () =>
    method = $("[name=shopmethod]").val()
    if @shopIds.data("shopids")
      if !_.isArray(@shopIds.data("shopids"))
        @shopIdsTemp = @shopIds.data("shopids").replace("[","").replace("]","");
    if @shopIdsTemp.length
       $.ajax
        url: "/api/subStore/service-provider/paging?idList=#{@shopIdsTemp}"
        type: "GET"
        contentType: "application/json"
        success: (data) =>
          $(".shop-container").removeClass('hide')
          data.data.data.map((item)=>
            if method == "detail"
              item.hidedel = 1
          );
          $(".shop-container tbody.shop-list").html shopListTemplate({data: data.data.data})

  #初始化商品选择modal
  formChooseItemInit: () =>
    $("#shop-choose-form").on "click", ".shop-choose", @chooseItem
    $("#shop-choose-form").on "click", ".shop-cancel", @cancelItem
    $('.js-shop-filter').on 'click', @searchShop
    $("#shop-choose-form").on 'submit', @submitShops
    $(".shop-choose").on "click", ".delete", @deleteItem
    $(".choose-shop-all").on "click", @selectAllShop

  selectAllShop: () =>
    checked = $("[name=select-shop-all]").prop "checked"
    result = []
    if checked
      $("#shop-choose-form .shop-choose").each ->
        id = $(this).data("userid");
        result.push(id)
      $("#shop-choose-form .shop-choose").removeClass("btn-primary shop-choose").addClass("btn-info shop-cancel").text("取消")
      @shopIdsTemp = @shopIdsTemp.concat(result)
    else
      $("#shop-choose-form .shop-cancel").each ->
        id = $(this).data("userid");
        result.push(id)
      $("#shop-choose-form .shop-cancel").removeClass("btn-info shop-cancel").addClass("btn-primary shop-choose").text("选取")
      @shopIdsTemp = @shopIdsTemp.filter((item)-> !result.includes(item));
    $("[name=shopids]").data("shopids", @shopIdsTemp)

  deleteItem: (evt) =>
    data = $(evt.currentTarget).data("info")
    shopId = data.id
    if _.isArray(@shopIdsTemp)
      @shopIdsTemp.splice($.inArray(shopId, @shopIdsTemp),1);
    else
      arr = @shopIdsTemp.replace("[","").replace("]","").split(',')
      arr.splice($.inArray(shopId, arr),1);
      @shopIdsTemp = arr;
    if @shopIdsTemp.length>0
      $.ajax
        url: "/api/subStore/service-provider/paging?idList=#{@shopIdsTemp.join(',')}"
        type: "GET"
        contentType: "application/json"
        success: (data) =>
          $("[name=shopids]").data "shopids", @shopIdsTemp.join(',')
          $(".shop-container tbody.shop-list").html shopListTemplate({data: data.data.data})
          $(".modal").spin(false)
    else
      $("[name=shopids]").data "shopids", @shopIdsTemp.join(',')
      $(".shop-container tbody.shop-list").html shopListTemplate({data: []})
      $(".modal").spin(false)

  #提交选中的商品
  submitShops: (evt) =>
    evt.preventDefault()
    if !@shopIdsTemp.length
      new Modal
        "icon": "error"
        "title": "保存失败"
        "content": "至少需要选择一个商品"
      .show()
      return false
    $.ajax
      url: "/api/subStore/service-provider/paging?idList=#{@shopIdsTemp}"
      type: "GET"
      contentType: "application/json"
      success: (data) =>
        $(".shop-container").removeClass('hide')
        $("[name=shopids]").data("shopids", @shopIdsTemp)
        $(".shop-container tbody.shop-list").html shopListTemplate({data: data.data.data})
        $(".modal").spin(false)
        $("#shop-choose-form button.close").click()

  #初始化已选取的商品
  initItemChoosed: (data) =>
    $.each data.data.data, (n,v)=>
      itemId = data.data.data[n].id
      if $.inArray(itemId, @shopIdsTemp) > -1
        data.data.data[n].choosed = true
    data
  #选中商品
  chooseItem: (evt) =>
    data = $(evt.currentTarget).data("info")
    shopId = $(evt.currentTarget).data("userid")
    @shopIdsTemp.push(shopId)
    @shopIds.data("shopIds",@shopIdsTemp)
    $(evt.currentTarget).removeClass("btn-primary shop-choose").addClass("btn-info shop-cancel").text("取消")

  #取消商品
  cancelItem: (evt) =>
    data = $(evt.currentTarget).data("info")
    shopId = $(evt.currentTarget).data("userid")
    @shopIdsTemp.splice($.inArray(shopId, @shopIdsTemp),1)
    delete @shopIdsTemp[shopId]
    @shopIds.data("shopIds",@shopIdsTemp)
    # $.each data.skus, (n,v)=>
    #   @skuIdsTemp.splice($.inArray(v.id, @skuIdsTemp),1)
    #   delete @skusDiscountInfoTemp[v.id]
    $(evt.currentTarget).removeClass("btn-info shop-cancel").addClass("btn-primary shop-choose").text("选取")

  createItemFn: () =>
    @shopIdsTemp = @shopIds.data("shopIds") || []
    @itemSearchKey = undefined
    @shopCategoryId= ""
    @renderItems(1, "init")

  renderItems: (pageNo,type) =>
    # @shopIdsTemp = @shopIds.data("shopids")
    url = "/api/subStore/service-provider/paging?pageNo=#{pageNo}&pageSize=15"
    if @shopNameVal
      url += "&name=#{@shopNameVal}"
    if @shopAddresVal
      url += "&address=#{@shopAddresVal}"
    $(".modal").spin("small")
    $.ajax
      url: url
      type: "GET"
      contentType: "application/json"
      success: (data) =>
        @initItemChoosed(data)
        if type is "init"
          new Modal(chooseShopTemplate({data: data.data.data, shopsHtml: shopChooseListTemplate({data: data.data.data})})).show()
          @formChooseItemInit()
        else
          $(".shop-choose-dialog tbody.shop-choose-list").html shopChooseListTemplate({data: data.data.data})
        if type
          new Pagination(".shop-choose-pagination").total(data.data.total).show 15,
            num_display_entries: 3
            page_size_switch: false
            jump_switch: false
            maxPage: -1
            callback: (pageNo) =>
              @renderItems pageNo + 1
        $(".modal").spin(false)

module.exports = chooseShop

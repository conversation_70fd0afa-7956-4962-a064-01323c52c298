{{#each data}}
<tr>
  <td data-info="{{json this}}">
    {{#equals method "detail"}}{{else}}
    <label class="item-label">
      <input type="checkbox" value="{{item.id}}" name="new-select-item-list" class="new-js-select-item">
    </label>
    &nbsp;
    {{/equals}}
    <span class="top-text">
      <a class="jump-to-item" href="/items/{{item.id}}" target="_blank" title="{{item.name}}">{{item.name}}</a>
      <br>
      {{#equals item.lowPrice item.highPrice}}
      ￥{{formatPrice item.lowPrice}}
      {{else}}
      ￥{{formatPrice item.lowPrice}}-{{formatPrice item.highPrice}}
      {{/equals}}
    </span>
  </td>
  <td>
    <a href="javascript:;" class="new-showSkus" data-info="{{json skus}}">{{#equals ../promotionType 3}}设置{{/equals}}共{{skuNum}}个规格</a>
  </td>
  {{#equals type 4}}
    <td>
      <input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'')">
    </td>
  {{/equals}}
  <td class="right-text">
    {{#equals method "detail"}}
    -
    {{else}}
    <a class="new-delete" data-info="{{json this}}">删除</a>
    {{/equals}}
  </td>
</tr>
{{/each}}

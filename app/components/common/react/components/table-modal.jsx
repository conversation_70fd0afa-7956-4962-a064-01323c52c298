const {useState,useEffect,useRef,Fragment} = React;
const { Modal, Tabs, Table, Input, Button, Radio,Checkbox,Row,Col,Space,message,Form,Select, DatePicker}= antd;
const {PlusCircleFilled} = icons;
const { TabPane } = Tabs;
// const InputNumber = Input.InputNumber
const { InputNumber,TextArea } = Input;
const { RangePicker } = DatePicker;
const FormItem = Form.Item;
import request from "../../../utils/plugins/axios/request";
export default ({
    searchConfigs,
    tableColumn,
    show,
    closeFn,
    title,
    header,
    dataUrl,
    searchParams,
    scroll,
    modalWidth
}) => {
    const [form] = Form.useForm()
    const [configs,setConfigs] = useState([])
    const [dataSource,setDataSource] = useState([])
    const [totalData,setTotalData] = useState(0)
    let [pagination, setPagination] = useState({ pageSize: 20, current: 1 });
    function switchType(item) {
        switch (item.type) {
            case "INPUT":
                return (
                    <Input
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        maxLength={item.maxLength || 9999}
                        autoComplete={item.autoComplete || null}
                    />
                );
            case "INPUTNUMBER":
                return (
                    <InputNumber
                        disabled={item.disabled}
                        placeholder={item.placeholder || `请输入${item.labelName}`}
                        max={item.max}
                        min={item.min}
                    />
                );
            case "SELECT":
                return (
                    <Select
                        onChange={item.onChange ? e => item.onChange(e, form) : e => {}}
                        mode={item.mode}
                        style={{ width: "100%" }}
                        showSearch
                        disabled={item.disabled}
                        optionFilterProp="children">
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Option value={ite.value || ite.id} key={index}>
                                        {ite.name}
                                    </Option>
                                );
                            })}
                    </Select>
                );
            case "CHECKBOX":
                return (
                    <Checkbox.Group>
                        {item.list.length &&
                            item.list.map((ite, index) => {
                                return (
                                    <Checkbox value={ite.value} key={index}>
                                        {ite.name}
                                    </Checkbox>
                                );
                            })}
                    </Checkbox.Group>
                );
            case "DATE":
                return <DatePicker showTime />;
            case "DATERANGE":
                return <RangePicker showTime />;
            case "PASSWORD":
                return <Input.Password />;
            default:
                return null;
        }
    }

    const search = (pages)=>{
        let defaultPage = pages ||{
            current: 1,
            pageSize: 20,
        };
        form.validateFields().then(values => {
            Object.keys(values).map(item => {
                if (values[item] === "" || values[item] === null || values[item] === undefined ) {
                    delete values[item];
                }
            });
            searchConfigs.map((item)=>{
                if(item.type === 'DATERANGE'){
                    // searchValue
                    const arrs = values[item.labelKey];
                    console.log("arrs:",arrs)
                    if(arrs && arrs.length > 0){
                        values[item.searchValue[0]]= arrs[0].valueOf();
                        values[item.searchValue[1]]= arrs[1].valueOf();
                        delete values[item.labelKey]
                    }
                }
            })
            fetch(defaultPage, values);
        });
    }

    const reset = ()=>{
        form.resetFields();
        let defaultPage = {
            current: 1,
            pageSize: 20,
        };
        fetch(defaultPage,{});
    }
    function fetch(page, params) {
        // setLoading(true);
        let pagi = Object.assign(pagination, page);

        request({
            url:dataUrl,
            data: {
                currentPage: pagi.current,
                pageSize: pagi.pageSize,
                // ...props,
                ...searchParams,
                ...params,
            },
            needMask: true,
            success: res => {
                // setLoading(false);
                setDataSource(res.dataList);
                setTotalData(Object.assign(totalData, { [res.page.currentPage]: res.dataList }));
                // calcKeys(res.page.currentPage, res.dataList);
                let page = {
                    current: res.page.currentPage,
                    total: res.page.totalCount,
                    pageSize: res.page.pageSize,
                    showTotal: total => `共${total}条`,
                };
                setPagination(page);
            },
        });
    }

    useEffect(() => {
        if (show) {
            fetch({ pageSize: 20, current: 1 });
            form.resetFields();
            form.setFieldsValue({...searchParams});
        }
    }, [show]);

    useEffect(()=>{
        if(searchConfigs && searchConfigs.length){
            const requests = [];
            searchConfigs.map((item, index) => {
                // 可以加参数的 自己再写一个参数即可
                if (item.url) {
                    requests.push(
                        new Promise((resolve, reject) => {
                            request({
                                url: item.url,
                                needMask: false,
                                success: res => {
                                    resolve({ data: res, index: index });
                                },
                                fail: () => {
                                    reject();
                                },
                            });
                        }),
                    );
                }
            });
            Promise.all(requests)
                .then(res => {
                    res.map(item => {
                        // console
                        searchConfigs[item.index].list = item.data;
                    });
                    // this.setState({ configList: row });
                    setConfigs(searchConfigs)
                })
                .catch(err => {});
        }
    },[searchConfigs])

    return <Modal
        title={title}
        open={show}
        width={modalWidth ||1200}
        onCancel={()=>{
            form.resetFields();
            closeFn && closeFn();
        }}
        onOk={()=>{
            form.resetFields();
            closeFn && closeFn();
        }}
    >
            <Fragment>
            {
                searchConfigs && <Fragment>
                    <Form form={form}>
                        <Row gutter={[16, 10]}>
                          {
                            configs.map((item,index)=>{
                                return (
                                    <Col
                                        span={item.type ==='DATERANGE'?12: 6}
                                        md={item.type ==='DATERANGE'?12: 6}
                                        lg={item.type ==='DATERANGE'?12: 6}
                                        index={index}
                                    >
                                        <FormItem label={item.labelName} name={item.labelKey} key={index}>
                                            {switchType(item)}
                                        </FormItem>
                                    </Col>
                                )})
                            }
                            <Col>
                                <FormItem>
                                    <Space>
                                        <Button type="primary" onClick={()=>search()}>
                                            查询
                                        </Button>
                                        <Button onClick={reset}>重置</Button>
                                    </Space>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </Fragment>
            }
            {header}
            <Table
                columns={tableColumn}
                dataSource={dataSource}
                style={{maxHeigth:'800px'}}
                pagination={{
                    ...pagination,
                    onChange:(page,pageSize)=>{
                        console.log(page,pageSize)
                        search({
                           current: page,
                            pageSize
                        })
                    }
                }}
                scroll={scroll||{
                    x:1000,
                    y:500
                }}
            />
        </Fragment>
    </Modal>
}

const  { useMemo, useRef, useState, useEffect, useCallback, memo } = React
const { Select, Spin } = antd;

import useIsMounted from "../hook/useMouted";

const initPagination = { current: 1, pageSize: 20 };

const DebounceSelect = ({
    fetchOptions, // 异步获取选项的函数
    fixedParams = {}, // 固定参数
    debounceTimeout = 500,
    ...pureProps
}) => {
    const fetchRef = useRef(0);
    const completeRef = useRef(false); // 是否已经获取完所有数据
    const [searchValue, setSearchValue] = useState();
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState([]);
    const [pagination, setPagination] = useState(initPagination);

    const isMounted = useIsMounted();

    const debounceFetcher = useMemo(() => {
        const loadOptions = params => {
            if (!isMounted.current) return;
            fetchRef.current += 1;
            const fetchId = fetchRef.current;
            setFetching(true);
            fetchOptions(params)
                .then(res => {
                    if (fetchId !== fetchRef.current) {
                        return;
                    }
                    const { list, complete } = res;
                    const { current } = pagination;
                    setOptions(current > 1 ? [...options, ...list] : list);
                    complete && (completeRef.current = complete);
                })
                .catch(err => {
                    console.log(err);
                })
                .finally(() => {
                    setFetching(false);
                });
        };
        return _.debounce(loadOptions, debounceTimeout);
    }, [debounceTimeout, fetchOptions, options, pagination]);

    useEffect(() => {
        debounceFetcher({ value: searchValue, ...pagination, ...fixedParams });
        // eslint-disable-next-line react-hooks/exhaustive-deps\
        return () => {};
    }, [pagination, searchValue]);

    const onDropdownVisibleChange = open => {
        open && !options.length && debounceFetcher({ value: searchValue, ...pagination, ...fixedParams });
    };

    const props = useMemo(
        () =>
            pagination
                ? {
                      ...pureProps,
                      onPopupScroll: e => {
                          if (completeRef.current) return;
                          const {
                              target: { scrollTop, clientHeight, scrollHeight },
                          } = e;
                          if (scrollHeight <= scrollTop + clientHeight) {
                              setPagination(prev => ({ ...prev, current: prev.current + 1 }));
                          }
                      },
                  }
                : pureProps,
        [pagination, pureProps],
    );

    const onSearch = useCallback(newValue => {
        setSearchValue(newValue);
        completeRef.current = false;
        setPagination(initPagination);
    }, []);

    return (
        <Select
            filterOption={false}
            onSearch={onSearch}
            onClear={onSearch}
            loading={fetching}
            options={options}
            onDropdownVisibleChange={onDropdownVisibleChange}
            dropdownRender={originNode => <Spin spinning={fetching}>{originNode}</Spin>}
            {...props}></Select>
    );
};

export default memo(DebounceSelect);

import {loading} from "./utils/loading";

const {useRef, Fragment} = React;
const {Button, message,Modal} = antd;
/**
 *
 * @param rowId 操作的行id，一般用于列表中多行数据。区分行导出定时器使用。 默认1
 * @param type 按钮类型
 * @param exportButtonText 导出文案
 * @param exportUrl 导出url
 * @param onGetExportParam 获取导出参数
 * @return {JSX.Element}
 * @constructor
 */
export default function ExportButton({ rowId=1,
                                       type="button",
                                       exportButtonText="导出",
                                       exportUrl,
                                       onGetExportParam = () => {
                                       },
                                       method="post",
                                       contentType
                                     }) {
  const timerRef = useRef(new Map())
  const exportDetail = () => {
   loading.wait("导出中,请稍等")
    if (!timerRef.current.has(rowId)) {
      const data = onGetExportParam()
      $.ajax({
        url: exportUrl,
        type: method,
        contentType: contentType || "application/x-www-form-urlencoded",
        data: data,
        success: (data) => {
          if(!data.success) {
            loading.waitEnd()
            Modal.error({
              title: "导出失败",
              content: `导出失败,${data.errorMsg}`,
            })
          }else{
            const intervalId = setInterval(() => {
              $.ajax(
                {
                  url: `/api/dataExportTask/findById`,
                  type: "GET",
                  data: {taskId: data.data},
                  contentType:"application/x-www-form-urlencoded",
                  success: (res) => {
                    if (res.data.fileUrl) {
                      loading.waitEnd()
                      window.open(res.data.fileUrl)
                      clearInterval(timerRef.current.get(rowId))
                      message.success("导出成功")
                      timerRef.current.delete(rowId)
                    } else {
                      console.log(res)
                    }
                  }
                })
            }, 1000)
            timerRef.current.set(rowId, intervalId)
          }
        },
        error: (data) => {
          loading.waitEnd()
          Modal.error({
            title: "导出失败",
            content: data.responseJSON.message,
          })
        }
      })
    }


  }
  return <Fragment>
    {type==="button"&& <Button className="btn" onClick={() => exportDetail()}>{exportButtonText} &#xe638;</Button>}
    {type==="link"&&<a className={"link"}>{exportButtonText}</a>}
  </Fragment>

}

const {useState, Fragment} = React;
const {Button, Modal, Upload, message} = antd;
const {PlusOutlined} = icons
export default function ImportButton({templateUrl, importNotice}) {
  const [open, setOpen] = useState(false)
  const accept = "xls,xlsx"
  const picUploadButton = (
    <div>
      <PlusOutlined/>
      <div style={{marginTop: 8, color: "rgba(0, 0, 0, 0.65)"}}>上传</div>
    </div>
  );
  const doDoneResponse = (response) => {
    if (!response) {
      return
    }
    const {success, result, error} = response
    if (success === true && result === true) {
      Modal.success({
        content: "上传成功",
        title: "提示信息",
        onOk: () => setOpen(false)
      })
    } else {
      // 其他原因则是success:false
      if (success === false) {
        Modal.error({
          content: "导入失败，" + error,
          title: "提示信息"
        })
      } else {
        //接口变化为成功返回一个success:true，失败返回一个success:true,result:${code}
        // excel数据内容校验有误时，下载数错校验文件
        Modal.error({
          content: "导入文件内容校验有误，请查看下载的错误文件",
          title: "提示信息"
        })
        const $eleForm = $("<form method='get' enctype=\"multipart/form-data\"><input name='code' value='" + result + "'></form>");
        $eleForm.attr("action", "/api/subStore/get-excel-by-code")
        $(document.body).append($eleForm);
        //提交表单，实现下载
        $eleForm.submit();
      }

    }
  }
  return <Fragment>
    <Modal open={open}
           width={800}
           cancelText={'关闭'}
           okButtonProps={{style: {display: 'none'}}}
           onCancel={() => setOpen(false)}
           destroyOnClose
           title={"导入"}>
      <div className="import-container">
        <div className={"box"}>
          <div className="title">步骤1：下载模板并完成数据录入</div>
          <div className="text">
            {importNotice}
          </div>
          <a className="link" href={templateUrl} target="_blank">
            下载数据模板
          </a>
        </div>
        <div className={"box"}>
          <div className="title">步骤2：上传已经录入的数据文件</div>
          <div className="text">
            文件后缀必须为xls或xlsx（即Excel格式），您可以点击上传文件或者拖拽文件至上传框中
          </div>
          <Upload
            listType={"picture-card"}
            name='file'
            accept={".xls,.xlsx"}
            action='/api/subStore/import-subStore'
            beforeUpload={(file) => {
              let suffix = file.name.split(".").reverse()[0].toLocaleLowerCase();
              if (accept.length > 0 && !accept.includes(suffix)) {
                message.error(`对不起，只支持类型为${accept}的文件`);
                return Upload.LIST_IGNORE;
              }
            }}
            maxCount={1}
            onChange={info => {
              const {file} = info
              if (file.status === "done") {
                doDoneResponse(file.response)
              }
            }}
          >
            {picUploadButton}
          </Upload>
        </div>
      </div>
    </Modal>
    <Button className="btn" onClick={() => setOpen(true)}>导入 &#xe639;</Button>
  </Fragment>
}

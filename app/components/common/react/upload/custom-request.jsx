// eslint-disable-next-line no-undef,no-unused-vars
export default function customRequest({
                         data,
                         file,
                         filename,
                         headers,
                         onError,
                         onProgress,
                         onSuccess,
                         withCredentials,
                       }) {
  // eslint-disable-next-line no-undef
  const formData = new FormData()
  if (data) {
    Object.keys(data).forEach(key => {
      formData.append(key, data[key])
    })
  }
  formData.append(filename, file)
  axios
    .post("/api/user/files/upload?folderId=0", formData, {
      withCredentials,
      headers,
      onUploadProgress: ({ total, loaded }) => {
        onProgress({ percent: Math.round((loaded / total) * 100).toFixed(2) }, file)
      },
    })
    .then(({ data: response }) => {
      file.url = response.image
      onSuccess(response, file)
    })
    .catch(onError)
  return {
    abort() {
      console.log("upload progress is aborted.")
    },
  }
}

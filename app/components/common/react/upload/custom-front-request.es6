import { lib } from "../utils/lib";
import request from "../../../utils/plugins/axios/request";

function customFrontRequest({
                              data,
                              file,
                              filename,
                              onError,
                              onProgress,
                              onSuccess,
                              subPath,
                              maxFileSize = 10485760
                            }) {
  // eslint-disable-next-line no-undef
    const bucket = "dante-minshop"
    const region = "cn-hangzhou"
    const suffix = file.name.split(".").reverse()[0].toLocaleLowerCase();
    const key = `miniShop/${subPath}/${lib.generateNumberString(false,18)}_${parseInt(Math.random() * 1000)}.${suffix}`;
  request({
    url: '/mall-admin/api/oss/post-signature',
    method: 'POST',
    data: {
      bucket,
      region,
      maxFileSize,
      uploadDir: key

    },
    success: function (data) {
      let formData = new FormData();
      formData.append("success_action_status", "200");
      formData.append("policy", data.policy);
      formData.append("x-oss-signature", data.signature);
      formData.append("x-oss-signature-version", data.version);
      formData.append("x-oss-credential", data.xossCredential);
      formData.append("x-oss-date", data.xossDate);
      formData.append("key", key);
      formData.append("x-oss-security-token", data.securityToken);
      formData.append("file", file);

      // formData.append("name", "dev/" + key);
      // formData.append("key", key);
      // formData.append(
      //   "policy", "ewogICAgImV4cGlyYXRpb24iOiAiMjA0OS0xMi0zMVQwNTo0ODowNy4wMDBaIiwKICAgICJjb25kaXRpb25zIjogWwogICAgICAgIFsKICAgICAgICAgICAgImNvbnRlbnQtbGVuZ3RoLXJhbmdlIiwKICAgICAgICAgICAgMCwKICAgICAgICAgICAgMTA0ODU3NjAwMAogICAgICAgIF0KICAgIF0KfQ=="
      // );
      // formData.append("OSSAccessKeyId", "LTAI5tNGqjjEfJ1n3xbV42v9");
      // formData.append("success_action_status", "200");
      // formData.append("signature", "oRnH+J0Ru/bPN/E1aAAO76P+DHg=");
     // formData.append("file", file);
      axios
        .request({
          url: `https://${data.host}`,
          method: "POST",
          data: formData,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            'Content-Disposition': 'inline',
          },
          onUploadProgress: ({ total, loaded }) => {
            onProgress && onProgress({ percent: Math.round((loaded / total) * 100).toFixed(2) }, file)
          },
        })
        .then((json) => {
          file.url = `https://${data.host}/${key}`
          onSuccess(json, file)
        })
        .catch(onError)
    },
  })

  return {
    abort() {
      console.log("upload progress is aborted.")
    },
  }
}

module.exports = {
  customFrontRequest,
}

export function isWindow(obj) {
  return obj !== null && obj !== undefined && obj === obj.window
}

export default function getScroll(target, top) {
  if (typeof window === "undefined") {
    return 0
  }
  const method = top ? "scrollTop" : "scrollLeft"
  let result = 0
  if (isWindow(target)) {
    result = target[top ? "pageYOffset" : "pageXOffset"]
    // eslint-disable-next-line no-undef
  } else if (target instanceof Document) {
    result = target.documentElement[method]
    // eslint-disable-next-line no-undef
  } else if (target instanceof HTMLElement) {
    result = target[method]
  } else if (target) {
    // According to the type inference, the `target` is `never` type.
    // eslint-disable-next-line max-len
    // Since we configured the loose mode type checking, and supports mocking the target with such shape below::
    //    `{ documentElement: { scrollLeft: 200, scrollTop: 400 } }`,
    //    the program may fall into this branch.
    // Check the corresponding tests for details. Don't sure what is the real scenario this happens.
    result = target[method]
  }

  if (target && !isWindow(target) && typeof result !== "number") {
    const targetDocumentElement =
      (target.ownerDocument ? target.ownerDocument : target).documentElement
    if (targetDocumentElement != null) { result = targetDocumentElement[method] }
  }
  return result
}

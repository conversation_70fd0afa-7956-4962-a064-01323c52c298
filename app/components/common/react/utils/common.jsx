const getParam = (key, url) => {
    var strs = (url || window.location.href).split("?")[1];
    if (!strs) {
        return null;
    }
    var json = {};
    var b = strs.split("&");
    for (var i = 0; i < b.length; i++) {
        var [_key, _value] = b[i].split("=");
        json[_key] = _value;
    }
    if (key == undefined) {
        return json;
    }
    if (json[key] == undefined) {
        return null;
    }
    return decodeURIComponent(json[key]);
}

export {
    getParam
}

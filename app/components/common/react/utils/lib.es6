/**
 yyyy-MM-dd : 年月日
 yyyy-MM-dd hh:mm:ss 年月日 时分秒
 **/
Date.prototype.format = function (fmt) {
  //author: meizz
  var o = {
    "M+": this.getMonth() + 1, //月份
    "d+": this.getDate(), //日
    "h+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }
  return fmt;
};

var lib = {
  config: {
    webToken: "admin",
    env: "online",
    hostLogin: "integration.yang800.com.cn",
    // openByWindow:false,
    // pageTitleCanEmpty:false
    loadUrlList: [], // 加载load
  },
  throttle(fn, delay) {
    var timer;
    return function () {
      var _this = this;
      var args = arguments;
      if (timer) {
        return;
      }
      timer = setTimeout(function () {
        fn.apply(_this, args);
        timer = null; // 在delay后执行完fn之后清空timer，此时timer为假，throttle触发可以进入计时器
      }, delay);
    };
  },
  debounce(fn, delay) {
    var timer; // 维护一个 timer
    return function () {
      var _this = this; // 取debounce执行作用域的this
      var args = arguments;
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(function () {
        fn.apply(_this, args); // 用apply指向调用debounce的对象，相当于_this.fn(args);
      }, delay);
    };
  },


  getParam(key, url) {
    var strs = (url || window.location.href).split("?")[1];
    if (!strs) {
      return null;
    }
    var json = {};
    var b = strs.split("&");
    for (var i = 0; i < b.length; i++) {
      var [_key, _value] = b[i].split("=");
      json[_key] = _value;
    }
    if (key == undefined) {
      return json;
    }
    if (json[key] == undefined) {
      return null;
    }
    return decodeURIComponent(json[key]);
  },


  isType(obj, type) {
    if (typeof obj !== "object") return false;
    const typeString = Object.prototype.toString.call(obj);
    let flag;
    switch (type) {
      case "Array":
        flag = typeString === "[object Array]";
        break;
      case "Date":
        flag = typeString === "[object Date]";
        break;
      case "RegExp":
        flag = typeString === "[object RegExp]";
        break;
      default:
        flag = false;
    }
    return flag;
  },
  /**
   * deep clone
   * @param  {[type]} parent object 需要进行克隆的对象
   * @return {[type]}        深克隆后的对象
   */
  clone(parent) {
    // 维护两个储存循环引用的数组
    const parents = [];
    const children = [];

    const _clone = parent => {
      if (parent === null) return null;
      if (typeof parent !== "object") return parent;

      let child, proto;

      if (this.isType(parent, "Array")) {
        // 对数组做特殊处理
        child = [];
      } else if (this.isType(parent, "RegExp")) {
        // 对正则对象做特殊处理
        child = new RegExp(parent.source);
        if (parent.lastIndex) child.lastIndex = parent.lastIndex;
      } else if (this.isType(parent, "Date")) {
        // 对Date对象做特殊处理
        child = new Date(parent.getTime());
      } else {
        // 处理对象原型
        proto = Object.getPrototypeOf(parent);
        // 利用Object.create切断原型链
        child = Object.create(proto);
      }

      // 处理循环引用
      const index = parents.indexOf(parent);

      if (index != -1) {
        // 如果父数组存在本对象,说明之前已经被引用过,直接返回此对象
        return children[index];
      }
      parents.push(parent);
      children.push(child);

      for (let i in parent) {
        // 递归
        child[i] = _clone(parent[i]);
      }

      return child;
    };
    return _clone(parent);
  },
  setCookie: function (key, val, time = 1) {
    //设置cookie方法
    var date = new Date(); //获取当前时间
    var expiresDays = time; //将date设置为n天以后的时间
    date.setTime(date.getTime() + expiresDays * 24 * 3600 * 1000); //格式化为cookie识别的时间
    document.cookie = key + "=" + val + ";expires=" + date.toString(); //设置cookie
  },
  getCookie: function (key) {
    //获取cookie方法
    /*获取cookie参数*/
    var getCookie = document.cookie.replace(/[ ]/g, ""); //获取cookie，并且将获得的cookie格式化，去掉空格字符
    var arrCookie = getCookie.split(";"); //将获得的cookie以"分号"为标识 将cookie保存到arrCookie的数组中
    var tips; //声明变量tips
    for (var i = 0; i < arrCookie.length; i++) {
      //使用for循环查找cookie中的tips变量
      var arr = arrCookie[i].split("="); //将单条cookie用"等号"为标识，将单条cookie保存为arr数组
      if (key == arr[0]) {
        //匹配变量名称，其中arr[0]是指的cookie名称，如果该条变量为tips则执行判断语句中的赋值操作
        tips = arr[1]; //将cookie的值赋给变量tips
        break; //终止for循环遍历
      }
    }
    return tips;
  },

  setSession(key, value) {
    // 存储数据
    sessionStorage.setItem(key, JSON.stringify(value));
  },
  getSession(key) {
    // 获取数据
    const value = sessionStorage.getItem(key);
    return value ? JSON.parse(value) : null;
  },
  removeSession(key) {
    // 删除数据
    sessionStorage.removeItem(key);
  },
  clear() {
    // 清空所有数据
    sessionStorage.clear();
  },
  formatPrice(price, type) {
    if (price == null) return;
    price = parseInt(price);
    let formatedPrice, roundedPrice;

    if (type === 1) {
      formatedPrice = price / 100;
      roundedPrice = parseInt(price / 100);
    } else {
      formatedPrice = (price / 100).toFixed(2);
      roundedPrice = parseInt(price / 100).toFixed(2);
    }
    return formatedPrice === roundedPrice ? roundedPrice : formatedPrice;
  },
  getShareHost() {
    const env = process.env.BUILD_ENV
    let mainHost = '';
    switch (env) {
      case 'local':
        mainHost = "http://localhost:10091";
        break
      case 'inte':
        mainHost = "https://mall-m-inte.yang800.cn/";
        break
      case 'pre':
        mainHost = "https://mall-m-pre.yang800.cn/";
        break
      case 'test':
        mainHost = "https://mtest.mall.yang800.cn/";
        break
      case 'stag':
        mainHost = "https://mall-m-stag.yang800.cn/";
        break
      case 'online':
        mainHost = "https://m.mall.yang800.com/";
        break
      default:
        break;

    }
    return mainHost;
  },
  getDesignHost() {
    const env = process.env.BUILD_ENV
    let mainHost = '';
    switch (env) {
      case 'local':
        mainHost = "http://localhost:10091";
        break
      case 'inte':
        mainHost = "https://applet-design-inte.yang800.cn/";
        break
      case 'pre':
        mainHost = "https://applet-design.yang800.cn/";
        break
      case 'test':
        mainHost = "https://applet-design.yang800.com.cn/";
        break
      case 'stag':
        mainHost = "https://applet-design-stag.yang800.com/";
        break
      case 'online':
        mainHost = "https://design1.yang800.com/design.htm";
        break
      default:
        break;

    }
    return mainHost;
  },
  /**
   * 社群模式
   * @return {boolean}
   */
  isCommunityOperation() {
    return this.getCookie('salesPattern') === 'communityOperation'
  },
  isSubStore() {
    return this.getCookie('salesPattern') === 'subStore'
  },
  /**
   * 社群模式
   * @return {boolean}
   */
  isCommonShop() {
    return sessionStorage.salesPattern === 'commonShop'
  },
  /**
   * 时间拼接
   * @param start
   * @param end
   * @return {string}
   */
  joinTimeStr(start, end) {
    let formattedStartDate = moment(start).format('YYYY-MM-DD HH:mm');
    let formattedEndDate = moment(end).format('YYYY-MM-DD HH:mm');
    return `${formattedStartDate}~${formattedEndDate}`
  },
  /**
   * 时间格式化
   * @param start
   * @param type
   * @return {string}
   */
  formatTimeStr(start, type) {
    if (!start) {
      return ''
    }
    if ("second" === type) {
      return moment(start).format('YYYY-MM-DD HH:mm:ss');
    }if ("day" === type) {
      return moment(start).format('YYYY-MM-DD');
    } else {
      return moment(start).format('YYYY-MM-DD HH:mm');
    }
  },
  getShopId(){
    return sessionStorage.getItem('shopId')
  },
  generateNumberString (notUuid,length) {
    let n =Date.now()
   return notUuid?  Array.from({ length: length }, (_, i) =>{
      if(i===0 ||i===0){
        return "9"
      }else{
        n = Math.floor(n / 10)
        return ((n + 10 * Math.random()) % 10 | 0).toString()
      }
    }
    ).join(""): "xxxxxxxxxxxx-xxxxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
      let t = (n + 16 * Math.random()) % 16 | 0;
       n = Math.floor(n / 16)
     return  ("x" === e ? t : 3 & t | 8).toString(16)
    }));
  }





};

window.__lib__ = lib;
module.exports.lib = lib;

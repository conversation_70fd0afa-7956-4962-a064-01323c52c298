import raf from "./raf";
import getScroll,{isWindow} from "./getScroll";
import {easeInOutCubic} from "./easings";


export default function scrollTo(y, options = {}) {
  // eslint-disable-next-line no-undef
  const { getContainer = () => window, callback, duration = 450 } = options
  const container = getContainer()
  const scrollTop = getScroll(container, true)
  const startTime = Date.now()

  const frameFunc = () => {
    const timestamp = Date.now()
    const time = timestamp - startTime
    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration)
    if (isWindow(container)) {
      // eslint-disable-next-line no-undef
      container.scrollTo(window.pageXOffset, nextScrollTop)
      // eslint-disable-next-line no-undef
    } else if (container instanceof Document || container.constructor.name === "HTMLDocument") {
      container.documentElement.scrollTop = nextScrollTop
    } else {
      container.scrollTop = nextScrollTop
    }
    if (time < duration) {
      raf(frameFunc)
    } else if (typeof callback === "function") {
      callback()
    }
  }
  raf(frameFunc)
}

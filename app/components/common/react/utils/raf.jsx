let raf = function raf(callback) {
  return +setTimeout(callback, 16);
}
let caf = function caf(num) {
  return clearTimeout(num);
};
// eslint-disable-next-line no-undef
if (typeof window !== "undefined" && "requestAnimationFrame" in window) {
  raf = (callback) =>
    // eslint-disable-next-line no-undef
    window.requestAnimationFrame(callback)
  // eslint-disable-next-line no-undef
  caf = (handle) => window.cancelAnimationFrame(handle)
}

let rafUUID = 0

const rafIds = new Map()

function cleanup(id) {
  rafIds.delete(id)
}

const wrapperRaf = (callback, times = 1) => {
  rafUUID += 1
  const id = rafUUID

  function callRef(leftTimes) {
    if (leftTimes === 0) {
      // Clean up
      cleanup(id)

      // Trigger
      callback()
    } else {
      // Next raf
      const realId = raf(() => {
        callRef(leftTimes - 1)
      })

      // Bind real raf id
      rafIds.set(id, realId)
    }
  }

  callRef(times)

  return id
}

wrapperRaf.cancel = (id) => {
  const realId = rafIds.get(id)
  cleanup(id)
  return caf(realId)
}

export default wrapperRaf

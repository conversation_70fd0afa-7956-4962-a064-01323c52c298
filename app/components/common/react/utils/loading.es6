module.exports.loading = {
  waitEnd() {
    if (document.getElementById("react-single-app-wait")) {
      document.getElementById("react-single-app-wait").remove();
    }
  },
  wait(tip="正在处理中，请稍等",time) {
    var div = document.getElementById("react-single-app-wait");
    if (!div) {
      div = document.createElement("div");
      div.id = "react-single-app-wait";
      div.innerHTML = `
                <div class='mask' style="display:flex;justify-content:center;align-items: center;background: rgba(256,256,256,0.5);opacity: 1;">
                    <div class="ant-spin ant-spin-lg ant-spin-spinning center">
                        <span class="ant-spin-dot ant-spin-dot-spin">
                            <i class="ant-spin-dot-item"></i>
                            <i class="ant-spin-dot-item"></i>
                            <i class="ant-spin-dot-item"></i>
                            <i class="ant-spin-dot-item"></i>
                        </span>
                        <div>${tip}</>
                    </div>
                </div>
            `;
      document.body.append(div);
      if (time) {
        setTimeout(lib.waitEnd, time);
      }
    }
  }
}

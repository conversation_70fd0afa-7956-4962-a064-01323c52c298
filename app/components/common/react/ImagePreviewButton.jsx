const { Button, Image } = antd;
const {Fragment, useState} =React;
export default function ImagePreviewButton({src,customButton=false, buttonText="查看"}) {
  const [visible, setVisible] = useState(false);
  return (
    <Fragment>
      { customButton? React.cloneElement(buttonText,{onClick:()=>setVisible(true)}):
        <Button type="link" onClick={() => setVisible(true)}>
          {buttonText}
        </Button>
      }
      <Image
        style={{
          display: 'none',
        }}
        src={src}
        preview={{visible, src: src,
          onVisibleChange: (value) => {setVisible(value)},
        }}
      />
    </Fragment>
  );
};

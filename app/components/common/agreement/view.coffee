###
  登录框组件
  author by terminus.io (jsann)
###

Modal = require("pokeball").Modal
Cookie = require("utils/plugins/cookie")
modalAgreement = Handlebars.templates["common/agreement/frontend_templates/modal_agreement"]
loginModalTemplate = Handlebars.templates["common/login/frontend_templates/login_modal"]


class Agreement

  constructor: ($)->
   @bindEvent()

  bindEvent: ->
    $(document).on "click", "#reply-agreement", @closeAgreementModel
    $(document).on "click", "#do-agreement", @doAgreement
    $("#modalViewCancelId").on("click", () => window.modalAgreement.close())

  closeAgreementModel: =>
    $("body").removeClass("body-modal")
    window.modalAgreement.close()

  doAgreement: =>
    from=$(document).find(".modal").data("from")
    href=$(document).find(".modal").data("href")
    Cookie.set("Agreement", true, 10000, document.domain);
    $("body").removeClass("body-modal")
    window.modalAgreement.close()
    if(from=="fromLoginModal")
      $modal = $(loginModalTemplate({href}))
      new Modal($modal).show()
      #解决循环require 返回空对象问题   Login  require  Agreement； Agreement
      Login = require("common/login/view")
      new Login((selector) => $modal.find(selector))
    else
      window.location.href = href

 #弹出协议框  from 登录弹框、点击链接
Agreement.showAgreementModal=(href,from) =>
    $modal = $(modalAgreement({href:href,from:from}))
    window.modalAgreement = new Modal($modal)
    window.modalAgreement.show()
    new Agreement((selector) => $modal.find(selector))
    $("#modalViewCancelId").on("click", () => window.modalAgreement.close())
    $("body").addClass("body-modal")
    $.ajax
      type: "GET"
      url: '/api/agreement'
      dataType: "html"
      success: (data) =>
        $(document).find(".modal")
          .css("z-index", 999)
          .css("top", "50%")
          .css("left", "50%")
          .css("-webkit-transform", "translate(-50%,-50%)")
          .css("-ms-transform", "translate(-50%,-50%)")
          .css("transform", "translate(-50%,-50%)")
        modalFooter = '<div class=\'modal-footer\'>
        <button class=\'btn btn-info btn-medium close\' id=\'reply-agreement\'>拒 绝</button>
        <button class=\'btn btn-primay btn-medium \' id=\'do-agreement\'>同 意</button>
         </div>'
        $(document).find("#ifm-agreement").html(data + modalFooter)

module.exports = Agreement

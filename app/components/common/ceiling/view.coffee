###
  吊顶组件
  author by terminus.io (zl)
###
Cookie = require("utils/plugins/cookie")
Modal = require("pokeball").Modal
contactServiceTemplate = Handlebars.templates["common/ceiling/frontend_templates/contact-service"]

class Ceiling

  constructor: ($) ->
    @$userName = $("#js-user-name")
    @$userAction = $("#js-user-action")
    @$userCartCount = $("#js-cart-count")
    @$sellerCenterLink = $(".js-seller-center-link")
    @$time = $("#js-time")
    @$favorite = $("#js-add-to-favorite")
    @$smallCart = $("#header-cart")
    @$logout = $("#js-ceiling-user-logout")
    @$login = $("#js-login-in")
    @$shoplogin = $(".log-in-shop")
    @$register = $("#js-user-register")
    @$contactService = $("#js-ceiling-contact-service")
    @bindEvent()

  bindEvent: ->
#若是yang800域名则调用统一登录，其他域名还是调自已的登录,若是.cn结尾的表示是测试环境否则是正
    reg = /(^.*.yang800.com$)/;
    if reg.test(window.location.hostname)
      $(".log-in-shop").on("click", @scmLogin)
      $(".log-in-shop").data("href", "http://scm.yang800.com/login.htm?redirectUrl=http://mall.yang800.com")
      if(document.referrer.indexOf('yang800') > -1)
        @getLoginUser()


    reg = /(^.*.yang800.cn$)/;
    if reg.test(window.location.hostname)
      $(".log-in-shop").data("href", "http://scm.yang800.cn/login.htm?redirectUrl=http://mall.yang800.cn")
      $(".log-in-shop").on("click", @scmLogin)
      if(document.referrer.indexOf('yang800') > -1)
        @getLoginUser()

    reg=/(^.*.youshuntong.cn$)/;
    if reg.test(window.location.hostname)
      $(".return-policy").show()
    @showTime()
    @getCartCount()
    @$favorite.on("click", @addToFavorite)
    @$logout.on("click", @userLogout)
    @$login.on("click", @userLogin)
    @$register.on("click", @userRegister)
    @$contactService.on("click", @contactService)
    $(document).on "click", "#closeModel", @closeModel
    $('.home-head-link').on("click", @homeHeadLinkClick)

  homeHeadLinkClick: (evt)=>
    target = $(evt.target)
    href = target.data("href")
    agreement = target.data("agreement")
    if agreement and Cookie.get("Agreement") != "true"
      Agreement = require("common/agreement/view")
      Agreement.showAgreementModal(href, "fromLoginPage")
    else
      window.location.href = href

#如果未登录，且是其他站点跳过来的，因为整合单点登录，要创建用户登录有点慢所以每隔1秒刷新一下
#if($('.user-info').text().indexOf('欢迎来到电商平台')>-1&&document.referrer)
# setTimeout("window.location.reload()", 3000)

# 根据时间判断时段
  showTime: =>
    now = new Date()
    hour = now.getHours()
    time = switch
      when hour < 11
        "#{i18n.ct('morning', 'common')}！"
      when 11 <= hour and hour < 13
        "#{i18n.ct('noon', 'common')}！"
      when 13 <= hour and hour < 18
        "#{i18n.ct('afternoon', 'common')}！"
      else
        "#{i18n.ct('evening', 'common')}！"
    @$time.append time

  getLoginUser: =>
    $.ajax
      url: "/api/user/current"
      type: "GET"
      success: (data)=>
        $("body").overlay(false)
        if data
          newFeebusUrl = 'http://parana-dev.yang800.cn'
          if(/yang800.com$/.test(window.location.host))
            newFeebusUrl = 'http://mall-new.yang800.com'
          if /yang800.cn$/.test(window.location.host)
            newFeebusUrl = 'http://mall-new.yang800.cn'
          $('.user-info').text(data.name)
          $("#js-user-action").html("<a href=\"javascript:;\" id=\"js-ceiling-user-logout\">退出登录</a>")
          $("#js-ceiling-user-logout").on("click", @userLogout)
          # $("#sellerCenter").html("<li class=\"nav-item\"><a rel=\"nofollow\" href=\"/seller/index\"><span>商家中心</span></a></li><li class=\"nav-item\"><a rel=\"nofollow\" href=\""+newFeebusUrl+"\"><span>新版商家中心</span></a></li>")
          # 第三方平台进来的，且是商家时获取店铺信息
          if data.shopId && data.type == 2
            @getShopInfo(data.shopId)

  getShopInfo: (shopId)->
# 新开店，当前登录用户里还没有shopId,调接口获取当前登录用户的shopId /api/shop/my-shop报Forbidden
    $.ajax
      url: '/api/shop/' + shopId
      type: "GET"
      async: false
      success: (data)=>
        if(data)
#把shopId写入sessionStorage,用于订单列表获取店铺信息
          Cookie.set("shopId", data.id, 30, document.domain);
          sessionStorage.shopId = data.id

        sessionStorage.shopLogo = data.imageUrl
        Cookie.set("shopLogo", data.imageUrl, 30, document.domain);
        if data.extra && data.extra.salesPattern
          sessionStorage.salesPattern = data.extra.salesPattern
          sessionStorage.shopInfo = JSON.stringify(data)
          Cookie.set("salesPattern", data.extra.salesPattern, 30, document.domain);
          Cookie.set("shopInfo", JSON.stringify(data), 30, document.domain);
          Cookie.set("openFans", data.extra.openFans, 30, document.domain);
        else
          sessionStorage.removeItem("salesPattern")
          sessionStorage.removeItem("shopInfo")
          Cookie.set("salesPattern", '', 30, document.domain);
          Cookie.set("shopInfo", '', 30, document.domain);
          Cookie.set("openFans", '', 30, document.domain);
          if data
            sessionStorage.salesPattern = 'commonShop'
            Cookie.set("salesPattern", 'commonShop', 30, document.domain);
            sessionStorage.shopInfo = JSON.stringify(data)

  userRegister: =>
    target = top.location.href
    window.location.href = "/register?target=#{target}"

  contactService: =>
    window.contactServiceModal = new Modal(contactServiceTemplate({}))
    $("body").addClass("body-modal")
    window.contactServiceModal.show()
    reg = /(^.*.youshuntong.cn$)/;
    if reg.test(window.location.hostname)
      $('#contact-service-container').replaceWith('<div>联系方式：021-60731311</div>')

  closeModel: =>
    $("body").removeClass("body-modal")
    window.contactServiceModal.close()

# 用户登出
  userLogout: (evt) =>
    $.ajax
      type: "POST"
      url: "/api/user/logout"
      success: =>
        window.location.href = "/"

# 跳转到用户登录页
  userLogin: (evt) =>
    target = top.location.href
    if(Cookie.get("Agreement") == "true")
      @jumpLogin("/login?target=#{target}")
    else
      Agreement = require("common/agreement/view")
      Agreement.showAgreementModal("/login?target=#{target}", "fromLoginPage")

  scmLogin: (evt)=>
    href = $(evt.currentTarget).data("href")
    if(Cookie.get("Agreement") == "true")
      @jumpLogin(href)
    else
      Agreement = require("common/agreement/view")
      Agreement.showAgreementModal("href", "fromLoginPage")

#去登录页
  jumpLogin: (href)=>
    window.location.href = href

# 获取购物车数量
  getCartCount: () =>
    if (@$userCartCount.length > 0)
      $.ajax
        url: "/api/carts/count",
        type: "GET",
        success: (data) =>
          if data
            this.$userCartCount.text(data)
        error: (data) =>

# 添加收藏
  addToFavorite: ->
    window = top.window
    document = top.document
    if window.sidebar and window.sidebar.addPanel # Mozilla Firefox Bookmark
      window.sidebar.addPanel document.title, window.location.href, ""
    else if window.external and ("AddFavorite" of window.external) # IE Favorite
      window.external.AddFavorite location.href, document.title
    else if window.opera and window.print # Opera Hotlist
      @title = document.title
    else # webkit - safari/chrome
      alert "#{i18n.ct('notSupport', 'common')} " + ((if navigator.userAgent.toLowerCase().indexOf("mac") isnt -1 then "Command/Cmd" else "CTRL")) + " + D #{i18n.ct('toAdd', 'common')}"

module.exports = Ceiling

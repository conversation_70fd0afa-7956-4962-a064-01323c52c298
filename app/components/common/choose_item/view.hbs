{{#component "item-choose js-comp"}}
  <div class="item-container hide">
    <table class="table">
      <thead>
        <tr>
          <th>商品名</th>
           <th width="240">{{#equals type 3}}折扣{{else}}适用规格{{/equals}}</th>
           {{#equals type 4}}<th>设置积分</th>{{/equals}}
          <th width="78" class="right-text">操作</th>
        </tr>
      </thead>
      <tbody class="item-list"></tbody>
      <tbody>
        <tr class="control">
          <td colspan="4">
            {{#equals method "detail"}}{{else}}
            <label class="item-label">
              <input type="checkbox" value="" name="select-item-list-all" class="js-select-item" {{#equals method "detail"}}disabled{{/equals}}>
              &nbsp;全选
            </label>
            {{#equals type 3}}
            <a class="btn btn-secondary btn-small js-set-all-sku">折扣</a>
            {{/equals}}
            <a class="btn btn-secondary btn-small delete-all">删除</a>
            {{/equals}}
            <div class="item-list-pagination pull-right"></div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  {{#equals method "detail"}}{{else}}<a class="btn btn-primary btn-medium js-item-choose" style="background-color:#1b7aff;font-size:15px;border:none;border-radius:6px;height:30px;width:85px;color:white;">添加商品</a>{{/equals}}
  <input type="hidden" name="skuIds" data-skuids="[{{skuIds}}]">
  <input type="hidden" name="itemIds" data-itemids="[{{itemIds}}]">
  <input type="hidden" name="skusWithDiscount" data-skuswithdiscount="{{skusWithDiscount}}">
  <input type="hidden" name="itemSkus" data-itemskus="[]">
  <input type="hidden" name="method" value="{{method}}">
  <input type="hidden" name="promotionType" value="{{type}}">
{{/component}}

###
  选择商品及sku组件
  author by terminus.io (zl)
###
Tip = require "common/tip_and_alert/view"
Modal = require("pokeball").Modal
Pagination = require("pokeball").Pagination
skuChooseTemplate = Handlebars.templates["common/choose_item/frontend_templates/choose_sku"]
itemChooseTemplate = Handlebars.templates["common/choose_item/frontend_templates/choose_item"]
itemChooseListTemplate = Handlebars.templates["common/choose_item/frontend_templates/item_choose_list"]
itemListTemplate = Handlebars.templates["common/choose_item/frontend_templates/item_list"]
selAllSkuDiscountTemplate = Handlebars.templates["common/choose_item/frontend_templates/all_sku_discount"]
priceTemplate = Handlebars.templates["trade/buyer/pre_order/frontend_templates/trans_price"]

class chooseItemAndSku

  constructor: ($)->
    @chooseSkuForm = "#sku-choose-form"
    @createBtn = $(".js-item-choose")
    @chooseItemForm = "#item-choose-form"
    @formChooseDialogForm = "#form-choose-dialog"
    @$skuIdsIpt = $("[name=skuIds]")
    @$itemIdsIpt = $("[name=itemIds]")
    @$pTypeIpt = $("[name=promotionType]")
    @$skusWithDiscountIpt = $("[name=skusWithDiscount]")
    @$itemSkusIpt = $("[name=itemSkus]")
    @promotionType = parseInt @$pTypeIpt.val()
    @skuIds = @$skuIdsIpt.data("skuids")
    @itemIds = @$itemIdsIpt.data("itemids")
    @skuIdsTemp = @skuIds.slice(0)
    @itemIdsTemp = @itemIds.slice(0)
    @itemSearchKey = undefined
    @itemSearchType = "itemName"
    @shopCategoryId = ""
    @basePage = 1
    @method = true if $("[name=method]").val() is "detail"
    @skuInited = false
    @skusDiscountInfo = @$skusWithDiscountIpt.data("skuswithdiscount")
    @skusDiscountInfoTemp = {}
    @itemSkus = []
    @itemSkusTemp = {}
    @bindEvent()

  bindEvent: ->
    that = this
    @createBtn.on "click", @createItemFn
    @itemListInit()
    @delaytime()

  delaytime: ->
    t = setTimeout @carbonPadding, 1000

  carbonPadding: ->
    $(".component-standard-container").css("padding", "20px")

  itemListInit: =>
    if @itemIds.length
      @renderBaseItems(@parseIdsForPage(@itemIds, 1, 5), "init")
    if @skusDiscountInfo
      $.each @skusDiscountInfo, (n,v)=>
        @skusDiscountInfoTemp[v.skuId] = v
      @renderBaseItems(@parseIdsForPage(@itemIds, 1, 5), "init")

  initItemSkusTemp: (data)=>
    $.each data,(n, v)=>
      if !@itemSkusTemp[v.item.id]
        @itemSkusTemp[v.item.id] = {}
        @itemSkusTemp[v.item.id].itemId = v.item.ids
        @itemSkusTemp[v.item.id].skuIds = []
        $.each v.skus, (i, o)=>
          if $.inArray(o.id, @skuIds) > -1
            @itemSkusTemp[v.item.id].skuIds.push o.id

  #####弹框商品列表
  #商品选择modal
  createItemFn: (evt)=>
    @skuIdsTemp = @skuIds.slice(0)
    @itemIdsTemp = @itemIds.slice(0)
    @itemSearchKey = undefined
    @shopCategoryId= ""
    @renderItems(1, "init")

  #初始化商品选择modal
  formChooseItemInit: () =>
    $(".item-choose-dialog .menu .sub").on "click", @changeSearchType
    $(".item-choose-dialog [name=select-item-all]").on "click", @triggleAll
    $("#item-choose-form").on "click", ".choose", @chooseItem
    $("#item-choose-form").on "click", ".cancel", @cancelItem
    $("#item-choose-form").on "click", ".choose-all", @chooseItemSelected
    $("#item-choose-form").on "click", ".cancel-all", @cancelItemSelected
    # choose_item
    $("#choose_item").on "click", ".close", @closeChooseModal
    @initProuctType()
    $(@chooseItemForm).on "submit", @submitItem
    $(@formChooseDialogForm).on "submit", @submitFormChooseMenu
  closeChooseModal: () => 
    @chooseItemModal.close()
  #搜索类型
  changeSearchType: (evt)=>
    text = $.trim $(evt.currentTarget).find("span:first").text()
    textReserve = if text is "商品名称" then "商品代码(货号) " else "商品名称"
    if !$(evt.currentTarget).hasClass("sub")
      $(".item-choose-dialog .menu li.sub").toggleClass("hide")
    else
      $(".item-choose-dialog .menu li").find("span:first").text(text)
      $(".item-choose-dialog .menu li.sub").addClass("hide").find("span:first").text(textReserve)
      @itemSearchType = if $(".item-choose-dialog .menu li.sub").text().trim() is "商品代码(货号)" then "itemName" else "itemCode"

   submitFormChooseMenu: (evt)=>
    evt.preventDefault();
    data = $(".form-choose-dialog").serializeObject()
    @itemSearchType =data.itemSearchType
    @shopCategoryId =data.shopCategoryId
    @searchItemFn(data)

  #搜索商品
  searchItemFn: (data)=>
    keyWords =data.itemSearchKey
    @itemSearchKey = keyWords
    if !keyWords
      @itemSearchKey = undefined
    @renderItems(1, "search")

  initProuctType: ()=>
    $.ajax
      url:"/api/seller/shopCategories/fullTree?shopId="+ sessionStorage.shopId
      type: "GET"
      contentType: "application/json"
      async: true
      success: (data) =>
        for item in data
           $("#js-product-type1").append("<option value= '#{item.id}'>#{item.name}</option>")
        $("#js-product-type1").selectric("refresh")

  #分页重新渲染商品
  renderItems: (pageNo, type)=>
    if @promotionType == 4
      url = if @itemSearchKey then "/api/seller/find-item-with-skus?pageNo=#{pageNo}&pageSize=5&#{@itemSearchType}=#{@itemSearchKey}&typees=1" else "/api/seller/find-item-with-skus?pageNo=#{pageNo}&pageSize=5&typees=1"
    else 
      if @promotionType < 3
        url = if @itemSearchKey then "/api/seller/find-item-with-skus?pageNo=#{pageNo}&pageSize=5&#{@itemSearchType}=#{@itemSearchKey}&typees=1,2" else "/api/seller/find-item-with-skus?pageNo=#{pageNo}&pageSize=5&typees=1,2"
      if @promotionType > 2
        url = if @itemSearchKey then "/api/seller/find-item-with-skus?pageNo=#{pageNo}&pageSize=5&#{@itemSearchType}=#{@itemSearchKey}&typees=3,4,5" else "/api/seller/find-item-with-skus?pageNo=#{pageNo}&pageSize=5&typees=3,4,5"
    if @shopCategoryId
      url = url + '&shopCategoryId=' + @shopCategoryId
    $(".modal").spin("small")
    $.ajax
      url: url
      type: "GET"
      contentType: "application/json"
      success: (data) =>
        @initItemChoosed(data)
        if type is "init"
          @chooseItemModal =  new Modal(itemChooseTemplate({data: data, itemsHtml: itemChooseListTemplate({data: data})}))
          @chooseItemModal.show()
          @formChooseItemInit()
        else
          $(".item-choose-dialog tbody.item-choose-list").html itemChooseListTemplate({data: data})
        if type
          new Pagination(".item-choose-pagination").total(data.total).show 5,
            num_display_entries: 3
            page_size_switch: false
            jump_switch: false
            maxPage: -1
            callback: (pageNo) =>
              @renderItems(pageNo+1)
              $(".item-choose-dialog [name=select-item-all]").prop "checked", false
        $(".modal").spin(false)


  #初始化已选取的商品
  initItemChoosed: (data) =>
    $.each data.data, (n,v)=>
      itemId = v.item.id
      if $.inArray(itemId, @itemIdsTemp) > -1
        data.data[n].item.choosed = true
    data

  #全选/全不选
  triggleAll: (evt) =>
    isChecked = $(evt.currentTarget).prop "checked"
    toChecked = if isChecked then true else false
    $(".item-choose-dialog [name=select-item]").prop "checked", toChecked

  #选取商品
  chooseItem: (evt) =>
    data = $(evt.currentTarget).data("info")
    itemId = data.item.id
    @itemIdsTemp.push itemId
    @itemSkusTemp[itemId] = {} if !@itemSkusTemp[itemId]
    @itemSkusTemp[itemId].skuIds = []
    $.each data.skus, (n,v)=>
      @skuIdsTemp.push v.id if @promotionType isnt 3
      @itemSkusTemp[itemId].itemId = itemId
      @itemSkusTemp[itemId].skuIds.push v.id if @promotionType isnt 3
    $(evt.currentTarget).removeClass("btn-primary choose").addClass("btn-info cancel").text("取消")

  #取消商品
  cancelItem: (evt) =>
    data = $(evt.currentTarget).data("info")
    itemId = data.item.id
    @itemIdsTemp.splice($.inArray(itemId, @itemIdsTemp),1)
    delete @itemSkusTemp[itemId]
    $.each data.skus, (n,v)=>
      @skuIdsTemp.splice($.inArray(v.id, @skuIdsTemp),1)
      delete @skusDiscountInfoTemp[v.id]
    $(evt.currentTarget).removeClass("btn-info cancel").addClass("btn-primary choose").text("选取")

  #批量选取商品
  chooseItemSelected: =>
    $(".item-choose-dialog [name=select-item]:checked").closest("tr").find(".choose").click()

  #批量取消商品
  cancelItemSelected: =>
    $(".item-choose-dialog [name=select-item]:checked").closest("tr").find(".cancel").click()

  #确认商品信息
  submitItem: (evt)=>
    evt.preventDefault()
    if !@itemIdsTemp.length
      new Modal
        "icon": "error"
        "title": "保存失败"
        "content": "至少需要选择一个商品"
      .show()
      return false
    @itemIdsTemp = @itemIdsTemp.sort().reverse().slice(0)
    @skuIdsTemp = @skuIdsTemp.sort().reverse().slice(0)
    @itemIds = @itemIdsTemp.slice(0)
    @skuIds = @skuIdsTemp.slice(0)
    @copySTempToS()
    @copyISTempToIS()
    @$skuIdsIpt.data "skuids", @skuIdsTemp.slice(0)
    @$itemIdsIpt.data "itemids", @itemIdsTemp.slice(0)
    @$skusWithDiscountIpt.data "skuswithdiscount", @skusDiscountInfo.slice(0)
    @$itemSkusIpt.data "itemskus", @itemSkus.slice(0)
    @renderBaseItems(@parseIdsForPage(@itemIdsTemp, 1, 5), "init", "close")

  #返回相应页面的ids
  parseIdsForPage: (arr, page, per)=>
    if page is 0
      ids = []
    else
      ids = arr.slice( (page-1)*per , page * per)
    ids

  #####页面商品列表

  #初始化base商品选择
  formItemInit: () =>
    $("[name=select-item-list-all]").on "click", @triggleAllBase
    $(".item-choose").on "click", ".delete", @deleteItem
    $(".item-choose").on "click", ".delete-all", @deleteItemSelected
    $(".item-choose").on "click", ".showSkus", @showSkus
    $(".item-choose").on "click", ".js-set-all-sku", @showSetAllSku

  #全选/全不选
  triggleAllBase: (evt) =>
    isChecked = $(evt.currentTarget).prop "checked"
    toChecked = if isChecked then true else false
    $("[name=select-item-list]").prop "checked", toChecked

  showSetAllSku: (evt)=>
    return if !$(".item-choose [name=select-item-list]:checked").length
    new Modal(selAllSkuDiscountTemplate({})).show()
    $("#set-sku-all-form").on "submit", (evt)=>
      evt.preventDefault()
      discountType = parseInt $("#set-sku-all-form").find(".control-group").find("[name=discountType-all]:checked").val()
      discountIpt = if discountType is 1 then "[name=reduceNum-all]" else "[name=discountNum-all]"
      discountNum = $(discountIpt).val()
      if !discountNum
        new Modal
          "icon": "error"
          "title": "应用失败"
          "content": "请正确填写数值"
        .show()
        return
      $.each $(".item-choose [name=select-item-list]:checked"), (n,v)=>
        data = $(v).closest("tr").find(".delete").data("info")
        $.each data.skus, (i,j)=>
          skuId = j.id
          if $.inArray(j.id, @skuIdsTemp) <= -1
            @skuIdsTemp.push skuId
          @itemSkusTemp[j.itemId] = {} if !@itemSkusTemp[j.itemId]
          @itemSkusTemp[j.itemId].skuIds = [] if !@itemSkusTemp[j.itemId].skuIds
          if $.inArray(j.id, @itemSkusTemp[j.itemId].skuIds) <= -1
            @itemSkusTemp[j.itemId].skuIds.push j.id
          @skusDiscountInfoTemp[skuId] = {}
          @skusDiscountInfoTemp[skuId].skuId = skuId
          @skusDiscountInfoTemp[skuId].reduceFee = @deformatPrice(discountNum) if discountType is 1
          @skusDiscountInfoTemp[skuId].discount = @deformatDiscount(discountNum) if discountType is 2
      @copySTempToS()
      @copyISTempToIS()
      @skuIds = @skuIdsTemp.sort().reverse().slice(0)
      @$skuIdsIpt.data "skuids", @skuIds.slice(0)
      @$itemSkusIpt.data "itemskus", @itemSkus.slice(0)
      @$skusWithDiscountIpt.data "skuswithdiscount", @skusDiscountInfo.slice(0)
      @renderBaseItems(@parseIdsForPage(@itemIds, @basePage, 5), "page", "", @basePage)
      $(".item-choose [name=select-item-list-all]").prop "checked", false
      $("#set-sku-all-form button.close").click()

  #删除商品
  deleteItem: (evt)=>
    data = $(evt.currentTarget).data("info")
    itemId = data.item.id
    @itemIds.splice($.inArray(itemId, @itemIds),1)
    $.each data.skus, (n,v)=>
      if $.inArray(v.id, @skuIds) > -1
        @skuIds.splice($.inArray(v.id, @skuIds),1)
        delete @skusDiscountInfoTemp[v.id]
    if @itemIds.length <= (@basePage - 1) * 5
      @basePage -= 1 if @basePage > 1
    delete @itemSkusTemp[itemId]
    @copySTempToS()
    @copyISTempToIS()
    @renderBaseItems(@parseIdsForPage(@itemIds, @basePage, 5), "page", "", @basePage)
    @$skuIdsIpt.data "skuids", @skuIds.slice(0)
    @$itemIdsIpt.data "itemids", @itemIds.slice(0)
    @$itemSkusIpt.data "itemskus", @itemSkus.slice(0)
    @$skusWithDiscountIpt.data "skuswithdiscount", @skusDiscountInfo.slice(0)

  #删除选中商品
  deleteItemSelected: (evt)=>
    $.each $(".item-choose [name=select-item-list]:checked"), (n,v)=>
      data = $(v).closest("tr").find(".delete").data("info")
      itemId = data.item.id
      @itemIds.splice($.inArray(itemId, @itemIds),1)
      $.each data.skus, (i,j)=>
        skuId = j.id
        if $.inArray(skuId, @skuIds) > -1
          @skuIds.splice($.inArray(skuId, @skuIds),1)
          delete @skusDiscountInfoTemp[skuId]
      delete @itemSkusTemp[itemId]
    @copySTempToS()
    @copyISTempToIS()
    if @itemIds.length <= (@basePage - 1) * 5
      @basePage -= 1 if @basePage > 1
    @renderBaseItems(@parseIdsForPage(@itemIds, @basePage, 5), "page", "", @basePage)
    @$skuIdsIpt.data "skuids", @skuIds.slice(0)
    @$itemIdsIpt.data "itemids", @itemIds.slice(0)
    @$itemSkusIpt.data "itemskus", @itemSkus.slice(0)
    @$skusWithDiscountIpt.data "skuswithdiscount", @skusDiscountInfo
    $(".item-choose [name=select-item-list-all]").prop "checked", false

  #分页重新渲染创建页商品
  renderBaseItems: (ids, type, needClose, curPage)=>
    if !ids.length
      $(".item-list").html("").closest(".item-container").addClass("hide")
      return
    #$(".discount-create").spin("medium")
    $.ajax
      url: "/api/promotion/selected-items?itemIds=#{ids}"
      type: "GET"
      contentType: "application/json"
      success: (data) =>
        @initItemSkusTemp(data)
        $.each data, (n, v)=>
          num = 0
          $.each v.skus, (i, o)=>
            if $.inArray(o.id, @skuIds) > -1
              num += 1
          data[n].skuNum = num
          data[n].method = "detail" if @method
        $(".item-list").html itemListTemplate({data: data, promotionType: @promotionType})
        if type
          new Pagination(".item-list-pagination").total(@itemIds.length).show 5,
            current_page: if curPage then curPage - 1 else 0
            num_display_entries: 3
            page_size_switch: false
            jump_switch: false
            item_text: "项 "
            maxPage: -1
            callback: (pageNo) =>
              @basePage = pageNo + 1
              @renderBaseItems( @parseIdsForPage(@itemIds, pageNo + 1, 5) )
        if type is "init" and !@skuInited
          @formItemInit()
          @skuInited = true
        if needClose is "close"
          $("#item-choose-form button.close").click()
        $(".item-list").closest(".item-container").removeClass("hide")
        # $(".discount-create").spin(false)

  #####弹框规格列表
  showSkus: (evt)=>
    @skuIdsTemp = @skuIds.slice(0)
    @skusDiscountInfoTemp = {}
    $.each @skusDiscountInfo, (n,v)=>
      @skusDiscountInfoTemp[v.skuId] = {}
      @skusDiscountInfoTemp[v.skuId].skuId = v.skuId
      @skusDiscountInfoTemp[v.skuId].reduceFee = v.reduceFee if v.reduceFee
      @skusDiscountInfoTemp[v.skuId].discount = v.discount if v.discount

    data = $(evt.currentTarget).data("info")
    method = "detail" if @method
    $.each data, (n,v)=>
      skuId = v.id
      if $.inArray(skuId, @skuIds) > -1
        data[n].choosed = true
        if @promotionType is 3 and @skusDiscountInfoTemp[skuId]
          data[n].reduceFee = @skusDiscountInfoTemp[skuId].reduceFee if @skusDiscountInfoTemp[skuId].reduceFee
          data[n].discount = @skusDiscountInfoTemp[skuId].discount if @skusDiscountInfoTemp[skuId].discount
          data[n].discountType = if @skusDiscountInfoTemp[skuId].reduceFee then 1 else 2
      else
        delete data[n].choosed
        delete data[n].reduceFee
        delete data[n].discount
        delete data[n].discountType
    new Modal(skuChooseTemplate({data: data, method, promotionType: @promotionType})).show()
    @formChooseSkuInit()

  #初始化sku选择modal
  formChooseSkuInit: () =>
    $("#sku-choose-form").on "click", ".sku-choose", @chooseSku
    $("#sku-choose-form").on "click", ".sku-cancel", @cancelSku
    $("#sku-choose-form").on "click", ".set-all", @setAllSkuDiscount
    $("#sku-choose-form").on "blur", "input[name=reduceFee]", @changeSkuDiscount
    $("#sku-choose-form").on "blur", "input[name=discount]", @changeSkuDiscount
    $(@chooseSkuForm).on "submit", @submitSku

  setAllSkuDiscount: (evt) =>
    discountType = parseInt $(evt.currentTarget).closest(".control-group").find("[name=discountType-all]:checked").val()
    discountIpt = if discountType is 1 then "[name=reduceNum-all]" else "[name=discountNum-all]"
    discountNum = parseFloat $(discountIpt).val()
    if discountType is 2 and discountNum < 0.1
      discountNum = 0.1
    if discountType is 2 and discountNum >=10
      discountNum = 9.9
    if !discountNum
      new Modal
        "icon": "error"
        "title": "应用失败"
        "content": "请正确填写数值"
      .show()
      return
    $("[name^=discountType-][value=#{discountType}]").prop "checked", true
    $.each $("[name^=discountType-][value=#{discountType}]").parent().next("input"), (n,v)=>
      @setSkuDiscount($(v), discountNum, discountType)

  # 改变输入框数值
  changeSkuDiscount: (evt, noModal)=>
    discountType = parseInt $(evt.currentTarget).prev("label").find("[name^=discountType]").val()
    discountNum = parseFloat $(evt.currentTarget).val()
    if !discountNum and $(evt.currentTarget).prev("label").find("[name^=discountType]").prop("checked") and !noModal
      new Modal
        "icon": "error"
        "title": "应用失败"
        "content": "请正确填写数值"
      .show()
      return
    @setSkuDiscount($(evt.currentTarget), discountNum, discountType) if discountNum

  # 设置降价
  setSkuDiscount: (obj, discountNum, type)=>
    if type is 1
      price = if obj.data("price") then obj.data("price") else 0
      discountNum = parseInt discountNum * 100
      if parseInt(price) > 0 and discountNum >= price
        discountNum = (price - 1)
      obj.val priceTemplate({price: discountNum})
    if type is 2
      discountNum = discountNum.toFixed(1)
      discountNum = 9.9 if discountNum > 9.9
      discountNum = 0.1 if discountNum < 0.1
      obj.val discountNum

  #选取sku
  chooseSku: (evt) =>
    skuId =$(evt.currentTarget).data("info")
    itemId = $(evt.currentTarget).data("itemid")
    if $.inArray(skuId, @skuIdsTemp) <= -1
      @skuIdsTemp.push skuId
      @itemSkusTemp[itemId].skuIds.push skuId
      $(evt.currentTarget).removeClass("btn-primary sku-choose").addClass("btn-info sku-cancel").text("取消")

  #取消sku
  cancelSku: (evt) =>
    skuId =$(evt.currentTarget).data("info")
    itemId = $(evt.currentTarget).data("itemid")
    @skuIdsTemp.splice($.inArray(skuId, @skuIdsTemp),1)
    @itemSkusTemp[itemId].skuIds.splice($.inArray(skuId, @itemSkusTemp[itemId].skuIds),1)
    $(evt.currentTarget).removeClass("btn-info sku-cancel").addClass("btn-primary sku-choose").text("选取")

  #doDiscount
  doDiscountSubmit: =>
    $.each $(".item-choose-list tr"), (n, v)=>
      data = $(v).data("info")
      discountType = parseInt $(v).find("[name^=discountType-]:checked").val()
      discountNum = parseFloat $(v).find("[name^=discountType-]:checked").parent().next("input").val()
      price = data.price
      if discountNum
        temp = {}
        temp.skuId = data.id
        if discountType is 1
          discountNum = ((price - 1)/100).toFixed(2) if discountNum*100 >= price
          discountNum = 0.01 if discountNum < 0.01
          temp.reduceFee = @deformatPrice(discountNum)
        else
          discountNum = discountNum.toFixed(1)
          discountNum = 9.9 if discountNum > 9.9
          discountNum = 0.1 if discountNum < 0.1
          temp.discount = @deformatDiscount(discountNum)
        if $.inArray(data.id, @skuIdsTemp) <= -1
          @skuIdsTemp.push data.id
          @itemSkusTemp[data.itemId].skuIds.push data.id
        @skusDiscountInfoTemp[data.id] = temp
      else
        delete @skusDiscountInfoTemp[data.id]
        if $.inArray(data.id, @skuIdsTemp) > -1
          @skuIdsTemp.splice($.inArray(data.id, @skuIdsTemp),1)
          @itemSkusTemp[data.itemId].skuIds.splice($.inArray(data.id, @itemSkusTemp[data.itemId].skuIds),1)

  doSkuCheck: =>
    result = true
    thisSkuIdsTemp = []
    $.each $(".item-choose-list tr"), (n, v)=>
      data = $(v).data("info")
      if @promotionType is 3
        discountNum = parseFloat $(v).find("[name^=discountType-]:checked").parent().next("input").val()
      if discountNum
        if $.inArray(data.id, thisSkuIdsTemp) <= -1
          thisSkuIdsTemp.push data.id
      else
        if $.inArray(data.id, thisSkuIdsTemp) > -1
          thisSkuIdsTemp.splice($.inArray(data.id, thisSkuIdsTemp),1)
      result = false if thisSkuIdsTemp.length < 1
    return result

  submitSku: (evt) =>
    evt.preventDefault()
    if @promotionType is 3
      skuChecked = @doSkuCheck()
      if !skuChecked
        new Modal
          "icon": "error"
          "title": "保存失败"
          "content": "至少需要设置一个规格"
        .show()
        return false
      @doDiscountSubmit()
      @skusDiscountInfo = []
      @copySTempToS()

    if !@skusDiscountInfo.length and @promotionType is 3
      new Modal
        "icon": "error"
        "title": "保存失败"
        "content": "至少需要设置一个规格"
      .show()
      return false

    if !$("#sku-choose-form a.sku-cancel").length and @promotionType isnt 3
      new Modal
        "icon": "error"
        "title": "保存失败"
        "content": "至少需要选择一个规格"
      .show()
      return false

    @copyISTempToIS()
    @skuIds = @skuIdsTemp.sort().reverse().slice(0)
    @$skuIdsIpt.data "skuids", @skuIds.slice(0)
    @$skusWithDiscountIpt.data "skuswithdiscount", @skusDiscountInfo.slice(0)
    @$itemSkusIpt.data "itemskus", @itemSkus.slice(0)
    @renderBaseItems(@parseIdsForPage(@itemIds, @basePage, 5), "page", "", @basePage)
    $("#sku-choose-form button.close").click()

  deformatPrice: (price)=>
    if price is NaN
      price = null
    else
      price *= 100
    price

  deformatDiscount: (discount)=>
    if discount is NaN
      discount = null
    else
      discount *= 10
    discount

  copySTempToS: =>
    @skusDiscountInfo = []
    $.each @skusDiscountInfoTemp, (n,v)=>
      @skusDiscountInfo.push v

  copyISTempToIS: =>
    @itemSkus = []
    $.each @itemSkusTemp, (n,v)=>
      @itemSkus.push v

module.exports = chooseItemAndSku

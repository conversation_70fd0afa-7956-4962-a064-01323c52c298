<div class="modal">
  <div class="modal-header">
    <h2>选择商品</h2>
  </div>
  <form class="form-choose-dialog" id="form-choose-dialog">
    <fieldset>
      <div class="control-group sp">
        <span>
          <select class="select trade-status-select" style="width:119px" name="itemSearchType" >
              <option value='itemName'>商品名称</option>
              <option value='itemCode'>商品代码(货号)</option>
          </select>
          <input type="text" name="itemSearchKey" class="item-filter-item" value="">
        </span>
        <span class="span3">
           <label for="">商品分类：</label>
            <select class="select trade-status-select" name="shopCategoryId" id="js-product-type1">
              <option value=''>全部</option>
            </select>
          </label>
          </span>
        <button type="submit" class="btn btn-secondary js-item-filter">搜索</button>
      </div>
    </fieldset>
  </form>
  <form class="form form-aligned item-choose-form" id="item-choose-form">
    <fieldset>
      <div class="item-choose-dialog modal-body">
        <div class="table-wrapper">
        {{!-- <div class="control-group sp">
          <label class="control-label menu">
            <ul>
              <li><span>商品名称</span>&nbsp;<span class="caret"></span></a></li>
              <li class="sub hide"><span>商品代码(货号) </span></li>
              <li class="sub2 hide"><span>商品分类 </span></li>
            </ul>
          </label>
          <input type="text" name="itemSearchKey" class="" value="">
          <a class="searchBtn" href="javascript:;">搜索</a>
        </div> --}}
        <table class="table">
          <thead>
            <tr>
              <th width="200" style="overflow: visible">商品名</th>
              <th width="240">价格（元）</th>
              <th width="140">商品分类</th>
              <th width="78" class="right-text">操作</th>
            </tr>
          </thead>
          <tbody class="item-choose-list">{{{itemsHtml}}}</tbody>
          <tbody>
            <tr class="control">
              <td colspan="3">
                <label class="item-label">
                  <input type="checkbox" value="" name="select-item-all" class="js-select-item" >
                  &nbsp;全选
                </label>
                <a class="btn btn-secondary btn-small choose-all">选取</a>
                <a class="btn btn-secondary btn-small cancel-all">取消</a>
                <div class="item-choose-pagination pull-right"></div>
              </td>
            </tr>
          </tbody>
        </table>
        </div>
      </div>
      <div class="modal-footer" id="choose_item">
        <button class="btn btn-success btn-primary btn-medium" type="submit">确认</button>
        <button class="btn btn-info btn-medium close">取消</button>
      </div>
    </fieldset>
  </form>
</div>

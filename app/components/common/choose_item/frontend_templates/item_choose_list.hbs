{{#each data.data}}
<tr>
  <td>
    <label class="item-label" width="50px">
      <input type="checkbox" value="{{item.id}}" name="select-item" class="js-select-item" >
    </label>
    &nbsp;<a class="jump-to-item" href="/items/{{item.id}}" target="_blank" title="{{item.name}}">{{item.name}}</a>
  </td>
  <td>
    {{#equals item.lowPrice item.highPrice}}
      ￥{{formatPrice item.lowPrice}}
    {{else}}
      ￥{{formatPrice item.lowPrice}}-{{formatPrice item.highPrice}}
    {{/equals}}
  </td>
  <td>
        {{categoryPath}}
    </td>
  <td class="right-text">
    {{#if item.choosed}}
    <a class="btn btn-info btn-small cancel" data-info="{{json this}}">取消</a>
    {{else}}
    <a class="btn btn-primary btn-small choose" data-info="{{json this}}">选取</a>
    {{/if}}
  </td>
</tr>
{{/each}}

<div class="modal">
  <div class="modal-header">
    <h2>选择规格</h2>
  </div>
  <form class="form form-aligned sku-choose-form" id="sku-choose-form">
    <fieldset>
      <div class="sku-choose-dialog modal-body">
      <div class="table-wrapper">
        {{#equals promotionType 3}}{{#equals method "detail"}}{{else}}
        <div class="control-group">
          <label class="label-list">批量设置：&nbsp;</label>
          <label class="label-list"><input name="discountType-all" type="radio" value="1" checked> 降价</label><input type="text" name="reduceNum-all" onkeyup="value=value.replace(/[^\d]/g,'')" class="input-small h20"> 元
          &nbsp;&nbsp;&nbsp;&nbsp;
          <label class="label-list"><input name="discountType-all" type="radio" value="2"> 打折</label><input type="text" name="discountNum-all" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" class="input-small h20"> 折
          <a href="javascript:;" class="btn btn-primary btn-small pull-right set-all">应用</a>
        </div>
        {{/equals}}{{/equals}}
        <table class="table">
          <thead>
            <tr>
              <th>销售属性</th>
              <th width="{{#equals promotionType 3}}100{{else}}240{{/equals}}">价格（元）</th>
              <th width="{{#equals promotionType 3}}320{{else}}78{{/equals}}" class="right-text">{{#equals method "detail"}}状态{{else}}操作{{/equals}}</th>
            </tr>
          </thead>
          <tbody class="item-choose-list">
            {{#each data}}
            <tr data-info="{{json this}}">
              <td>
                <span class="skuName cutwords" title="{{#each attrs}}{{attrVal}}&#10;{{/each}}">
                {{#each attrs}}
                  {{attrVal}}<br>
                {{/each}}
                </span>
              </td>
              <td>{{formatPrice price}}</td>
              <td class="right-text">
                {{#equals ../method "detail"}}
                  {{#equals ../promotionType 3}}
                    {{#if reduceFee}}降价：{{formatPrice reduceFee}}元{{/if}}
                    {{#if discount}}打折：{{divide discount 10}}折{{/if}}
                  {{else}}
                    {{#if choosed}}
                      已选择
                    {{else}}
                      未选择
                    {{/if}}
                  {{/equals}}
                {{else}}
                  {{#equals ../promotionType 3}}
                    <label class="label-list"><input name="discountType-{{id}}" type="radio" value="1" {{#equals discountType 1}}checked{{/equals}}{{#unless discountType}}checked{{/unless}}> 降价</label>
                    <input type="text" name="reduceFee" onkeyup="value=value.replace(/[^\d]/g,'')" class="input-small h20" data-price="{{price}}" value="{{formatPrice reduceFee}}"> 元
                    &nbsp;&nbsp;
                    <label class="label-list"><input name="discountType-{{id}}" type="radio" value="2" {{#equals discountType 2}}checked{{/equals}}> 打折</label>
                    <input type="text" name="discount" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" class="input-small h20" value="{{#if discount}}{{divide discount 10}}{{/if}}"> 折
                  {{else}}
                    {{#if choosed}}
                      <a class="btn btn-info btn-small sku-cancel" data-info="{{id}}" data-itemid="{{itemId}}">取消</a>
                    {{else}}
                      <a class="btn btn-primary btn-small sku-choose" data-info="{{id}}" data-itemid="{{itemId}}">选取</a>
                    {{/if}}
                  {{/equals}}
                {{/equals}}
              </td>
            </tr>
            {{/each}}
          </tbody>
        </table>
        </div>
      </div>
      <div class="modal-footer">
        {{#equals method "detail"}}{{else}}
        <button class="btn btn-success btn-primary btn-medium" type="submit">确认</button>
        {{/equals}}
        <button class="btn btn-info btn-medium close">{{#equals method "detail"}}关闭{{else}}取消{{/equals}}</button>
      </div>
    </fieldset>
  </form>
</div>

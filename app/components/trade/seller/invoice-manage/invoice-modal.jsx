const {useRef, useEffect} = React;
const {Modal, message, Upload, Button} = antd;
const {DTEditForm} = dtComponents;
const {UploadOutlined} = icons;

const InvoiceModal = ({open, onClose, mode = 'add', invoiceData = {}}) => {
    const editFormRef = useRef();
    
    // 模式判断
    const isView = mode === 'view';
    const isEdit = mode === 'edit' || mode === 'add';
    const title = mode === 'add' ? '开票' : mode === 'view' ? '查看发票' : '编辑发票';

    // 表单配置
    const configs = {
        basicInfo: {
            title: "基础信息",
            configs: [
                {
                    type: 'INPUT',
                    fProps: {
                        label: '订单编号',
                        name: 'orderNo',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: true,
                        style: {width: "100%"}
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '申请时间',
                        name: 'applyTime',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: true,
                        style: {width: "100%"}
                    }
                }
            ]
        },
        invoiceInfo: {
            title: "开票信息",
            configs: [
                {
                    type: 'SELECT',
                    fProps: {
                        label: '发票类型',
                        name: 'invoiceType',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                        rules: isEdit ? [{required: true, message: '请选择发票类型'}] : []
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        options: [
                            {label: '增值税专用发票', value: 'special'},
                            {label: '增值税普通发票', value: 'normal'}
                        ]
                    }
                },
                {
                    type: 'SELECT',
                    fProps: {
                        label: '抬头类型',
                        name: 'titleType',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                        rules: isEdit ? [{required: true, message: '请选择抬头类型'}] : []
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        options: [
                            {label: '企业', value: 'enterprise'},
                            {label: '个人', value: 'personal'}
                        ]
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '发票抬头',
                        name: 'invoiceTitle',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                        rules: isEdit ? [{required: true, message: '请输入发票抬头'}] : []
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        placeholder: isView ? '' : '请输入发票抬头'
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '税号',
                        name: 'taxNo',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                        rules: isEdit ? [{required: true, message: '请输入税号'}] : []
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        placeholder: isView ? '' : '请输入税号'
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '发票金额',
                        name: 'invoiceAmount',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: true,
                        style: {width: "100%"}
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '开户银行',
                        name: 'bankName',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        placeholder: isView ? '' : '请输入开户银行'
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '银行账户',
                        name: 'bankAccount',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        placeholder: isView ? '' : '请输入银行账户'
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '企业地址',
                        name: 'companyAddress',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        placeholder: isView ? '' : '请输入企业地址'
                    }
                },
                {
                    type: 'INPUT',
                    fProps: {
                        label: '企业电话',
                        name: 'companyPhone',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                    },
                    cProps: {
                        disabled: isView,
                        style: {width: "100%"},
                        placeholder: isView ? '' : '请输入企业电话'
                    }
                }
            ]
        }
    };

    // 添加文件上传区域
    if (mode === 'add') {
        // 新增模式显示上传区域
        configs.uploadInfo = {
            title: "发票上传",
            configs: [
                {
                    type: 'CUSTOM',
                    fProps: {
                        label: '发票文件',
                        name: 'invoiceFile',
                        labelCol: {span: 6},
                        wrapperCol: {span: 18},
                        extra: "仅支持文件上传PDF格式，不超过10M"
                    },
                    render: () => (
                        <Upload
                            accept=".pdf"
                            maxCount={1}
                            beforeUpload={(file) => {
                                const isPDF = file.type === 'application/pdf';
                                if (!isPDF) {
                                    message.error('只能上传PDF格式文件!');
                                }
                                const isLt10M = file.size / 1024 / 1024 < 10;
                                if (!isLt10M) {
                                    message.error('文件大小不能超过10MB!');
                                }
                                return isPDF && isLt10M;
                            }}
                        >
                            <Button icon={<UploadOutlined />}>上传</Button>
                        </Upload>
                    )
                }
            ]
        };
    } else if (isView && invoiceData.invoiceFileUrl) {
        // 查看模式显示下载链接
        configs.invoiceInfo.configs.push({
            type: 'CUSTOM',
            fProps: {
                label: '发票文件',
                name: 'invoiceFile',
                labelCol: {span: 6},
                wrapperCol: {span: 18},
            },
            render: () => (
                <div>
                    <Button
                        type="link"
                        icon={<UploadOutlined />}
                        onClick={() => window.open(invoiceData.invoiceFileUrl)}
                    >
                        查看发票PDF
                    </Button>
                </div>
            )
        });
    }

    // 处理确认操作
    const handleOk = async () => {
        if (isView) {
            onClose();
            return;
        }

        try {
            const values = await editFormRef.current.form.validateFields();
            console.log('表单数据:', values);
            
            // 这里添加保存逻辑
            message.success(mode === 'add' ? '开票成功' : '保存成功');
            onClose();
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    // 处理取消操作
    const handleCancel = () => {
        onClose();
    };

    // 初始化表单数据
    useEffect(() => {
        if (open && editFormRef.current) {
            if (mode === 'view' || mode === 'edit') {
                // 设置表单初始值
                editFormRef.current.form.setFieldsValue({
                    orderNo: invoiceData.orderNo || '2432432',
                    applyTime: invoiceData.applyTime || '2022-01-02 12:12:12',
                    invoiceType: invoiceData.invoiceType || 'special',
                    titleType: invoiceData.titleType || 'enterprise',
                    invoiceTitle: invoiceData.invoiceTitle || 'XXX',
                    taxNo: invoiceData.taxNo || 'XXX',
                    invoiceAmount: invoiceData.invoiceAmount || 'XXX',
                    bankName: invoiceData.bankName || 'XXX',
                    bankAccount: invoiceData.bankAccount || 'XXX',
                    companyAddress: invoiceData.companyAddress || 'XXX',
                    companyPhone: invoiceData.companyPhone || 'XXX'
                });
            }
        }
    }, [open, mode, invoiceData]);

    return (
        <Modal
            title={title}
            open={open}
            onOk={handleOk}
            onCancel={handleCancel}
            width={600}
            okText={isView ? '关闭' : '确认'}
            cancelText={isView ? null : '取消'}
            cancelButtonProps={isView ? {style: {display: 'none'}} : {}}
        >
            <DTEditForm
                ref={editFormRef}
                mode={isView ? "read" : "edit"}
                configs={configs}
                layout={{
                    mode: 'appoint',
                    colNum: 1,
                }}
            />
        </Modal>
    );
};

export default InvoiceModal;

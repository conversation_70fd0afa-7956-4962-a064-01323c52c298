const {Fragment, useState, useEffect, useRef,useMemo} = React;
const {Button, Space, Alert,message,Modal,Steps,Divider} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents
import request from "../../../utils/plugins/axios/request";

const App = ()=>{
    return (<div>
        sssdada
    </div>)
}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
            method:'post',
            //   headers:Object.assign(params.headers,{
            //     "Content-Type": "application/json"
            //   }),
            //   data: JSON.stringify(params.data || {}),
              shopId: sessionStorage.shopId,
        })
        request(obj);
      }
    }
  })}>
    <App />
  </SpaConfigProvider>, document.getElementById("invoice-manage")
);

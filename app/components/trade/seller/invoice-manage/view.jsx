const {useRef, useState} = React;
const {Button, Space, message, Tag} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents
import request from "../../../utils/plugins/axios/request";
import InvoiceModal from "./invoice-modal";

const App = () => {
    const searchListRef = useRef();

    // 弹框状态管理
    const [modalOpen, setModalOpen] = useState(false);
    const [modalMode, setModalMode] = useState('add'); // 'add', 'edit', 'view'
    const [currentInvoiceData, setCurrentInvoiceData] = useState({});

    // 获取 maria 配置，ID 为 111
    const getConfig = async () => {
        const data = await new Promise((resolve, reject) => {
            $.ajax({
                url: "https://maria.yang800.com/api/data/v2/1021/866",
                success: (res) => {
                    resolve(res);
                },
                error: (err) => {
                    reject(err);
                }
            });
        });
        return data.data;
    };

    // 自定义表格渲染函数
    const tableCustomFun = {
        // 订单状态渲染
        orderStatusRender: (row) => {
            const statusMap = {
                '锁定中': { color: 'blue', text: '锁定中' },
                '待开票': { color: 'orange', text: '待开票' },
                '已开票': { color: 'green', text: '已开票' },
                '已关闭': { color: 'gray', text: '已关闭' }
            };
            const status = statusMap[row.orderStatus] || { color: 'default', text: row.orderStatus };
            return <Tag color={status.color}>{status.text}</Tag>;
        },

        // 开票状态渲染
        invoiceStatusRender: (row) => {
            const statusMap = {
                '锁定中': { color: 'blue', text: '锁定中' },
                '待开票': { color: 'orange', text: '待开票' },
                '已开票': { color: 'green', text: '已开票' },
                '已关闭': { color: 'gray', text: '已关闭' }
            };
            const status = statusMap[row.invoiceStatus] || { color: 'default', text: row.invoiceStatus };
            return <Tag color={status.color}>{status.text}</Tag>;
        },

        // 红冲状态渲染
        redFlushStatusRender: (row) => {
            if (row.redFlushStatus) {
                return <Tag color="red">{row.redFlushStatus}</Tag>;
            }
            return '-';
        },

        // 操作按钮渲染
        operationRender: (row) => {
            const operations = [];

            if (row.orderStatus === '待开票') {
                operations.push(
                    <a key="invoice" onClick={() => handleInvoice(row)}>开票</a>
                );
                operations.push(
                    <a key="cancel" onClick={() => handleCancel(row)}>关闭</a>
                );
            }

            if (row.orderStatus === '已开票') {
                operations.push(
                    <a key="view" onClick={() => handleView(row)}>查看发票</a>
                );
                operations.push(
                    <a key="redFlush" onClick={() => handleRedFlush(row)}>红冲</a>
                );
            }

            return (
                <Space>
                    {operations}
                </Space>
            );
        },

        // 发票类型渲染
        invoiceTypeRender: (row) => {
            return row.invoiceType || '增值税专用发票';
        }
    };

    // 操作处理函数
    const handleInvoice = (row) => {
        setCurrentInvoiceData(row);
        setModalMode('add');
        setModalOpen(true);
    };

    const handleCancel = (row) => {
        message.info(`关闭操作：${row.orderNo}`);
        // 这里添加关闭逻辑
    };

    const handleView = (row) => {
        setCurrentInvoiceData(row);
        setModalMode('view');
        setModalOpen(true);
    };

    const handleRedFlush = (row) => {
        message.info(`红冲操作：${row.orderNo}`);
        // 这里添加红冲逻辑
    };

    // 关闭弹框
    const handleModalClose = () => {
        setModalOpen(false);
        setCurrentInvoiceData({});
        // 刷新列表
        searchListRef.current?.load();
    };

    return (
        <div style={{ padding: '20px' }}>
            <SearchList
                ref={searchListRef}
                scrollMode="tableScroll"
                paginationConfig={{
                    size: "default",
                    showPosition: 'bottom'
                }}
                searchConditionConfig={{
                    size: "middle",
                }}
                getConfig={getConfig}
                tableCustomFun={tableCustomFun}
                renderRightOperation={() => {
                    return (
                        <Space>
                            {/* <Button type="primary">手动开票</Button> */}
                            <Button>导出</Button>
                        </Space>
                    );
                }}
            />

            {/* 开票弹框 */}
            <InvoiceModal
                open={modalOpen}
                mode={modalMode}
                invoiceData={currentInvoiceData}
                onClose={handleModalClose}
            />
        </div>
    );
};
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
            method:'post',
            //   headers:Object.assign(params.headers,{
            //     "Content-Type": "application/json"
            //   }),
            //   data: JSON.stringify(params.data || {}),
              shopId: sessionStorage.shopId,
        })
        request(obj);
      }
    }
  })}>
    <App />
  </SpaConfigProvider>, document.getElementById("invoice-manage")
);

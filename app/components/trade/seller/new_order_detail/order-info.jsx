// import React, {useEffect, useMemo, useState} from "react";

const {Fragment, useState, useEffect, useRef, useMemo} = React;
const {Button, Space, Alert, message, Modal, Steps, Row, Col} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents
import PanelGroup from "./panel-group";

const {EyeOutlined, EyeInvisibleOutlined} = icons;
import RowItem from "./row-item";
import {getParam} from "../../../common/react/utils/common"
import request from "../../../utils/plugins/axios/request";
// import {ORDER_STATUS} from "@/page/order/order-const";

export default function OrderInfo({orderItem = {}, shipmentInfo, onEyeClick}) {
  const [desensitize, setDesensitize] = useState(true)
  const [receiverInfo, setReceiverInfo] = useState({})
  const [payOpen,setPayOpen] = useState(true)
  const [payInfo,setPayInfo] = useState({})
  /**
   * 收货人
   */
  const desensitizeReceiverInfo = () => {
    setDesensitize(!desensitize);
    const id = getParam('id')
    $.ajax({
      url: `/api/order/${id}/showReceiverInfo`,
      type: 'get',
      success: function (res) {
        const receiverInfo = res.orderReceiverInfos[0].receiverInfo;
        if (desensitize) {
          setReceiverInfo(receiverInfo)
        } else {
          setReceiverInfo(orderItem.orderReceiverInfos[0].receiverInfo)
        }
      }
    })
  }

    /**
   * 支付信息加解密
   */
    const desensitizePayInfo = () => {
        setPayOpen(!payOpen);
        const id = getParam('id')
        request({
          url: `/api/v2/shopOrder/payInfo/of/decode?orderId=${id}`,
          method: 'get',
          success: function (data) {
            // const receiverInfo = res.orderReceiverInfos[0].receiverInfo;
            if (payOpen) {
                setPayInfo(data)
            } else {
                setPayInfo(orderItem.payInfo)
            }
          }
        })
      }

  useEffect(() => {
    if (Array.isArray(orderItem.orderReceiverInfos) && orderItem.orderReceiverInfos.length ) {
        setReceiverInfo(orderItem.orderReceiverInfos[0].receiverInfo)
    }
    if(orderItem.payInfo){
        setPayInfo(orderItem.payInfo)
    }
  }, [orderItem])

  const isRePayOrder = orderItem.shopOrder.paymentWay === "friendPay"

  return (<PanelGroup>
    <div className={'main-layout-row'}>
      <div style={{width: "100%"}}>
        <PanelGroup
          title={<Fragment>
            <span style={{border: 'bolder'}}> 收件信息</span>
            <a style={{whiteSpace: 'nowrap'}} onClick={() => {
              desensitizeReceiverInfo()
            }}>
              {
                !desensitize ?
                  <EyeOutlined className={'style-view-icon'}/>
                  :
                  <EyeInvisibleOutlined className={'style-view-icon'}/>
              }

            </a>
          </Fragment>}
          style={{padding: 0}}
          children={
            <div className="main-row-detail-info-wrapper">
              <RowItem label={'收件人姓名'} labelStyle={{minWidth: '70px'}}>
                <span>{receiverInfo ? receiverInfo.receiveUserName : '-'}</span>
              </RowItem>
              <RowItem label={'收件人手机号'} labelStyle={{minWidth: '70px'}}>
                {/* receiverInfo */}
                <span>{receiverInfo ? receiverInfo.mobile : '-'}</span>
              </RowItem>
              <RowItem label={'收件人地址'} labelStyle={{minWidth: '70px'}}>
                {/* 支付平台字段没有 */}
                <span>{receiverInfo ?
                  `${receiverInfo.province || ""}
                    ${receiverInfo.city || ""}
                    ${receiverInfo.region || ""}
                    ${receiverInfo.street || ""}
                    ${receiverInfo.detail || ""}
                    ${receiverInfo.postcode || ""}` : '-'}
                                </span>
              </RowItem>
            </div>
          }/>
      </div>
      <div style={{width: "100%"}}>
        <PanelGroup
          title={'买家信息'}
          style={{padding: 0}}
          children={<div className="main-row-detail-info-wrapper">
            <RowItem label={'用户昵称'}>
              <div className="main-contact">
                <span className="main-infoItem">{orderItem ? orderItem.shopOrder.buyerName : '-'}</span>
              </div>
            </RowItem>
            <RowItem label={'买家留言'}>
              <span className="">{orderItem ? orderItem.shopOrder.buyerNote : '无'}</span>
            </RowItem>
          </div>
          }/>

      </div>
      <div style={{width: "100%"}}>
        <PanelGroup
          title={<Fragment>
            <span style={{border: 'bolder'}}> 支付信息</span>
            <a style={{whiteSpace: 'nowrap'}} onClick={() => {
            //   desensitizeReceiverInfo()
            desensitizePayInfo()
            }}>
              {
                !payOpen ?
                  <EyeOutlined className={'style-view-icon'}/>
                  :
                  <EyeInvisibleOutlined className={'style-view-icon'}/>
              }

            </a>
          </Fragment>}
          style={{padding: 0}}
          children={
            <div className="main-row-detail-info-wrapper">
              <RowItem label={'支付方式'} labelStyle={{minWidth: '70px'}}>
                {/* {{#equals shopOrder.payType 1}}在线支付{{/equals}}{{#equals shopOder.payType 2}}货到付款{{/equals}} */}
                <span>{orderItem.shopOrder && orderItem.shopOrder.payType === 1 ? "在线支付" : null}</span>
                <span>{orderItem.shopOrder && orderItem.shopOrder.payType === 2 ? "货到付款" : null}</span>
              </RowItem>
              <RowItem label={'支付平台'} labelStyle={{minWidth: '70px'}}>
                {/* 支付平台字段没有 */}
                <span>{orderItem && orderItem.payment ? orderItem.payment.channel : '-'}</span>
              </RowItem>
              <RowItem label={'支付时间'} labelStyle={{minWidth: '70px'}}>
                <span>{orderItem && orderItem.payment && orderItem.payment.paidAt ? moment(orderItem.payment.paidAt).format("YYYY-MM-DD HH:mm:ss") : '-'}</span>
              </RowItem>
              {
                isRePayOrder?
                <RowItem label={'订购人'} labelStyle={{minWidth: '70px'}}>
                <span>{orderItem ? orderItem.buyerName : '-'}</span>
              </RowItem>
                :
                null
                
              }
              {
                isRePayOrder?
                // buyerThirdId
                <RowItem label={'支付人'} labelStyle={{minWidth: '70px'}}>
                <span>{orderItem ? orderItem.buyerThirdId : '-'}</span>
              </RowItem>
              :
              <RowItem label={'支付人'} labelStyle={{minWidth: '70px'}}>
                <span>{payInfo ? payInfo.payName : '-'}</span>
              </RowItem>
              }
               {
                isRePayOrder?
                <RowItem label={'订购人手机号'} labelStyle={{minWidth: '70px'}}>
                    <span>{orderItem.buyerMobile|| '-'}</span>
                </RowItem>
                :<RowItem label={'手机号'} labelStyle={{minWidth: '70px'}}>
                <span>{payInfo ? payInfo.phone : '-'}</span>
              </RowItem>
              }
              
              {
                isRePayOrder?
                <RowItem label={'订购人身份证'} labelStyle={{minWidth: '70px'}}>
                    <span>{orderItem ? orderItem.buyerNo : '-'}</span>
                </RowItem>
                    :
                <RowItem label={'支付人身份证'} labelStyle={{minWidth: '70px'}}>
                    <span>{payInfo ? payInfo.payIdCard : '-'}</span>
                </RowItem>
              }
              <RowItem label={'支付平台流水单号'} labelStyle={{minWidth: '70px'}}>
                <span>{orderItem && orderItem.payment ? orderItem.payment.paySerialNo : '-'}</span>
              </RowItem>

            </div>
          }/>

      </div>
    </div>
  </PanelGroup>)
}

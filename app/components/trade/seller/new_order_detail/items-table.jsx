// ;
// import {Table} from "antd";
// import ItemProduct from "@/page/order/components/item-product";

const { Fragment, useState, useEffect, useRef } = React;
const { Button, Space, Alert, message, Modal, Steps, Table } = antd;

import ComboGoodsModal from "../new_order_manage/combo-goods-modal"
export default function ItemsTable({ dataSource, detail }) {
    const columns = [
        {
            "title": "商品信息",
            "width": 250,
            render: (text, record, index) => {
                return <Fragment>
                <a href={`/items/${record.itemId}`} target="_blank">{record.itemName}</a>
                {
                    record.skuComboList && record.skuComboList.length >0 ?
                    <div>
                        {/* //组合品 */}
                        <ComboGoodsModal skuComboList={record.skuComboList||[]}  />
                    </div>
                    :
                    <div style={{color:'#888'}}>
                        {/* //组合品 */}
                        {record.specification}
                    </div>
                }

                </Fragment>
            }
        },
        // {
        //     "title": "SKU",
        //     "dataIndex": "skuId",
        //     "width": 100,
        // },
        {
            "title": "总价",
            "dataIndex": "fee",
            "width": 100,
            render: (text, record, index) => {
                return text / 100
            }
        },
        {
            "title": "单价/数量",
            "dataIndex": "refundStatusDescription",
            "width": 126,
            render: (text, record, index) => {
                return <Fragment>
                    <span className="main-yuan">¥</span>{record.price / 100}
                    <div className="table_order-list-table-cell-item table_sub">x{record.quantity}</div>
                </Fragment>
            }
        },
        {
            "title": "优惠金额",
            "dataIndex": "discount",
            "align": "right",
            "width": 200,
            render: (text, record, index) => {
                const couponPrice = (!!record.discountDetail &&record.discountDetail.itemCouponPrice||0) + (!!record.discountDetail && record.discountDetail.shopCouponPrice||0)
                return <div>
                    <span>{record.discountDetail&&record.discountDetail.discount ? `¥${record.discountDetail.discount}` : '无优惠'}</span>
                    {record.discountDetail &&
                        <div style={{ color: "#888" }}>
                            {couponPrice > 0 && <div>
                                含优惠券：{couponPrice}
                                </div>}
                            {!!record.discountDetail.giftPrice && record.discountDetail.giftPrice > 0 && <div> 含福豆：{record.discountDetail.giftPrice}</div>}
                            {!!record.discountDetail.cashPrice && record.discountDetail.cashPrice > 0 && <div>含福卡：{record.discountDetail.cashPrice}</div>}
                        </div>
                    }
                </div >
            }
        },
        {
            "title": "订单总额",
            "dataIndex": "fee",
            "width": 200,
            render: (text, record, index) => {
                const obj = {
                    children: <div>
                        <span>¥{detail && detail.shopOrder && detail.shopOrder.fee / 100}</span>
                        <div style={{ color: "#888" }}>
                            <div>含运费：{detail && detail.shopOrder && detail.shopOrder.shipFee / 100}</div>
                            <div>含税费：{detail && detail.totalTax && detail.totalTax / 100}</div>
                        </div>
                    </div>,
                    props: {},
                };
                if (index === 0) {
                    obj.props.rowSpan = dataSource.length;
                } else {
                    obj.props.rowSpan = 0;
                }
                return obj;

            }
        },
        {
            "title": "优惠信息",
            "dataIndex": "discount",
            "width": 200,
            render: (text, record, index) => {
                const obj = {
                    children: <div>
                        <div style={{ color: "#888" }}>
                            {
                                detail && detail.shopOrder && detail.shopOrder.feeDetail &&
                                <div>
                                    {/* <div>优惠券：{detail.shopOrder.feeDetail.couponPrice}</div> */}
                                    <div>商品券：{detail.shopOrder.feeDetail.itemCouponPrice}</div>
                                    <div>店铺券：{detail.shopOrder.feeDetail.shopCouponPrice}</div>
                                    <div>福豆：{detail.shopOrder.feeDetail.giftPrice}</div>
                                    <div>福卡：{detail.shopOrder.feeDetail.cashPrice}</div>
                                </div>
                            }
                        </div>
                    </div>,
                    props: {},
                };
                if (index === 0) {
                    obj.props.rowSpan = dataSource.length;
                } else {
                    obj.props.rowSpan = 0;
                }
                return obj;
            }
        },
        {
            "title": "买家实付",
            "dataIndex": "totalPrice",
            "width": 200,
            render: (text, record, index) => {
                const obj = {
                    children: <div>
                        <span>¥{detail && detail.shopOrder && detail.shopOrder.fee / 100}</span>
                    </div>,
                    props: {},
                };
                if (index === 0) {
                    obj.props.rowSpan = dataSource.length;
                } else {
                    obj.props.rowSpan = 0;
                }
                return obj;
            }
        },
    ]
    return <Table columns={columns}
        rowKey={'skuOrderNo'}
        tableLayout={'auto'}
        pagination={false}
        dataSource={dataSource} />
}

const {useRef, Fragment,useEffect,useState} = React;
const { Modal,Space,Image,message,Table,Tooltip } = antd;


export default ({
    skuComboList,
})=>{
    const [open,setOpen] = useState(false);

    const [str,setStr] = useState('');

    useEffect(()=>{
        setStr((skuComboList||[]).map(item=>{
            return `${item.comboItemName}*${item.comboSkuQuantity}`
        }).join('+'))
    },[skuComboList])

    const onClose = ()=>{
        setOpen(false)
    }

    const columns = [
        {
          title: '商品信息',
          dataIndex: 'comboItemName',
          width: '60%',
          render: (text, record) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <img 
                src={record.comboItemMainImage} 
                style={{ width: '50px', height: '50px', marginRight: '10px' }}
                alt={text}
              />
              <div>
                <div>{text}</div>
                <div style={{ color: '#999', fontSize: '12px' }}>{record.comboSkuSpecification}</div>
                <div style={{ color: '#999', fontSize: '12px' }}>
                  ID: {record.comboItemId}
                </div>
                
              </div>
            </div>
          ),
        },
        {
          title: '数量',
          dataIndex: 'comboSkuQuantity',
          width: '40%',
          align: 'right',
          render: (text) => `x${text}`,
        }
      ];
    
    return (<Fragment>
        <div className="combo-info-rect" onClick={()=>{
            setOpen(true);
        }}>
            <Tooltip title={str}>
                <div className="ellispis-text" >组合：{str}</div>
            </Tooltip>
            <a className="combo-details-text">详情</a>
        </div>
        <Modal
            title="组合商品信息"
            open={open}
            onCancel={onClose}
            footer={[
                <button 
                    key="ok" 
                    className="btn btn-primary"
                    onClick={onClose}
                >
                我知道了
                </button>
            ]}
            width={600}
        >
                <Table 
                    columns={columns}
                    dataSource={skuComboList}
                    pagination={false}
                    rowKey="id"
                    bordered={false}
                />
            </Modal>
    </Fragment>)
}
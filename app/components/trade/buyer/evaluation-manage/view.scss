.template-content {
    textarea.ant-input {
        width: 200px !important;
    }

    .reply-card-list {
        width: 100%;
        padding-top: 24px;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .reply-card {
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    }

    .reply-card-content {
        color: rgba(0, 0, 0, .45)
    }

    .reply-card-footer {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}

.evaluationContent {
    margin-bottom: 0 !important;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;

    p {
        margin-bottom: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        span {
            background: transparent !important;
        }
    }
}

.evaluationContent-tip {
    p {
        span {
            background: transparent !important;
        }
    }
}
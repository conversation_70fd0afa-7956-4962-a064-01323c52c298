/**
 * @description: 评价管理
 * <AUTHOR>
 */
import request from "../../../utils/plugins/axios/request";
import EvaluationConfigModal from "./components/evaluation-config-modal"
import Template from "./components/template"
import { Audit_Enum } from "./enum";
const { useRef, useState } = React;
const { Space, Button, Modal, message, Input, Tabs, Rate, Typography, Tooltip } = antd;
const { SearchList, Spa, SpaConfigProvider } = dtComponents
const { Paragraph } = Typography;
const TextArea = Input.TextArea

export const ServiceProviderStore = () => {
	const searchListRef = useRef();
	const [tabValue, setTabValue] = useState("");
	const [tabNum, setTabNum] = useState({});
	const [selects, setSelects] = useState([])
	const [countList, setCountList] = useState({});
	const [evaluationConfigOpen, setEvaluationConfigOpen] = useState(false)
	const [templateOpen, setTemplateOpen] = useState(false);

	const getConfig = async function () {
		const data = await axios.get("https://maria.yang800.com/api/data/v2/953/785")
		return data.data.data;
	};

	// 获取tab数量
	const getTabNum = (obj) => {
		let data = { ...obj }
		delete data.status;
		request({
			url: `/mall-admin/api/item/evaluation-p/count`,
			method: "POST",
			data: { ...data, shopId: sessionStorage.shopId },
			success: (res) => {
				setTabNum(res)
			},
		})
	}

	// 评分信息
	const getCount = (itemId) => {
		request({
			url: "/mall-admin/api/item/evaluation-p/info",
			data: { shopId: sessionStorage.shopId, itemId: itemId },
			success: (res) => {
				setCountList({ ...res, itemId })
			},
		})
	}

	const handleBatch = ({ title, content, requestFunction }) => {
		if (selects.length === 0) {
			return message.error("请勾选数据");
		}

		let ids = selects.map(item => item.id);

		Modal.confirm({
			title: title,
			content: content,
			onOk: () => {
				return requestFunction(ids);
			}
		});
	};

	const batchDelete = () => {
		handleBatch({
			title: "删除",
			content: "确定删除评价吗?",
			requestFunction: (ids) => {
				request({
					url: `/mall-admin/api/item/evaluation-p/deleteBatch`,
					data: { idList: ids },
					success: (res) => {
						searchListRef.current.load();
						message.success("删除成功");
					},
				});
			}
		});
	};

	// 标记通过
	const batchPass = () => {
		handleBatch({
			title: "通过",
			content: "确定通过评价吗？",
			requestFunction: (ids) => {
				request({
					url: `/mall-admin/api/item/evaluation-p/auditBatch`,
					data: { idList: ids, auditStatus: Audit_Enum.EVALUATION_PASS },
					success: (res) => {
						searchListRef.current.load();
						message.success("标记成功");
					},
				});
			}
		});
	};

	// 标记驳回
	const batchReject = () => {
		let rejectReason = '';

		handleBatch({
			title: "驳回",
			content: <div>
				<p>确定驳回评价吗？</p>
				<TextArea
					rows={1}
					maxLength={64}
					placeholder="请输入驳回原因"
					onChange={(e) => { rejectReason = e.target.value }}
				/>
			</div>,
			requestFunction: (ids) => {
				request({
					url: `/mall-admin/api/item/evaluation-p/auditBatch`,
					data: { idList: ids, rejectReasons: rejectReason, auditStatus: Audit_Enum.EVALUATION_REJECT },
					success: (res) => {
						searchListRef.current.load();
						message.success("标记成功");
					},
				});
			}
		});
	};

	const tabItems = [
		{
			key: '',
			label: `全部`,
		},
		{
			key: Audit_Enum.EVALUATION_WAIT,
			label: `待审核 （${tabNum.waitCount || "0"}）`,
		},
		{
			key: Audit_Enum.EVALUATION_PASS,
			label: `已通过 （${tabNum.passCount || "0"}）`,
		},
		{
			key: Audit_Enum.EVALUATION_REJECT,
			label: `已驳回 （${tabNum.rejectCount || "0"}）`,
		},
	]
	return (
		<SearchList
			ref={searchListRef}
			scrollMode={"tableScroll"}
			paginationConfig={{ size: "default", showPosition: 'bottom' }}
			searchConditionConfig={{
				size: "middle",
			}}
			getConfig={getConfig}
			onSearchReset={() => {
				setTabValue("");
			}}
			onSearch={(obj, type) => {
				getTabNum(obj)
				getCount(obj.itemId);
			}}
			onTableSelected={(ids, rows) => {
				setSelects([...rows]);
			}}
			tableCustomFun={{
				score: (row) => (
					<Rate value={row.scoreCode / 20} disabled/>
				),
				orderId: (row) => (
					<a  type='link' target="_blank" href={`/seller/order-detail?id=${row.orderId}`}>{row.orderId}</a>
				),
				evaluationContent: (row) => (
					// <Paragraph
					// 	className="evaluationContent"
					// 	ellipsis={{ rows: 1, tooltip: <span dangerouslySetInnerHTML={{ __html: row.evaluationContent }}></span> }}
					// >
					// 	<p dangerouslySetInnerHTML={{ __html: row.evaluationContent }}></p>
					// </Paragraph>
					<Tooltip className="evaluationContent" title={<span className="evaluationContent-tip" dangerouslySetInnerHTML={{ __html: row.evaluationContent }}></span>} >
						<p dangerouslySetInnerHTML={{ __html: row.evaluationContent }}></p>
					</Tooltip>
				),
				getOperation: (row) => {
					return (
						<a href={`/seller/evaluation-reply?id=${row.id}`} target="_blank">查看</a>
					)
				}
			}}
			renderOperationTopView={() => {
				return (
					<Tabs
						activeKey={tabValue}
						items={tabItems}
						onChange={(e) => {
							setTabValue(e);
							searchListRef.current.changeImmutable({ auditStatus: e })
						}} />
				)
			}}
			renderLeftOperation={() => {
				return (
					<Space>
						<Button onClick={() => { batchDelete(selects) }}>批量删除</Button>
						<Button onClick={() => { batchPass(selects) }}>标记通过</Button>
						<Button onClick={() => { batchReject(selects) }}>标记驳回</Button>
						<div>
							<span>SPU：{countList.itemId || ""}</span>
							<span style={{ marginLeft: 32 }}>总评价数：{countList.totalCount || 0}</span>
							<span style={{ marginLeft: 24 }}>平均分：{countList.averageScore || 0}</span>
							<span style={{ marginLeft: 24 }}>好评度：{countList.praise || 0}%</span>
						</div>
					</Space>
				)
			}}
			renderRightOperation={() => {
				return (
					<Space>
						<Button onClick={() => { }}>
							<a href="/seller/sensitive-word" target="_blank">敏感词配置</a>
						</Button>
						<Button onClick={() => { setEvaluationConfigOpen(true) }}>默认评价配置</Button>
						<Button onClick={() => { setTemplateOpen(true) }}>回复模板配置</Button>
					</Space>
				)
			}}
			renderModal={() => {
				return <div>
					{
						evaluationConfigOpen &&
						<EvaluationConfigModal
							open={evaluationConfigOpen}
							onClose={() => {
								setEvaluationConfigOpen(false)
								searchListRef.current.load();
							}}
						/>
					}
					{
						templateOpen &&
						<Template
							open={templateOpen}
							onClose={() => { setTemplateOpen(false) }}
						/>
					}
				</div>
			}}
		/>
	)
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
	_openPage: () => {
	}, request: {
		request: (params) => {
			const obj = Object.assign(params, {
				method: 'POST',
			})
			request(obj);
		}
	}
})}><ServiceProviderStore />
</SpaConfigProvider>, document.getElementById("evaluation-manage")
);

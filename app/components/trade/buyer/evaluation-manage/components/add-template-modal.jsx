/**
 * @description: json数据渲染
 * @author: wangchunting
 */
import { configs } from './utils/add-config'
import request from "../../../../utils/plugins/axios/request";
const { useEffect, useState } = React;
const { Form, Modal, message } = antd;
const { SourceInfoComponent } = dtComponents

const AddTemplateModal = ({ row, open, onClose }) => {
    const [form] = Form.useForm()
    const [id, setId] = useState(null)

    useEffect(() => {
        form.setFieldsValue(row)
        setId(row.id)
    }, [row])

    // 提交
    const handleOk = () => {
        form.validateFields().then(values => {
            let data = { ...values }
            if (id) { data.id = id }
            request({
                url: `/mall-admin/api/item/evaluation/template/saveOrEdt`,
                method: "POST",
                data: data,
                success: (res) => {
                    message.success("保存成功");
                    onClose();
                },
            })
        });
    };
    
    // 取消
    const handleCancel = () => {
        onClose();
        form.resetFields();
    };
    const layout = {
        labelCol: {
            style: { width: '138px', paddingBottom: '24px' },
        },
    }
    return (
        <Modal
            title={id ? "编辑模板" : "新建模板"}
            width={400}
            open={open}
            keyboard={false}
            maskClosable={false}
            onCancel={handleCancel}
            onOk={handleOk}>
            <Form {...layout} layout="inline" form={form}>
                <SourceInfoComponent data={configs} infoEdit={false} />
            </Form>
        </Modal>

    )
}
export default AddTemplateModal

/** 
 * @description: 回复模版配置
 * @author: wangchunting
 */
import request from "../../../../utils/plugins/axios/request";
import AddTemplateModal from "./add-template-modal"
const { useEffect, useState } = React;
const { Form, Modal, message, Card, Typography, Space, Button } = antd;
const { FormSelect, FormInput } = dtComponents
const { FormOutlined, DeleteOutlined } = icons;
const { Title, Paragraph, Text } = Typography;

const Template = ({ open, onClose }) => {
    const [form] = Form.useForm()
    const [addTemplateOpen, setAddTemplateOpen] = useState(false);
    const [row, setRow] = useState({});
    const [dataList, setDataList] = useState([]);

    useEffect(() => {
        getDataList()
    }, [])

    // 获取列表
    function getDataList(values = {}) {
        request({
            url: `/mall-admin/api/item/evaluation/template/list`,
            data: values,
            success: (res) => {
                setDataList(res)
            },
        })
    }

    // 搜索
    const handleSearch = () => {
        form.validateFields().then(values => {
            getDataList(values)
        });
    }

    // 重置
    const handleReset = () => {
        form.resetFields();
        getDataList()
    }

    // 删除
    function handleDelete(item) {
        Modal.confirm({
            title: "删除",
            content: "确定删除模板吗？",
            onOk: () => {
                request({
                    url: '/mall-admin/api/item/evaluation/template/delete',
                    method: "POST",
                    data: { id: item.id },
                    success: () => {
                        message.success("删除成功");
                        getDataList();
                    }
                })
            }
        })
    }
    const layout = {
        labelCol: {
            style: { width: '138px', paddingBottom: '24px' },
        },
    }

    return (
        <Modal
            title="回复模板配置"
            width={880}
            open={open}
            keyboard={false}
            maskClosable={false}
            onCancel={onClose}
            footer={null}
            className="template-content"
        >
            <Form {...layout} layout="inline" form={form} >
                <FormInput
                    fProps={{ label: "回复内容", name: 'templateContentLike' }}

                />
                <FormSelect
                    fProps={{ label: '回复类型', name: 'templateType' }}
                    url="/mall-admin/api/item/evaluation/template/templateType"
                />
                <Button type="primary" onClick={handleSearch} style={{ marginRight: 16 }}>
                    查询
                </Button>
                <Button onClick={handleReset}>重置</Button>
            </Form>
            <Button type="primary" onClick={() => { setAddTemplateOpen(true) }} style={{ marginRight: 16 }}>
                新建模板
            </Button>
            <div className="reply-card-list">
                {dataList.length ? dataList.map((item, index) => (
                    <Card
                        key={index}
                        className="reply-card"
                        bordered
                        size="small"
                        bodyStyle={{ paddingBottom: 12 }}
                    >
                        <Title level={5}>{item.templateTypeDesc}</Title>
                        <Paragraph className="reply-card-content">{item.templateContent}</Paragraph>
                        <div className="reply-card-footer">
                            <Text type="secondary">创建时间：{item.createdTimeDesc}</Text>
                            <Space>
                                <Button
                                    type="text"
                                    icon={<FormOutlined />}
                                    title="编辑"
                                    onClick={() => {
                                        setAddTemplateOpen(true);
                                        setRow(item);
                                    }}
                                />
                                <Button
                                    type="text"
                                    icon={<DeleteOutlined />}
                                    title="删除"
                                    onClick={() => {
                                        handleDelete(item)
                                    }}
                                />
                            </Space>
                        </div>
                    </Card>
                )) : ""}
            </div>
            {
                addTemplateOpen &&
                <AddTemplateModal
                    row={row}
                    open={addTemplateOpen}
                    onClose={() => {
                        setAddTemplateOpen(false);
                        setRow({})
                        getDataList()
                    }}
                />
            }
        </Modal>

    )
}
export default Template;

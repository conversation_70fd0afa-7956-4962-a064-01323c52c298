/** 
 * @description: 默认评价配置
 * @author: wangchunting
 */
import request from "../../../../utils/plugins/axios/request";
import { Star_Enum } from "../enum";
const { useEffect, useState } = React;
const { QuestionCircleOutlined } = icons
const { Form, Modal, message, Tooltip, Rate } = antd;
const { FormTextArea, FormSelect, FormRangePicker, FormSwitch } = dtComponents

const FormRenderDemo = ({ row, open, onClose }) => {
    const [form] = Form.useForm()
    const [textEvaluationSwitch, setTextEvaluationSwitch] = useState(false)
    const [evaluationSwitch, setEvaluationSwitch] = useState(false)
    const [detail, setDetail] = useState({})

    useEffect(() => {
        getDetail()
    }, [])

    // 获取详情
    function getDetail() {
        request({
            url: `/mall-admin/api/item/evaluation/config/get`,
            success: (res) => {
                if (res.effectiveTimeStart) {
                    res.effectiveTimeStart = [moment(moment(res.effectiveTimeStart).format("yyyy-MM-DD")), moment(moment(res.effectiveTimeEnd).format("yyyy-MM-DD"))]
                }
                res.scoreCode = res.scoreCode / 20
                form.setFieldsValue(res)
                setTextEvaluationSwitch(res.textEvaluationSwitch)
                setEvaluationSwitch(res.evaluationSwitch)
                setDetail(res)
            },
        })
    }

    // 提交
    const handleOk = () => {
        form.validateFields().then(values => {
            const [start, end] = values.effectiveTimeStart
            let data = {
                ...values,
                id: detail.id ? detail.id : null,
                // scoreCode: values.scoreCode && Star_Enum.find(item => item.name == values.scoreCode * 20).id,
                scoreCode: values.scoreCode && values.scoreCode * 20,
                effectiveTimeStart: start ? moment(start).format('x') : null,
                effectiveTimeEnd: end ? moment(end).format('x') : null,
            }
            request({
                url: `/mall-admin/api/item/evaluation/config/saveOrEdt`,
                method: "POST",
                data: data,
                success: (res) => {
                    message.success("默认评价配置提交成功");
                    onClose();
                },
            })
        });
    };

    // 取消
    const handleCancel = () => {
        onClose();
        form.resetFields();
    };

    const layout = {
        labelCol: {
            style: { width: '138px', paddingBottom: '24px' },
        },
    }
    return (
        <Modal
            title="默认评价配置"
            width={480}
            open={open}
            keyboard={false}
            maskClosable={false}
            onCancel={handleCancel}
            onOk={handleOk}>
            <Form {...layout} layout="inline" form={form}>
                <FormSwitch
                    fProps={
                        {
                            label: <div>
                                默认评价开关
                                <Tooltip placement="top" title="关闭后不生成默认评价，不参与好评度计算">
                                    <QuestionCircleOutlined style={{ marginLeft: 10 }} />
                                </Tooltip>
                            </div>,
                            name: 'evaluationSwitch',
                        }
                    }
                    cProps={{
                        checkedChildren: '开',
                        unCheckedChildren: '关',
                        checked: evaluationSwitch
                    }}
                    onChange={(checked) => {
                        setEvaluationSwitch(checked)
                    }}
                />
                <FormRangePicker
                    fProps={
                        {
                            label: '有效时间',
                            name: 'effectiveTimeStart',
                            rules: { required: true }
                        }
                    }
                />
                <Form.Item name="scoreCode" label="默认评分" required>
                    <Rate />
                </Form.Item>

                <FormSelect
                    fProps={
                        {
                            label: '是否开启文字评价',
                            name: 'textEvaluationSwitch',
                            rules: { required: true },
                            initialValue: -1
                        }
                    }
                    list={[{ label: '是', value: 1 }, { label: '否', value: -1 }]}
                    onChange={(value) => {
                        setTextEvaluationSwitch(value)
                    }}
                />
                { // 是否开启文字评价为是,则展示
                    textEvaluationSwitch === 1 && (
                        <FormTextArea
                            fProps={
                                {
                                    label: '文字评价',
                                    name: 'evaluationContent',
                                    rules: [
                                        { required: true, message: '请输入文字评价' },
                                        {
                                            max: 255,
                                            message: "不允许超过255个字符",
                                        }
                                    ],
                                }
                            }
                            cProps={{
                                rows: 1,
                            }}
                        />
                    )
                }

                <FormSelect
                    fProps={
                        {
                            label: '用户是否可见',
                            name: 'userSeeSwitch',
                            rules: { required: true },
                            initialValue: 1
                        }
                    }
                    list={[{ label: '是', value: 1 }, { label: '否', value: -1 }]}
                />
            </Form>
        </Modal>

    )
}
export default FormRenderDemo

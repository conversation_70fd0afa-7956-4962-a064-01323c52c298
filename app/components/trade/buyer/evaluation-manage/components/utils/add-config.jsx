/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line no-undef
const { FormSelect, FormTextArea } = dtComponents
export const configs = [{
  type: FormSelect,
  fProps: {
    label: "回复类型",
    name: "templateType",
    rules: [{ required: true, message: "请选择回复类型" }],
  },
  url: "/mall-admin/api/item/evaluation/template/templateType",
  cProps: {
    width: 200,
  }
},
{
  type: FormTextArea,
  fProps: {
    label: "回复内容",
    name: "templateContent",
    rules: [
      { required: true, message: "请输入回复内容" },
      {
        max: 255,
        message: "不允许超过255个字符",
      }
    ],
  },
  cProps: {
    width: 200,
    rows: 3
  }
}]

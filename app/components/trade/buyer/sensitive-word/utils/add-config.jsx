/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line import/prefer-default-export, no-undef
const { FormInput, FormTextArea } = dtComponents
export const configs = [{
  type: FormInput,
  fProps: {
    label: "敏感词",
    name: "word",
    rules: [
            { required: true, message: "请输入敏感词" },
      {
        pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]{1,6}$/,
        message: "请输入中英文数字，最长限制6位",
      }],
    cProps: {
      width: 200,
      maxLength: 6,
    }
  },
},
{
  type: FormTextArea,
  fProps: {
    label: "备注",
    name: "remark",
    rules: [
      {
        max: 16,
        // message: "请输入最长限制16位字符",
        message: "不允许超过16字符",
      }]
  },
  cProps: {
    width: 200,
    rows: 1
  }
}]

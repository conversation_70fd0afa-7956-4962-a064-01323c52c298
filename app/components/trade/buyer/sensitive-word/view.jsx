/**
 * @description: 敏感词配置
 * <AUTHOR>
 */
import request from "../../../utils/plugins/axios/request";
import AddModal from "./components/add-modal"
import ImportCommonButton from "../../../common/react/import/importCommonButton";
const { useRef, useState } = React;
const { Space, Button, Modal, message } = antd;
const { SearchList, Spa, SpaConfigProvider } = dtComponents

export const ServiceProviderStore = () => {
	const searchListRef = useRef();
	const [selects, setSelects] = useState([])
	const [addModalOpen, setAddModalOpen] = useState(false);
	const [row, setRow] = useState({});

	const getConfig = async function () {
		const data = await axios.get("https://maria.yang800.com/api/data/v2/954/786")
		return data.data.data;
	};

	const handleBatch = ({ title, content, requestFunction, type, id }) => {
		let ids = [];
		if (type === "batch") {
			if (selects.length === 0) {
				return message.error("请勾选数据");
			}

			ids = selects.map(item => item.id);
		} else {
			ids = id
		}

		Modal.confirm({
			title: title,
			content: content,
			onOk: () => {
				return requestFunction(ids);
			}
		});
	};

	const batchDelete = ({ id, type }) => {
		handleBatch({
			title: "删除",
			content: "确定删除敏感词吗？",
			type: type,
			id: id, // 单条删除
			requestFunction: (ids) => {
				request({
					url: `/mall-admin/api/sensitive/word/deleteBatch`,
					data: { idList: ids },
					success: (res) => {
						searchListRef.current.load();
						message.success("删除成功");
					},
				});
			}
		});
	};

	return (
		<SearchList
			ref={searchListRef}
			scrollMode={"tableScroll"}
			paginationConfig={{ size: "default", showPosition: 'bottom' }}
			searchConditionConfig={{
				size: "middle",
			}}
			getConfig={getConfig}
			onTableSelected={(ids, rows) => {
				setSelects([...rows]);
			}}
			renderRightOperation={() => {
				return ( 
					<ImportCommonButton
						importNotice={"注意事项：\n" +
							"1.批量导入导购，请下载Excel模板 。\n" +
							"2.门店、导购信息要核对清楚，导购手机号码不能重复\n" +
							"3.导入进来的导购信息，直接就划分门店了。"}
						templateUrl={'https://dante-img.oss-cn-hangzhou.aliyuncs.com/敏感词导入.xlsx'}
						importAjaxUrl={'/mall-admin/api/sensitive/word/import'}
					/>)
			}}
			renderLeftOperation={() => {
				return (
					<Space>
						<Button onClick={() => { batchDelete({ type: 'batch' }) }}>批量删除</Button>
						<Button onClick={() => { setAddModalOpen(true) }}>新增</Button>
					</Space>
				)
			}}
			tableCustomFun={{
				getOperation: (row) => {
					return (
						<Space>
							<a onClick={() => {
								setAddModalOpen(true);
								setRow(row);
							}}>编辑</a>
							<a onClick={() => { batchDelete({ id: [row.id], type: 'single' }) }}>删除</a>
						</Space>
					)
				}
			}}
			renderModal={() => {
				return <AddModal
					row={row}
					open={addModalOpen}
					onClose={() => {
						setRow({})
						setAddModalOpen(false)
						searchListRef.current.load();
					}}
				/>
			}}
		/>
	)
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
	_openPage: () => {
	}, request: {
		request: (params) => {
			const obj = Object.assign(params, {
				method: 'POST',
			})
			request(obj);
		}
	}
})}><ServiceProviderStore />
</SpaConfigProvider>, document.getElementById("sensitive-word")
);

/** 
 * @description: 回复模版配置
 * @author: wangchunting
 */
import request from "../../../../utils/plugins/axios/request";
const { useEffect, useState } = React;
const { FormSelect, FormInput } = dtComponents
const { Modal, Table, Form, Button } = antd;

function ReplyTemplateModal({ open, onSelect, onClose, selectedRowId }) {
    const [form] = Form.useForm()
    const [dataList, setDataList] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    useEffect(() => {
        getDataList()
        if (selectedRowId) {
            setSelectedRowKeys([selectedRowId]);
        }
    }, [selectedRowId])

    // 获取列表
    function getDataList(values = {}) {
        request({
            url: `/mall-admin/api/item/evaluation/template/list`,
            data: values,
            success: (res) => {
                console.log(72, res)
                setDataList(res)
            },
        })
    }
    const onSelectChange = (selectedRowKeys) => {
        setSelectedRowKeys(selectedRowKeys);
    };

    const handleOk = () => {
        const selectedRow = dataList.find(item => item.id === selectedRowKeys[0]);

        onSelect(selectedRow);
        onClose();
    };

    const rowSelection = {
        type: 'radio',
        selectedRowKeys,
        onChange: onSelectChange,
    };

    // 搜索
    const handleSearch = () => {
        form.validateFields().then(values => {
            getDataList(values)
        });
    }

    // 重置
    const handleReset = () => {
        form.resetFields();
        getDataList()
    }

    const layout = {
        labelCol: {
            style: { width: '138px', paddingBottom: '24px' },
        },
    }

    const columns = [
        {
            title: '回复类型',
            dataIndex: 'templateTypeDesc',
            width: 150,
        },
        {
            title: '回复内容',
            dataIndex: 'templateContent',
        },
    ];

    return (
        <Modal
            title="回复模板配置"
            open={open}
            onCancel={onClose}
            onOk={handleOk}
            width={900}
        >
            <Form {...layout} layout="inline" form={form} >
                <FormInput
                    fProps={{ label: "回复内容", name: 'templateContentLike' }}
                />
                <FormSelect
                    fProps={{ label: '回复类型', name: 'templateType' }}
                    url="/mall-admin/api/item/evaluation/template/templateType"
                />
                <Button type="primary" onClick={handleSearch} style={{ marginRight: 16 }}>
                    查询
                </Button>
                <Button onClick={handleReset}>重置</Button>
            </Form>
            <Table
                rowKey="id"
                columns={columns}
                dataSource={dataList}
                pagination={false}
                className="reply-table"
                rowSelection={rowSelection}
            />
        </Modal>
    );
}

export default ReplyTemplateModal;
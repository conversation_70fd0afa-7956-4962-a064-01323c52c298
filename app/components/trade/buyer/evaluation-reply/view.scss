.evaluation-reply-container {
  display: flex;
  padding: 16px;
  font-family: 'Arial', sans-serif;

  .left {
    flex: 2;
    padding-right: 24px;
    border-right: 1px solid #eee;
  }

  .right {
    flex: 1;
    padding-left: 24px;
  }

  .userInfo {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
  }

  .userDetails {
    margin-left: 12px;
  }

  .username {
    font-size: 20px;
    font-weight: bold;
  }

  .phone {
    font-size: 14px;
    color: #555;
    margin-top: 8px;
  }

  .stars {
    margin-top: 8px;
  }

  .date {
    color: #888;
    font-size: 12px;
    margin-left: 24px;
  }

  .highlight {
    color: #f5001d;
    font-weight: bold;
    margin-bottom: 12px;
  }

  .warning {
    margin-bottom: 4px;
  }

  .pass {
    color: green;
    // font-weight: bold;
  }

  .section {
    margin-bottom: 20px;

    h3 {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .red {
    background: #ff4d4f;
    color: white;
    padding: 0 4px;
    border-radius: 4px;
    margin: 0 2px;
  }

  .imageList {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .buttonGroup {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }

  .replyBox {
    margin-top: 16px;
  }

  .replyLabel {
    font-weight: bold;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.reply-toolbar {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
}

.reply-footer {
  margin-top: 16px;
  text-align: right;
}
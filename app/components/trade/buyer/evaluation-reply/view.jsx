/**
 * @description: 评价管理
 * <AUTHOR>
 */
import request from "../../../utils/plugins/axios/request";
import { getParam } from "../../../common/react/utils/common"
import { Audit_Status_Enum, ReplyStatus_Enum } from "./enum";
import { Star_Enum } from "../evaluation-manage/enum";
import Template from "./components/template"
const { useState, useEffect } = React;
const { ConfigProvider, locales, Button, Modal, Image, Input, Rate, message } = antd;
const { Spa, SpaConfigProvider } = dtComponents
const TextArea = Input.TextArea
const { zh_CN } = locales

export const EvaluationReply = () => {
	const [detail, setDetail] = useState({}) // 评价详情
	const [replyRequest, setReplyRequest] = useState({}) // 回复内容 
	const [editReplyDisabled, setEditReplyDisabled] = useState(false) // 回复是否可编辑
	const [templateOpen, setTemplateOpen] = useState(false) // 回复模板配置弹窗
	const [selectedRowId, setSelectedRowId] = useState(null);
	const [auditStatus, setAuditStatus] = useState() // 评价状态
	const [id, setId] = useState(getParam('id'))// 评价id

	useEffect(() => {
		getDetail()
	}, [])

	// 获取详情
	function getDetail(detailId) {
		request({
			url: '/mall-admin/api/item/evaluation-p/detail',
			data: { id: detailId ? detailId : id },
			needMask: true,
			success: (res) => {
				// 星级通过id取出name，用于展示
				res.score = Star_Enum.find(item => item.id == res.score).name
				setDetail(res)
				setAuditStatus(res.auditStatus)
				setReplyRequest(res.replyResponse || {})
				setEditReplyDisabled(res.replyStatus === ReplyStatus_Enum.REPLY_RECEIVED)
			},
		})
	}

	function getDataList() {
		request({
			url: '/mall-admin/api/item/evaluation-p/list',
			data: {
				currentPage: 1,
				pageSize: 1,
				totalPage: 1,
				totalCount: 0,
				auditStatus: Audit_Status_Enum.EVALUATION_WAIT
			},
			success: (res) => {
				if (res.dataList.length === 0) {
					// 不存在，则页面关闭回到分页列表
					window.location.href = '/seller/evaluation-manage'
				} else {
					// 存在，则页面刷新成下一条
					// setDetail(res.dataList[0])
					setId(res.dataList[0].id)
					getDetail(res.dataList[0].id)
				}
			}
		})
	}

	// 二次确认操作统一方法
	function handleAction(options) {
		const { title, content, url, data, successMessage, callback } = options;

		Modal.confirm({
			title: title,
			content: content,
			onOk: () => {
				request({
					url: url,
					data: data,
					success: () => {
						message.success(successMessage);
						if (callback && typeof callback === 'function') {
							callback();
						}
					},
					fail: (error) => {
						message.error('An error occurred');
						if (errorCallback && typeof errorCallback === 'function') {
							errorCallback(error);
						}
					}
				});
			},
		});
	}

	// 通过，下一条
	function handlePass() {
		handleAction({
			title: '通过',
			content: '确定通过评价吗？',
			url: '/mall-admin/api/item/evaluation-p/audit',
			data: { id: id, auditStatus: "EVALUATION_PASS" },
			successMessage: '执行成功',
			callback: () => {
				// 调用列表，取下一条数据
				getDataList()
				// 清空回复
				setReplyRequest({})
			},
		});
	}

	// 驳回，下一条
	const handleReject = () => {
		let rejectReason = '';
		Modal.confirm({
			title: '驳回',
			content: <div>
				<p>确定驳回评价吗？</p>
				<TextArea
					rows={1}
					maxLength={64}
					placeholder="请输入驳回原因"
					onChange={(e) => { rejectReason = e.target.value }}
				/>
			</div>,
			onOk: () => {
				request({
					url: '/mall-admin/api/item/evaluation-p/audit',
					data: { id: id, auditStatus: "EVALUATION_REJECT", rejectReasons: rejectReason },
					success: () => {
						message.success("执行成功");
						// 调用列表，取下一条数据
						getDataList()
						setReplyRequest({})
					},
				})
			}
		})
	}

	// 删除评价
	const handleDeleteEvaluation = () => {
		handleAction({
			title: '删除',
			content: '确定删除评价吗？',
			url: '/mall-admin/api/item/evaluation-p/delete',
			data: { id: id },
			successMessage: '删除成功',
			callback: () => {
				// 处理成功提示“删除成功”，页面关闭回到分页列表
				window.location.href = '/seller/evaluation-manage'
			},
		});
	}

	// 发布回复
	const handlePublishReply = () => {
		if (!replyRequest.replyContent) {
			message.error('请输入回复内容')
			return
		}
		handleAction({
			title: '回复',
			content: '确定发布回复吗？',
			url: '/mall-admin/api/item/evaluation-p/reply',
			data: {
				id: id,
				replyRequest: replyRequest
			},
			successMessage: '发布成功',
			callback: () => {
				setEditReplyDisabled(true)
				getDetail()
			}
		})
	}

	// 删除回复
	const handleDeleteReply = () => {
		handleAction({
			title: '删除',
			content: '确定删除回复吗？',
			url: '/mall-admin/api/item/evaluation-p/deleteReply',
			data: { replyId: replyRequest.replyId },
			successMessage: '删除成功',
			callback: () => {
				setEditReplyDisabled(true)
				getDetail()
			}
		})
	}

	return (
		<div className="evaluation-reply-container">
			{/* 左侧内容 */}
			<div className="left">
				<div className="userInfo">
					<img
						src={detail.headImg && detail.headImg}
						alt="avatar"
						className="avatar"
					/>
					<div className="userDetails">
						<div className="username">{detail.userName}</div>
						<div className="phone">电话：{detail.phone}</div>
						<div>
							<Rate value={detail.score / 20} disabled className="stars" />
							<span className="date">{detail.evaluationTimeDesc}</span>
						</div>
					</div>
				</div>

				<div className="highlight">
					{
						auditStatus === Audit_Status_Enum.EVALUATION_REJECT &&
						<div className="warning">驳回：{detail.rejectReasons}</div>
					}
					{
						auditStatus === Audit_Status_Enum.EVALUATION_PASS &&
						<div className="pass">通过</div>
					}
				</div>

				<div className="section">
					<h3>订单信息：</h3>
					<div>订单号：{detail.orderId}</div>
					<div>商品名称：{detail.skuName}</div>
				</div>

				<div className="section">
					<h3>评价内容：</h3>
					<div dangerouslySetInnerHTML={{ __html: detail.evaluationContent }}></div>
				</div>

				<div className="section">
					<h3>图片：</h3>
					<div className="imageList">
						{detail.evaluationUrlList && detail.evaluationUrlList.map((src, idx) => (
							<Image key={idx} src={src} width={120} height={120} />
						))}
					</div>
				</div>
			</div>

			{/* 右侧回复 */}
			<div className="right">
				<div className="buttonGroup">

					{
						[Audit_Status_Enum.EVALUATION_WAIT].includes(auditStatus) &&
						<Button type="primary" onClick={() => { handlePass() }}>通过，下一条</Button>
					}
					{
						[Audit_Status_Enum.EVALUATION_WAIT].includes(auditStatus) &&
						<Button onClick={() => { handleReject() }}>驳回，下一条</Button>
					}
					<Button onClick={() => { handleDeleteEvaluation() }}>删除评价</Button>
					{
						!editReplyDisabled &&
						<Button onClick={() => { handlePublishReply() }}>发布回复</Button>
					}
					{
						editReplyDisabled &&
						<Button onClick={() => { setEditReplyDisabled(false) }}>编辑回复</Button>
					}
					{ //通过、驳回下已回复展示
						detail.replyStatus === ReplyStatus_Enum.REPLY_RECEIVED && [Audit_Status_Enum.EVALUATION_PASS, Audit_Status_Enum.EVALUATION_REJECT].includes(auditStatus) &&
						<Button onClick={() => { handleDeleteReply() }}>删除回复</Button>
					}
				</div>
				<div className="replyBox">
					<div className="replyLabel">
						<span>回复：</span>
						{
							!editReplyDisabled &&
							<Button size="small" onClick={() => { setTemplateOpen(true) }}>
								回复模板
							</Button>
						}
					</div>
					<TextArea
						disabled={editReplyDisabled}
						value={replyRequest.replyContent}
						rows={8}
						maxLength={255}
						placeholder="请输入回复内容"
						onChange={(e) => { setReplyRequest({ replyContent: e.target.value, replyId: replyRequest.replyId }) }}
					/>
				</div>
			</div>
			{
				templateOpen &&
				<Template
					id={id}
					open={templateOpen}
					onClose={() => { setTemplateOpen(false) }}
					onSelect={(keys) => {
						setReplyRequest({ replyContent: keys.templateContent, replyId: "" });
						setSelectedRowId(keys.id);
					}}
					selectedRowId={selectedRowId}
				/>
			}
		</div>
	);
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
	_openPage: () => {
	}, request: {
		request: (params) => {
			const obj = Object.assign(params, {
				method: 'POST',
			})
			request(obj);
		}
	}
})}> <ConfigProvider locale={zh_CN}><EvaluationReply /></ConfigProvider>
</SpaConfigProvider>, document.getElementById("evaluation-reply")
);

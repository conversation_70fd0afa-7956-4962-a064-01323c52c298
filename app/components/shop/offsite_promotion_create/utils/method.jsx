/**
 * 转换编辑值
 * @param {Object} detail - 包含详细信息的对象
 * @returns {Object} 返回一个包含转换后的编辑值的对象
 */
export const transformEditValues = (detail) => {
  if (detail.bannerUrl) {
    detail.bannerUrl = [{url: detail.bannerUrl}]
  }
  if (detail.exampleUrl) {
    detail.exampleUrl = [{url: detail.exampleUrl}]
  }
  if (detail.activityStartTime) {
    detail.activityStartTime = moment(detail.activityStartTime)
  }
  if (detail.activityEndTime) {
    detail.activityEndTime = moment(detail.activityEndTime)
  }
  if (detail.auditStartTime) {
    detail.auditStartTime = moment(detail.auditStartTime)
  }
  if (detail.auditEndTime) {
    detail.auditEndTime = moment(detail.auditEndTime)
  }
  if (detail.rewardsTime) {
    detail.rewardsTime = moment(detail.rewardsTime)
  }
  return detail
}

export const transformSubmitValues = (values, status) => {
  values.status = status
  if (values.activityStartTime) {
    values.activityStartTime = values.activityStartTime.valueOf()
  }
  if (values.activityEndTime) {
    values.activityEndTime = values.activityEndTime.valueOf()
  }
  if (values.auditStartTime) {
    values.auditStartTime = values.auditStartTime.valueOf()
  }
  if (values.auditEndTime) {
    values.auditEndTime = values.auditEndTime.valueOf()
  }
  if (values.rewardsTime) {
    values.rewardsTime = values.rewardsTime.valueOf()
  }
  if (values.bannerUrl && values.bannerUrl.length) {
    values.bannerUrl = values.bannerUrl[0].url
  }
  if (values.exampleUrl && values.exampleUrl.length) {
    values.exampleUrl = values.exampleUrl[0].url
  }
  return values
}


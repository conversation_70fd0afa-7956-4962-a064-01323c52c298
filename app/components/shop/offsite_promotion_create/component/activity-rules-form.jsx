import ItemRichEditor from "./item-rich-editor";
const {Fragment} = React;
const {Form, Input} = antd;
const {TextArea} = Input;


const ActivityRulesForm = () => {

  return (
    <Fragment>
      <Form.Item
        name="activityRule"
        label="规则介绍"
        rules={[{required: true, message: '请输入规则介绍'},
          { max: 4096, message: "内容不能超过 4096 个字" }]}
      >
        <ItemRichEditor/>
      </Form.Item>

      <Form.Item
        name="rewardsRule"
        label="奖励机制"
        rules={[{required: true, message: '请输入奖励机制'},
          { max: 4096, message: "内容不能超过 4096 个字" }]}
      >
        <TextArea rows={4} showCount={true}/>
      </Form.Item>

      <Form.Item
        name="checkRule"
        label="核销规则"
        rules={[{required: true, message: '请输入核销规则'},  { max: 4096, message: "内容不能超过 4096 个字" }]}
      >
        <TextArea rows={4}/>
      </Form.Item>
    </Fragment>
  );
};

export default ActivityRulesForm;

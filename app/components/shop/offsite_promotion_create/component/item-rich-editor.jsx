
const {Input} = antd
const {useEffect, useRef,useState} = React
const { TextArea } = Input;

function ItemRichEditor({ value = '', onChange }) {
  const editorRef = useRef(null);
  const [tinymceInit,setTinymceInit] = useState(false);
  const editorInputIng = useRef(false);
  const textAreaId = `editor-${Math.random().toString(36).substr(2, 9)}`;
  useEffect(()=>{

    if(editorRef.current && tinymceInit){
      if(!editorInputIng.current){
        editorRef.current && editorRef.current.setContent(value || '');
      }
    }
  },[value,tinymceInit])
  useEffect(() => {
    // Initialize the editor
    tinymce.init({
      selector: `#${textAreaId}`,
      language: 'zh_CN',
      menubar: false,
      toolbar: `formatselect | link image |  bold italic strikethrough forecolor backcolor
       alignleft aligncenter alignright alignjustify
        numlist bullist outdent indent`,
      promotion: false,
      image_dimensions:false,
      branding: false,
      statusbar:false,
      setup: (editor) => {
        editorRef.current = editor;

        // Sync content back to parent
        editor.on('change keyup', () => {
          const content = editor.getContent();
          editorInputIng.current= true
          if (onChange) onChange(content);
        });
      },
      init_instance_callback: (editor) => {
        setTinymceInit(true)
        editor.setContent(value || '');
      },
    });

    return () => {
      // Clean up the editor
      if (editorRef.current) {
        tinymce.get(editorRef.current.id).remove();
        editorRef.current = null;
      }
    };
  }, []);

  return (
    <div>
      <TextArea id={textAreaId}/>
    </div>
  );
}

export default ItemRichEditor;

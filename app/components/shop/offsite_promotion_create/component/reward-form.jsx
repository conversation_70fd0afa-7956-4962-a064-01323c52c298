
const {useState,useEffect} = React;
const {Form, Select, InputNumber, Row, Col,DatePicker} = antd;
import request from "../../../utils/plugins/axios/request";

/**
 * 奖励规则
 */
const RewardForm = ({form}) => {
  const rewardsType = Form.useWatch("rewardsType", form);
  const [rewardsTypeList,setRewardsTypeList] = useState([])
  useEffect(()=>{
   request({
     url: '/mall-admin/api/promotion/activity/rewardsType',
     method: 'POST',
     success: (res) => {
       setRewardsTypeList(res)
     }
   })
  },[])
  return <div>
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="奖励类型" name={"rewardsType"}  rules={[{required: true, message: '请选择奖励类型'}]} >
          <Select className={"attr-item-select"}
                  style={{width: '75%'}}
                  placeholder="请选择奖励类型" options={rewardsTypeList} fieldNames={{label: "name", value: "id"}}/>
        </Form.Item>
      </Col>
      {
        rewardsType==="GIFT_AND_CASH_TYPE"?
          <Col span={12}>
            <Form.Item label={"福卡金额"} name={"fortuneCardRewardsNum"}
                       rules={[ { required: true, message: "请输入数额" },
                         { pattern: /^[1-9]\d{0,5}$/, message: "仅支持正整数，不超过6位" }]}>
              <InputNumber
                min={0}
                placeholder="请输入数额"
                style={{width: 150}}
              />
            </Form.Item>
          </Col>:null
      }
      <Col span={12}>
        <Form.Item label={(rewardsType==="GIFT_TYPE"||rewardsType==="GIFT_AND_CASH_TYPE")?"福豆数量":"福卡金额"} name={"rewardsNum"}
                   rules={[ { required: true, message: "请输入数额" },
                     { pattern: /^[1-9]\d{0,5}$/, message: "仅支持正整数，不超过6位" }]}>
          <InputNumber
            min={0}
            placeholder="请输入数额"
            style={{width: 150}}
            addonAfter={(rewardsType==="GIFT_TYPE"||rewardsType==="GIFT_AND_CASH_TYPE")?"":"元"}
          />
        </Form.Item>
      </Col>


        <Col span={12}>
          <Form.Item label="奖励发放时间" name="rewardsTime" rules={[{required: true, message: '请选择奖励发放时间'}]}>
            <DatePicker
                         showTime={{ format: 'HH:mm' }} // 显示时分
                         format="YYYY-MM-DD HH:mm"
                         style={{width: '75%'}}/>
          </Form.Item>
        </Col>
      { rewardsType==="GIFT_TYPE"||rewardsType==="GIFT_AND_CASH_TYPE"?
        <Col span={12}>
          <Form.Item label="福豆有效期（天）" name="validityDay" rules={[
            {required: true, message: '请输入福豆有效期'},
            {pattern: /^[1-9]\d*$/, message: "只能输入正整数"}]}>
            <InputNumber
              step={1}
              max={9999999}
              placeholder={"请输入福豆发放后有效天数"}
              style={{width: 150}}
            />
          </Form.Item>
        </Col>:null
      }
    </Row>
  </div>
}
export default RewardForm

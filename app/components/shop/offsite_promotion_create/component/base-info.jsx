import customRequest from "../../../common/react/upload/custom-request";

const {Fragment, useState,useEffect} = React;
const {Form, Space, Upload, Input, message, Image, DatePicker, Row, Col, Button, InputNumber,Switch} = antd;
const {DTInput} = dtComponents
const {UploadOutlined} = icons

function UploadImage({value, onChange}) {
  const [imageUrl, setImageUrl] = useState([])
  useEffect(()=>{
    if(Array.isArray(value) && value.length>0){
      setImageUrl(value)
    }
  },[value])

  const innerOnChange = (index, {file, fileList}) => {
    if (file.status === "uploading") {
      setImageUrl([file])
    } else if (file.status === "done") {
      setImageUrl(fileList)
      onChange(fileList)
    } else if (file.status === "removed") {
      setImageUrl(fileList)
      onChange(fileList)
    } else if (file.status === "error") {
      message.error("上传失败")
    }
  }
  return <div style={{
    width: "485px",
    height: "335px",
    border: "2px dashed #999",
    borderRadius: "10px",
    padding: "16px",
    textAlign: "center",
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-end",
    alignItems: "center"
  }}>
    {imageUrl && imageUrl.length > 0 && (
      <Image
        width={375}
        height={235.5}
        src={imageUrl[0].url}
      />
    )}
    <Upload
      customRequest={customRequest}
      showUploadList={false}
      maxCount={1}
      size={5}
      accept={[".jpg", ".jpeg", ".png"]}
      onChange={(file) => {
        innerOnChange(0, file)
      }}
    >
      <Button style={{marginTop: 16}} type={"primary"} icon={<UploadOutlined/>}>上传图片</Button>
    </Upload>
    <span>建议尺寸：750x422像素，支持jpg、jpeg、png格式</span>
  </div>
}

/**
 * 商品基本信息
 */
const BaseInfo = (props) => {

  return <div>
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="活动名称" name="activityName" rules={[
          {required: true, message: '请输入活动名称'},
          { max: 255, message: '内容长度不能超过 255 个字！' }
        ]}>
          <DTInput trimMode={"all"} placeholder="请输入活动名称"/>
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="活动专题" name="activityTopic" rules={[{required: true, message: '请输入活动专题'},
          { max: 128, message: '内容长度不能超过 128 个字！' },]}>
          <DTInput trimMode={"all"} style={{width: '75%'}}/>
        </Form.Item>
      </Col>
    </Row>
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="活动描述" name="activityDes" rules={[
          {required: true, message: '请输入活动描述'} ,
          { max: 255, message: '内容长度不能超过 255 个字！' },]} >
          <DTInput trimMode={"all"} className={"attr-item-input"} placeholder="请输入活动描述"/>
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="报名人数限制" style={{marginBottom: 0}}>
          <Space align="baseline">
            <Form.Item name="signUpNumLimitFlag"   valuePropName="checked"	getValueFromEvent={(e) => (e ? 1 : 0)}  noStyle>
              <Switch checkedChildren="启用"
                      unCheckedChildren="禁用" />
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => prevValues.signUpNumLimitFlag !== currentValues.signUpNumLimitFlag}
            >
              {({getFieldValue}) =>
                getFieldValue('signUpNumLimitFlag') ? (
                  <Form.Item
                    name="signUpNumMax"
                    rules={[
                      {required: true, message: '请输入限制人数'},
                      {pattern: /^[1-9]\d*$/, message: '请输入正整数'},
                      {validator: (_, value) =>
                        value && value.toString().length > 6
                          ? Promise.reject('不允许超过6位')
                          : Promise.resolve()
                      }
                    ]}
                  >
                    <InputNumber min={1} style={{width: '100%'}} placeholder="限制人数" />
                  </Form.Item>
                ) : null
              }
            </Form.Item>
          </Space>
        </Form.Item>
      </Col>
    </Row>
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="活动开始时间" name="activityStartTime"
                   rules={[{required: true, message: '请选择活动开始时间'}]}>
          <DatePicker  style={{width: '75%'}}
                       showTime={{ format: 'HH:mm',defaultValue: moment("00:00:00", "HH:mm:ss")  }} // 显示时分
                       format="YYYY-MM-DD HH:mm"/>
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="活动结束时间" name="activityEndTime"
                   rules={[{required: true, message: '请选择活动结束时间'}]}>
          <DatePicker  style={{width: '75%'}}
                       showTime={{ format: 'HH:mm',defaultValue: moment("23:59:59", "HH:mm:ss") }} // 显示时分
                       format="YYYY-MM-DD HH:mm" />
        </Form.Item>
      </Col>
    </Row>

    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="审核开始时间" name="auditStartTime" rules={[{required: true, message: '请选择审核开始时间'}]}>
          <DatePicker  style={{width: '75%'}}
                       showTime={{ format: 'HH:mm',defaultValue: moment("00:00:00", "HH:mm:ss") }}
                       format="YYYY-MM-DD HH:mm"/>
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="审核结束时间" name="auditEndTime" rules={[{required: true, message: '请选择审核结束时间'}]}>
          <DatePicker  style={{width: '75%'}}
                       showTime={{ format: 'HH:mm' ,defaultValue: moment("23:59:59", "HH:mm:ss")}} // 显示时分
                       format="YYYY-MM-DD HH:mm"/>
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="活动banner"
                   name="bannerUrl"
                   rules={[{required: true, message: '请上传活动banner'}]}>
          <UploadImage/>
        </Form.Item>
      </Col>
    </Row>
  </div>
}


export default BaseInfo


const {useState, useEffect} = React;
const {Form, Radio,Image} = antd;
import request from "../../../utils/plugins/axios/request";

/**
 * 推广信息
 */
const AdvertisingForm = () => {
  const [promotionChannelList,setPromotionChannelList]=useState([])
  useEffect(()=>{
    request({
      url: '/mall-admin/api/promotion/activity/channel',
      method: 'POST',
      success: (res) => {
        setPromotionChannelList(res)
      }
    })
  },[])
  return (
    <Form.Item label="推广渠道" name={"promotionChannel"} style={{marginBottom: 0}}
               rules={[{required: true, message: '请选择推广渠道'}]}>
      <Radio.Group>
        {
          promotionChannelList.map((item, index) => {
            return (
              <Radio key={item.id} value={item.id}>
                {item.name}<img style={{marginLeft: 8}} height={28} width={28} src={item.url}/>
              </Radio>
            );
          })
        }
      </Radio.Group>
    </Form.Item>

  );
};

export default AdvertisingForm;

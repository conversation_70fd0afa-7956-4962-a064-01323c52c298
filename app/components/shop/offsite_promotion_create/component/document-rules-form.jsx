import customRequest from "../../../common/react/upload/custom-request";

const {Fragment} = React;
const {Form, Input} = antd;
const {TextArea} = Input;
const {DTUploaderFile} = dtComponents


const DocumentRulesForm = () => {
  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e.fileList;
  };
  return (
    <Fragment>
      <Form.Item
        name="attention"
        label="注意事项"
        rules={[{required: true, message: '请输入注意事项'},
          {max: 255, message: "内容不能超过 255 个字"}]}
      >
        <TextArea rows={4} showCount/>
      </Form.Item>

      <Form.Item
        name="exampleUrl"
        label="截图示例"
        extra="最大10MB，支持jpg、jpeg、png格式"
        valuePropName="fileList"
        getValueFromEvent={normFile}
        rules={[{required: true, message: '请上传截图示例'}]}
      >
        <DTUploaderFile
          maxCount={1}
          accept={[".jpg", ".jpeg", ".png"]}
          listType={"picture-card"}
          customRequest={customRequest}
        />
      </Form.Item>
    </Fragment>
  );
};

export default DocumentRulesForm;

import {lib} from "../../common/react/utils/lib";

const { useEffect,useRef,useState} = React;
const {Form, Button, ConfigProvider, locales, Space,message,Layout} = antd;
const {Spa, SpaConfigProvider} = dtComponents
const {zh_CN} = locales
import request from "../../utils/plugins/axios/request";
import ItemBlock from "../../items/seller/item-edit/component/base/item-block";
import BaseInfo from "./component/base-info";
import RewardForm from "./component/reward-form";
import AdvertisingForm from "./component/advertising-form";
import ActivityRulesForm from "./component/activity-rules-form";
import DocumentRulesForm from "./component/document-rules-form";
import {transformEditValues, transformSubmitValues} from "./utils/method";
import getScroll from "../../common/react/utils/getScroll";
import scrollTo from "../../common/react/utils/scrollTo";
const {Sider} = Layout;
const sharpMatcherRegx = /#(\S+)$/;

export const OffsitePromotionCreate = () => {
  let itemId = lib.getParam("id");
  const [form] = Form.useForm();
  const rightID = `right_edit`;
  useEffect(() => {
    document.getElementById(rightID).addEventListener("scroll", handleScroll);
    if(itemId){
      getDetail()
    }
    return () => {
      document.getElementById(rightID).removeEventListener("scroll", handleScroll);
    };
  }, [form])
  const getDetail = () => {
    request({
      url: '/mall-admin/api/promotion/activity/detail',
      method: "POST",
      needMask: true,
      data: {
        id:itemId
      },
      success: (data) => {
        form.setFieldsValue(transformEditValues(data))
      }
    })
  }

  function save(status) {
    form.validateFields().then((values) => {
      const data = transformSubmitValues(values, status)
      if(itemId){
        data.id = itemId
      }
      request({
        url: '/mall-admin/api/promotion/activity/save',
        method: "POST",
        data,
        needMask: true,
        success: (data) => {
          message.success("提交成功")
          setTimeout(() => {
            window.location.href = "/seller/offsite-promotion-activity"
          }, 1000)
        }
      })
    })
  }
  const animating = useRef(false);
  const normalNavArr = [
    {name: "基础信息", id: `${itemId}-edit-basic`},
    {name: "奖励规则", id: `${itemId}-edit-reward`},
    {name: "推广信息", id: `${itemId}-edit-advertising`},
    {name: "活动规则", id: `${itemId}-edit-activity-rules`},
    {name: "资料规则", id: `${itemId}-edit-document-rules`},
  ];
  const [currentId, setCurrentId] = useState(`${itemId}-edit-basic`);
  const handleScroll = () => {
    if (animating.current) {
      return;
    }
    const scrollTop = document.getElementById(rightID).scrollTop;
    let section = Array.from(document.getElementById(rightID).children);
    let activeChannel;
    section.map(item => {
      let itemTop = item.offsetTop;
      if (scrollTop > itemTop - 140) {
        activeChannel = item.id;
      }
    });
    setCurrentId(activeChannel);
  };

  const handleScrollClick = item => {
    let targetOffset = 0;
    setCurrentId(item);
    const container = document.getElementById(rightID);
    const scrollTop = getScroll(container, true);
    const sharpLinkMatch = sharpMatcherRegx.exec(`#${item}`);
    if (!sharpLinkMatch) {
      return;
    }
    const targetElement = document.getElementById(sharpLinkMatch[1]);
    if (!targetElement) {
      return;
    }
    const eleOffsetTop = getOffsetTop(targetElement, container);
    let y = scrollTop + eleOffsetTop;
    y -= targetOffset !== undefined ? targetOffset : 0;
    animating.current = true;
    scrollTo(y, {
      callback: () => {
        animating.current = false;
      },
      getContainer: getContainer,
    });
  };
  const getOffsetTop = (element, container) => {
    if (!element.getClientRects().length) {
      return 0;
    }
    const rect = element.getBoundingClientRect();
    if (rect.width || rect.height) {
      if (container === window) {
        container = element.ownerDocument.documentElement;
        return rect.top - container.clientTop;
      }
      return rect.top - container.getBoundingClientRect().top;
    }
    return rect.top;
  };
  const getContainer = () => {
    return document.getElementById(rightID);
  };
  return (
    <ConfigProvider locale={zh_CN}>
      <Layout className="Layout_box">
        <Sider className="Sider">
          <div className="publish_good_left_con">
            <div className="publish_good_left_con_data">
              {normalNavArr &&
                normalNavArr.map(item => {
                  return (
                    <a>
                      <div
                        className={`publish_good_left_test ${
                          currentId === item.id ? "actived" : ""
                        }`}
                        onClick={() => handleScrollClick(item.id)}>
                        {item.name}
                      </div>
                    </a>
                  );
                })}
            </div>
          </div>
        </Sider>
        <Layout className="Layout_content">
        <Form
          form={form}
          style={{height: "100%"}}
          layout="vertical"
        >
          <div id={rightID}
               style={{overflow: "scroll", height: "100%", paddingBottom: 55, marginLeft: 16}}>
          <ItemBlock title={"基础信息"}
                     style={{marginTop: 0}}
                     action={<Space>
            <Button onClick={() => {
              window.history.back()
            }}>取消</Button>
            <Button onClick={() => save("ACTIVITY_CREATE")}>保存草稿</Button>
            <Button type="primary" onClick={() => save("ACTIVITY_WAIT")}>保存&发布</Button>
          </Space>} id={`${itemId}-edit-basic`}>
            <BaseInfo/>
          </ItemBlock>
          <ItemBlock title={"奖励规则"} id={`${itemId}-edit-reward`}>
            <RewardForm form={form}/>
          </ItemBlock>
          <ItemBlock title={"推广信息"} id={`${itemId}-edit-advertising`}>
            <AdvertisingForm/>
          </ItemBlock>
          <ItemBlock title={"活动规则"} id={`${itemId}-edit-activity-rules`}>
            <ActivityRulesForm/>
          </ItemBlock>
          <ItemBlock title={"资料规则"} id={`${itemId}-edit-document-rules`}>
            <DocumentRulesForm/>
          </ItemBlock>
          </div>
        </Form>
        </Layout>
      </Layout>
    </ConfigProvider>
  )
}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          method: 'post',
        })
        request(obj);
      }
    }
  })}><OffsitePromotionCreate/>
  </SpaConfigProvider>, document.getElementById("offsite-promotion-create")
);

import request from "../../utils/plugins/axios/request";
import ItemBlock from "../../items/seller/item-edit/component/base/item-block";
import {lib} from "../../common/react/utils/lib";
import ItemRichEditor from "../offsite_promotion_create/component/item-rich-editor";
import getScroll from "../../common/react/utils/getScroll";
import scrollTo from "../../common/react/utils/scrollTo";

const {useState, useEffect, useRef} = React;
const {Descriptions, Image, Layout} = antd;
const {Spa, SpaConfigProvider} = dtComponents
const {Sider} = Layout;
const sharpMatcherRegx = /#(\S+)$/;

export const OffsitePromotionDetail = () => {
  const [formData, setFormData] = useState({});
  let itemId = lib.getParam("id");
  const rightID = `right_edit`;
  useEffect(() => {
    document.getElementById(rightID).addEventListener("scroll", handleScroll);
    if (itemId) {
      getDetail()
    }
    return () => {
      document.getElementById(rightID).removeEventListener("scroll", handleScroll);
    };
  }, [])
  const getDetail = () => {
    request({
      url: '/mall-admin/api/promotion/activity/detail',
      method: "POST",
      needMask: true,
      data: {
        id: itemId
      },
      success: (data) => {
        setFormData(data)
      }
    })
  }
  const animating = useRef(false);
  const normalNavArr = [
    {name: "活动信息", id: `${itemId}-edit-basic`},
    {name: "奖励规则", id: `${itemId}-edit-reward`},
    {name: "推广信息", id: `${itemId}-edit-advertising`},
    {name: "活动规则", id: `${itemId}-edit-activity-rules`},
    {name: "资料规则", id: `${itemId}-edit-document-rules`},
  ];
  const [currentId, setCurrentId] = useState(`${itemId}-edit-basic`);
  const handleScroll = () => {
    if (animating.current) {
      return;
    }
    const scrollTop = document.getElementById(rightID).scrollTop;
    let section = Array.from(document.getElementById(rightID).children);
    let activeChannel;
    section.map(item => {
      let itemTop = item.offsetTop;
      if (scrollTop > itemTop - 140) {
        activeChannel = item.id;
      }
    });
    setCurrentId(activeChannel);
  };

  const handleScrollClick = item => {
    let targetOffset = 0;
    setCurrentId(item);
    const container = document.getElementById(rightID);
    const scrollTop = getScroll(container, true);
    const sharpLinkMatch = sharpMatcherRegx.exec(`#${item}`);
    if (!sharpLinkMatch) {
      return;
    }
    const targetElement = document.getElementById(sharpLinkMatch[1]);
    if (!targetElement) {
      return;
    }
    const eleOffsetTop = getOffsetTop(targetElement, container);
    let y = scrollTop + eleOffsetTop;
    y -= targetOffset !== undefined ? targetOffset : 0;
    animating.current = true;
    scrollTo(y, {
      callback: () => {
        animating.current = false;
      },
      getContainer: getContainer,
    });
  };
  const getOffsetTop = (element, container) => {
    if (!element.getClientRects().length) {
      return 0;
    }
    const rect = element.getBoundingClientRect();
    if (rect.width || rect.height) {
      if (container === window) {
        container = element.ownerDocument.documentElement;
        return rect.top - container.clientTop;
      }
      return rect.top - container.getBoundingClientRect().top;
    }
    return rect.top;
  };
  const getContainer = () => {
    return document.getElementById(rightID);
  };
  return (
    <Layout className="Layout_box">
      <Sider className="Sider">
        <div className="publish_good_left_con">
          <div className="publish_good_left_con_data">
            {normalNavArr &&
              normalNavArr.map(item => {
                return (
                  <a>
                    <div
                      className={`publish_good_left_test ${
                        currentId === item.id ? "actived" : ""
                      }`}
                      onClick={() => handleScrollClick(item.id)}>
                      {item.name}
                    </div>
                  </a>
                );
              })}
          </div>
        </div>
      </Sider>
      <Layout className="Layout_content">
        <div id={rightID}
             style={{overflow: "scroll", height: "100%", paddingBottom: 55, marginLeft: 16}}>
          <ItemBlock style={{marginTop: 0}} id={`${itemId}-edit-basic`}>
            <Descriptions title="活动信息" bordered={false} column={2}>
              <Descriptions.Item label="活动名称">{formData.activityName}</Descriptions.Item>
              <Descriptions.Item label="活动专题">{formData.activityTopic}</Descriptions.Item>
              <Descriptions.Item
                label="活动开始时间">{lib.formatTimeStr(formData.activityStartTime)}</Descriptions.Item>
              <Descriptions.Item
                label="活动结束时间">{lib.formatTimeStr(formData.activityEndTime)}</Descriptions.Item>
              <Descriptions.Item
                label="审核开始时间">{lib.formatTimeStr(formData.auditStartTime)}</Descriptions.Item>
              <Descriptions.Item label="审核结束时间">{lib.formatTimeStr(formData.auditEndTime)}</Descriptions.Item>
              <Descriptions.Item label="奖励发放时间">{lib.formatTimeStr(formData.rewardsTime)}</Descriptions.Item>
              <Descriptions.Item label="奖励有效期（天）">{formData.validityDay}</Descriptions.Item>
            </Descriptions>
            <Descriptions>
              <Descriptions.Item label="活动Banner" span={3}>
                {formData.bannerUrl ?
                  <Image
                    width={200}
                    src={formData.bannerUrl}
                    alt="上传示例"
                  /> : '未上传'}
              </Descriptions.Item>
            </Descriptions>
          </ItemBlock>
          <ItemBlock id={`${itemId}-edit-reward`}>
            <Descriptions title="奖励规则">
              <Descriptions.Item label="奖励类型">{formData.rewardsTypeName}</Descriptions.Item>
              {
                formData.rewardsType === "GIFT_AND_CASH_TYPE" ?
                  <Descriptions.Item label={"福卡金额"}>{formData.fortuneCardRewardsNum}</Descriptions.Item>:null
              }
              <Descriptions.Item label={(formData.rewardsType==="GIFT_TYPE"||formData.rewardsType==="GIFT_AND_CASH_TYPE")?"福豆数量":"福卡金额"}>{formData.rewardsNum}</Descriptions.Item>
            </Descriptions>
          </ItemBlock>
          <ItemBlock id={`${itemId}-edit-advertising`}>
            <Descriptions title="推广信息">
              <Descriptions.Item label="推广渠道">
                {formData.promotionChannelName}
              </Descriptions.Item>
            </Descriptions>
          </ItemBlock>
          <ItemBlock id={`${itemId}-edit-activity-rules`}>
            <Descriptions title="活动规则">
              <Descriptions.Item label="规则介绍" span={3}>
                <ItemRichEditor value={formData.activityRule}/>
              </Descriptions.Item>

              <Descriptions.Item label="奖励机制" span={3}>
                {formData.rewardsRule}
              </Descriptions.Item>

              <Descriptions.Item label="核销规则" span={3}>
                {formData.checkRule}
              </Descriptions.Item>
            </Descriptions>
          </ItemBlock>
          <ItemBlock id={`${itemId}-edit-document-rules`}>
            <Descriptions title="资料规则" column={1}>
              <Descriptions.Item label="注意事项">
                {formData.attention}
              </Descriptions.Item>
              <Descriptions.Item label="上传示例">
                <Image
                  width={200}
                  src={formData.exampleUrl}
                  alt="上传示例"
                />
              </Descriptions.Item>
            </Descriptions>
          </ItemBlock>
        </div>
      </Layout>
    </Layout>
  )
}
ReactDOM.render(
  <SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          method: 'post',
        })
        request(obj);
      }
    }
  })}><OffsitePromotionDetail/>
  </SpaConfigProvider>
  , document.getElementById("offsite-promotion-detail")
);

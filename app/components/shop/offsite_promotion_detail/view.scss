@import "compass/css3/inline-block";
@import "pokeball/theme";

.offsite-promotion-detail{
  height: calc(100% - 50px);
  .ant-layout {
    background: transparent;
  }
  .Layout_box {
    height: 100%;

    .Sider {
      background: transparent;

      .ant-layout-sider-children {
        height: auto;
      }

      .Anchor {
        height: 500px;

        .Anchor_link {
          display: block;
          height: 50px;
          line-height: 50px;
        }
      }
    }

    .Layout_content {
      background: transparent;
      overflow: auto;

      .ant-descriptions-item-container {
        align-items: center;
      }

      .mandatory_title {
        color: #f00;
        margin-right: 5px;
      }
    }

    .ant-descriptions {
      // padding: 20px;
    }

    .ant-descriptions-view {
      // padding: 20px;
      // margin-top: 20px;
    }

    .ant-descriptions-title {
      margin-left: 24px;
    }

    .ant-table-wrapper {
      padding: 0 24px 24px;
    }

    .ant-descriptions-item-label {
      margin-left: 24px;
    }
  }
  #right_edit {
    z-index: 2;
    width: 1024px;
    margin-left: 258px;
    padding-bottom: 55px;
    padding-right: 18px
  }
  .publish_good_left_con {

    min-width: 160px;
    height: 100%;
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(229, 229, 229, 0.5);

    .publish_good_left_con_data {
      border-radius: 2px;

      a {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;

        .publish_good_left_test {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          margin-bottom: 4px;
          padding-left: 20px;
          height: 40px;
          line-height: 40px;
          cursor: pointer;
          position: relative;
        }

        .actived {
          background: #f0f6ff;
          color: #0268ff;
        }

        .actived::before {
          content: "";
          position: absolute;
          left: 0;
          width: 4px;
          height: 40px;
          background: #0268ff;
          border-radius: 0px 100px 100px 0px;
        }
      }
    }
  }
}

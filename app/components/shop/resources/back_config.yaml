pagingSubSeller:
  type: http
  method: GET
  url: /api/seller/sub/paging
  query:
    - key: pageNo
    - key: pageSize
sellerRolePaging:
  type: http
  method: GET
  url: /api/seller/role/paging
  query:
    - key: id
    - key: status
    - key: pageNo
    - key: pageSize
findByIdForUpdate:
  type: http
  method: GET
  url: /api/seller/role/find-for-edit
  query:
    - key: id
listShopSites:
  type: SPRING
  uri: io.terminus.galaxy.web.design.service.EcpSiteService:listShopSites
findShopByLoginUser:
  type: http
  method: GET
  url: /api/shop/{_MY_SHOP_ID_}
findDeliveryFee:
  method: GET
  type: http
  url: /api/seller/paging-delivery-fee-template-detail
  query:
    - key: pageNo
    - key: pageSize
findActivityTool:
  method: GET
  type: http
  url: /api/seller/promotion-def/valid
findActivities:
  method: GET
  type: http
  url: /api/seller/promotion/paging
  query:
    - key: name
    - key: type
    - key: promotionDefId
    - key: status
    - key: pageNo
    - key: pageSize
findPromotionById:
  method: GET
  type: http
  url: /api/seller/promotion/find-for-edit
  query:
    - key: promotionId

shopPayListByShopId:
  method: GET
  type: http
  url: /api/shop/profile/pay/list

advertPositionListByShopId:
  method: GET
  type: http
  url: /api/advColumn/findSellerCanEditColumn

pagingAdvertList:
  method: GET
  type: http
  url: /api/adv/paging
  query:
    - key: name
    - key: columnId
    - key: status
    - key: pageNo
    - key: pageSize
    - key: sortBy
findDistributionShopList:
  method: GET
  type: http
  url: /api/weShop/manage/paging
  query:
    - key: mobile
    - key: weShopName
    - key: shopId:_MY_SHOP_ID_
    - key: status:1
    - key: pageNo
    - key: pageSize
findWeDistributionApplyAudit:
  method: GET
  type: http
  url: /api/weShop/manage/paging
  query:
    - key: mobile
    - key: status
    - key: weShopName
    - key: shopId:_MY_SHOP_ID_
    - key: statuses:0,-1
    - key: pageNo
    - key: pageSize

findProfitChangeRecord:
  method: GET
  type: http
  url: /api/weShop/profitChangeRecord/paging
  query:
    - key: weShopId
    - key: account
    - key: type
    - key: startSettleAt
    - key: endSettleAt
    - key: pageNo
    - key: pageSize

findWithdrawDepositApplyList:
  method: GET
  type: http
  url: /api/weWithdrawDetail/seller/paging
  query:
    - key: applicantName
    - key: mobile
    - key: status
    - key: statuses
    - key: pageNo
    - key: pageSize

findWechatAppletInformation:
  method: GET
  type: http
  url: /api/decoration/shopWxa/findByShopId
  query:
    - key: name
    - key: appId
    - key: appSecret

findWechatAppletProject:
  method: GET
  type: http
  url: /api/decoration/ShopWxaProject/findByShopWxa/{shopWxaId}
  query:
    - key: shopWxaId
    - key: name
    - key: templateId
    - key: imageUrl
    - key: description
    - key: extra

findLabelSet:
  method: GET
  type: http
  url: /api/seller/tag
  query:
    - key: _id
    - key: shopId
    - key: name
    - key: createdAt
    - key: updateAt
    - key: lastOP
    - key: empty
    - key: pageNo
    - key: pageSize

findShopUserList:
  method: GET
  type: http
  url: /api/subStore/member/page
  query:
    - key: userId
    - key: name
    - key: mobile
    - key: createdFrom
    - key: createdTo
    - key: userRole
    - key: status
    - key: statuses
    - key: pageNo
    - key: pageSize

findUserTagsList:
  method: GET
  type: http
  url: /api/seller/tag/user
  query:
    - key: userName
    - key: mobile
    - key: tagName
    - key: shopId
    - key: name
    - key: userId
    - key: tagId
    - key: shopId
    - key: _id
    - key: total
    - key: pageNo
    - key: pageSize

findOrderAuditRules:
  method: GET
  type: http
  url: /api/orderAuthRule
  query:
    - key:itemId
    - key: skuId
    - key: pageNo
    - key: pageSize

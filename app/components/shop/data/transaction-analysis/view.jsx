import request from "../../../utils/plugins/axios/request";
import TimeSelector from "..//components/TimeSelector";
import MetricsDisplay from "./components/MetricsDisplay";
import FunnelChart from "./components/FunnelChart";
import { getDefaultDate, getTimeByType, fetchData, dealChartData } from "../utils/show";
import { TIMETYPE, MEMBERTYPE, TRANSACTIONTYPE } from "../utils/enum";
import TrendChart from "../components/TrendChart";
import {  forwardMetricOptions, reverseMetricOptions } from './utils/setting';
const { useState, useEffect } = React;
const { Row, Col, locales, ConfigProvider } = antd;
const { Spa, SpaConfigProvider } = dtComponents;
const { zh_CN } = locales;

// 主组件 - 交易分析
const TransactionAnalysis = () => {
  // ① 时间选择器状态
  const [timeType, setTimeType] = useState('day'); // 默认按日
  const [selectedDate, setSelectedDate] = useState(''); // 选中的时间

  // ② 交易数据状态
  const [transactionData, setTransactionData] = useState({
    // 访问数据
    visitNum: 0,
    visitNumWith: 0,
    
    // 下单数据
    orderPeopleNum: 0,
    orderPeopleNumWith: 0,
    orderNum: 0,
    orderNumWith: 0,
    orderAmount: 0,
    orderAmountWith: 0,
    
    // 成交数据
    dealPeopleNum: 0,
    dealPeopleNumWith: 0,
    dealNum: 0,
    dealNumWith: 0,
    dealAmount: 0,
    dealAmountWith: 0,
    
    // 转化率数据
    visitToOrderRate: 0,
    orderToTransactionRate: 0,
    transactionToRefundRate: 0
  });

  // ① 时间选择器变化处理
  const handleTimeChange = ({ timeType, timeRange }) => {
    setTimeType(timeType);
    setSelectedDate(timeRange);
    if (timeRange) {
      // 更新所有数据
      fetchTransactionData(timeType, timeRange);
    }
  };

  // 获取交易数据
  const fetchTransactionData = async (type, date) => {
    let params = {
      timeType: TIMETYPE[type],
      timeDesc: getTimeByType(type, date)
    }

    // 获取环比数据
    let ringInfo = await fetchData('/data-dashboard/api/dashboard/trade/getTradeOverviewData', params);
    setTransactionData(ringInfo);
  };
  // 创建反向交易图表数据获取函数
  const getChartData = async (selectedMetric) => {
    console.log('getReverseChartData called with:', { selectedMetric, timeType, selectedDate });
    let params = {
      timeType: TIMETYPE[timeType],
      timeDesc: getTimeByType(timeType, selectedDate),
      type: TRANSACTIONTYPE[selectedMetric]
    }
    console.log('Reverse chart params:', params);
    // 获取同比数据
    const chartInfo = dealChartData((await fetchData('/data-dashboard/api/dashboard/trade/getTradeDataHistory', params) || []) || []);
    console.log('Reverse chart data:', chartInfo);
    return chartInfo;
  };

  // 页面初始化
  useEffect(() => {
    // 默认选中前一天并加载数据
    let defaultDate = getDefaultDate(timeType);
    setSelectedDate(defaultDate);
    fetchTransactionData(timeType, defaultDate);
  }, []);


  return (
    <div className="transaction-analysis-container">
      {/* 顶部：标题和时间选择器 */}
      <div className="header-section">
        <h2 className="page-title">交易概况</h2>
        <TimeSelector
          timeType={timeType}
          selectedDate={selectedDate}
          onChange={handleTimeChange}
          excludeTimeType={['hour']}
        />
      </div>

      {/* 主要内容区域 */}
      <div className="main-content">
        <Row gutter={0} style={{ height: '100%' }}>
          {/* 左侧：交易概况 - 严格按照图片布局 */}
          <div className="left-panel">
            <MetricsDisplay
              timeType={timeType}
              data={transactionData}
            />
          </div>

          {/* 右侧：漏斗图 - 严格按照图片布局 */}
          <div className="right-panel">
            <FunnelChart
              conversionRates={transactionData}
            />
          </div>
        </Row>
      </div>

      {/* 底部：图表区域 */}
      <div className="charts-section">
        {/* 正向交易图表 */}
        <TrendChart
          title="正向交易"
          timeType={timeType}
          selectedDate={selectedDate}
          metricOptions={forwardMetricOptions}
          defaultMetric={forwardMetricOptions[0].value}
          showMetricSelector={true}
          chartType="transaction"
          getChartDataFn={(selectedMetric) => getChartData(selectedMetric || forwardMetricOptions[0].value)}
          className="forward-transaction"
        />
        {/* 逆向交易图表 */}
        <TrendChart
          title="逆向交易"
          timeType={timeType}
          selectedDate={selectedDate}
          metricOptions={reverseMetricOptions}
          defaultMetric={reverseMetricOptions[0].value}
          showMetricSelector={true}
          chartType="transaction"
          getChartDataFn={(selectedMetric) => getChartData(selectedMetric || reverseMetricOptions[0].value)}
          className="reverse-transaction"
        />
      </div>
    </div>
  );
};

ReactDOM.render(<SpaConfigProvider spa={new Spa({
  _openPage: () => {
  }, request: {
    request: (params) => {
      params.url = params.url + "?shopId=" + sessionStorage.shopId
      const obj = Object.assign(params, {})
      request(obj);
    }
  }
})}>
  <ConfigProvider locale={zh_CN}>
    <TransactionAnalysis />
  </ConfigProvider>
</SpaConfigProvider>, document.getElementById("transaction-analysis")
);

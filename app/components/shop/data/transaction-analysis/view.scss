// 交易分析页面样式
.transaction-analysis {
  height: auto !important;

  .transaction-analysis-container {
    min-height: auto;

    .page-title {
      margin: 0;
      color: #262626;
      font-size: 24px;
      font-weight: 600;
    }

    // 顶部标题和时间选择器
    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 24px;
      background: white;
      border-radius: 8px;

      // TimeSelector组件样式
      .time-type-select {
        width: 100px;
      }
    }

    // 主要内容区域 - 严格按照图片布局
    .main-content {
      position: relative;

      .left-panel {
        height: 100%;
        padding-right: 12px;
        flex-grow: 1;
      }

      .right-panel {
        height: 100%;
        padding-left: 12px;
        position: absolute;
        right: 90px;
      }
    }

    // 图表区域
    .charts-section {
      margin-bottom: 24px;
    }

    // 交易概况指标卡片样式
    .metrics {
      width: 100%;
      margin-bottom: 8px;

      .metrics-row {
        display: flex;
        padding-left: 16px;
        margin-bottom: 6px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .metric-card {
        width: 200px;
        flex: 0 1 auto;
        margin-right: 12px;
        padding: 16px;


        &:last-child {
          margin-right: 0;
        }

        &-header {
          display: flex;
          align-items: center;
        }

        .metric-title-simple {
          font-size: 14px;
          color: #666;
          margin-right: 5px;
        }

        &-value {
          font-size: 24px;
          font-weight: bold;
          color: #333;
        }

        &-change {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
        }

        &__description {
          color: #999;
        }

        &__change-text {
          &--green {
            color: #52c41a;
          }

          &--red {
            color: #ff4d4f;
          }

          &--gray {
            color: #8c8c8c;
          }
        }
      }
    }

    // 漏斗图组件样式 - 自定义CSS实现
    .funnel-chart {
      margin-bottom: 24px;

      .funnel-container {}

      .funnel-chart-wrapper {
        position: relative;
        display: flex;
        align-items: flex-start;
        gap: 20px;
      }

      // 自定义漏斗图样式
      .custom-funnel {
        width: 300px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        gap: 0; // 确保各部分之间没有间距

        .funnel-section {
          position: relative;
          transition: all 0.3s ease;
          margin: 0; // 确保没有外边距
          border: none; // 确保没有边框
          height: 80px; // 与MetricsDisplay行高度一致
          margin-bottom: 6px; // 移除间距，让连接处更紧密

          &:hover {
            opacity: 0.9;
          }
        }
      }



      // 转化率标签 - 定位在漏斗连接处
      .conversion-labels {
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 100%;
        // pointer-events: none; // 不影响漏斗图的交互
      }

      .conversion-label {
        .conversion-title {
          font-size: 12px;
        }

        .conversion-rate {
          font-size: 12px;
        }
      }
    }

    // 图表组件样式
    .forward-transaction,
    .reverse-transaction {
      margin-bottom: 24px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .chart-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          color: #262626;

          .title-icon {
            background-color: #1890ff;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
          }
        }

        .chart-controls {
          display: flex;
          align-items: center;

          .chart-compare-text {
            color: #666;
            font-size: 12px;
            margin-right: 16px;
          }
        }
      }

      .chart-container {
        width: 100%;
        height: 300px;
        margin-top: 16px;
      }
    }

    // 通用卡片样式
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: none;

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 24px;

        .ant-card-head-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .ant-form-item {
      margin: 0;
    }

    .increase {
      color: #ff4d4f;
    }

    .decrease {
      color: #52c41a;
      
    }

    .stable {
      color: #999;
    }
  }
}
const { Tooltip } = antd;
const { ContainerFilled, PayCircleFilled, UserOutlined, QuestionCircleOutlined  } = icons;

// 漏斗图组件 - 自定义CSS实现，不使用ECharts
const FunnelChart = ({ conversionRates }) => {
  // 漏斗数据配置 - 按照图片的4个部分
  const funnelData = [
    {
      name: '访客',
      color: '#2474FF', // 蓝色
      icon: <UserOutlined />
    },
    {
      name: '下单',
      color: '#13CAF0', // 青色
      icon: <ContainerFilled  />
    },
    {
      name: '成交',
      color: '#FAAD14', // 橙色
      icon: <PayCircleFilled />
    },
    {
      name: '流失',
      color: '#CCCCCC', // 灰色
      icon: <div style={{ display: 'flex', alignItems: 'center' }}>
        <img src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/84700666984.png" style={{width: '16px', height: '16px'}}></img>
      </div>
    }
  ];

  // 转化率数据 - 放在漏斗连接处
  const conversionData = [
    {
      title: '访问-下单转化率',
      rate: conversionRates.visitToOrderRate || '0.00%',
      position: 'between-0-1', // 访客和下单之间
      tips: '该时间段内下单的用户数 / 该时间段内访问的用户数 × 100%'
    },
    {
      title: '下单-成交转化率',
      rate: conversionRates.orderToTransactionRate || '0.00%',
      position: 'between-1-2', // 下单和成交之间
      tips: '该时间段内成交的用户数 / 该时间段内下单的用户数 × 100%'
    },
     {
      title: '成交-退款转化率',
      rate: conversionRates.transactionToRefundRate || '0.00%',
      position: 'between-2-3', // 底部
      tips: '该时间段内退款的用户数 / 该时间段内成交的用户数 × 100%'
    }
    // {
    //   title: '访问-成交转化率',
    //   rate: data.visitors > 0 ? ((data.transactions || 0) / data.visitors * 100).toFixed(2) : '0.00',
    //   position: 'between-2-3', // 成交和流失之间
    //   tips: ''
    // },
   
  ];

  // 计算每个漏斗部分的高度 - 与MetricsDisplay行高度一致
  const rowHeight = 110.55; // 与MetricsDisplay的metric-card高度一致

  return (
    <div className="funnel-chart">
      <div className="funnel-container">
        <div className="funnel-chart-wrapper">
          {/* 自定义漏斗图 */}
          <div className="custom-funnel">
            {funnelData.map((item, index) => (
              <div
                key={index}
                className="funnel-section"
                style={{
                  backgroundColor: item.color,
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#fff',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  height: `${rowHeight}px`,
                  // 创建梯形效果 - 更符合图片中的漏斗形状
                  clipPath: index === 0
                    ? 'polygon(5% 0, 95% 0, 85% 100%, 15% 100%)' // 顶部最宽
                    : index === 1
                    ? 'polygon(15% 0, 85% 0, 75% 100%, 25% 100%)' // 第二层
                    : index === 2
                    ? 'polygon(25% 0, 75% 0, 65% 100%, 35% 100%)' // 第三层
                    : 'polygon(35% 0, 65% 0, 55% 100%, 45% 100%)', // 底部最窄
                }}
              >
                <span style={{ marginRight: index ==funnelData.length - 1 ? '2px' : '8px' }}>{item.icon}</span>
                <span>{item.name}</span>
              </div>
            ))}
          </div>

          {/* 转化率文字 - 定位在漏斗连接处 */}
          <div className="conversion-labels">
            {conversionData.map((item, index) => {
              let topPosition,rightPosition;

              // 根据位置计算top值
              if (item.position === 'between-0-1') {
                topPosition = rowHeight - 26; // 第一个和第二个之间
                rightPosition =  -72;
              } else if (item.position === 'between-1-2') {
                topPosition = rowHeight * 2 - 22; // 第二个和第三个之间
                rightPosition =  -42;
              } else if (item.position === 'between-2-3') {
                topPosition = rowHeight * 3 - 16; // 第三个和第四个之间
                rightPosition = -8;
              } else if (item.position === 'bottom') {
                topPosition = rowHeight * 3 + 55; // 底部
                rightPosition =  6;
              }

              return (
                <div
                  key={index}
                  className="conversion-label"
                  style={{
                    position: 'absolute',
                    top: `${topPosition}px`,
                    right: `${rightPosition}px`,
                    fontSize: '12px',
                    color: '#666',
                    whiteSpace: 'nowrap'
                  }}
                >
                  <div className="conversion-title" style={{ marginBottom: '2px' }}>
                    {item.title}
                    {
                      item.tips && (
                        <Tooltip title={item.tips || '11111'}>
                          <QuestionCircleOutlined style={{ marginLeft: '4px' }} />
                        </Tooltip>
                      )
                    }
                  </div>
                  <div className="conversion-rate" style={{ fontWeight: 'bold' }}>
                    {parseFloat(item.rate).toFixed(2)}%
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

// 导出组件
export default FunnelChart;

const { Card, Tooltip } = antd;
const { QuestionCircleOutlined, } = icons;
import { compareText, getMetricConfig } from '../../utils/show'
import { meticsConfig } from '../utils/setting'


// 交易概况组件 - 按照图片简洁网格布局
const MetricsDisplay = ({ timeType, data }) => {


  // 使用传入的数据，如果没有数据则使用默认值
  const metrics = [
    [
      {
        key: 'visitors',
        title: '访问人数',
        value: data.visitNum,
        changeValue: data.visitNumWith,
        tips: '统计周期内买家访问您店铺（浏览平台图文、浏览平台活动页面、访问商品详情页及店铺其他页面）的去重人数（访客数统计以人为单位，即同一客户多次查看店铺例如上午一次、下午一次，访客数计为1）'
      },
    ],
    [
      {
        key: 'firstOrderUser',
        title: '首次下单用户数',
        value: data.firstBuyUserNum,
        changeValue: data.firstBuyUserNumWith,
        tips: '统计周期内，首次下单的用户数'
      },
      {
        key: 'orderUsers',
        title: '下单人数',
        value: data.orderPeopleNum,
        changeValue: data.orderPeopleNumWith,
        tips: '统计周期内，所有在小程序内成功下单的客户总数，去重'
      },
      {
        key: 'orderCount',
        title: '下单笔数',
        value: data.orderNum,
        changeValue: data.orderNumWith,
        tips: '下单订单数	统计周期内，所有在小程序内成功下单的订单总数'
      },
      {
        key: 'orderAmount',
        title: '下单金额（元）',
        value: data.orderAmount,
        changeValue: data.orderAmountWith,
        tips: '下单金额	统计周期内，所有在小程序内成功下单的订单实付金额的合计值'
      },
    ],
    [
      {
        key: 'transactionUsers',
        title: '成交人数',
        value: data.transactionPeopleNum,
        changeValue: data.transactionPeopleNumWith,
        tips: '统计周期内，已支付成功的客户总数，包含已退款的客户，去重'
      },
      {
        key: 'transactionCount',
        title: '成交笔数',
        value: data.transactionNum,
        changeValue: data.transactionNumWith,
        tips: '统计周期内，支付成功的订单数，包含已退款的订单'
      },
      {
        key: 'transactionAmount',
        title: '成交金额（元）',
        value: data.transactionAmount,
        changeValue: data.transactionAmountWith,
        tips: '统计周期内，所有支付成功订单实付金额的合计值，含已退款的订单金额'
      },
      {
        key: 'customerPrice',
        title: '客单价（元）',
        value: data.averageOrderValue,
        changeValue: data.averageOrderValueWith,
        tips: '统计周期内，成交金额/成交人数'
      },
    ],
    [
      {
        key: 'refundUsers',
        title: '退款买家数',
        value: data.refundPeopleNum,
        changeValue: data.refundPeopleNumWith,
        tips: '统计周期内，退款成功的客户总数，去重'
      },
      {
        key: 'refundCount',
        title: '退款笔数',
        value: data.refundNum,
        changeValue: data.refundNumWith,
        tips: '退款时间在统计时间内，成功退款的订单总数量'
      },
      {
        key: 'refundAmount',
        title: '退款金额（元）',
        value: data.refundAmount,
        changeValue: data.refundAmountWith,
        tips: '退款时间在统计时间内，成功退款订单的实际退款总金额，非申请退款金额'
      }
    ]
  ];

  

  return (
    <div className='metrics'>
      {
        metrics.map((metrics, metricsIdx) => (
          <div className='metrics-row' style={{ backgroundColor: meticsConfig[metricsIdx].bgColor }}>
            {metrics.map(metric => (
              <div key={metric.key} className="metric-card">
                <div className="metric-card-header">
                  <span className="metric-title-simple">{metric.title}</span>
                  <Tooltip title={metric.tips}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                </div>
                <div className="metric-card-value">{metric.value || 0}</div>
                <div className="metric-card-change">
                  <span className="metric-card__description">
                    {compareText(timeType)}
                  </span>
                  <span className={`metric-card__change-text ${getMetricConfig(metric.changeValue).color}`}>
                    {getMetricConfig(metric.changeValue).sign}{metric.changeValue}
                  </span>
                </div>
              </div>
            ))
            }
          </div>
        ))
      }
    </div>
  );
};

// 导出组件
export default MetricsDisplay;

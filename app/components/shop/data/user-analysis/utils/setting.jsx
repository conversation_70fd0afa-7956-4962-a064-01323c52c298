// 增量数据配置（根据图片内容）
export const getMetrics = (data) => {
    return [
        {
            key: 'visitors',
            title: '访问人数（UV）',
            value: data.visitors.value,
            changeValue: data.visitors.changeValue,
            tips: '统计周期内买家访问您店铺（浏览平台图文、浏览平台活动页面、访问商品详情页及店铺其他页面）的去重人数（访客数统计以人为单位，即同一客户多次查看店铺例如上午一次、下午一次，访客数计为1）'
        },
        {
            key: 'newMembers',
            title: '新增新客数',
            value: data.newMembers.value,
            changeValue: data.newMembers.changeValue,
            tips: '统计时间内买家访问平台的次数'

        },
        {
            key: 'conversion',
            title: '平台浏览量（PV）',
            value: data.platformViewNums.value,
            changeValue: data.platformViewNums.changeValue,
            tips: <div>
                <div>统计时间内买家访问平台的次数</div>
                <div>注1：访问平台的行为包含浏览平台图文、浏览平台活动页面、访问商品详情页及店铺其他页面</div>
                <div>注2：浏览量统计以次数为单位，即同一客户多次查看店铺例如上午一次、下午一次，浏览量计为2</div>
            </div>
        }
    ];
}
.user-analysis {
    padding: 24px;
    background-color: #fff;

    .user-analysis-container {

        // 页面标题
        .page-header {
            margin-bottom: 16px;

            .page-title {
                margin: 0;
                font-size: 20px;
                font-weight: bold;
                color: #262626;
            }
        }

        // ① 时间选择器区域
        .time-selector-section {
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .page-title {
                font-size: 24px;
                font-weight: bold;
                color: #262626;
            }

            .time-selector__select {
                width: 100px;
            }
        }

        // 数据展示区域
        .data-section {
            position: relative;
        }

        // 分割线样式
        .data-divider {
            position: absolute;
            top: 0;
            bottom: 0;
            left: calc(75% - 1px); // 18/24 = 75%
            width: 1px;
            background-color: #e8e8e8;
            z-index: 1;
        }

        // 图表区域
        .chart-section {
            margin: 0 20px 24px;
        }


        // ② 增量数据样式
        .incremental-data {
            background: white;
            border-radius: 8px;
            padding: 24px;

            &__header {
                margin-bottom: 20px;
            }

            &__title {
                margin: 0;
                font-size: 16px;
                font-weight: bold;
                color: #262626;
            }

            &__content {
                .ant-col {
                    margin-bottom: 16px;
                }
            }
        }

        // 指标卡片样式
        .metric-card {
            position: relative;
            transition: all 0.3s ease;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid rgba(0, 0, 0, 0.1);

            &:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            &--selected {
                border-color: #1890ff !important;
                background-color: #f0f8ff !important;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
            }

            &.clickunable{
                cursor: default;
            }

            &__header {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
            }

            &__title {
                font-size: 14px;
                color: #666;
                font-weight: normal;
                margin-right: 5px;
            }

            &__icon {
                color: #1890ff;
                font-size: 16px;
                cursor: pointer;
            }

            &__value {
                font-size: 32px;
                font-weight: bold;
                margin-bottom: 8px;
                color: #262626;
                line-height: 1;
            }

            &__change {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
            }

            &__description {
                color: #999;
            }

            &__change-text {
                font-weight: 500;

                &--increase {
                    color: #ff4d4f;
                    
                }

                &--decrease {
                    color: #52c41a;
                }

                &--stable {
                    color: #999;
                }
            }

            &__selected-indicator {
                position: absolute;
                top: 8px;
                left: 8px;
            }

            &__selected-dot {
                width: 8px;
                height: 8px;
                background-color: #1890ff;
                border-radius: 50%;
            }
        }

        // ③ 存量数据样式
        .stock-data {
            margin-top: 20px;

            .stock-card {
                background: white;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                border: 2px solid rgba(0, 0, 0, 0.1);

                &__header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                }

                &__title {
                    font-size: 14px;
                    color: #666;
                    font-weight: normal;
                    margin-right: 5px;
                }

                &__icon {
                    color: #1890ff;
                    font-size: 16px;
                }

                &__value {
                    font-size: 32px;
                    font-weight: bold;
                    margin-bottom: 8px;
                    color: #262626;
                    line-height: 1;
                }

                &__description {
                    font-size: 12px;
                    color: #999;
                    display: flex;
                    justify-content: space-between;
                }
            }
        }

        // ④ 折线图样式
        .trend-chart {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            &__header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 16px;
                border-bottom: 1px solid #f0f0f0;
            }

            &__title {
                margin: 0;
                font-size: 16px;
                font-weight: bold;
                color: #262626;
            }

            &__legend {
                display: flex;
                gap: 24px;
            }

            &__legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            &__legend-line {
                width: 12px;
                height: 2px;
                border-radius: 1px;
            }

            &__legend-text {
                font-size: 12px;
                color: #666;
            }

            &__legend-value {
                font-size: 12px;
                color: #262626;
                font-weight: 500;
            }

            &__container {
                position: relative;
                height: 300px;
                display: flex;
            }

            &__y-axis {
                width: 50px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding: 20px 0;
            }

            &__y-label {
                font-size: 12px;
                color: #999;
                text-align: right;
                padding-right: 8px;
            }

            &__plot-area {
                flex: 1;
                position: relative;
                margin: 20px 0;
                border-left: 1px solid #e8e8e8;
                border-bottom: 1px solid #e8e8e8;
            }

            &__grid {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            &__grid-line {
                height: 1px;
                background-color: #f5f5f5;
            }

            &__lines {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
            }

            &__line {
                position: absolute;
                height: 2px;

                &--primary {
                    background: linear-gradient(90deg, #1890ff, #40a9ff);
                    top: 30%;
                    left: 0;
                    right: 0;
                }

                &--secondary {
                    background: linear-gradient(90deg, #52c41a, #73d13d);
                    top: 60%;
                    left: 0;
                    right: 0;
                }
            }

            &__points {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            &__point {
                width: 6px;
                height: 6px;
                background-color: #1890ff;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    width: 8px;
                    height: 8px;
                    box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
                }
            }

            &__x-axis {
                display: flex;
                justify-content: space-between;
                padding: 8px 20px 0 50px;
            }

            &__x-label {
                font-size: 12px;
                color: #999;
                text-align: center;
            }

            &__current-metric {
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px solid #f0f0f0;
                text-align: center;
            }

            &__metric-tip {
                font-size: 12px;
                color: #666;
                background-color: #f5f5f5;
                padding: 4px 12px;
                border-radius: 12px;
            }
        }

        // 通用样式
        .ant-card {
            border-radius: 8px;

            .ant-card-body {
                padding: 20px;
            }
        }
    }

    .increase {
        color: #ff4d4f;
    }

    .decrease {
        color: #52c41a;
    }

    .stable {
        color: #999;
    }

    .ant-form-item{
        margin: 0;
    }


}
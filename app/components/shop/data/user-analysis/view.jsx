import request from "../../../utils/plugins/axios/request";
import TimeSelector from "../components/TimeSelector";
import IncrementalData from "./components/IncrementalData";
import StockData from "./components/StockData";
import TrendChart from "../components/TrendChart";
import { getDefaultDate, getTimeByType, dealChartData, fetchData } from "../utils/show";
import { TIMETYPE, MEMBERTYPE } from "../utils/enum";
import { getMetrics } from './utils/setting';
const { useState, useEffect } = React;
const { Row, Col, locales, ConfigProvider } = antd;
const { Spa, SpaConfigProvider } = dtComponents;
const { zh_CN } = locales

// 主组件 - 会员分析
const UserAnalysis = () => {

  const [ringData, setRingData] = useState({
    visitors: {
      value: 0,
      changeValue: 0
    },
    newMembers: {
      value: 0,
      changeValue: 0
    },
    platformViewNums: {
      value: 0,
      changeValue: 0
    },
    totalMembers: {
      value: 0,
      changeValue: 0
    },
  });
  // ① 时间选择器状态
  const [timeType, setTimeType] = useState('hour'); // 默认按小时
  const [selectedDate, setSelectedDate] = useState(''); // 最近时间

  // ② 增量数据状态
  const [selectedMetric, setSelectedMetric] = useState('visitors'); // 默认选中第一项

  // ④ 折线图数据状态
  const [chartData, setChartData] = useState({
    current: [], // 当前时间数据
    comparison: [] // 同比时间数据
  });

  // 获取指标配置
  const metrics = getMetrics(ringData);



  // ① 时间选择器变化处理
  const handleTimeChange = ({ timeType, timeRange }) => {
    console.log('时间选择器变化:', timeType, timeRange);
    setTimeType(timeType);
    setSelectedDate(timeRange);
    if (timeRange) {
      // 更新②④部分数据
      fetchDataByTime(timeType, timeRange);
    }
  };

  // ② 增量数据点击处理
  const handleMetricClick = (metricKey) => {
    setSelectedMetric(metricKey);
    // 更新④折线图显示对应指标
    updateChartByMetric(metricKey);
  };

  

  // 根据时间维度获取数据
  const fetchDataByTime = async (type, date) => {
    let params = {
      timeType: TIMETYPE[type],
      timeDesc: getTimeByType(type, date)
    }
    // 获取环比数据
    let ringInfo = await fetchData('/data-dashboard/api/dashboard/user/getUserOverviewData', params);
    setRingData({
      visitors: {
        value: ringInfo.visitNum,
        changeValue: ringInfo.visitNumWith
      },
      newMembers: {
        value: ringInfo.newMemberNum,
        changeValue: ringInfo.newMemberNumWith
      },
      platformViewNums: {
        value: ringInfo.platformViewNum,
        changeValue: ringInfo.platformViewNumWith
      },
      totalMembers: {
        value: ringInfo.tocalMemberNum,
        changeValue: ringInfo.tocalMemberNumWith
      }
    });

    // 获取同比数据
    let res = await fetchData('/data-dashboard/api/dashboard/user/getUserTrendData', { ...params, type: MEMBERTYPE[selectedMetric] });
    
    const chartInfo = dealChartData(res || []);
    console.log('chartInfo1', chartInfo)
    setChartData(chartInfo);
  };

  // 根据指标更新图表
  const updateChartByMetric = async (metricKey) => {
    // 这里添加图表数据更新逻辑
    let params = {
      timeType: TIMETYPE[timeType],
      timeDesc: getTimeByType(timeType, selectedDate)
    }
    // 获取同比数据
    const chartInfo = dealChartData(await fetchData('/data-dashboard/api/dashboard/user/getUserTrendData', { ...params, type: MEMBERTYPE[metricKey] }) || []);
    console.log('chartInfo', chartInfo);
    setChartData(chartInfo);
  };

  // 页面初始化
  useEffect(() => {
    // 默认选中第一项并加载数据
    let defaultDate = getDefaultDate(timeType);
    setSelectedDate(defaultDate);
    fetchDataByTime(timeType, defaultDate);
  }, []);

  return (
    <div className="user-analysis-container">
      {/* 页面标题 */}

      {/* ① 时间选择器 */}
      <div className="time-selector-section">
        <h2 className="page-title">会员概况与趋势</h2>
        <TimeSelector
          timeType={timeType}
          selectedDate={selectedDate}
          onChange={handleTimeChange}
        />
      </div>

      {/* ②③ 数据展示区域 */}
      <div className="data-section">
        <Row gutter={24}>
          {/* ② 增量数据 */}
          <Col span={18}>
            <IncrementalData
              timeType={timeType}
              selectedMetric={selectedMetric}
              onMetricClick={handleMetricClick}
              data={ringData}
            />
            {/* ④ 折线图：趋势图表 */}
            <div className="chart-section">
              <TrendChart
                selectedMetric={selectedMetric}
                timeType={timeType}
                data={chartData}
                chartType="user"
                metricOptions={metrics.map(item => ({ value: item.key, label: item.title }))}
                onPointClick={(point) => {
                  // 点击图表点时的处理
                  // console.log('图表点击:', point);
                }}
              />
            </div>
          </Col>

          {/* 分割线 */}
          <div className="data-divider"></div>

          {/* ③ 存量数据：累计数据 */}
          <Col span={6}>
            <StockData
              timeType={timeType}
              data={ringData.totalMembers}
            />
          </Col>
        </Row>
      </div>

    </div>
  );
};

ReactDOM.render(<SpaConfigProvider spa={new Spa({
  _openPage: () => {
  }, request: {
    request: (params) => {
      params.url = params.url + "?shopId=" + sessionStorage.shopId
      const obj = Object.assign(params, {})
      request(obj);
    }
  }
})}>
  <ConfigProvider locale={zh_CN}>
    <UserAnalysis />
  </ConfigProvider>
</SpaConfigProvider>, document.getElementById("user-analysis")
);

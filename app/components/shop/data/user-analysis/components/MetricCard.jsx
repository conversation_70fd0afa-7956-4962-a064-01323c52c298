const { Card, Tooltip } = antd;
const { QuestionCircleOutlined } = icons;
import { compareText, getMetricConfig } from '../../utils/show';

// 指标卡片组件
const MetricCard = ({
  metric,
  isSelected,
  onClick,
  timeType,
  clickable = true,
  showRing = true,
}) => {
  return (
    <Card
      className={`metric-card ${isSelected ? 'metric-card--selected' : ''} ${!clickable ? 'clickunable' : ''}`}
      onClick={() => onClick(metric.key)}
      hoverable
      bodyStyle={{ padding: '20px' }}
    >
      <div className="metric-card__header">
        <span className="metric-card__title">{metric.title}</span>
        <Tooltip title={metric.tips}>
          <QuestionCircleOutlined />
        </Tooltip>
      </div>

      <div className="metric-card__value">
        {metric.value || 0}
      </div>

      <div className={`metric-card__change `} style={{ visibility: showRing ? 'visible' : 'hidden' }}>
            <span className="metric-card__description">{compareText(timeType)}</span>
            <span className={`metric-card__change-text metric-card__change-text--${getMetricConfig(metric.changeValue).color}`}>
              {getMetricConfig(metric.changeValue).sign}{metric.changeValue}
            </span>
          </div>

      {/* 选中状态指示器 */}
      {isSelected && (
        <div className="metric-card__selected-indicator">
          <div className="metric-card__selected-dot"></div>
        </div>
      )}
    </Card>
  );
};

// 导出组件
export default MetricCard;

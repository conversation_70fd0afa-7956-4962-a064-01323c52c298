const { Row, Col } = antd;
import MetricCard from './MetricCard';
import { getMetrics } from '../utils/setting';

// ② 增量数据组件 - 页面进入默认选中第一项，点击后高光展示
const IncrementalData = ({ selectedMetric, onMetricClick, timeType, data  }) => {

  const metrics = getMetrics(data);

  return (
    <div className="incremental-data">

      <Row gutter={[16, 16]} className="incremental-data__content">
        {metrics.map((metric) => (
          <Col span={8} key={metric.key}>
            <MetricCard
              metric={metric}
              isSelected={selectedMetric === metric.key}
              onClick={onMetricClick}
              timeType={timeType}
            />
          </Col>
        ))}
      </Row>
    </div>
  );
};

// 导出组件
export default IncrementalData;

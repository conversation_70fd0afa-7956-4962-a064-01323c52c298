const { Card, Tooltip } = antd;
const { QuestionCircleOutlined, } = icons;
import { compareText, getMetricConfig } from '../../utils/show'
import MetricCard from './MetricCard';

// ③ 存量数据组件 - 展示累计数据，与其他模块无关联交互
const StockData = ({ timeType, data }) => {

  // ③ 存量数据配置（根据图片内容）
  const stockMetrics = [
    {
      key: 'totalMembers',
      title: '累计用户数',
      value: data.value,
      changeValue: data.changeValue,
      suffix: '人',
      tips: '截止到当前时间累计注册用户数，不受时间选择约束'
    }
  ];
  
  return (
    <div className="stock-data">
      {stockMetrics.map((metric, index) => (
        <MetricCard
          key={index}
          metric={metric}
          isSelected={false}
          onClick={() => {}}
          timeType={timeType}
          showRing={false}
        />
      ))}
    </div>
  );
};

// 导出组件
export default StockData;

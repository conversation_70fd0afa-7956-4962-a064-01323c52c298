/**
 * @description: 通用趋势折线图组件 - 用于用户分析和交易分析
 * @author: 
 */
import request from "../../../utils/plugins/axios/request";
const { useState, useEffect, useRef } = React;
const { Card, Select } = antd;

// 通用折线图组件 - 支持用户分析和交易分析
const TrendChart = ({
  title,
  selectedMetric,
  timeType,
  data,
  onPointClick,
  metricOptions,
  defaultMetric,
  selectedDate,
  showMetricSelector = false,
  chartType = 'user', // 'user' | 'transaction'
  getChartDataFn,
  className = 'trend-chart'
}) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  // 内部状态管理 - 当前选中的指标
  const [currentMetric, setCurrentMetric] = useState(selectedMetric || defaultMetric);

  // 根据选中指标获取提示信息
  const getMetricTip = (metric) => {
    console.log("metricOptions", metricOptions, metric);
    if (metricOptions) {
      return (metricOptions.find(option => option.value === metric) || {}).label || '';
    }
    return metric;
  };

  // 处理指标选择变化
  const handleMetricChange = (value) => {
    setCurrentMetric(value);
  };

  // 同步外部传入的selectedMetric
  useEffect(() => {
    if (selectedMetric) {
      setCurrentMetric(selectedMetric);
    } else if (defaultMetric) {
      setCurrentMetric(defaultMetric);
    }
  }, [selectedMetric, defaultMetric]);

  // 渲染图表
  const renderChart = (data) => {
    if (!chartRef.current) return;

    // 如果已经有图表实例，先销毁
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    let fullDateLabels = data.fullDateLabels;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    const metricTip = getMetricTip(currentMetric);

    console.log('metricTip', metricTip)

    // 根据图表类型设置不同的配置
    const isTransactionChart = chartType === 'transaction';

    // 图表配置 - 根据图表类型设置不同颜色
    const colors = ["#1890ff", "#52c41a"];

    console.log('data.xAxisLabels', data.xAxisLabels);

    const option = {
      color: colors,
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          // 获取数据点索引
          const dataIndex = params[0].dataIndex;

          // 根据时间类型决定显示的标签
          let displayLabel = params[0].name;
          if (timeType === 'day' && fullDateLabels && fullDateLabels[dataIndex]) {
            // 在day模式下，使用完整的日期标签
            displayLabel = fullDateLabels[dataIndex];
          }

          let result = displayLabel + '<br/>';
          params.forEach(param => {
            result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: [
          `12月前${metricTip}`,
          `${metricTip}`,
        ],
        left: "center",
        bottom: "0%"
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: data.xAxisLabels || [],
        axisLine: {
          lineStyle: {
            color: '#E5E5E5'
          }
        },
        axisLabel: {
          color: '#999',
          interval: timeType === 'week' ? 0 : (timeType === 'day' ? 1 : 'auto') // 周维度显示所有标签，日维度隔一个显示
        }
      },
      yAxis: {
        type: "value",
        minInterval: 1,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E5E5E5',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#999'
        }
      },
      series: [
        {
          name: `12月前${metricTip}`,
          type: "line",
          smooth: true,
          // connectNulls: false,
          data: data.comparison || [],
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            borderWidth: 2
          },
          lineStyle: {
            width: 2
          }
        },
        {
          name: `${metricTip}`,
          type: "line",
          smooth: true,
          // connectNulls: false,
          data: data.current || [],
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            borderWidth: 2
          },
          lineStyle: {
            width: 2
          }
        }
      ]
    };

    // 设置图表配置
    chartInstance.current.setOption(option);

    // 添加点击事件
    chartInstance.current.on('click', function (params) {
      if (onPointClick) {
        onPointClick({
          label: params.name,
          index: params.dataIndex,
          metric: currentMetric,
          value: params.value
        });
      }
    });
  };

  // 监听窗口大小变化，调整图表大小
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      // 组件卸载时销毁图表实例
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  // 当选中指标或时间类型变化时，重新获取数据
  useEffect(() => {
    if (getChartDataFn && timeType && selectedDate) {
      const loadData = async () => {
        try {
          const chartData = await getChartDataFn(currentMetric);
          if (chartData) {
            renderChart(chartData);
          }
        } catch (error) {
          console.error('获取图表数据失败:', error);
        }
      };
      loadData();
    }
  }, [currentMetric, timeType, selectedDate, getChartDataFn]);

  // 当外部传入的数据变化时，重新渲染图表
  useEffect(() => {
    if (data) {
      const chartData = {
        xAxisLabels: data.xAxisLabels  || [],
        current: data.current  || [],
        comparison: data.comparison || [],
        fullDateLabels: data.fullDateLabels || [],
      };
      renderChart(chartData);
    }
  }, [data, currentMetric]);

  return (
    <Card className={className} bodyStyle={{ padding: '24px' }}>
      {/* 图表头部 - 显示标题和指标选择器 */}
      {(title || showMetricSelector) && (
        <div className="chart-header">
          {title && (
            <div className="chart-title">
              {title}
            </div>
          )}
          {showMetricSelector && metricOptions && (
            <div className="chart-controls">
              <Select
                value={currentMetric}
                onChange={handleMetricChange}
                options={metricOptions}
                style={{ width: 120, marginLeft: 16 }}
                size="small"
              />
            </div>
          )}
        </div>
      )}

      <div
        ref={chartRef}
        className="trend-chart__container"
        style={{ width: "100%", height: "330px" }}
      ></div>
    </Card>
  );
};

// 导出组件
export default TrendChart;

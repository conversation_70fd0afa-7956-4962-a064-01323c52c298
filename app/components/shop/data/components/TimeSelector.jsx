const { useState, useEffect } = React;
const { Select, Space, TimePicker, Form } = antd;
const { Option } = Select;
import PickerWithType from './PickerWithType';
import { getDefaultDate } from '../utils/show';

// ① 时间选择器组件 - 简洁版本，符合图片样式
const TimeSelector = React.memo(({ timeType, selectedDate, onChange, excludeTimeType = [] }) => {
  const [form] = Form.useForm();
  const [type, setType] = useState(timeType);

  const timeOptions = [
    { value: 'hour', label: '按小时' },
    { value: 'day', label: '按日' },
    { value: 'week', label: '按周' },
    { value: 'month', label: '按月' }
  ].filter(item => !excludeTimeType.includes(item.value));

  

  // 处理时间类型变化
  const handleTypeChange = (value) => {
    setType(value);
    
    // 根据类型设置默认时间
    let defaultDate = getDefaultDate(value);
    
    form.setFieldsValue({ timeType: value, timeRange: defaultDate });
    
    // 触发外部onChange回调
    if (onChange) {
      onChange({ timeType: value, timeRange: defaultDate });
    }
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (date) => {
    if (date) {
      form.setFieldsValue({ timeRange: date });
      
      // 触发外部onChange回调
      if (onChange) {
        const values = form.getFieldsValue();
        onChange(values);
      }
    }
  };

  // 初始化
  useEffect(() => {
    if (timeType) {
      setType(timeType);
    }
    
    // 如果有初始日期，使用它；否则，根据类型设置默认日期
    let initialDate = getDefaultDate(timeType);
    form.setFieldsValue({ timeType: type, timeRange: initialDate });
  }, []);

  return (
    <Form form={form}>
      <Space.Compact>
        <Form.Item name="timeType">
          <Select 
            value={type} 
            onChange={handleTypeChange} 
            options={timeOptions} 
            style={{ width: '100px' }}
            placeholder="请选择"
          />
        </Form.Item>
        <Form.Item name="timeRange">
          <PickerWithType 
            type={type} 
            onChange={handleTimeRangeChange} 
            value={form.getFieldValue('timeRange')}
          />
        </Form.Item>
      </Space.Compact>
    </Form>
  );
});

// 导出组件
export default TimeSelector;

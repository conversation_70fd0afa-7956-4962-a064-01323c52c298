

const { DatePicker, TimePicker } = antd;
const { useState, useEffect, useRef, Fragment, useMemo } = React;
const PickerWithType = ({
    type,
    onChange,
    value,
}) => {
    // 禁用小时选择的函数
    const disableNonHour = (time) => {
        const currentHour = new Date().getHours();
        
        return {
            disabledHours: () => {
                // 禁用当前小时及之后的小时，只允许选择0到当前小时-1
                return [...Array(24).keys()].filter(hour => hour >= currentHour);
            },
            disabledMinutes: () => {
                // 禁用所有非0的分钟（即只允许整点）
                return [...Array(60).keys()].filter(minute => minute !== 0);
            },
            disabledSeconds: () => {
                // 禁用所有秒数
                return [...Array(60).keys()];
            }
        };
    }

    // 根据不同时间维度禁用日期的函数
    const disabledDate = (current) => {
        if (!current) return false;
        
        switch (type) {
            case 'day':
                // 只可以选择昨天，以及昨天倒推365天
                return current.isAfter(moment().subtract(1, 'day'), 'day') || 
                       current.isBefore(moment().subtract(366, 'day'), 'day');
            
            case 'week':
                // 只可选择上周，以及上周倒推52周
                return current.isAfter(moment().subtract(1, 'week'), 'week') || 
                       current.isBefore(moment().subtract(53, 'week'), 'week');
            
            case 'month':
                // 只可选择上月，以及上月倒推12个月
                return current.isAfter(moment().subtract(1, 'month'), 'month') || 
                       current.isBefore(moment().subtract(13, 'month'), 'month');
            
            default:
                return false;
        }
    };

    return <Fragment>
        {
            type === 'hour' ? <TimePicker
                format="HH:mm"
                placeholder="请选择时间"
                disabledTime={disableNonHour}
                showNow={false}
                value={value}
                onChange={onChange}
            /> : <DatePicker 
                picker={type == 'day' ? 'date' : type} 
                onChange={onChange} 
                value={value}
                placeholder="请选择时间"
                disabledDate={disabledDate}
            />
        }
    </Fragment>
};

export default PickerWithType;

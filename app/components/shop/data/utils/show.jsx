import request from "../../../utils/plugins/axios/request";

// 时间对比文本
export const compareText = (type) => {
  switch (type) {
    case 'hour':
      return "较前1小时"
    case 'day':
      return "较前1日"
    case 'week':
      return "较上1周"
    case 'month':
      return "较上1月"
    case 'year':
      return "较上1年"
  }
}

// 获取变化值的颜色和符号
export const getMetricConfig = (changeValue) => {
  let val = parseInt(changeValue)
  let color = ''
  let sign = ''
  if (val > 0) {
    color = 'increase';
    sign = '↗ ';
  } else if (val < 0) {
    color = 'decrease';
    sign = '↘ ';
  } else {
    color = 'stable';
  }
  return { color, sign };
};

// 根据时间类型获取默认时间
export const getDefaultDate = (type) => {
  let defaultDate;
  switch (type) {
    case 'hour':
      // 设置为上一个小时
      defaultDate = moment().subtract(1, 'hour').startOf('hour');
      break;
    case 'day':
      // 设置为昨天
      defaultDate = moment().subtract(1, 'day').startOf('day');
      break;
    case 'week':
      // 设置为上周一
      defaultDate = moment().subtract(1, 'week').startOf('week');
      break;
    case 'month':
      // 设置为上月初
      defaultDate = moment().subtract(1, 'month').startOf('month');
      break;
    case 'year':
      // 设置为上年初
      defaultDate = moment().subtract(1, 'year').startOf('year');
      break;
    default:
      defaultDate = null;
  }
  return defaultDate;
}

// 根据时间类型获取时间的格式
export const getTimeByType = (type, time) => {
  switch (type) {
    case 'hour':
      return moment(time).format('YYYY-MM-DD HH');
    case 'day':
      return moment(time).format('YYYY-MM-DD');
    case 'week':
      return moment(time).format('YYYY-WW');
    case 'month':
      return moment(time).format('YYYY-MM');
    case 'year':
      return moment(time).format('YYYY');
    default:
      return time;
  }
}

// 对接口返回的同比数据进行处理
export const dealChartData = (data, timeType) => {
  console.log('timeType:', timeType)
  let [xAxisLabels, fullDateLabels, current, comparison] = [[], [], [], []];
  data.map((item, index) => {
    if (timeType == 'hour' && index % 2 === 1) {
      xAxisLabels.push('');
    } else {
      xAxisLabels.push(item.time);
    }
    fullDateLabels.push(item.time);
    current.push(item.num);
    comparison.push(item.lastYearNum);
  })
  return { xAxisLabels, fullDateLabels, current, comparison };
}


export const fetchData = async (url, params) => {
  return await request({
    url,
    needMask: true,
    method: 'post',
    miniShopConfig: {
      isNewServer: true
    },
    data: params
  })
};
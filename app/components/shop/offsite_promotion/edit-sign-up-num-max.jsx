import request from "../../utils/plugins/axios/request";
import ActivityStatusEnum from "./activity-status-enum";


const {useMemo, Fragment,useState,useEffect} = React;
const { Space,message,InputNumber,Button} = antd;
const {EditOutlined} =icons;

export default function EditSignUpNumMax({row,load}) {
    const [edit,setEdit] = useState(false)
    const [value,setValue] = useState(row[""])
    useEffect(()=>{
        setValue(row["signUpNumMax"])
    },[row])

    return <Fragment>
        {
            !edit?
            <Space>
                {row["signUpNumStr"]}
              {
                (row.signUpNumLimitFlag==0 || row.status === ActivityStatusEnum.ACTIVITY_END||
                row.status === ActivityStatusEnum.ACTIVITY_DOWN)? null :
                <EditOutlined style={{color:'#1890ff'}} onClick={()=>{
                  setEdit(true)
                }} />
              }
            </Space>
            :
            <Space>
                <InputNumber
                    precision={0}
                    value={value}
                    onChange={(e)=>{
                        setValue(e);
                    }}
                />
                <Button size='small' onClick={()=>{
                  if(!/^[1-9]\d{0,5}$/.test(value)){
                    message.warning("仅支持正整数，不超过6位")
                    return
                  }
                    request({
                        url:'/mall-admin/api/promotion/activity/editSignUpNumMax',
                        type:'post',
                        data:{
                            id:row.id,
                          signUpNumMax:value,
                        },
                        success:()=>{
                            message.success("修改成功")
                            load && load()
                            setEdit(false)
                        }
                    })
                }}>保存</Button>
            </Space>
        }
    </Fragment>
}

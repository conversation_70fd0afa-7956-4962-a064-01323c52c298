import request from "../../utils/plugins/axios/request";
import ActivityStatusEnum from "./activity-status-enum";
import {lib} from "../../common/react/utils/lib";
import {ActivityUserStatusEnum} from "../offsite_promotion_view_registration/activity-userstatus-enum";
import EditSignUpNumMax from "./edit-sign-up-num-max";

const {Fragment, useRef,useState} = React;
const {Space, Button, Modal, message, Image,Tabs} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents


export const OffsitePromotion = () => {
  const searchListRef = useRef();
  const [tabValue, setTabValue] = useState("");
  const [tabNum, setTabNum] = useState({});
  const getConfig = async function () {
    const data = await axios.get("https://maria.yang800.com/api/data/v2/938/763")
    return data.data.data;
  };
  const getTabNum = (obj) => {
    let data={...obj}
    delete data.status;
    request({
      url: `/mall-admin/api/promotion/activity/total`,
      method: "POST",
      data:data,
      success: (res) => {
        setTabNum(res)
      },
    })
  }
  const doDelete = (row) => {
    Modal.confirm({
      title: "发布",
      content: "确定删除活动吗",
      onOk: () => new Promise((resolve, reject) => {
        return request({
          url: `/mall-admin/api/promotion/activity/delete`,
          data: {id: row.id},
          success: (res) => {
            resolve(res)
            searchListRef.current.load()
            message.success("删除成功")
          },
          fail: reject
        })
      })
    })

  }
  const doPublish = (row) => {
    Modal.confirm({
      title: "发布",
      content: "确定发布活动吗？",
      onOk: () => new Promise((resolve, reject) => {
        return request({
          url: `/mall-admin/api/promotion/activity/publish`,
          data: {id: row.id},
          success: (res) => {
            resolve(res)
            searchListRef.current.load()
            message.success("发布成功")
          },
          fail: reject
        })
      })
    })
  }
  const doOff = (row) => {
    Modal.confirm({
      title: "下架",
      content: "确定下架活动吗？",
      onOk: () => new Promise((resolve, reject) => {
        return request({
          url: `/mall-admin/api/promotion/activity/down`,
          data: {id: row.id},
          success: (res) => {
            resolve(res)
            searchListRef.current.load()
            message.success("下架成功")
          },
          fail: reject
        })
      })
    })
  }

  function joinTimeStr(start, end) {
    let formattedStartDate = moment(start).format('YYYY-MM-DD HH:mm');
    let formattedEndDate = moment(end).format('YYYY-MM-DD HH:mm');
    return `${formattedStartDate}~${formattedEndDate}`
  }
  function status2icon(status) {
    if (status === ActivityStatusEnum.ACTIVITY_CREATE) {//未发布
      return <i className="create-icon"></i>;
    } else if (status === ActivityStatusEnum.ACTIVITY_WAIT) {//进行中
      return <i className="wait-icon"></i>;
    } else if (status === ActivityStatusEnum.ACTIVITY_IN_PROGRESS) {//待开始
      return <i className="in-progress-icon"></i>;
    } else if (status === ActivityStatusEnum.ACTIVITY_END) {//已结束
      return <i className="end-icon"></i>;
    } else if (status === ActivityStatusEnum.ACTIVITY_DOWN) {//已下架
      return <i className="down-icon"></i>;
    }
  }

  return (
    <SearchList
      ref={searchListRef}
      scrollMode={"tableScroll"}
      paginationConfig={{size: "default", showPosition: 'bottom'}}

      searchConditionConfig={{
        size: "middle",
      }}
      getConfig={getConfig}
      onSearchReset={()=>{
        setTabValue("");
      }}
      onSearch={(obj,type)=>{
        getTabNum(obj)
      }}
      renderOperationTopView={() => {
        return <div>
          <Tabs
            activeKey={tabValue}
            items={[
              {
                key: '',
                label: `全部（${tabNum.allNum||"0"}）`,
              },
              {
                key: ActivityStatusEnum.ACTIVITY_CREATE,
                label: `未发布 （${tabNum.createNum||"0"}）`,
              },
              {
                key: ActivityStatusEnum.ACTIVITY_WAIT,
                label: `待开始 （${tabNum.waitNum||"0"}）`,
              },
              {
                key: ActivityStatusEnum.ACTIVITY_IN_PROGRESS,
                label: `进行中（${tabNum.inProgressNum||"0"}）`,
              },
              {
                key: ActivityStatusEnum.ACTIVITY_END,
                label: `已结束（${tabNum.endNum||"0"}）`,
              },
              {
                key: ActivityStatusEnum.ACTIVITY_DOWN,
                label: `已下架（${tabNum.downNum||"0"}）`,
              }
            ]} onChange={(e) => {
            setTabValue(e);
            const obj = {
              status: e,
            }
            searchListRef.current.changeImmutable(obj)
          }}/>
        </div>
      }}
      tableCustomFun={{
        auditNumFn: (row) => {
          return  <a className={"link"} href={`/seller/offsite-promotion-view-registration?activityId=${row.id}&_status=${ActivityUserStatusEnum.ACTIVITY_USER_WAIT}&_auditStartTime=${row.auditStartTime}&_auditEndTime=${row.auditEndTime}`}>{row.auditNum}</a>

      },
        statusNameFn: (row) => {
          return <div>
            {
              status2icon(row.status)
            }
            <span>{row.statusName}</span>
          </div>
        },
        bannerFn: (row) => {

          return <div>
            <Image  src={row.bannerUrl} width={120} height={60}/>
          </div>
        },
        activityTimeFn: (row) => {
          return <div>
            {joinTimeStr(row.activityStartTime, row.activityEndTime)}
          </div>
        },
        auditTimeFn: (row) => {

          return <div>
            {joinTimeStr(row.auditStartTime, row.auditEndTime)}
          </div>
        },
        publishTimeFn: (row) => {
          return <div>
            {lib.formatTimeStr(row.publishTime,"second")}
          </div>
        },
        rewardsTimeFn: (row) => {
          return <div>
            {lib.formatTimeStr(row.rewardsTime)}
          </div>
        },
        createdTimeFn: (row) => {
          return <div>
            {lib.formatTimeStr(row.createdTime,"second")}
          </div>
        },
        signUpNumStrFn: (row) => {
          return <div>
            <EditSignUpNumMax
              row={row}
              load={() => {
                searchListRef.current.load()
              }}
            />
          </div>
        },
        myOperation: (row) => {
          return <Space wrap>
            {row.status === ActivityStatusEnum.ACTIVITY_CREATE &&
              <a className={"link"} onClick={() => doPublish(row)}>发布</a>}

            {(row.status === ActivityStatusEnum.ACTIVITY_IN_PROGRESS
                || row.status === ActivityStatusEnum.ACTIVITY_WAIT
                || row.status === ActivityStatusEnum.ACTIVITY_END
                ||row.status ===  ActivityStatusEnum.ACTIVITY_DOWN) &&
              <a className={"link"} href={`/seller/offsite-promotion-view-registration?activityId=${row.id}&_auditStartTime=${row.auditStartTime}&_auditEndTime=${row.auditEndTime}`}>查看报名</a>}
            {(row.status === ActivityStatusEnum.ACTIVITY_CREATE
                || row.status === ActivityStatusEnum.ACTIVITY_WAIT) &&
              <a className={"link"} href={`/seller/offsite-promotion-create?id=${row.id}`}>编辑</a>}
            {(row.status === ActivityStatusEnum.ACTIVITY_IN_PROGRESS
                || row.status === ActivityStatusEnum.ACTIVITY_END
              ||row.status ===  ActivityStatusEnum.ACTIVITY_DOWN)//活动状态【进行中】【已结束】
              && <a className={"link"} href={`/seller/offsite-promotion-detail?id=${row.id}`}>查看</a>}
            {(row.status === ActivityStatusEnum.ACTIVITY_CREATE
              || row.status === ActivityStatusEnum.ACTIVITY_WAIT
                || row.status === ActivityStatusEnum.ACTIVITY_DOWN) //活动状态【未发布】【未开始】【已下架】
              && <a className={"link"} onClick={() => doDelete(row)}>删除</a>}
            {(row.status === ActivityStatusEnum.ACTIVITY_END
              || row.status ===  ActivityStatusEnum.ACTIVITY_WAIT
                || row.status ===  ActivityStatusEnum.ACTIVITY_IN_PROGRESS ) &&
              <a className={"link"} onClick={() => doOff(row)}>下架</a>}
          </Space>
        },
      }}
      renderLeftOperation={() => {
        return <Space>
          <Button type={"primary"}
                  onClick={() => window.open("/seller/offsite-promotion-create", "_self")}>新增活动</Button>
        </Space>
      }}
      renderModal={() => {
        return <Fragment>

        </Fragment>
      }}
    />

  )
}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          method: 'post',
        })
        request(obj);
      }
    }
  })}><OffsitePromotion/>
  </SpaConfigProvider>, document.getElementById("offsite-promotion")
);

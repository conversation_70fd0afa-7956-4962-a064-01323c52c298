
const {Button, Table,Switch,Modal,message,Badge,Alert,
    Card, Select, Row, Col, Divider,Space,Tag
} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents
const {useState,useEffect,useRef,Fragment} = React;
import request from "../../../utils/plugins/axios/request";
import CellItem from "./cell-item";
import PayTypeModal from "./pay-type-modal";
import ReleaseModal from "./release-modal";
import ReceiptAccountModal from "./receipt-account-modal";
const { Option } = Select;
const PAY_INFO_KEY_TO_NAME = {
    // 联动支付
    "clientId":"client_id",
    "clientSecret":"client_secret",
    // 微信支付
    "mchId":"商户号",//微信商户号
    "appId":"APPID", //微信小程序的 app id
    "paySecret":"APIv2/v3密钥",//支付秘钥
    "certFileStatus":"证书",//证书的上传状态

    // 支付宝支付
    "pid":"商户号",
    "account": "支付宝账号", //account
    "paySecret":"支付密钥",
    // 通联支付(收银宝)
    "cusid": "商户号",
    "appid": "appid",
    "signType": "签名方式",
    "publicKey": "公钥",
    "privateKey": "私钥",
    //  通联支付(通商云)
    "appid": "通联appId",
    "secretKey": '密钥',
    "certPassword":"私钥证书文件对应的密码",
    "bizUserPercent": "托管代付小B账户的分账百分比",
    "accountSetNo": "账户集编号",
    "vspMerchantid": "收银宝集团商户号",
    "vspCusid": "收银宝商户号",
    "vspAppId":"集团商户号appid",
    "vspTermid": "终端信息",
    "tlCertPath": "公钥文件",
    "certPath": "私钥文件",
    // 但丁支付
    "signSecret": "签名秘钥",
    "signType": "签名类型",
    "appId": "商户应用号",
    "serviceUrl": "网关地址"
}

const PAY_TYPE_STATUS_TO_TEXT = {
    '1': "success", // 使用中
    '2': "warning", // 灰度中
    '-1': "default", // 已停用
    "-2": "error", //冻结
}

const PAY_TYPE_STATUS_TO_ALIAS = {
    ACTIVE:1, // 使用中
    // GRAY_RELEASE: 2,// 灰度中
    INACTIVE: 2, // 已停用
    // FROZEN: -2 //冻结
}

const Page = () => {

    const [data, setData] = useState([]);
    const [selectList, setSelectList] = useState([]);
    const [receiptAccountModalVisible, setReceiptAccountModalVisible] = useState(false);
    const [currentPayInfo, setCurrentPayInfo] = useState(null);

    const getList = () => {
        $.ajax({
            url:'/api/shop/profile/pay/list',
            type:'GET',
            success: (res) =>{
                setData(res.data);
            },
            fail: (err)=>{
                console.log(err);
            }
        })
    }

    const renderOperate = (row) => {
        return (<div>
            {
                row.disable === PAY_TYPE_STATUS_TO_ALIAS.INACTIVE && (
                <Fragment>
                    <PayTypeModal type="edit" editRow={row} reload={getList} />
                    {/* <Button type="link" onClick={()=>allRelease(row)}>全量发布</Button>
                    <ReleaseModal editRow={row} reload={getList}/> */}
                </Fragment>
                )
            }
            {
               row.showOpenChannelAccount && !row.hasOpenedEnterpriseAccount && (
                    <Button type="link" onClick={() => {
                        setCurrentPayInfo(row);
                        setReceiptAccountModalVisible(true);
                    }}>添加收款账号</Button>
                )
            }
        </div>)
    }

    const renderConfigInfo = (row)=>{
        const detail = row.detail;
        const result = [];
        for (const key in detail) {
            if (detail[key]) {
                const element = detail[key];
                result.push({
                    title: PAY_INFO_KEY_TO_NAME[key] || key,
                    value: element
                })
            }
        }
        return (<div>
            {
                result.map((item,index)=><CellItem key={index}
                    {...item}
                />)
            }
        </div>)
    }

    const renderStatus = (row) => {


        return (<div>
            {/* <Badge status={PAY_TYPE_STATUS_TO_TEXT[row.status]} /> */}
            {/* {row.statusName} */}
            <Switch checked={row.disable === PAY_TYPE_STATUS_TO_ALIAS.ACTIVE}
                checkedChildren="启用"
                unCheckedChildren="停用"
                onChange={(val)=>{
                    request({
                        url:'/api/shop/profile/pay/disable',
                        data:{
                            id: row.id,
                            disable: val?1:2
                        },
                        success(){
                            getList();
                            getSelectList();
                        }
                    })
                }}
            />
        </div>)
    }

    useEffect(()=>{
        getList();
        getSelectList();
    },[])

    const getSelectList = ()=>{
        request({
            url:'/api/shop/profile/pay/select',
            method:'POST',
            success: (res) => {
                console.log("res:",res);
                setSelectList(res.map(item=>{
                    return {
                        ...item,
                        name: item.payChannelName,
                        id: item.id
                    }
                }));
            }
        })
    }

    return (
        <div>
            {/* <Button>新增支付方式</Button> */}
            <h2 style={{fontWeight:'bold'}}>支付选择</h2>
            <Alert  style={{marginBottom:'20px'}} message="【重要提示】切换支付方式时，请提前下架商品或通知消费者不要下单，否则将会影响订单正常发货和退款流程" type="warning" showIcon closable />
            <Header list={data} load={getList} selectList={selectList} />
            <div style={{display:'flex',alignItems:'center',justifyContent:'space-between'}}>
                <h2 style={{fontWeight:'bold'}}>支付方式配置</h2>
                <PayTypeModal type="add" editRow={{}} reload={()=>{
                    getSelectList();
                    getList();
                }} />
            </div>

            <Table
                scroll={{
                    x: 1000
                }}
                columns={[
                    {
                        title: '使用渠道',
                        dataIndex: 'usageChannelName',
                        key: 'usageChannelName',
                        width: 100,
                    },
                    {
                        title: '支付方式',
                        dataIndex: 'payChannelName',
                        width: 100,
                        key: 'payChannelName',
                    },
                    {
                        title: '支付配置信息',
                        width: 400,
                        render: renderConfigInfo
                    },
                    {
                        title: '状态',
                        // dataIndex: 'statusName',
                        // key: 'statusName',
                        width: 100,
                        render: renderStatus
                    },
                    {
                        title: '操作',
                        width: 200,
                        render: renderOperate
                    },
                ]}
                dataSource={data}
            />
            <ReceiptAccountModal
                visible={receiptAccountModalVisible}
                onCancel={() => setReceiptAccountModalVisible(false)}
                onOk={() => {
                    setReceiptAccountModalVisible(false);
                    getList(); // 刷新列表数据
                }}
                payInfo={currentPayInfo}
            />
        </div>
    )
}

const Header =  ({list,load,selectList}) => {
    const [retailPayMethod, setRetailPayMethod] = useState('');
    const [retailFullPayMethod, setRetailFullPayMethod] = useState('');
    const [wholesalePayMethod, setWholesalePayMethod] = useState('');
    const [wholesaleFullPayMethod, setWholesaleFullPayMethod] = useState('');
    const [detail,setDetail] = useState({tradeType:2,})

    const getDetail = ()=>{
        request({
            url: '/api/shop/pay/channel/detail',
            type: 'GET',
            success: (res) => {
                setDetail(res)
                const channelDutyPaid = res.channelDutyPaid;
                const channelBonded = res.channelBonded;
                setRetailPayMethod();
                setRetailFullPayMethod();
                setWholesalePayMethod();
                setWholesaleFullPayMethod();
                if(channelDutyPaid){
                   channelDutyPaid.grayType && setRetailPayMethod(channelDutyPaid.grayType.proFileId);
                   channelDutyPaid.allType && setRetailFullPayMethod(channelDutyPaid.allType.proFileId);
                }
                if(channelBonded){
                   channelBonded.grayType && setWholesalePayMethod(channelBonded.grayType.proFileId);
                   channelBonded.allType && setWholesaleFullPayMethod(channelBonded.allType.proFileId);
                }


            }
        })
    }

    useEffect(()=>{
        getDetail();
    },[])

    const allRelease = (row) => {
        const content = !detail.channelDutyPaid || ! detail.channelDutyPaid.grayType?
         `全量发布后当前支付方式【${row.payChannelName}】将覆盖全部用户，是否确认发布？`: `全量发布全量发布后当前支付方式【${row.payChannelName}】将覆盖全部用户，原灰度方式将被终止，您可重新选择其他灰度支付方式`
        const content1 = !detail.channelBonded || !detail.channelBonded.grayType?
         `全量发布后当前支付方式【${row.payChannelName}】将覆盖全部用户，是否确认发布？`: `全量发布全量发布后当前支付方式【${row.payChannelName}】将覆盖全部用户，原灰度方式将被终止，您可重新选择其他灰度支付方式`

        Modal.confirm({
            title:'全量发布',
            content: row.tradeType === 0?content:content1,
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                request({
                    url: `/api/shop/pay/channel/release`,
                    method:'POST',
                    data:{
                        id: row.id,
                        proFileId: row.proFileId,
                        relationType: row.type,
                        tradeType: row.tradeType
                    },
                    success: (res)=>{
                         message.success("发布成功");
                            load();
                            getDetail()
                    }
                })
            }
        })
    }

    const stopGray = (row) => {
        Modal.confirm({
            title:row.type === 1?'取消灰度':'取消全量',
            content:
            row.type === 1?<Fragment>
                <h3>取消灰度是否取消【{row.payChannelName}】支付方式灰度</h3>
                <p>1. 取消后若当前贸易类型下有全量发布的支付方式，所有灰度用户将采用默认全量方式</p>
                <p>2. 若当前贸易类型下无全量发布支付方式，灰度用户将无法下单，请提前设置</p>
            </Fragment>:
            <Fragment>
                <p>是否取消【{row.payChannelName}】支付方式发布？</p>
                <p>取消后用户将无法下单，请提前设置</p>
            </Fragment>,
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                request({
                    url: `/api/shop/pay/channel/cancel`,
                    method:'POST',
                    data:{
                        id: row.id,
                        type: row.type,
                        tradeType: row.tradeType
                    },
                    success: (res)=>{
                        message.success("操作成功");
                        load();
                        getDetail();
                    }
                })
            }
        })
    }

    return (
        <div style={{ padding: '20px' }}>
            <Row gutter={16}>
                {
                    [0,2].includes(  detail.tradeType) && <Col span={12}>
                    <Card title="完税（大贸）订单" bordered={true}>
                        <Row >
                            <span style={{ display: 'inline-block', width: '100px' }}>灰度支付方式:</span>
                            <Select
                                style={{ width: 200 }}
                                value={retailPayMethod}
                                // disabled={detail.channelDutyPaid.grayType.id}
                                onChange={value => setRetailPayMethod(value)}
                            >
                                {
                                    selectList.map(item=>
                                        <Option value={item.id}>{item.name}</Option>
                                    )
                                }
                            </Select>
                            {/* <Button type="primary" style={{ marginLeft: '10px' }} round>灰度发布</Button> */}
                            <Space style={{paddingLeft:'20px'}}>
                                {
                                    detail.channelDutyPaid && detail.channelDutyPaid.grayType ?
                                    <Fragment>
                                        <Button onClick={()=>{
                                            stopGray({
                                                id: detail.channelDutyPaid.grayType.id,
                                                type: 1, //1 灰度, 2 全量
                                                tradeType: 0, // 0 大贸,1 保税
                                                payChannelName: detail.channelDutyPaid.grayType.payChannelName
                                            })
                                        }}>取消发布</Button>
                                        {/* <Button>修改</Button> */}
                                        <ReleaseModal editRow={{
                                            id:detail.channelDutyPaid && detail.channelDutyPaid.grayType ? detail.channelDutyPaid.grayType.id:null,
                                            name: selectList.filter(item=>item.id === retailPayMethod).length > 0?
                                            selectList.filter(item=>item.id === retailPayMethod)[0].name
                                            :'',
                                            proFileId:retailPayMethod,
                                            type: 1, //1 灰度, 2 全量
                                            tradeType: 0, // 0 大贸,1 保税
                                            memberList: detail.channelDutyPaid.grayType.memberList
                                        }} reload={()=>{
                                            console.log("sss")
                                            getDetail();
                                            load();
                                        }}/>
                                    </Fragment>
                                    :
                                    // <Button type="primary" style={{ marginLeft: '10px' }} round>灰度发布</Button>
                                    <ReleaseModal editRow={{
                                        proFileId:retailPayMethod,
                                         name: selectList.filter(item=>item.id === retailPayMethod).length > 0?
                                        selectList.filter(item=>item.id === retailPayMethod)[0].name
                                        :'',
                                        type: 1, //1 灰度, 2 全量
                                        tradeType: 0, // 0 大贸,1 保税
                                    }} reload={()=>{
                                        console.log("sss")
                                        getDetail();
                                           load();
                                    }}/>
                                }
                            </Space>
                        </Row>
                        <div style={{padding:'10px',marginBottom:'20px'}}>
                            <Space>
                                {detail.channelDutyPaid && detail.channelDutyPaid.grayType &&
                                detail.channelDutyPaid.grayType.memberList &&
                                detail.channelDutyPaid.grayType.memberList.map((item)=>{
                                    return (<Tag  bordered={false} key={item} closable={false} >{item}</Tag>)
                                })}
                            </Space>
                        </div>

                        <Row>
                            <span style={{ display: 'inline-block', width: '100px' }}>全量支付方式:</span>
                            <Select
                                style={{ width: 200 }}
                                value={retailFullPayMethod}
                                onChange={value => setRetailFullPayMethod(value)}
                            >
                                {
                                    selectList.map(item=>
                                        <Option value={item.id}>{item.name}</Option>
                                    )
                                }
                            </Select>
                             <Space style={{paddingLeft:'20px'}}>
                                {
                                    detail.channelDutyPaid && detail.channelDutyPaid.allType ?
                                    <Fragment>
                                        <Button onClick={()=>{
                                            stopGray({
                                                id: detail.channelDutyPaid.allType.id,
                                                type: 2, //1 灰度, 2 全量
                                                tradeType: 0, // 0 大贸,1 保税
                                                payChannelName: detail.channelDutyPaid.allType.payChannelName
                                            })
                                        }}> 取消全量</Button>
                                        <Button onClick={()=>{
                                            if(!retailFullPayMethod){
                                                return message.warn("请选择支付方式")
                                            }
                                            allRelease({
                                                type: 2, //1 灰度, 2 全量
                                                tradeType: 0, // 0 大贸,1 保税
                                                id: detail.channelDutyPaid.allType.id,
                                                proFileId: retailFullPayMethod,
                                                payChannelName: selectList.filter(item=>item.id === retailFullPayMethod).length > 0?
                                                selectList.filter(item=>item.id === retailFullPayMethod)[0].name : ''
                                            })
                                        }}>修改</Button>
                                    </Fragment>
                                    :
                                    <Button type="primary" style={{ marginLeft: '10px' }} round onClick={()=>{
                                        if(!retailFullPayMethod){
                                            return message.warn("请选择支付方式")
                                        }
                                        allRelease({
                                            type: 2, //1 灰度, 2 全量
                                            tradeType: 0, // 0 大贸,1 保税
                                            // id: detail.channelDutyPaid.id,
                                            proFileId: retailFullPayMethod,
                                            payChannelName: selectList.filter(item=>item.id === retailFullPayMethod).length > 0?
                                            selectList.filter(item=>item.id === retailFullPayMethod)[0].name : ''
                                        })
                                    }}>全量发布</Button>
                                }
                            </Space>

                        </Row>
                    </Card>
                </Col>
            }
            {
                [1,2].includes(detail.tradeType) &&
                    <Col span={12}>
                        <Card title="保税 (全球购) 订单" bordered={true}>
                            <Row >
                            <span style={{ display: 'inline-block', width: '100px' }}>灰度支付方式:</span>
                            <Select
                                style={{ width: 200 }}
                                value={wholesalePayMethod}
                                // disabled={detail.channelDutyPaid.grayType.id}
                                onChange={value => setWholesalePayMethod(value)}
                            >
                                {
                                    selectList.map(item=>
                                        <Option value={item.id}>{item.name}</Option>
                                    )
                                }
                            </Select>
                            {/* <Button type="primary" style={{ marginLeft: '10px' }} round>灰度发布</Button> */}
                             <Space style={{paddingLeft:'20px'}}>
                                {
                                    detail.channelBonded && detail.channelBonded.grayType ?
                                    <Fragment>
                                        <Button onClick={()=>{
                                            stopGray({
                                                id: detail.channelBonded.grayType.id,
                                                type: 1, //1 灰度, 2 全量
                                                tradeType: 1, // 0 大贸,1 保税
                                                payChannelName: detail.channelBonded.grayType.payChannelName
                                            })
                                        }}>取消发布</Button>
                                        {/* <Button>修改</Button> */}
                                        <ReleaseModal editRow={{
                                            id:wholesalePayMethod,
                                            name: selectList.filter(item=>item.id === wholesalePayMethod).length > 0?
                                            selectList.filter(item=>item.id === wholesalePayMethod)[0].name
                                            :'',
                                               proFileId:wholesalePayMethod,
                                            type: 1, //1 灰度, 2 全量
                                            tradeType: 1, // 0 大贸,1 保税

                                            memberList:detail.channelBonded && detail.channelBonded.grayType? detail.channelBonded.grayType.memberList:[]
                                        }} reload={()=>{
                                            console.log("sss")
                                            getDetail();
                                               load();
                                        }}/>
                                    </Fragment>
                                    :
                                    // <Button type="primary" style={{ marginLeft: '10px' }} round>灰度发布</Button>
                                    <ReleaseModal editRow={{
                                        proFileId:wholesalePayMethod,
                                        name: selectList.filter(item=>item.id === wholesalePayMethod).length > 0?
                                        selectList.filter(item=>item.id === wholesalePayMethod)[0].name
                                        :'',
                                        type: 1, //1 灰度, 2 全量
                                        tradeType: 1, // 0 大贸,1 保税
                                    }} reload={()=>{
                                        console.log("sss")
                                        getDetail();
                                           load();
                                    }}/>
                                }
                            </Space>
                        </Row>
                        <div style={{padding:'10px',marginBottom:'20px'}}>
                            <Space>
                                {detail.channelBonded && detail.channelBonded.grayType &&detail.channelBonded.grayType.memberList
                                 && detail.channelBonded.grayType.memberList.map((item)=>{
                                    return (<Tag  bordered={false} key={item} closable={false} >{item}</Tag>)
                                })}
                            </Space>
                        </div>

                        <Row>
                            <span style={{ display: 'inline-block', width: '100px' }}>全量支付方式:</span>
                            <Select
                                style={{ width: 200 }}
                                value={wholesaleFullPayMethod}
                                onChange={value => setWholesaleFullPayMethod(value)}
                            >
                                {
                                    selectList.map(item=>
                                        <Option value={item.id}>{item.name}</Option>
                                    )
                                }
                            </Select>
                             <Space style={{paddingLeft:'20px'}}>
                                {
                                    detail.channelBonded && detail.channelBonded.allType ?
                                    <Fragment>
                                        <Button onClick={()=>{
                                            stopGray({
                                                id: detail.channelBonded.allType.id,
                                                type: 2, //1 灰度, 2 全量
                                                tradeType: 1, // 0 大贸,1 保税
                                                payChannelName: detail.channelBonded.allType.payChannelName
                                            })
                                        }}> 取消全量</Button>
                                        <Button onClick={()=>{
                                            if(!wholesaleFullPayMethod){
                                                return message.warn("请选择支付方式")
                                            }
                                            allRelease({
                                                type: 2, //1 灰度, 2 全量
                                                tradeType: 1, // 0 大贸,1 保税
                                                id: detail.channelBonded.allType.id,
                                                proFileId: wholesaleFullPayMethod,
                                                payChannelName: selectList.filter(item=>item.id === wholesaleFullPayMethod).length > 0?
                                                selectList.filter(item=>item.id === wholesaleFullPayMethod)[0].name : ''
                                            })
                                        }}>修改</Button>
                                    </Fragment>
                                    :
                                    <Button type="primary" style={{ marginLeft: '10px' }} round onClick={()=>{
                                        if(!wholesaleFullPayMethod){
                                            return message.warn("请选择支付方式")
                                        }
                                        allRelease({
                                            type: 2, //1 灰度, 2 全量
                                            tradeType: 1, // 0 大贸,1 保税
                                            // id: detail.channelBonded.id,
                                            proFileId: wholesaleFullPayMethod,
                                            payChannelName: selectList.filter(item=>item.id === wholesaleFullPayMethod).length > 0?
                                            selectList.filter(item=>item.id === wholesaleFullPayMethod)[0].name : ''
                                        })
                                    }}>全量发布</Button>
                                }
                            </Space>

                        </Row>
                        </Card>
                    </Col>
                }
            </Row>
            <Divider style={{ marginTop: '30px', marginBottom: '30px' }} />
        </div>
    );
};

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {request: (params) => {
        const obj = Object.assign(params,{
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(params.data),
            shopId: sessionStorage.shopId
        })
        $.ajax(obj)
    }}
  })}>
    <Page/>
  </SpaConfigProvider>, document.getElementById("new-shop-pay-list")
);

import  request from "../../utils/plugins/axios/request"
import { customFrontRequest } from "../../common/react/upload/custom-front-request";
import { picUploadButton } from "../../items/seller/item-edit/component/base/item-image-form-item";
import {fetchHftxBank, fetchUnitebank} from "./method";
import DebounceSelect from "../../common/react/components/debounce-select";


const { useState, useEffect,useCallback } = React;
const { Modal, Form, Input, Select, DatePicker, Button, message, Row, Col } = antd;
const { UploadOutlined } = icons;
const { DTUploaderFile } = dtComponents;

const { Option } = Select;
const FormItem = Form.Item;

const ReceiptAccountModal = ({ visible, onCancel, onOk, payInfo }) => {
  const [form] = Form.useForm();
  const [banks, setBanks] = useState([]);

  // 表单布局配置
  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };

  useEffect(() => {
    // 加载银行列表
    fetchBanks();
  }, []);

  const fetchBanks = () => {
    // 模拟银行列表数据，实际应从API获取
    const bankList = [
      { code: 'ICBC', name: '中国工商银行' },
      { code: 'ABC', name: '中国农业银行' },
      { code: 'BOC', name: '中国银行' },
      { code: 'CCB', name: '中国建设银行' },
      { code: 'COMM', name: '交通银行' },
      { code: 'PSBC', name: '中国邮政储蓄银行' },
      { code: 'CMB', name: '招商银行' },
      { code: 'SPDB', name: '浦发银行' },
      { code: 'CITIC', name: '中信银行' },
    ];
    setBanks(bankList);
  };

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList ? e.fileList : [];
  };

  const beforeUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传JPG/PNG格式的图片!');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片大小不能超过10MB!');
    }
    return isJpgOrPng && isLt10M;
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // 处理日期格式
      if (values.legalCertExpDateStart) {
        values.legalCertExpDateStart = values.legalCertExpDateStart.valueOf();
      }
      if (values.legalCertExpDateEnd) {
        values.legalCertExpDateEnd = values.legalCertExpDateEnd.valueOf();
      }
      if (values.bizLicenseExpDateStart) {
        values.bizLicenseExpDateStart = values.bizLicenseExpDateStart.valueOf();
      }
      if (values.bizLicenseExpDateEnd) {
        values.bizLicenseExpDateEnd = values.bizLicenseExpDateEnd.valueOf();
      }

      // 处理文件上传
      if (Array.isArray(values.legalCertPhotoCopyF) && values.legalCertPhotoCopyF.length>0 ) {
        values.legalCertPhotoCopyF = values.legalCertPhotoCopyF[0].url;
      }
      if (Array.isArray(values.legalCertPhotoCopyB) && values.legalCertPhotoCopyB.length>0 ) {
        values.legalCertPhotoCopyB = values.legalCertPhotoCopyB[0].url;
      }
      if (Array.isArray(values.bizLicensePhotoCopy) && values.bizLicensePhotoCopy.length>0 ) {
        values.bizLicensePhotoCopy = values.bizLicensePhotoCopy[0].url;
      }

      // 直接在弹框内提交请求
      request({
        url: '/api/register/company',
        method: 'POST',
        data: {
          ...values,
          payId: payInfo && payInfo.id ? payInfo.id : ''
        },
        success: function() {
          message.success('收款账号添加成功');
          form.resetFields(); // 重置表单
          onOk();
        }
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const fetchBankOptions = async ({ value, current, pageSize }) => {
    let res = {
      newOptions: [],
      selectRes: { list: [], complete: false },
    };
      try {
        const response = await fetchHftxBank({
          bankNameLike: value,
          pageSize,
          currentPage: current,
        });
        const list =  response.dataList

        res = {
          newOptions: list,
          selectRes: {
            list: list.map(item => {
              return {
                label: item.name,
                value: item.id,
              };
            }),
            complete: current >= response.page.totalPage,
          },
        };
      } catch (error) {
        console.log(error);
      }
      return res.selectRes
    }
  const fetchUnitebankOptions = async ({ value, current, pageSize }) => {
    let res = {
      newOptions: [],
      selectRes: { list: [], complete: false },
    };
    try {
      const response = await fetchUnitebank({
        bankNameLike: value,
        pageSize,
        currentPage: current,
      });
      const list =  response.dataList

      res = {
        newOptions: list,
        selectRes: {
          list: list.map(item => {
            return {
              label: item.name,
              value: item.name,
              code: item.id,
            };
          }),
          complete: current >= response.page.totalPage,
        },
      };
    } catch (error) {
      console.log(error);
    }
    return res.selectRes
  }
  return (
    <Modal
      title="添加收款账号"
      visible={visible}
      okText="确定"
      cancelText="取消"
      onCancel={onCancel}
      onOk={handleSubmit}
      width={1000}
      destroyOnClose
    >
      <Form form={form} {...formItemLayout}>
        <Row gutter={24}>
          {/* 左列 */}
          <Col span={12}>
            {/* 企业基本信息 */}
            <FormItem
              label="企业名称"
              name="name"
              rules={[{ required: true, message: '请输入企业名称' }]}
            >
              <Input placeholder="请输入企业名称" />
            </FormItem>

            <FormItem
              label="企业简称"
              name="shortName"
              rules={[{ required: true, message: '请输入企业简称' }]}
            >
              <Input placeholder="请输入企业简称" />
            </FormItem>

            <FormItem
              label="企业地址"
              name="address"
              rules={[{ required: true, message: '请输入企业地址' }]}
            >
              <Input placeholder="请输入企业地址" />
            </FormItem>

            <FormItem
              label="企业电话"
              name="phone"
              rules={[{ required: true, message: '请输入企业电话' }]}
            >
              <Input placeholder="请输入企业电话" />
            </FormItem>

            {/* 法人信息 */}
            <FormItem
              label="法人姓名"
              name="legalPerson"
              rules={[{ required: true, message: '请输入法人姓名' }]}
            >
              <Input placeholder="请输入法人姓名" />
            </FormItem>

            <FormItem
              label="法人证件号"
              name="legalCertId"
              rules={[{ required: true, message: '请输入法人证件号' }]}
            >
              <Input placeholder="请输入法人证件号" />
            </FormItem>

            <FormItem
              label="法人手机号"
              name="legalMobile"
              rules={[{ required: true, message: '请输入法人手机号' }]}
            >
              <Input placeholder="请输入法人手机号" />
            </FormItem>

            <FormItem
              label="法人证件生效日期"
              name="legalCertExpDateStart"
              rules={[{ required: true, message: '请选择法人证件生效日期' }]}
            >
              <DatePicker placeholder="请选择日期" style={{ width: '100%' }} />
            </FormItem>

            <FormItem
              label="法人证件失效日期"
              name="legalCertExpDateEnd"
              rules={[{ required: true, message: '请选择法人证件失效日期' }]}
            >
              <DatePicker placeholder="请选择日期" style={{ width: '100%' }} />
            </FormItem>

            <FormItem
              label="法人证件正面照"
              name="legalCertPhotoCopyF"
              valuePropName="fileList"
              getValueFromEvent={normFile}
              rules={[{ required: true, message: '请上传法人证件正面照' }]}
            >
              <DTUploaderFile
                maxCount={1}
                size={10}
                accept={[".jpg", ".jpeg", ".png"]}
                listType={"picture-card"}
                uploadButton={picUploadButton("上传正面照")}
                customRequest={customFrontRequest}
                beforeUpload={beforeUpload}
              />
            </FormItem>

            <FormItem
              label="法人证件背面照"
              name="legalCertPhotoCopyB"
              valuePropName="fileList"
              getValueFromEvent={normFile}
              rules={[{ required: true, message: '请上传法人证件背面照' }]}
            >
              <DTUploaderFile
                maxCount={1}
                size={10}
                accept={[".jpg", ".jpeg", ".png"]}
                listType={"picture-card"}
                uploadButton={picUploadButton("上传背面照")}
                customRequest={customFrontRequest}
                beforeUpload={beforeUpload}
              />
            </FormItem>
          </Col>

          {/* 右列 */}
          <Col span={12}>
            {/* 联系人信息 */}
            <FormItem
              label="联系人姓名"
              name="contactName"
              rules={[{ required: true, message: '请输入联系人姓名' }]}
            >
              <Input placeholder="请输入联系人姓名" />
            </FormItem>

            <FormItem
              label="联系人手机号"
              name="contactMobile"
              rules={[{ required: true, message: '请输入联系人手机号' }]}
            >
              <Input placeholder="请输入联系人手机号" />
            </FormItem>

            <FormItem
              label="联系人邮箱"
              name="contactEmail"
              rules={[{ required: true, message: '请输入联系人邮箱' }]}
            >
              <Input placeholder="请输入联系人邮箱" />
            </FormItem>

            {/* 营业执照信息 */}
            <FormItem
              label="营业执照号"
              name="bizLicenseCode"
              rules={[{ required: true, message: '请输入营业执照号' }]}
            >
              <Input placeholder="请输入营业执照号" />
            </FormItem>

            <FormItem
              label="营业执照生效日期"
              name="bizLicenseExpDateStart"
              rules={[{ required: true, message: '请选择营业执照生效日期' }]}
            >
              <DatePicker placeholder="请选择日期" style={{ width: '100%' }} />
            </FormItem>

            <FormItem
              label="营业执照失效日期"
              name="bizLicenseExpDateEnd"
              rules={[{ required: true, message: '请选择营业执照失效日期' }]}
            >
              <DatePicker placeholder="请选择日期" style={{ width: '100%' }} />
            </FormItem>

            <FormItem
              label="营业执照照片"
              name="bizLicensePhotoCopy"
              valuePropName="fileList"
              getValueFromEvent={normFile}
              rules={[{ required: true, message: '请上传营业执照照片' }]}
            >
              <DTUploaderFile
                maxCount={1}
                size={5}
                accept={[".jpg", ".jpeg", ".png"]}
                listType={"picture-card"}
                uploadButton={picUploadButton("上传营业执照")}
                customRequest={customFrontRequest}
                beforeUpload={beforeUpload}
              />
            </FormItem>

            {/* 银行账户信息 */}
            <FormItem
              label="开户银行"
              name="bankCode"
              rules={[{ required: true, message: '请选择开户银行' }]}
            >
              <DebounceSelect
                placeholder="请输入开户银行"
                fetchOptions={fetchBankOptions}
                style={{ width: '100%' }}
                showSearch={true}
                listHeight={350}
              />
            </FormItem>

            <FormItem
              label="账号"
              name="bankAcct"
              rules={[{ required: true, message: '请输入账号' }]}
            >
              <Input placeholder="请输入账号" />
            </FormItem>

            <FormItem
              label="户名"
              name="acctName"
              rules={[{ required: true, message: '请输入户名' }]}
            >
              <Input placeholder="请输入户名" />
            </FormItem>

            <FormItem
              label="收款行联行号"
              name="uniteBankName"
              rules={[{ required: true, message: '请输入收款行联行号' }]}
            >
              <DebounceSelect
                placeholder="请选择收款行联行号"
                fetchOptions={fetchUnitebankOptions}
                style={{ width: '100%' }}
                showSearch={true}
                listHeight={350}
                onChange={(value,option) => {
                  form.setFieldsValue({
                    uniteBankCode: option.code,
                  });
                }}
              />
            </FormItem>
            <FormItem
              label="收款行联行号"
              name="uniteBankCode"
              hidden
            >
              <Input/>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default ReceiptAccountModal;


const {Button, Table,Switch,Modal,message,Badge,Form,Input,Select,Alert,Space,Upload} = antd;
const FormItem = Form.Item;
const {SearchList, Spa, SpaConfigProvider,DTEditModal,DTUploaderFile} = dtComponents
const {useState,useEffect,useRef,Fragment} = React;
const {UploadOutlined} = icons

const paysList = [{
    name: '支付宝支付',
    id: 'alipay',
},{
    name: '微信支付',
    id: 'wechatpay',
},{
    name: '联动支付',
    id: 'umf',
},{
    name: '收银宝',
    id: 'allinpay',
},{
    name: '通商云',
    id: 'allinpay-yst',
},{
    name: '汇付天下',
    id: 'hftx_pay',
},]

export default ({type='add',editRow={},reload})=>{
    const [payType,setPayType] = useState('')
    const [visible,setVisible] = useState(false);
    const [isCert,setIsCert] = useState(false);
    const [isCert2,setIsCert2] = useState(false);
    const [form] =Form.useForm();
    const onFinish = (values) => {
        console.log("values:",values)
        const formdata = new FormData();
        if(values.file && values.file[0]){
            formdata.append('file', values.file[0].originFileObj);
        }
        if(values.file2 && values.file2[0]){
            formdata.append('file2', values.file2[0].originFileObj);
        }
        if(values.payType === 'allinpay-yst'){
            values.privateKeyPath = values.privateKeyPath[0].url;
            values.tlPublicKeyUrl = values.tlPublicKeyUrl[0].url;
        }
        formdata.append('payChannel',values.payChannel);
        const detail = Object.assign(values,{});
        delete detail.payChannel;
        delete detail.file;
        delete detail.file2
        formdata.append('detail',JSON.stringify(detail));
        $.ajax({
            url:type==='add'?'/api/shop/profile/pay/create':`/api/shop/profile/pay/${editRow.id}`,
            method: 'POST',
            async: false,
            cache: false,
            contentType: false,
            processData: false,
            "Content-Type": 'multipart/form-data',
            data: formdata,
            success: (res)=>{
                if(res.data){
                    message.success("操作成功")
                    setVisible(false);
                    form.resetFields();
                    setPayType("");
                    reload();
                } else {
                    message.error(res.errorMsg)
                }
            }
        })
    };
    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    const normFile = e => {
        if (Array.isArray(e)) return e
        return e.fileList
    }

    useEffect(()=>{
        if(editRow.payChannel && visible){
            setPayType(editRow.payChannel);
            if(editRow.payChannel === 'allinpay-yst'){
                editRow.privateKeyPath = [{url:editRow.privateKeyPath}];
                editRow.tlPublicKeyUrl = [{url:editRow.tlPublicKeyUrl}];
            }
            form.setFieldsValue(Object.assign({
                payChannel: editRow.payChannel,
            },editRow.detail));
        }
        if(visible){
            setIsCert(!!(editRow.detail && editRow.detail.certFileStatus));
            setIsCert2(!!(editRow.detail && editRow.detail.certPath && editRow.detail.tlCertPath))
        }
    },[editRow,visible])


    return (<Fragment>

        {
            type==='add'?
            <Button type="primary" style={{marginBottom:'20px'}} onClick={()=> setVisible(true)}>新增支付方式</Button>
            :
            <Button type="link"  onClick={()=> setVisible(true)}>编辑</Button>
        }

        <Modal
            title={type==='add'?"新增支付方式":"编辑支付方式"}
            visible={visible}
            onOk={()=>{
                form.submit()
            }}
            okText={"确定"}
            cancelText={"取消"}
            onCancel={(e)=>{
                form.resetFields();
                setPayType("");
                setVisible(false);}}
        >
            <Alert
                message="配置前请仔细阅读操作指引"
                type="info"
                action={
                    <Space direction="vertical">
                        <Button size="small" type="primary"
                           onClick={()=>{
                            window.open("/seller/show-guide-text")
                            // window.location.href = '/seller/show-guide-text'
                           }}
                        >
                            查看指引
                        </Button>
                    </Space>
                }
            />
            <Form
                labelCol={{ span: 10,}}
                wrapperCol={{span: 14,}}
                style={{maxWidth: 700,padding:'20px' }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="on"
                form={form}
            >
                <Form.Item
                    label="支付方式"
                    name="payChannel"
                    rules={[{ required: true, message: '请选择支付方式' }]}
                >
                    <Select onChange={(e)=>{
                        setPayType(e);
                        form.resetFields();
                        form.setFieldsValue({
                            payChannel: e
                        })
                    }}>
                        {
                            paysList.map((item,index)=>(
                                <Select.Option key={index} value={item.id}>{item.name}</Select.Option>
                            ))
                        }
                    </Select>
                </Form.Item>
                {
                    payType === 'allinpay' && <Fragment>
                        <Form.Item
                            label="商户号"
                            name="cusid"

                            rules={[{ required: true, message: '请输入商户号' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="appid"
                            name="appid"
                            rules={[{ required: true, message: '请输入appid' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={255}/>
                        </Form.Item>
                        <Form.Item
                            label="签名方式"
                            name="signType"
                            rules={[{ required: true, message: '请选择签名方式' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Select>
                                <Select.Option value={"RSA"}>RSA</Select.Option>
                                <Select.Option value={"SM2"}>SM2</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="公钥"
                            name="publicKey"
                            rules={[{ required: true, message: '请输入公钥' }
                                // ,{
                                //     validator(_,value){
                                //         if(!value) return Promise.resolve('');
                                //         // if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                //         //     return Promise.reject('仅支持输入英文和数字')
                                //         // }
                                //         return Promise.resolve('')
                                //     }
                                // }
                            ]}
                        >
                            <Input maxLength={2048}/>
                        </Form.Item>
                        <Form.Item
                            label="私钥"
                            name="privateKey"
                            rules={[{ required: true, message: '请输入私钥' }
                                // ,{
                                //     validator(_,value){
                                //         if(!value) return Promise.resolve('');
                                //         if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                //             return Promise.reject('仅支持输入英文和数字')
                                //         }
                                //         return Promise.resolve('')
                                //     }
                                // }
                            ]}
                        >
                            <Input maxLength={1024}/>
                        </Form.Item>
                    </Fragment>
                }
                {
                    payType === 'alipay' && <Fragment>
                        <Form.Item
                            label="支付宝合作者身份id"
                            name="pid"
                            rules={[{ required: true, message: '请输入支付宝合作者身份id' }]}
                        >
                            <Input maxLength={32}/>
                        </Form.Item>
                        <Form.Item
                            label="支付秘钥"
                            name="paySecret"
                            rules={[{ required: true, message: '请输入支付秘钥' }]}
                        >
                            <Input maxLength={1024}/>
                        </Form.Item>
                    </Fragment>
                }
                {
                    payType === 'wechatpay' && <Fragment>
                        <Form.Item
                            label="商户号"
                            name="mchId"
                            rules={[
                                { required: true, message: '请输入商户号' },
                                { validator(_,value){
                                    if(!value) return Promise.resolve('')
                                    if(!/^\d{8,10}$/.test(value)){
                                        return Promise.reject('请输入8-10的数字')
                                    }
                                    return Promise.resolve('')
                                } }
                            ]}
                        >
                            <Input maxLength={32}/>
                        </Form.Item>
                        <Form.Item
                            label="APPID"
                            name="appId"
                            rules={[{ required: true, message: '请输入APPID' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input  maxLength={255}/>
                        </Form.Item>
                        <Form.Item
                            label="支付密钥"
                            name="paySecret"
                            rules={[{ required: true, message: '请输入支付密钥' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={1024}/>
                        </Form.Item>
                        <Form.Item
                            label="证书"
                            name="file"
                            valuePropName="fileList"
                            getValueFromEvent={normFile}
                            required={true}
                            rules={!isCert?[
                                {required: true,message:'请上传证书'}]:
                                    [{
                                    validator(_,value){
                                        console.log("file:",value,editRow)
                                        return Promise.resolve('')
                                    }
                                }
                            ]}
                        >
                            <Upload
                                name='file'
                                // action= 'https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188'
                                headers= {{
                                  authorization: 'authorization-text',
                                }}
                                accept={'.p12'}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined />}>上传证书</Button>
                            </Upload>
                        </Form.Item>
                    </Fragment>
                }
                {
                    payType === 'umf' && <Fragment>
                        <Form.Item
                            label="client_id"
                            name="clientId"
                            rules={[{ required: true, message: '请输入client_id' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={255} />
                        </Form.Item>
                        <Form.Item
                            label="client_secret"
                            name="clientSecret"
                            rules={[{ required: true, message: '请输入client_secret' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input  maxLength={32} />
                        </Form.Item>
                    </Fragment>
                }
                {
                    payType === 'allinpay-yst' && <Fragment>
                        <Form.Item
                            label="appId"
                            name="appId"

                            rules={[{ required: true, message: '请输入appId' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="密钥"
                            name="secretKey"

                            rules={[{ required: true, message: '请输入密钥' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="私钥证书文件对应的密码"
                            name="certPassword"

                            rules={[{ required: true, message: '请输入私钥证书文件对应的密码' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="托管代付小B账户的分账百分比"
                            name="bizUserPercent"

                            rules={[{ required: true, message: '请输入托管代付小B账户的分账百分比' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="账户集编号"
                            name="accountSetNo"

                            rules={[{ required: true, message: '请输入账户集编号' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="收银宝集团商户号"
                            name="vspMerchantid"

                            rules={[{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="收银宝商户号"
                            name="vspCusid"

                            rules={[{ required: true, message: '请输入收银宝商户号' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="商户号appid"
                            name="vspAppId"

                            rules={[{ required: true, message: '请输入商户号appid' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={32} />
                        </Form.Item>
                        <Form.Item
                            label="终端信息"
                            name="vspTermid"

                            rules={[{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9,*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字和,')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={512} />
                        </Form.Item>
                        <Form.Item
                            label="公钥证书"
                            name="file"
                            valuePropName="fileList" getValueFromEvent={normFile}
                            rules={!isCert2?[
                                {required: true,message:'请上传公钥证书'}]:
                                    [{
                                    validator(_,value){
                                        console.log("file:",value,editRow)
                                        return Promise.resolve('')
                                    }
                                }
                            ]}
                        >
                            <Upload
                                name='file'
                                // action= 'https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188'
                                headers= {{
                                  authorization: 'authorization-text',
                                }}
                                // accept={'.p12'}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined />}>上传证书</Button>
                            </Upload>
                        </Form.Item>
                        <Form.Item
                            label="私钥证书"
                            name="file2"
                            valuePropName="fileList" getValueFromEvent={normFile}
                            rules={!isCert2?[
                                {required: true,message:'请上传私钥证书'}]:
                                    [{
                                    validator(_,value){
                                        console.log("file:",value,editRow)
                                        return Promise.resolve('')
                                    }
                                }
                            ]}
                        >
                            <Upload
                                name='file2'
                                // action= 'https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188'
                                headers= {{
                                  authorization: 'authorization-text',
                                }}
                                // accept={'.p12'}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined />}>上传证书</Button>
                            </Upload>
                        </Form.Item>
                    </Fragment>
                }
                {
                    payType === 'hftx_pay' && <Fragment>
                        <Form.Item
                            label="商户应用号"
                            name="appId"
                            rules={[{ required: true, message: '请输入商户应用号' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={255}/>
                        </Form.Item>
                        <Form.Item
                            label="签名秘钥"
                            name="signSecret"
                            rules={[{ required: true, message: '请输入签名秘钥' },{
                                validator(_,value){
                                    if(!value) return Promise.resolve('');
                                    if(!/^[a-zA-Z0-9*]*$/.test(value)){
                                        return Promise.reject('仅支持输入英文和数字')
                                    }
                                    return Promise.resolve('')
                                }
                            }]}
                        >
                            <Input maxLength={1024}/>
                        </Form.Item>
                        <Form.Item
                            label="签名类型"
                            name="signType"
                            rules={[{ required: true, message: '请选择签名类型' }]}
                        >
                            <Select>
                                <Select.Option value={"HMAC_SHA256"}>HMAC_SHA256</Select.Option>
                                <Select.Option value={"MD5"}>MD5</Select.Option>
                                <Select.Option value={"SM3"}>SM3</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="网关地址"
                            name="serviceUrl"
                            rules={[{ required: true, message: '请输入网关地址' }]}
                        >
                            <Input maxLength={255}/>
                        </Form.Item>
                    </Fragment>
                }
            </Form>
        </Modal>
    </Fragment>)
}

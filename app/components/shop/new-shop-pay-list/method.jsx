import request from "../../utils/plugins/axios/request"

export function fetchHftxBank(data) {
  return new Promise((resolve, reject) => {
    request({
      url: "/pay/api/unipay/hftx/bank/select",
      method: "POST",
      miniShopConfig: {
        isNewServer: true
      },
      data,
      needMask: false,
      success: resolve,
      fail: reject,
    })
  })
}

export function fetchUnitebank(data) {
  return new Promise((resolve, reject) => {
    request({
      url: "/pay/api/unipay/hftx/unitebank/select",
      method: "POST",
      miniShopConfig: {
        isNewServer: true
      },
      data,
      needMask: false,
      success: resolve,
      fail: reject,
    })
  })
}

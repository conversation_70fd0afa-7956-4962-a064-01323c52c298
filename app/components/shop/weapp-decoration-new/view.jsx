import BarrageManage from "../marketing/barrage-manage/view";
import Category from "../weapp-decoration/category";
import CustomPage from "./mini-program/tab-page/custom-page";
import Design from "./design";
import DesignPage from "./design";
import InvitePage from "./mini-program/tab-page/invite-page";
import LoginPage from "./mini-program/tab-page/login-page";
import ModalPage from "./mini-program/tab-page/modal-page";
import RegisterPage from "./mini-program/tab-page/register-page";
import SubsidyTaskPage from "./mini-program/tab-page/subsidy-task-page";
import request from "../../utils/plugins/axios/request";
import LaunchScreen from "./mini-program/tab-page/launch-screen";
import ContentCommunity from "./mini-program/tab-page/content-community";

const {Tabs} = antd;

const {Spa, SpaConfigProvider} = dtComponents
const {useState, useMemo} = React;
// HOME("首页", 10),
// CUSTOM("自定义页", 20),
// REGISTER("付费注册页", 30),
// LOGIN_GUIDE("登录引导页", 40),
// TASK("补贴任务页", 50),
// FISSION_COUPON("裂变券邀请页", 60),
// CATEGORY("分类页", 70),
// MINE("我的", 80),
// ADD_HELPER("添加小助手", 90),


const App = () => {

  const [activeKey, setActiveKey] = useState("1");
  const [isCustomPage, setIsCustomPage] = useState(false)
  const [customPageConfig, setCustomPageConfig] = useState(null)

  const items = useMemo(() => {
    return [
      {
        key: "1",
        label: "首页",
        children: <DesignPage   pageUrl={ "pages/index/index"} setActiveKey={setActiveKey} pageType={10} isCustomPage={false} defalutData={true}/>,
      },
      {
        key: "content-community",
        label: "内容社区",
        children: <ContentCommunity  />,
      },
      {
        key: "category",
        label: "分类",
        children: <Category/>,
      },
      {
        key: "2",
        label: "弹框页",
        children: <ModalPage setActiveKey={setActiveKey} pageType={10} isCustomPage={false}/>,
      },
      {
        key: "3",
        label: "付费注册页",
        children: <RegisterPage pageType={30} isCustomPage={false} defalutData={false}/>,
      },
      {
        key: "4",
        label: "登录引导页",
        children: <LoginPage pageType={40} isCustomPage={false} defalutData={false}/>,
      },
      {
        key: "5",
        label: "补贴任务页",
        children: <SubsidyTaskPage pageType={50} isCustomPage={false} defalutData={false}/>,
      },
      {
        key: "6",
        label: "裂变券邀请页",
        children: <InvitePage pageType={60} isCustomPage={false} defalutData={false}/>,
      },
      {
        key: "7",
        label: "自定义页面",
        children: <CustomPage pageType={20} isCustomPage={true} defalutData={true} setIsCustomPage={setIsCustomPage}
                              setCustomPageConfig={setCustomPageConfig}/>,
      },
      {
        key: "8",
        label: "弹幕管理",
        children:
          <SpaConfigProvider spa={new Spa({
            _openPage: () => {
            }, request: {
              request: (params) => {
                const obj = Object.assign(params, {
                  type: 'POST',
                  contentType: 'application/json',
                })
                request(obj)
              }
            }
          })}>
            <BarrageManage/>
          </SpaConfigProvider>,
      },
      {
        key: "9",
        label: "开机屏",
        children: <LaunchScreen pageType={100} isCustomPage={false} defalutData={false}/>,
      },
    ];
  }, [setIsCustomPage, setCustomPageConfig])

  return <div style={{height: '100%' ,minWidth: '1200px'}}>
    {
      isCustomPage ? <Design
          isCustomPage={isCustomPage}
          setIsCustomPage={setIsCustomPage}
          setActiveKey={setActiveKey}
          setCustomPageConfig={setCustomPageConfig}
          customPageConfig={customPageConfig}
        />
        : <Tabs style={{backgroundColor: "#fff",paddingLeft: "24px"}} defaultActiveKey="1" activeKey={activeKey} onChange={setActiveKey}>
          {items.map((item) => (
            <Tabs.TabPane key={item.key} tab={item.label}>
              {item.children}
            </Tabs.TabPane>
          ))}
        </Tabs>
    }
  </div>
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          type: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(params.data),
          shopId: sessionStorage.shopId
        })
        $.ajax(obj)
      }
    }
  })}>
    <App/>
  </SpaConfigProvider>, document.getElementById("weapp-decoration-new")
);

@font-face {
  font-family: 'menufont';  /* project id 1257720 */
  src: url('//at.alicdn.com/t/font_1257720_t06iecujlpd.eot');
  src: url('//at.alicdn.com/t/font_1257720_t06iecujlpd.eot?#iefix') format('embedded-opentype'),
  url('//at.alicdn.com/t/font_1257720_t06iecujlpd.woff2') format('woff2'),
  url('//at.alicdn.com/t/font_1257720_t06iecujlpd.woff') format('woff'),
  url('//at.alicdn.com/t/font_1257720_t06iecujlpd.ttf') format('truetype'),
  url('//at.alicdn.com/t/font_1257720_t06iecujlpd.svg#iconfont') format('svg');
}
.bd{
    position : relative;
}
.weapp-decoration-new-menu{
    position: absolute;
    border-right: 1px solid #e7e7eb;
    width: 256px;
    .group{
        margin:0 80px;
        padding: 8px 0;
        border-top:solid 1px #e7e7eb;
        &:first-child{
            border:none;
            padding-top:0;
        }
        *{
            color: #353535;
        }

        .main-title{
            margin:0 -80px;
            span{
                font-family: menufont;
                font-size: 20px;
                margin-right: 28px;
                color:#555;
            }
            display: block;
            padding: 0 34px;
            line-height: 50px;
            font-size: 18px;
            cursor: pointer;
        }
        .sub-title-list{
            margin:0 -80px;
            a {
                padding-left: 82px;
                line-height: 36px;
            }
        }
        .main-title:hover , .sub-title-list a:hover{
            background-color: #eff1f3;
        }
        .selected{
            color: #1aad19;
            span{
                color:#1aad19;
            }
        }
        a{
            display: block;
            text-decoration: none;
        }
    }
}


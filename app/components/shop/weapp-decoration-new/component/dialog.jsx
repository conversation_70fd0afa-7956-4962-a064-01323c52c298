
function dialog(ComponentClass){
    var id = 'id-' + parseInt(Math.random() * 999999999) ;
    class Dialog extends React.Component{
        constructor(props){
            super(props);
            this.state = {
                data : props.data
            };
        }
        componentWillReceiveProps(props){
            this.setState({
                data : props.data
            })
        }
        componentDidMount(){
            // this.setState(this.props.data);
        }
        close(){
            $('#' + id).hide();
        }
        render(){
            var self = this;
            return (
                <div>
                    <div className='weapp-decoration-new-dialog' id={id}>
                        <div className='mask'></div>
                        <div className='dialog-content'>
                            <div className='close' onClick={this.props.methods.close || this.props.methods.cancel}>&#xe617;</div>
                            <ComponentClass data={this.state.data} methods={this.props.methods}></ComponentClass>
                        </div>
                    </div>
                </div>
            );
        }
    };
    Dialog.show = function(){
        $('#' + id).show();
    }
    Dialog.hide = function(){
        $('#' + id).hide();
    }
    Dialog.close = function(){
        $('#' + id).hide();
    }
    return Dialog;
}
export default dialog

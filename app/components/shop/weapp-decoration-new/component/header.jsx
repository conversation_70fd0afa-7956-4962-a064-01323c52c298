

class Header extends React.Component{
    constructor(props){
        super(props);
        this.state = {
            count : 10
        };
    }
    componentWillMount(){
    }
    componentDidMount(){
    }

    componentWillUpdate(){
    }
    componentDidUpdate(){
    }
    componentWillUnmount(){
    }
    render(){
        return (
            <div className='weapp-decoration-new-header nav'>
                <div className='logo'>Yang 800</div>
                <div className='user'>欢迎 <span>文拯</span> <div className='logout'>退出</div></div>
            </div>
        )
    }
}

export default Header;

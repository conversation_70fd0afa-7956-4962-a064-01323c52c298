@font-face {
  font-family: 'iconfont';  /* project id 1257720 */
  src: url('//at.alicdn.com/t/font_1257720_rt3xcwz2ji.eot');
  src: url('//at.alicdn.com/t/font_1257720_rt3xcwz2ji.eot?#iefix') format('embedded-opentype'),
  url('//at.alicdn.com/t/font_1257720_rt3xcwz2ji.woff2') format('woff2'),
  url('//at.alicdn.com/t/font_1257720_rt3xcwz2ji.woff') format('woff'),
  url('//at.alicdn.com/t/font_1257720_rt3xcwz2ji.ttf') format('truetype'),
  url('//at.alicdn.com/t/font_1257720_rt3xcwz2ji.svg#iconfont') format('svg');
}

/**
    日历组件图标替换
**/
.glyphicon{
    font-family: 'iconfont';
}
.glyphicon-arrow-left:before {
    content: "\e6e9";
}
.glyphicon-arrow-right:before {
    content: "\e6ea";
}


.weapp-decoration-new-header {
    padding: 0 40px;
    position: relative;
    height: 60px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(150,150,150,0.3);
    margin-bottom:30px;
    .logo{
        font-size: 24px;
        line-height: 60px;
        float: left;
        color: #353535;
        cursor: pointer;
    }
    .user{
        float: right;
        font-size: 16px;
        line-height: 60px;
        .logout{
            float: right;
            cursor: pointer;
            margin-left: 15px;
        }
    }
}
.bd{
    .main{
        margin-left:256px;
        padding:0 60px;
        .page-title{
            font-size: 20px;
            line-height: 60px;
            color:#353535;
            margin-bottom: 20px;

        }
        .panel{
            border-radius:6px;
            background:#fff;
            min-height: 100px;
            padding:20px 30px;
            margin-bottom: 35px;
            .panel-hd{
                margin-bottom: 20px;
                position: relative;
                .title{
                    font-size:16px;
                    color:#666;
                    span.strong{
                        margin: 10px;
                        color:#ff5500;
                    }
                }
                .sub-title{
                    font-size: 12px;
                    color:#888;
                }
            }
        }
        a , .link{
            color:#06f;
            text-decoration: none;
            margin:0 10px;
            cursor:pointer;
        }
        a:hover , .link:hover{
            color:#08f;
        }
    }
}
.form-inline{
    padding:10px 0;
    label{
        width: 150px;
        text-align: right;
        margin-right:20px;
    }
    .form-control{
        width: 390px;
    }
    .long{
        width:600px;
    }
    .short{
        width: 280px;
    }

}
.btn{
    margin:0 20px;
    padding-left:15px;
    padding-right:15px;
    min-width: 80px;
    outline: none !important;
    cursor: pointer;
}
.btn-primary{
    background:#06f;
    border-color:#07f;
}
.btn-primary:hover{
    background:#05f;
    border-color:#06f;
}
.btn-cool{
    background-image: linear-gradient(135deg,#ff971b,#ff5000);
    border-color:#f60 ;
    color:#fff;
}
.btn-cool:hover{
    background-image: linear-gradient(135deg,#ffa81b,#ff7000);
    border-color:#f60;
    color:#fff;
}


.table{
    word-break:break-all;
    word-wrap:break-all;
    thead{
        border:solid 1px #ddd;
        tr{
            th{
                background:#eee;
                line-height: 30px;
                border:none;
                max-width: 200px;
                text-align: center;
            }
        }
    }
    tbody{
        tr{
            td{
                border-top:none;
                border-bottom:solid 1px #ddd;
                padding:12px 8px;
                max-width: 200px;
                text-align: center;
            }
        }
        tr:last-child{
            td{
                border:none;
            }
        }
    }
}
.mask{
    position: fixed;
    left:0;
    right:0;
    top:0;
    bottom: 0;
    background: #000;
    opacity: 0.6;
    z-index:99;
}

// 弹出框
.modal{
    display: block;
    .mask{
        z-index:200;
    }

    .modal-dialog{
        position:fixed;
        top: 35%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index:201;
        width: 500px;
        .modal-body{
            padding-top:26px;
            -webkit-font-smoothing: antialiased;
            font-weight: normal;
            .icon{
                float: left;
                font-family:iconfont;
                font-size: 30px;
                margin:0px 15px 10px 5px;
            }
            .success{
                color: rgb(72 , 187 , 45);
                margin-top:-2px;
                font-size: 32px;
                &:before{
                    content : '\e613';
                }
            }
            .error{
                margin:0px 15px 10px 5px;
                color: #ff4040;
                &:before{
                    content : '\e6c6';
                }
            }
            .warn{
                color: #ff8000;
                &:before{
                    content : '\e60e';
                }
            }
            span{
                color:#f40;
                font-size:16px;
                padding:5px;
            }
            font-size: 16px;
            line-height: 24px;
            min-height: 80px;
        }
        .modal-footer{
            text-align:center;
        }
    }
}
//loading
.wait{
    .mask{
        z-index:300;
    }
    img{
        display: block;
        width: 80px;
        height: 80px;
        position: fixed;
        left: 50%;
        top:50%;
        margin-left:-40px;
        margin-right:-40px;
        z-index:301;
    }
}



















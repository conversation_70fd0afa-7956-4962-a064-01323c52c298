
import request from "../../../utils/plugins/axios/request";

const {useState, useEffect} = React;
const {Modal, Tree, Pagination, Input, Button, Checkbox} = antd;
const {SearchOutlined} = icons;


const PicasaDialog = ({data, showDialog, methods}) => {
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 20,
  });
  const [src, setSrc] = useState(data.src);
  const [list, setList] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [expandedKeys, setExpandedKeys] = useState([]);
  useEffect(() => {
    // 初始化时获取目录树
    if(showDialog){
      fetchTreeData();
    }
  }, [showDialog]);

  useEffect(() => {
    // 当选中目录变化时，重新获取素材列表
    if (selectedGroupId !== null) {
      fetchMaterialList(1);
    }
  }, [selectedGroupId]);

  // 获取目录树数据
  const fetchTreeData = async () => {
    try {
      const result = await new Promise((resolve, reject) => {
        request({
          url: '/mall-admin/api/material_group/listMaterialGroupTree',
          method: 'POST',
          data: {},
          success: (data) => {
            resolve(data);
          },
          fail: (error) => {
            console.error('获取目录树失败:', error);
            reject(error);
          }
        });
      });

      if (result && result.children) {
        // 转换数据格式为 Tree 组件需要的格式
        const convertToTreeData = (nodes) => {
          if (!Array.isArray(nodes)) return [];

          return nodes.map(node => ({
            title: node.groupName || node.label,
            key: `${node.id}`,
            children: node.children ? convertToTreeData(node.children) : []
          }));
        };

        const formattedData = convertToTreeData(result.children || []);
        setTreeData(formattedData);

        // 默认选中第一个目录
        if (result.children && result.children.length > 0) {
          const firstGroup = result.children[0];
          setExpandedKeys([`${firstGroup.id}`]);
          if (firstGroup.children && firstGroup.children.length > 0) {
            setSelectedGroupId(firstGroup.children[0].id+"");
          } else {
            setSelectedGroupId(firstGroup.id);
          }
        }
      }
    } catch (error) {
      console.error('获取目录树失败:', error);
    }
  };

  // 获取素材列表
  const fetchMaterialList = async (page = 1) => {
    if (!selectedGroupId) return;

    setLoading(true);
    try {
      const result = await new Promise((resolve, reject) => {
        request({
          url: '/mall-admin/api/material_group/listMaterial',
          method: 'POST',
          data: {
            groupId: selectedGroupId,
            currentPage: page,
            pageSize: pagination.pageSize,
            materialName: searchKeyword
          },
          success: (res) => resolve(res),
          fail: (error) => reject(error)
        });
      });

      if (result) {
        const materialList = result.dataList || [];
        const formattedList = materialList.map(item => ({
          src: item.materialUrl,
          status: 1,
          id: item.id,
          name: item.materialName
        }));

        setList(formattedList);
        setPagination(result.page);
      }
    } catch (error) {
      console.error('获取素材列表失败:', error);
    } finally {
      setLoading(false);
    }
  };
  // 处理目录树选择
  const handleTreeSelect = (selectedKeys) => {
    if (selectedKeys.length > 0) {
      setSelectedGroupId(selectedKeys[0]);
    }
  };

  // 处理分页变化
  const handlePageChange = (page) => {
    fetchMaterialList(page);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchMaterialList(1, searchKeyword);
  };

  // 处理搜索输入变化
  const handleSearchChange = (e) => {
    setSearchKeyword(e.target.value);
  };

  // 处理图片选择（勾选框点击）
  const handleImageSelect = (item) => {
    const isSelected = src === item.src;
    if (isSelected) {
      setSrc(''); // 取消选中
    } else {
      setSrc(item.src); // 选中
    }
  };

  // 处理单张图片点击（用于预览）
  const select = (item) => {
    setSrc(item.src);
  };

  // 确认选择
  const confirm = () => {
    methods.confirm(src);
  };

  // 跳转到内容素材库
  const goToMaterialLibrary = () => {
    // 这里需要根据实际路由进行跳转
    window.open('/seller/content-material-library', '_blank');
  };

   console.log("treeData", treeData);
  return (
    <Modal
      width={960}
      className="picasa-dialog"
      title="选择图片"
      open={showDialog}
      footer={null}
      onCancel={methods.cancel}
    >
      <div className="dialog-bd" style={{ display: 'flex', height: '620px' }}>
        {/* 左侧目录树 */}
        <div style={{ width: '208px', padding:'24px', borderRight: '1px solid #f0f0f0',overflow: 'auto'}}>
          <Tree
            treeData={treeData}
            onExpand={(keys) => {
              setExpandedKeys(keys);
            }}
            expandedKeys={expandedKeys}
            onSelect={handleTreeSelect}
            selectedKeys={selectedGroupId ? [selectedGroupId] : []}
          />
        </div>

        {/* 右侧图片列表 */}
        <div style={{ flex: 1, padding: '24px', display: 'flex', flexDirection: 'column' }}>
          {/* 搜索框 */}
          <div style={{ marginLeft: '16px' }}>
            <Input.Search
              style={{width:268}}
              placeholder="请输入图片名称"
              value={searchKeyword}
              onChange={handleSearchChange}
              onSearch={handleSearch}
              size="default"
            />
          </div>

          <div className="list" style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(118px, auto))' }}>
            {loading ? (
              <div style={{ width: '100%', textAlign: 'center', padding: '50px' }}>加载中...</div>
            ) : (
              list.map((item, index) => {
                const isPreview = src === item.src;
                return (
                  <div
                    className="pic"
                    key={index}
                    style={{
                      backgroundImage: `url(${item.src}?x-oss-process=image/resize,l_120)`,
                      width: '120px',
                      height: '120px',
                      margin: '5px',
                      cursor: 'pointer',
                      outline: isPreview ? '2px solid #1966ff' : '1px solid #d9d9d9',
                      position: 'relative'
                    }}
                    onClick={() => select(item)}
                  >
                    {/* 右上角勾选框 */}
                    <div
                      style={{
                        position: 'absolute',
                        top: '4px',
                        right: '4px',
                        zIndex: 10
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleImageSelect(item);
                      }}
                    >
                      <Checkbox checked={isPreview} />
                    </div>
                    {isPreview && <div className="select"></div>}
                  </div>
                );
              })
            )}
          </div>

          {/* 分页组件 */}
          <div className="pagination-area" >
            <Pagination
              current={pagination.currentPage}
              pageSize={pagination.pageSize}
              total={pagination.totalCount}
              onChange={handlePageChange}
              showSizeChanger={false}
              showTotal={(totalCount) => `共 ${totalCount} 条`}
            />
          </div>
        </div>
      </div>

      {/* 自定义底部 */}
      <div style={{
        borderTop: '1px solid #f0f0f0',
        padding: '16px 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ color: '#666' }}>
        <span style={{color:'rgb(137, 139, 143'}}>可前往内容素材库进行图片/视频的上传或清理，</span>
           <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              goToMaterialLibrary();
            }}
            style={{ color: '#1890ff', textDecoration: 'none' }}
          >
            内容素材库
          </a>
        </div>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button onClick={methods.cancel}>取消</Button>
          <Button
            type="primary"
            onClick={confirm}
            // disabled={!src}
          >
            确定
          </Button>
        </div>
      </div>
    </Modal>
  );
};


class Picasa extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      src: props.data.src,
    };
  }

  componentWillReceiveProps(props) {
    this.setState({
      src: props.data.src,
    });
  }

  confirm(src) {
    this.setState({
      src: src,
      showDialog: false,
    });
    this.props.methods.updateSrc(src);
  }

  cancel() {
    this.setState({
      showDialog: false,
    });
  }

  select() {
    this.setState({
      showDialog: true,
    });
  }

  render() {
    var style = {
      backgroundImage: `url(${(this.state.src || "#") + "?x-oss-process=image/resize,l_200"})`,
      width: this.props.data.style.width,
      height: this.props.data.style.height,
    };
    console.log("showDialog", this.state.showDialog)
    return (
      <div className="picasa">
        {this.state.showDialog ? (
          <PicasaDialog
            showDialog={this.state.showDialog}
            data={{src: this.state.src}}
            methods={{confirm: this.confirm.bind(this), cancel: this.cancel.bind(this)}}></PicasaDialog>
        ) : (
          ""
        )}
        <div className="thumb" onClick={this.select.bind(this)} style={style}>
          {this.state.src ? "" : <div className="icon"></div>}
        </div>
      </div>
    );
  }
}

export default Picasa;

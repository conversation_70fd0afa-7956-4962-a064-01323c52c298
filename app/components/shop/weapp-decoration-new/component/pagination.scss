.weapp-decoration-pagination {
  display: flex;
  margin: 0;
  padding: 15px;
  justify-content: center;
  align-items: center;
  -webkit-font-smoothing: antialiased;

  * {
    display: inline-block;
    font-size: 16px;
    line-height: 16px;
  }

  .panel-page {
    .prev, .next, .ellipsis {
      font-family: 'iconfont';
    }

    .prev:before {
      content: '\e64a';
    }

    .next:before {
      content: '\e648';
    }

    .ellipsis:before {
      content: '\e705';
    }

    .ellipsis, .current {
      padding-left: 0;
      padding-right: 0;
      margin: 0;
    }

    div, span {
      display: inline-block;
      padding: 5px;
      border: solid 1px #aaa;
      margin: 0 5px;
      border-radius: 3px;
      cursor: pointer;
      background: #fff;
      text-align: center;
      color: #555;
      min-width: 30px;
    }

    span.current {
      color: #06f;
      padding-left: 5px;
      padding-right: 5px;
    }

    div:hover {
      border-color: #666;
      color: #000;
    }

    span {
      border: none;
      background: none;
      cursor: auto;
      padding-left: 0;
      padding-right: 0;
    }
  }

  .panel-page-size {
    margin: 0 10px;

    select {
      display: inline-block;
      width: 60px;
      text-align: center;
      margin: 0 6px;
      height: 28px;
    }
  }

  .panel-jump {
    margin-left: 10px;
    display: flex;
    align-items: center;

    input {
      width: 50px;
      text-align: center;
      padding: 0;
      margin: 0 6px;
      height: 28px;
    }

    .btn {
      width: 36px;
      padding: 0;
      margin: -5px 0 0 5px;
      height: 28px;
      line-height: 28px;
      min-width: auto;
    }
  }
}













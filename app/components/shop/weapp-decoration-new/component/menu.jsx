

class Menu extends React.Component{
    constructor(props){
        super(props);
        this.state = {
        };
    }
    componentWillMount(){
    }
    componentDidMount(){
        var pathname = window.location.pathname.split('/').reverse()[0];
        $(`.menu a[href*="${pathname}"]`).addClass('selected');
    }
    componentWillUpdate(){
    }
    componentDidUpdate(){
    }
    componentWillUnmount(){
    }
    render(){
        return (
            <div className='weapp-decoration-new-menu'>
                <div className='group'>
                    <a className='main-title' href='space.htm'>
                        <span>&#xe604;</span>
                        图片空间
                    </a>
                </div>
                <div className='group'>
                    <div className='main-title'>
                        <span>&#xe6ac;</span>
                        小程序
                    </div>
                    <div className='sub-title-list'>
                        <a href='design.htm'>页面装修</a>
                        <a href='add-shop.htm'>小程序设置</a>
                    </div>
                </div>

            </div>
        )
    }
}
export default Menu;

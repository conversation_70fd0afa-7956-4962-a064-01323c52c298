const {message} = antd
/**
    data参数：
    current : 当前页
    total : 共多少页
    pageSize : 一页多少条
    methods ：
    //跳到第几页，每页多少条
    jumpTo(page , pageSize);
**/

class Pagination extends React.Component{
    constructor(props){
        super(props);
        if(props.data){
            props.data.current1 = props.data.current;
            this.state = props.data;
        }

    }
    componentWillReceiveProps(props){
        this.setState(props.data);
    }
    componentWillMount(){
    }
    componentDidMount(){
    }
    componentWillUpdate(){
    }
    componentDidUpdate(){
    }
    componentWillUnmount(){
    }
    setPageSize(e){
        var pageSize = parseInt(e.target.value);
        this.props.methods.jumpTo(1 , pageSize);
    }
    setCurrentPage(e){
        var current1 = e.target.value;
        var reg = /^\d{0,4}$/;
        if(reg.test(current1)){
            this.setState({
                current1 : current1
            })
        }
    }
    jumpTo(page){
        if(page < 1 ){
          message.warning('已是第一页')
            return ;
        }
        if(page == this.state.total + 1){
            message.warning('已是最后一页')
            return ;
        }
        if(page > this.state.total + 1){
           message.warning('你输入的页数过大')
            return ;
        }
        this.props.methods.jumpTo(page , this.state.pageSize);
    }
    render(){
        var self = this;
        if(!this.state){
            return (<div />);
        }
        let [current , total , pageSize] = [parseInt(this.state.current) , parseInt(this.state.total) , parseInt(this.state.pageSize)];
        let list = [];
        if(total < 6){
            for(var i=1;i<total+1;i++) list.push(i);
        }
        else if(current < 5){
            for(var i=1;i<Math.min(total , 6);i++) list.push(i);
            if(total > 6) list.push('...');
            list.push(total);
        }
        else if(current > total-5 ){
            list = [1];
            if(total>6) list.push('...');
            for(var i=total-5;i<Math.min(current+5 , total);i++) list.push(i);
            list.push(total);
        }
        else{
            list=[1 , '...'];
            for(var i=Math.max(1 , current-2); i<Math.min(current+3 , total+1);i++)list.push(i);
            list.push('...');
            list.push(total);
        }
        return (
            <div className='weapp-decoration-pagination' >
                <div className='panel-page'>
                    <div className='prev' onClick={this.jumpTo.bind(this , this.state.current-1)}></div>
                        {list.map(function(i , index){
                            if(current == i ){
                                return <span className='current' key={index}>{i}</span>
                            }
                            else if(i == '...'){
                                return <span className='ellipsis' key={index}></span>
                            }
                            else{
                                return <div key={index} onClick={self.jumpTo.bind(self, i)}>{i}</div>
                            }
                        })}
                    <div className='next' onClick={this.jumpTo.bind(this , this.state.current+1)}></div>
                </div>
                {/*<div className='panel-page-size'>*/}
                {/*    每页*/}
                {/*    <select className="form-control type" value={pageSize} onChange={this.setPageSize.bind(this)}>*/}
                {/*        <option value='10'>10</option>*/}
                {/*        <option value='20'>20</option>*/}
                {/*        <option value='30'>30</option>*/}
                {/*        <option value='50'>50</option>*/}
                {/*        <option value='100'>100</option>*/}
                {/*    </select>*/}
                {/*    条*/}
                {/*</div>*/}
                <div className='panel-jump'>
                    跳到
                        <input type="text" className="form-control" onChange={this.setCurrentPage.bind(this)} value={this.state.current1} />
                    页
                    <div className='btn btn-cool' stlyle={{marginLeft:'12px'}} onClick={this.jumpTo.bind(this, this.state.current1)}>GO</div>
                </div>
            </div>
        )
    }
}
export default Pagination;

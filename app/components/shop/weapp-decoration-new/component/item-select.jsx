
import Pagination from './pagination';
import dialog from './dialog';

import request from "../../../utils/plugins/axios/request";

class ItemSelectDialog extends React.Component{
    constructor(props){
        super(props);
        this.state = {
            ids : props.data.ids ,
            pagination : {
                pageNo : 1 ,
                shopId : window.shopId ,
                pageSize : 12 ,
                total : 1
            } ,
            list : []
        };
        this.jumpTo(1);
    }
    componentWillReceiveProps(props){
        this.setState ({
            ids : props.data.ids ,
            pagination : {
                pageNo : 1 ,
                shopId : window.shopId ,
                pageSize : 12 ,
                total : 1
            } ,
            list : []
        });
        this.jumpTo(1);
    }
    componentWillMount(){

    }
    componentDidMount(){
    }
    componentWillUpdate(){
    }
    componentDidUpdate(){
    }
    componentWillUnmount(){
    }
    jumpTo(page){
        var self = this;
        var pagination = this.state.pagination;
        pagination.pageNo = page;
        pagination.ignoreItemTag=true
        let url = '/mall-admin/api/items/news/search-in-shop'
        request({
            url : url ,
            data : pagination,
            needMask : false,
            success(json){
                pagination.total = Math.ceil(json.total / pagination.pageSize);
                self.setState({
                    list : json.list,
                    pagination : pagination
                })
            }
        })
    }
    select(id){
        var ids = this.state.ids;
        for(var i=0; i<ids.length; i++){
            if(ids[i] == id){
                ids.splice(i , 1);
                id = false;
            }
        }
        if(id !== false){
            ids.push(id);
        }

        this.setState({
            ids : ids
        })
    }
    confirm(){
        this.props.methods.confirm(this.state.ids);
    }
    render(){
        var self = this;
        var pagination = this.state.pagination;
        var map = {};
        this.state.ids.map(function(id){
            map[id] = true;
        })
        return (
            <div className='item-select-dialog'>
                <div className='dialog-hd'>
                    选择商品
                </div>
                <div className='dialog-bd'>
                    <div className='list'>
                        {
                            this.state.list.map(function(item , index){
                                return (
                                    <div className='node' key={index} onClick={self.select.bind(self , item.id)}>
                                        {
                                            map[item.id] ? <div className='selected' /> :''
                                        }
                                        <div className='pic' style={{backgroundImage : 'url('+ item.mainPic +')'}}>
                                            <div className='price'>￥{item.price / 100}</div>
                                        </div>
                                        <div className='title'>{item.title}</div>
                                    </div>
                                )
                            })
                        }
                    </div>
                    <Pagination data={{current : pagination.pageNo , pageSize : pagination.pageSize , total : pagination.total}}
                    methods={{jumpTo : this.jumpTo.bind(this)}} />
                </div>
                <div className='dialog-ft'>
                    <div className='btn btn-default' onClick={this.props.methods.cancel}>取消</div>
                    <div className='btn btn-cool' onClick={this.confirm.bind(this)}>确定</div>
                </div>
            </div>
        )
    }
}

var Dialog = dialog(ItemSelectDialog);
class ItemSelect extends React.Component{
    constructor(props){
        super(props);
        this.state = {
            showDialog : false,
            ids : props.data.ids
        }
    }
    componentWillReceiveProps(props){
        this.setState({
            ids : props.data.ids
        });
    }
    confirm(ids){
        this.setState({
            ids : ids ,
            showDialog : false
        });
        this.props.methods.updateIds(ids);
    }
    cancel(){
        this.setState({
            showDialog : false
        })
    }
    selectItem(){
        this.setState({
            showDialog : true
        })
    }
    render(){
        return (
            <div className='item-select'>
                {
                    this.state.showDialog ? <Dialog data={{ids : this.state.ids}} methods={{confirm : this.confirm.bind(this) , cancel : this.cancel.bind(this)}}></Dialog> : ''
                }
                <div className='btn btn-cool add' onClick={this.selectItem.bind(this)}>{this.state.ids.length == 0 ? '选择商品' : '选择商品'}  </div>
            </div>
        )
    }
}


export default ItemSelect;

.picasa-dialog {
  .ant-modal-body {
    border-bottom: 1px solid #eeeff0;
    overflow: hidden;
    padding: 0 !important;
  }

  .dialog-hd {
    height: 50px;
    background: #e7e7e7;
    border-bottom: 1px solid #ccc;
    position: relative;
    padding-left: 20px;
    line-height: 50px;
    font-size: 18px;
    -webkit-font-smoothing: antialiased;
    z-index: 10;

    button {
      position: relative;

      span {
        font-family: "iconfont";
        margin-right: 5px;
        font-weight: bold;
      }
    }

    input {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 10;
      opacity: 0;
      width: 115px;
      height: 34px;
      cursor: pointer;
      outline: none;
    }

    select {
      position: absolute;
      left: 260px;
      width: 150px;
      top: 7px;
    }

    .tip-text {
      display: inline-block;
      font-size: 16px;
      color: #555;

      span {
        color: #f30;
        font-weight: bold;
      }
    }
  }

  .dialog-bd {
    .list {
      flex: 1 1 0%;
      overflow-y: auto;
      display: grid;
      grid-auto-flow: row dense;
      margin: 24px 0 0 10px;
      grid-gap: 16px;
    }

    .pic {
      width: 120px;
      height: 120px;
      margin: 5px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
      margin-bottom: 10px;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
      cursor: pointer;

      .upload-mask {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        opacity: 0.8;
        background: #000;

        img {
          display: block;
          width: 80px;
          height: 80px;
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -40px;
          margin-top: -40px;
          z-index: 301;
        }
      }
    }

    .pagination .panel-page-size {
      display: none;
    }
  }
  .pagination-area {
    height: 48px;
    padding-right: 24px;
    padding-top: 24px;
    text-align: right;
  }

  .ft {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    background: #e7e7e7;
    border-top: 1px solid #ccc;
  }

  .drag-drop {
    display: none;
    position: absolute;
    z-index: 100;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #000;
    opacity: 0.6;
    border: dashed 5px #f50;
  }
}

.picasa {
  .dialog {
    display: block;
  }

  .thumb {
    width: 200px;
    height: 100px;
    border: solid 1px #aaa;
    border-radius: 5px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    position: relative;
    cursor: pointer;

    .icon:before {
      font-family: "iconfont";
      font-size: 40px;
      content: "\e623";
      position: absolute;
      left: 0;
      right: 0;
      top: 50%;
      text-align: center;
      line-height: 1;
      display: block;
      margin-top: -20px;
      color: #bbb;
    }
  }
}

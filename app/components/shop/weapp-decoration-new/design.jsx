import navigation from "./mini-program/module/navigation";
import image from "./mini-program/module/image";
import search from "./mini-program/module/search";
import video from "./mini-program/module/video";
import slider from "./mini-program/module/slider";
import guide from "./mini-program/module/guide";
import itemList from "./mini-program/module/item-list";
import hotArea from "./mini-program/module/hot-area";
import layoutTwo from "./mini-program/module/layout-two";
import layoutThree from "./mini-program/module/layout-three";
import notice from "./mini-program/module/notice";
import helper from "./mini-program/module/helper";
import rankList from "./mini-program/module/rank-list";
import request from "../../utils/plugins/axios/request";
import renderModuleUtils from "./utils";
import TaskSet from "./mini-program/pageset/task-set";
import PageType from "./PageEnum";
import PageEnum from "./PageEnum";
import InviteSet from "./mini-program/pageset/invite-page-set";
import LoginGuiderSet from "./mini-program/pageset/login-page-set";
import RegisterSet from "./mini-program/pageset/register-page-set";
import LaunchScreenSet from "./mini-program/pageset/launch-screen-set";
import { lib } from "../../common/react/utils/lib";
import PagePreviewQrCode from "./mini-program/component/get-qrcode";
import "./mini-program/component/page-settings";
import PageSettings from "./mini-program/component/page-settings";

var {Navigation, NavigationSet, getNavigationDefaultSet} = navigation;
var {Image, ImageSet, getImageDefaultSet} = image;
var {Search, SearchSet, getSearchDefaultSet} = search;
var {Video, VideoSet, getVideoDefaultSet} = video;
var {Slider, SliderSet, getSliderDefaultSet} = slider;
var {Guide, GuideSet, getGuideDefaultSet} = guide;
var {ItemList, ItemListSet, getItemListDefaultSet} = itemList;
var {HotArea, HotAreaSet, getHotAreaDefaultSet} = hotArea;
var {LayoutTwo, LayoutTwoSet, getLayoutTwoDefaultSet} = layoutTwo;
var {LayoutThree, LayoutThreeSet, getLayoutThreeDefaultSet} = layoutThree;
var {Notice, NoticeSet, getDefaultNoticeSet} = notice;
var {Helper, HelperSet, getHelperDefaultSet} = helper;
var {RankList, RankListSet, getRankListDefaultSet} = rankList;
var {TaskPageSet, getTaskPageDefaultSet} = TaskSet;
var {InvitePageSet, getInvitePageDefaultSet} = InviteSet;
var {LoginPageSet, getLoginPageDefaultSet} = LoginGuiderSet;
var {RegisterPageSet, getRegisterPageDefaultSet} = RegisterSet;
var {LaunchScreenPageSet, getLaunchScreenPageDefaultSet} = LaunchScreenSet;
// window.shopId = getParam("shop_id");
const {Fragment,createRef} = React;
const {message, Button, Space,Popover} = antd;
const {QuestionCircleOutlined, SettingOutlined} = icons
const imageSizeEnum= {
   [PageEnum.TASK]: '750 x 404',
   [PageEnum.REGISTER]: '无限制',
   [PageEnum.HOME]: '宽750  高度等比展示',
   [PageEnum.FISSION_COUPON]: '375 x 241',
   [PageEnum.LOGIN_GUIDE]: '750 x 1625',
   [PageEnum.CUSTOM]: '宽750  高度等比展示',
}
const config1 = {
  moduleList: [{
    name: "image",
    title: "单图",
    icon: "&#xe65f;",
    desc: "尺寸 750 * 750"
  }, {
    name: "hot-area",
    title: "热区",
    icon: "&#xe650;",
  },]
}
const config = {
  moduleList: [
    {
      name: "slider",
      title: "首页轮播",
      icon: "&#xe675;",
    },
    {
      name: "search",
      title: "搜索",
      icon: "&#xe61c;",
    },
    {
      name: "image",
      title: "单图 ",
      icon: "&#xe65f;",
       desc: "尺寸 4:3"
    },
    {
      name: "video",
      title: "视频",
      icon: "&#xe61b;",
    },
    {
      name: "guide",
      title: "导航栏",
      icon: "&#xe6c1;",
    },
    {
      name: "hot-area",
      title: "热区",
      icon: "&#xe650;",
    },
    {
      name: "notice",
      title: "通知",
      iconName: "icon-feebas-new",
      icon: "&#xeb43;",
    },
    {
      name: "modal",
      title: "弹窗",
      iconName: "icon-feebas-new",
      icon: "&#xe6ba;",
    },
    {
      name: "navigation",
      title: "头部导航",
      iconName: "icon-feebas-new",
      icon: "&#xe63b;",
    },
  ],
  layoutList: [
    {
      name: "layout-two",
      title: "两列布局",
      icon: "&#xe6c1;",
    }
  ],
  productList: [
    {
      name: "item-list",
      title: "商品组",
      icon: "&#xe619;",
    },
    {
      name: "rank-list",
      title: "排行",
      iconName: "icon-feebas-new",
      icon: "&#xe6c5;",
    },
  ],
  serviceList: [
    {
      name: "helper",
      title: "小助手",
      iconName: "icon-feebas-new",
      icon: "&#xe611;",
    },
  ],
  defaultData: {
    pages: [
      {
        id: 1,
        modules: [
          {
            name: "navigation",
            title: "头部导航",
            set: {
              id: 1,
              name: "首页",
              title: "店铺名称",
              shareMessage: "",
            },
          },
        ],
        activeModule: 0,
      },
    ],
    activePage: 0,
    indexPage: 0,
  }
}


const API = {
  async handleSave(params) {
    return request({
      url: '/mall-admin/api/wxappPages/save',
      needMask: true,
      method: "post",
      data: params
    })

  },
  async handlePublish(params) {
    return request({
      url: '/mall-admin/api/wxappPages/getPage',
      needMask: true,
      data: params
    })
  },
  async handleGetPage(params) {
    return request({
      url: '/mall-admin/api/wxappPages/getPage',
      needMask: true,
      data: params
    })
  }
}


class DesignPage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      moduleList: this.isOnlyPicAndHosPage(props.pageType) ? config1.moduleList : config.moduleList,
      layoutList: this.isOnlyPicAndHosPage(props.pageType) ? [] : config.layoutList,
      productList: this.isOnlyPicAndHosPage(props.pageType) ? [] : config.productList,
      serviceList: this.isOnlyPicAndHosPage(props.pageType) ? [] : config.serviceList,
      selectedLayout: null,
      pageId: null,
      pages: this.setDefaultInsertPageData(props.isInsertPage, props.pageType),
      activePage: 0,
      setPage: props.isInsertPage,
      showPageSettings: false, // 控制全局设置显示状态
    };
    this.qrcodeRef = createRef();

  }

  isOnlyPicAndHosPage(pageType) {
    return pageType === PageEnum.TASK || pageType === PageEnum.FISSION_COUPON || pageType === PageEnum.REGISTER||pageType === PageEnum.LAUNCH_SCREEN || pageType === PageEnum.LOGIN_GUIDE
  }

  // 判断是否应该显示全局设置
  shouldShowPageSettings() {
    const { isCustomPage, pageType } = this.props;
    // 只有首页和自定义页面显示全局设置
    return pageType === PageEnum.HOME || isCustomPage;
  }

  componentDidMount() {
    this.getPageData()
  }


  getPageData() {
    const {isCustomPage, isInsertPage, customPageConfig, pageType, defaultData} = this.props
    const data = this.getInitialPageData(isCustomPage, customPageConfig, pageType);
    if (defaultData) {
      this.setState(_.clone(defaultData));
    }
    API.handleGetPage(data).then(res => {
      let defaultDataClone = lib.clone(config.defaultData)
      if (!res.length) {
        return;
      }
      const {pageData, id} = res[0];

      if (!pageData) {
        this.setState({...defaultDataClone, pageId: id});
        return;
      }
      const parsedData = JSON.parse(pageData);
      this.setState({...parsedData, pageId: id});
    });
  }

  setDefaultInsertPageData(isInsertPage, pageType) {
    let pageSet = {}
    let page = {
      id: 1,
      pageType: pageType,
      modules: [],
      activeModule: 0,
    }
    switch (pageType) {
      case PageEnum.TASK:
        pageSet = getTaskPageDefaultSet()
        break;
      case PageEnum.FISSION_COUPON:
        pageSet = getInvitePageDefaultSet()
        break;
      case PageEnum.LOGIN_GUIDE:
        pageSet = getLoginPageDefaultSet()
        break;
      case PageEnum.REGISTER:
        pageSet = getRegisterPageDefaultSet()
        break;
      case PageEnum.LAUNCH_SCREEN:
        pageSet = getLaunchScreenPageDefaultSet()
        break;
      default:
        break;
    }
    page.pageSet = pageSet
    return [page]
  }

  calculateMaxPageId(pages) {
    return pages.reduce((maxId, page) =>
      Math.max(maxId, page.id), 1);
  }

  getInitialPageData(isCustomPage, customPageConfig, pageType) {
    return isCustomPage
      ? {
        pageType: 20,
        pageName: customPageConfig.pageName,
        id: customPageConfig.id,
        pageTypeName: '自定义页面'
      }
      : {
        pageType: pageType,
        pageTypeName: '首页',
      };
  }

  checkPageData(page) {
    if (page.pageType === PageEnum.REGISTER) {
      if (!page.pageSet.registerPrice) {
        message.error("请检查付费注册金额（元）填写")
        return false
      }
    }
    return true
  }

  checkData(pages,dialogId,pageId) {
    const page = pages[0]
    const modules = page.modules || []
    let flag = true
    if (!this.checkPageData(page)) {
      flag = false
    }

    for (let i = 0; i < modules.length; i++) {
      const module = modules[i]
      if (module.name === "notice") {
        if (!module.set.title) {
          message.error("请检查通知组件,公告标题是否填写")
          flag = false
          break
        }
        if (!module.set.content) {
          flag = false
          message.error("请检查通知组件,公告内容是否填写")
          break
        }
      } else if (module.name === "helper") {

      } else if (module.name === "rank-list") {

        if (!module.set.title) {
          flag = false
          message.error("请检查排行组件,排行榜名称是否填写")
          break
        }
        if (!module.set.shopCategoryId) {
          flag = false
          message.error("请检查排行组件,排行因素｜商品分类是否选择")
          break
        }
      }
      if (module.name === "slider") {
        this.checkHotAreaOrSlider(module, dialogId, pageId);
      } else if (module.name === "hot-area") {
        this.checkHotAreaOrSlider(module, dialogId, pageId);
      } else if (module.name === "guide") {
        this.checkHotAreaOrSlider(module, dialogId, pageId);
      } else if (module.name === "notice") {
        this.checkSingle(module, dialogId, pageId);
      } else if (module.name === "image") {
        this.checkSingle(module, dialogId, pageId);
      }
      console.log("dialogId", dialogId, "pageId", pageId)
    }
    return flag
  }

  checkHotAreaOrSlider(module, dialogId, pageId) {
    const list = module.set.list
    if (list) {
      for (let i = 0; i < list.length; i++) {
        let item = list[i]
        if (item.url && item.url.radioKey === "modal" && item.url.radioValue) {
          item.url.radioValue.map(id => dialogId.add(id))
        } else if (item.url && item.url.radioKey === "selectPage"
          && item.url.radioValue
          && item.url.radioValue.pageType === "custom"
          && item.url.radioValue.id) {
          pageId.add(item.url.radioValue.id)
        }
      }
    }
  }

  checkSingle(module, dialogId, pageId) {
    if (module.url && module.url.radioKey === "modal" && module.url.radioValue) {
      module.url.radioValue.map(item => dialogId.add(item))
    } else if (module.url && module.url.radioKey === "selectPage"
      && module.url.radioValue
      && module.url.radioValue.pageType === "custom"
      && module.url.radioValue.id) {
      pageId.add(module.url.radioValue)
    }
  }

  save(publishFlag) {
    const {isCustomPage, customPageConfig, pageType} = this.props
    const data = this.getInitialPageData(isCustomPage, customPageConfig, pageType);
    let dialogId = new Set(), pageId = new Set()
    if (this.checkData(this.state.pages,dialogId,pageId)) {
      API.handleSave({
        ...data,
        pageId: this.state.pageId,
        publishFlag,
        popUpIdList:[...dialogId],
        refPageIdList:[...pageId],
        data: JSON.stringify({
          pages: this.state.pages,
          activePage: this.state.activePage,
          indexPage: this.state.indexPage,
        }),
      }).then(res => {
        message.success(publishFlag ? "保存&发布成功" : "保存成功")
        this.getPageData()
        if (isCustomPage) {
          this.goBackToPageList()
        }
      })
    }
  }

  edit(index, isSubModule, parentIndex, layoutIndex) {
    const page = this.state.pages[this.state.activePage];
    page.activeIsSubModule = isSubModule
    if (isSubModule) {
      var activeModule = page.modules[parentIndex];
      page.activeModule = parentIndex;
      page.activeLayoutModule = layoutIndex;
      page.activeLayoutModuleIndex = index;
      activeModule.layoutIndex = layoutIndex;
    } else {
      page.activeModule = index;
      page.activeLayoutModule = -1;
      page.activeLayoutModuleIndex = -1;
    }
    this.setState({
      ...this.state,
      setPage: false,
      showPageSettings: false
    });
  }

  up(index, isSubModule, parentIndex, layoutIndex) {
    var data = this.state;
    data.setPage = false
    var page = data.pages[data.activePage];
    page.activeIsSubModule = isSubModule
    if (isSubModule) {
      var activeModule = page.modules[parentIndex];
      let layoutModules = activeModule.modules[layoutIndex];
      [layoutModules[index - 1], layoutModules[index]] = [layoutModules[index], layoutModules[index - 1]];
      if (page.activeLayoutModuleIndex === index) {
        page.activeLayoutModuleIndex--;
      } else if (page.activeLayoutModuleIndex === index + 1) {
        page.activeLayoutModuleIndex++;
      }
    } else {
      [page.modules[index - 1], page.modules[index]] = [page.modules[index], page.modules[index - 1]];
      if (page.activeModule == index) {
        page.activeModule--;
      } else if (page.activeModule == index - 1) {
        page.activeModule++;
      }
    }
    this.setState(data);
  }

  down(index, isSubModule, parentIndex, layoutIndex) {
    var data = this.state;
    data.setPage = false
    var page = data.pages[data.activePage];
    page.activeIsSubModule = isSubModule
    if (isSubModule) {

      var activeModule = page.modules[parentIndex];
      let layoutModules = activeModule.modules[layoutIndex];
      [layoutModules[index + 1], layoutModules[index]] = [layoutModules[index], layoutModules[index + 1]];
      if (page.activeLayoutModuleIndex === index) {
        page.activeLayoutModuleIndex++;
      } else if (page.activeLayoutModuleIndex === index + 1) {
        page.activeLayoutModuleIndex--;
      }
    } else {
      [page.modules[index + 1], page.modules[index]] = [page.modules[index], page.modules[index + 1]];
      if (page.activeModule == index) {
        page.activeModule++;
      } else if (page.activeModule == index + 1) {
        page.activeModule--;
      }
    }
    this.setState(data);
  }

  delete(index, isSubModule, parentIndex, layoutIndex) {
    var data = this.state;
    data.setPage = false
    var page = data.pages[data.activePage];
    page.activeIsSubModule = isSubModule
    if (isSubModule) {
      var activeModule = page.modules[parentIndex];
      let layoutModules = activeModule.modules[layoutIndex];
      if (page.activeLayoutModuleIndex === layoutModules.length - 1) {
        page.activeLayoutModuleIndex = 0;
      }
      layoutModules.splice(index, 1);
    } else {
      if (page.activeModule == page.modules.length - 1) {
        page.activeModule = 0;
      }
      page.modules.splice(index, 1);
    }

    this.setState(data);
  }

  editSet(item, isSubModule) {
    var data = this.state;
    var page = data.pages[data.activePage];
    if (isSubModule) {

    } else {
      page.modules[page.activeModule] = item;
    }
    this.setState(data);
  }

  handleLayoutSelect(index, ddd) {
    var data = this.state;
    data.setPage = false
    var page = data.pages[data.activePage];
    page.activeModule = index;
    var module = page.modules[index];
    if (module.name === "layout-two" || module.name === "layout-three") {
      module.layoutIndex = ddd;
    }
    this.setState(this.state);

  }

  checkLayoutModule(name) {
    if ("navigation" === name) {
      message.warning("头部导航组件不能加入到两列布局中")
      return false
    }else if ("notice" === name) {
      message.warning("暂不支持，通知组件加入到两列布局中")
      return false
    } else if ("helper" === name) {
      message.warning("暂不支持，小组手组件加入到两列布局中")
      return false
    } else if ("layout-two" === name) {
      message.warning("两列布局不能加入到两列布局中")
      return false
    }
    return true
  }

  addModule(name) {
    if (name == "modal") {
      this.props.setActiveKey('2')
      return
    }
    var defaultSet = {
      navigation: getNavigationDefaultSet(),
      image: getImageDefaultSet(),
      search: getSearchDefaultSet(),
      video: getVideoDefaultSet(),
      slider: getSliderDefaultSet(),
      guide: getGuideDefaultSet(),
      "item-list": getItemListDefaultSet(),
      "hot-area": getHotAreaDefaultSet(),
      "layout-two": getLayoutTwoDefaultSet(),
      "layout-three": getLayoutThreeDefaultSet(),
      notice: getDefaultNoticeSet(),
      helper: getHelperDefaultSet(),
      "rank-list": getRankListDefaultSet(),
    };

    var data = this.state;
    var page = data.pages[data.activePage];
    let activeModule
    if (page.modules.length > 0) {
      activeModule = page.modules[page.activeModule];
    }
    if (activeModule && (activeModule.name === "layout-two" || activeModule.name === "layout-three")) {
      if (this.checkLayoutModule(name)) {
        activeModule.modules[activeModule.layoutIndex].push(defaultSet[name]);
        this.setState(data);
      }
    } else {
      // 正常添加模块
      if (name == "search") {
        page.modules.splice(1, 0, defaultSet[name]);
        page.activeModule = 1;
        this.setState(data);
        setTimeout(() => {
          $(".panel-phone").animate(
            {
              scrollTop: 0,
            },
            500,
          );
        }, 100);
      }else if(name === "navigation"){
        let navigationFlag = false
        page.modules.map(item => {
          if(item.name === "navigation"){
            navigationFlag= true
          }
        })
        if(!navigationFlag){
          page.modules.splice(0, 0, defaultSet[name]);
          page.activeModule = 0;
          this.setState(data);
          setTimeout(() => {
            $(".panel-phone").animate(
              {
                scrollTop: 0,
              },
              500,
            );
          }, 100);
        }else{
          message.warning("头部导航组件只能有一个")
        }

      } else {
        page.modules.push(defaultSet[name]);
        page.activeModule = page.modules.length - 1;
        this.setState(data);
        setTimeout(() => {
          $(".panel-phone").animate(
            {
              scrollTop: $(".panel-phone .module:last-child")[0].offsetTop,
            },
            500,
          );
        }, 100);
      }
    }
  }

  editPageSet(item) {
    let data = this.state;
    let page = data.pages[data.activePage];
    page = {...page, ...item}
    data.pages[data.activePage] = page
    this.setState(data);
  }

  renderModuleSet(page) {
    const {activeIsSubModule, activeLayoutModule, activeLayoutModuleIndex} = page
    let item
    if (activeIsSubModule) {
      let module = page.modules[page.activeModule];
      item = module.modules[activeLayoutModule][activeLayoutModuleIndex]
    } else {
      item = page.modules[page.activeModule];
    }
    if (!item) {
      console.warn("‼️renderModuleSet item is null")
      return
    }
    
    const pageType = page.pageType||this.props.pageType || (this.props.customPageConfig? this.props.customPageConfig.pageType:'')
    
    let moduleSet
    // 原有的模块设置渲染逻辑
    switch (item.name) {
      case "navigation":
        moduleSet = <NavigationSet data={item} designData={this.state} editSet={this.editSet.bind(this)}
                                   isSubModule={activeIsSubModule}/>;
        break
      case "image":
        moduleSet = <ImageSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                              isSubModule={activeIsSubModule}
                              imageSize={imageSizeEnum[pageType]}
                              />;
        break
      case "search":
        moduleSet = <SearchSet data={item} editSet={this.editSet.bind(this)} isSubModule={activeIsSubModule}/>;
        break
      case "video":
        moduleSet = <VideoSet data={item} editSet={this.editSet.bind(this)} isSubModule={activeIsSubModule}/>;
        break
      case "slider":
        moduleSet = <SliderSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                               isSubModule={activeIsSubModule}/>;
        break
      case "guide":
        moduleSet = <GuideSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                              isSubModule={activeIsSubModule}/>;
        break
      case "item-list":
        moduleSet = <ItemListSet data={item} editSet={this.editSet.bind(this)} isSubModule={activeIsSubModule}/>;
        break
      case "hot-area":
        moduleSet = <HotAreaSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                                isSubModule={activeIsSubModule}/>;
        break
      case "layout-two":
        moduleSet = <LayoutTwoSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                                  isSubModule={activeIsSubModule}/>;
        break
      case "layout-three":
        moduleSet = <LayoutThreeSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                                    isSubModule={activeIsSubModule}/>;
        break
      case "notice":
        moduleSet = <NoticeSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                               isSubModule={activeIsSubModule}/>;
        break
      case "helper":
        moduleSet = <HelperSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                               isSubModule={activeIsSubModule}/>;
        break
      case "rank-list":
        moduleSet = <RankListSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)}
                                 isSubModule={activeIsSubModule}/>;
        break
      default:
        moduleSet = "";
        break
    }
    return (<Fragment>
      <div className="title">{item.title}
      </div>
      <div className="editor-panel">{moduleSet}</div>
    </Fragment>)
  }

  renderPageSet(page) {
    let pageSet
    switch (page.pageType) {
      case PageType.TASK:
        pageSet = <TaskPageSet data={page} editPageSet={this.editPageSet.bind(this)}/>
        break;
      case PageType.FISSION_COUPON:
        pageSet = <InvitePageSet data={page} editPageSet={this.editPageSet.bind(this)}/>
        break;
      case PageType.LOGIN_GUIDE:
        pageSet = <LoginPageSet data={page} editPageSet={this.editPageSet.bind(this)}/>
        break;

      case PageType.REGISTER:
        pageSet = <RegisterPageSet data={page} editPageSet={this.editPageSet.bind(this)}/>
        break;
      case PageType.LAUNCH_SCREEN:
        pageSet = <LaunchScreenPageSet data={page} editPageSet={this.editPageSet.bind(this)}/>
        break;
      default:
        pageSet = null
        break;
    }
    return <Fragment>{pageSet}</Fragment>
  }

  goBackToPageList() {
    this.props.setIsCustomPage(false)
    this.props.setCustomPageConfig(null)
    this.props.setActiveKey('7')
  }

  editSetPage() {
    this.setState({
      setPage: true,
      showPageSettings: false
    })
  }

  // 显示全局设置
  showPageSettings() {
    this.setState({
      showPageSettings: true,
      setPage: false
    })
  }

  // 处理全局设置变更
  handlePageSettingsChange = (updatedPageData) => {
    let data = this.state;
    let page = data.pages[data.activePage];
    page = {...page, ...updatedPageData}
    data.pages[data.activePage] = page
    this.setState(data);
  }


  render() {
    var self = this;
    var page = this.state.pages.length > 0 ? this.state.pages[this.state.activePage] : null;
    return (
      <div className="bd">
        <div className="page-title" style={{display: "flex", justifyContent: "space-between",alignItems: "center" }}>
          
          <div style={{marginLeft: "300px"}}><Popover placement="top" content={
            <PagePreviewQrCode pageUrl={this.props.pageUrl}  isCustomPage={this.props.isCustomPage} customPageId={this.state.pageId} />
          } >
            预览效果 <QuestionCircleOutlined />
          </Popover>
          </div>
          <div style={{display: "flex", justifyContent: "flex-end"}}>
            <Space>
              {this.props.isCustomPage && <Button
                onClick={() => {
                  this.goBackToPageList()
                }}
              >返回</Button>}
              <Button onClick={() => this.save(0)} type='primary'>
                保存
              </Button>
              <Button onClick={() => this.save(1)} type='primary'>
                保存 & 发布
              </Button>
            </Space>
          </div>
        </div>
        <div className="panel-bd">
          {
            this.props.pageType === PageEnum.LAUNCH_SCREEN ? null:  <div className="panel-module">
              <div className="title">组件库</div>
              <div className="content">
                {
                  this.state.layoutList.length > 0 ?
                    <div>
                      <div className="subTitle">布局组件</div>
                      {this.state.layoutList.map(function (layout, index) {
                        return (
                          <div
                            className="module"
                            key={index}
                            onClick={self.addModule.bind(self, layout.name)}>
                            <div
                              className={layout.iconName||'icon'}
                              dangerouslySetInnerHTML={{__html: layout.icon}}></div>
                            <div className="name">{layout.title}</div>
                          </div>
                        );
                      })}
                    </div> : null
                }
                <div>
                  <div className="subTitle">基础组件</div>
                  {this.state.moduleList.map(function (module, index) {
                    return (
                      <div
                        className="module"
                        key={index}
                        onClick={self.addModule.bind(self, module.name)}>
                        <div
                          className={module.iconName||'icon'}
                          dangerouslySetInnerHTML={{__html: module.icon}}></div>
                        <div className="name">{module.title}</div>
                      </div>
                    );
                  })}
                </div>
                {
                  this.state.productList.length > 0 ?
                    <div>
                      <div className="subTitle">商品组件</div>
                      {this.state.productList.map(function (product, index) {
                        return (
                          <div
                            className="module"
                            key={index}
                            onClick={self.addModule.bind(self, product.name)}>
                            <div
                              className={product.iconName||'icon'}
                              dangerouslySetInnerHTML={{__html: product.icon}}></div>
                            <div className="name">{product.title}</div>
                          </div>
                        );
                      })}
                    </div> : null
                }
                {
                  this.state.productList.length > 0 ?
                    <div>
                      <div className="subTitle">业务组件</div>
                      {this.state.serviceList.map(function (module, index) {
                        return (
                          <div
                            className="module"
                            key={index}
                            onClick={self.addModule.bind(self, module.name)}>
                            <div
                              className={module.iconName||'icon'}
                              dangerouslySetInnerHTML={{__html: module.icon}}></div>
                            <div className="name">{module.title}</div>
                          </div>
                        );
                      })}
                    </div> : null
                }
              </div>
            </div>
          }

          <div style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
            flex: "1 1 0%",
            overflow: "scroll",
            backgroundColor: "#f0f0f0"
          }}>
            <div style={this.props.bgStyle} onClick={() => this.editSetPage()}/>
            {/* 全局设置按钮 - 只在首页和自定义页面显示 */}
            {this.shouldShowPageSettings() && (
              <div
                style={{
                  position: "absolute",
                  top: "10px",
                  right: "10px",
                  zIndex: 10,
                  backgroundColor: "#fff",
                  border: "1px solid #d9d9d9",
                  borderRadius: "4px",
                  padding: "8px 12px",
                  cursor: "pointer",
                  fontSize: "12px",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                }}
                onClick={() => this.showPageSettings()}
                title="全局设置"
              >
                <SettingOutlined style={{ marginRight: "4px" }} />
                全局设置
              </div>
            )}
            {this.props.slot && this.props.slot(page)}
            <div className={"panel-phone-wrapper"} style={this.props.panePhoneWrapperStyle}>
              <div className={`iQsirn ${this.props.iQsirnStyle}`}>
                <div
                  className="panel-phone"
                  style={{
                    backgroundColor: (page && page.pageSet && page.pageSet.backgroundColor) || "#FFEEFF"
                  }}
                >
                  {page ? page.modules.map(function (item, index) {
                    var methods = {
                      edit: self.edit.bind(self),
                      up: self.up.bind(self),
                      down: self.down.bind(self),
                      delete: self.delete.bind(self),
                      handleLayoutSelect: self.handleLayoutSelect.bind(self),
                      editSet: self.editSet.bind(self),
                    }
                    return renderModuleUtils.renderModule(item, 0, index, 0, page.activeModule, methods, page.activeLayoutModule, page.activeLayoutModuleIndex, false, page);
                  }) : null}
                </div>
              </div>
            </div>
          </div>

          <div className="panel-module-set">
            <div className="editor">

              {
                this.state.showPageSettings ? (
                  <Fragment>
                    <div className="title">全局设置</div>
                    <div className="editor-panel">
                      <PageSettings
                        pageData={page}
                        onChange={this.handlePageSettingsChange}
                      />
                    </div>
                  </Fragment>
                ) : !this.state.setPage ? <Fragment>{page ? this.renderModuleSet(page) : null}</Fragment>
                  : <Fragment> {page ? this.renderPageSet(page) : null}</Fragment>
              }
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default DesignPage;

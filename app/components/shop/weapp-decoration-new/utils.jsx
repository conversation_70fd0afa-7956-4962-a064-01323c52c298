import navigation from "./mini-program/module/navigation";
import image from "./mini-program/module/image";
import search from "./mini-program/module/search";
import video from "./mini-program/module/video";
import slider from "./mini-program/module/slider";
import guide from "./mini-program/module/guide";
import itemList from "./mini-program/module/item-list";
import hotArea from "./mini-program/module/hot-area";
import layoutTwo from "./mini-program/module/layout-two";
import layoutThree from "./mini-program/module/layout-three";
import notice from "./mini-program/module/notice";
import helper from "./mini-program/module/helper";
import rankList from "./mini-program/module/rank-list";

var { Navigation, NavigationSet, getNavigationDefaultSet } = navigation;
var { Image, ImageSet, getImageDefaultSet } = image;
var { Search, SearchSet, getSearchDefaultSet } = search;
var { Video, VideoSet, getVideoDefaultSet } = video;
var { Slider, SliderSet, getSliderDefaultSet } = slider;
var { Guide, GuideSet, getGuideDefaultSet } = guide;
var { ItemList, ItemListSet, getItemListDefaultSet } = itemList;
var { HotArea, HotAreaSet, getHotAreaDefaultSet } = hotArea;
var { LayoutTwo, LayoutTwoSet, getLayoutTwoDefaultSet } = layoutTwo;
var { LayoutThree, LayoutThreeSet, getLayoutThreeDefaultSet } = layoutThree;
var { Notice, NoticeSet, getDefaultNoticeSet } = notice;
var { Helper, HelperSet, getHelperDefaultSet } = helper;
var { RankList, RankListSet, getRankListDefaultSet } = rankList;


const renderModuleUtils = {
  renderModule(item,parentIndex, index,layoutIndex, activeModule,methods,activeLayoutModule,activeLayoutModuleIndex,isSubModule) {
    switch (item.name) {
      case "navigation":
        return (
          <Navigation data={item} parentIndex={parentIndex} index={index} layoutIndex={layoutIndex} activeModule={activeModule}  key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>
        );
      case "image":
        return <Image data={item}   parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "search":
        return <Search data={item}  parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "video":
        return <Video data={item}   parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex}  activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "slider":
        return <Slider data={item}  parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "guide":
        return <Guide data={item}  parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "item-list":
        return <ItemList data={item} parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule} activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "hot-area":
        return <HotArea data={item} parentIndex={parentIndex}   index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "layout-two":
        return (
          <LayoutTwo data={item} parentIndex={parentIndex}  index={index}  layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule} activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>
        );
      case "layout-three":
        return (
          <LayoutThree data={item} parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>
        );
      case "notice":
        return <Notice data={item} parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "helper":
        return <Helper data={item} parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      case "rank-list":
        return <RankList data={item} parentIndex={parentIndex}  index={index} layoutIndex={layoutIndex} activeModule={activeModule} key={index} methods={methods} isSubModule={isSubModule}  activeLayoutModule={activeLayoutModule} activeLayoutModuleIndex={activeLayoutModuleIndex}/>;
      default:
        return "";
    }
  },

  renderModuleSet(activeModule) {


    // 原有的模块设置渲染逻辑
    switch (item.name) {
      case "navigation":
        return <NavigationSet data={item} designData={this.state} editSet={this.editSet.bind(this)} />;
      case "image":
        return <ImageSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "search":
        return <SearchSet data={item} editSet={this.editSet.bind(this)} />;
      case "video":
        return <VideoSet data={item} editSet={this.editSet.bind(this)} />;
      case "slider":
        return <SliderSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "guide":
        return <GuideSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "item-list":
        return <ItemListSet data={item} editSet={this.editSet.bind(this)} />;
      case "hot-area":
        return <HotAreaSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "layout-two":
        return <LayoutTwoSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "layout-three":
        return <LayoutThreeSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "notice":
        return <NoticeSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "helper":
        return <HelperSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      case "rank-list":
        return <RankListSet data={item} pages={this.state.pages} editSet={this.editSet.bind(this)} />;
      default:
        return "";
    }
  }
}
export default renderModuleUtils

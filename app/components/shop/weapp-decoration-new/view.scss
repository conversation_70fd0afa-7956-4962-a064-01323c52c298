.weapp-decoration-new {
  height: 100%;
  .bd {
    height: 100%;
    .main {
      position: relative;
      padding: 0 20px;
      margin-left: 0;
    }

    .page-title {
      position: relative;
      height: 50px;
      padding-bottom: 0px;

      .btn {
        margin-left: 0;
        margin-right: 20px;
        line-height: 28px;
        padding-left: 25px;
        padding-right: 25px;

        span {
          font-size: 20px;
          margin-right: 5px;
        }

        position: relative;
      }

      .btn:first-child,
      .btn:last-child {
        padding-right: 25px;
      }

      .btn .delete {
        font-family: "iconfont";
        font-size: 12px;
        color: #999;
        display: inline-block;
        margin-left: 10px;
      }

      .btn .delete:hover {
        color: #000;
      }

      .btn-active {
        background-image: linear-gradient(135deg, #09f, #06f);
        color: #fff;
        border-color: #06f;

        .delete {
          color: #ccc;
        }

        .delete:hover {
          color: #fff;
        }
      }

      .btn-active:hover {
        background-image: linear-gradient(135deg, #0bf, #07f);
        color: #fff;
        border-color: #07f;
      }

      .tools {
        right: 0;
        text-align: center;
        position: absolute;

        .btn {
          margin: 0 10px;
        }

        top: 0px;
      }
    }


  }
    .ant-tabs,
    .ant-tabs-content,
    .ant-tabs-content > div {
      height: 100%;
    }
   .ant-tabs>.ant-tabs-nav{
     margin: 0;
   }
    .new-search-condition-panel{
      display: none;

    }
    .category-goods-sort {
      display: flex;
      height: 100%;
      .ant-tabs-left>.ant-tabs-content-holder>.ant-tabs-content>.ant-tabs-tabpane{
        padding-left: 5px;
      }
      .category-goods-preview {
        height: 85%;
        background: #fff;
        border-radius: 16px;
        width: 375px;
        padding: 12px 0;
        .product-item {
          display: flex;
          gap: 10px;
          border-bottom: 1px solid #e0e0e0;
          padding: 10px 12px;
        }
        .product-image-wrap{
          text-align: center;
          width: 60px;
          height: 60px;
        }
        .product-image {
          width: 60px;
          height: 60px;
          object-fit: cover;
          border: 1px solid #ccc;
          border-radius: 4px;
        }

        .product-details {
          display: flex;
          flex-direction: column;
        }

        .product-name {
          font-size: 14px;
          color: #333;
        }

        .product-price {
          font-size: 16px;
          color: #e60023;
          font-weight: bold;
        }
      }

      .category-list {
        padding: 0 20px;
      }
      .sort-goods-list{
        display: flex;
        flex-direction: column;
        width: 360px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        max-height: 85%;


        .product-image-wrap{
          text-align: center;
          width: 60px;
          height: 60px;
        }
        .product-info{
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          flex: 1;
          .product-name{
            white-space: break-spaces;
            word-break: break-all;
          }
        }
        .product-image {
          width: 60px;
          height: 60px;
          object-fit: cover;
          border: 1px solid #ccc;
          border-radius: 4px;
        }
        .product-price{
          display: flex;
          font-size: 14px;
          justify-content: space-between;
        }
        .draggable-goods-list{
          flex:1;
          overflow: scroll;
          padding: 0 20px;
        }

      }
      .category-list-card {
        margin: 0 24px;
        padding: 20px;
        width: 360px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)
      }
      .product-list {
        min-width: 269px;
        height: 100%;
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 15px;
      }
    }
}

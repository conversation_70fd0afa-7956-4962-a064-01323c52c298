import module from "../module/module";
import ItemRichEditor from "./item-rich-editor";
var {  ModuleSet } = module;
const { Input } = antd;

class TaskPageSet extends ModuleSet {
  handleTitleChange = e => {
    const value = e.target.value;
    this.state.item.pageSet.title = value;
    this.refreshPageSet();
  };

  handleContentChange = e => {
    const value = e.target.value;
    this.state.item.pageSet.subTitle = value;
    this.refreshPageSet();
  };

  handleRuleChange = value => {
    this.state.item.pageSet.taskRule = value;
    this.refreshPageSet();
  };



  render() {
    let item = this.state.item;
    const {pageSet} = item;

    return (
      <div className="notice-set">

        <div className="form-group">
          <label>页面标题</label>
          <Input
            value={pageSet?pageSet.title:""}
            onChange={this.handleTitleChange}
            placeholder="请输入页面标题"
          />
          <span style={{marginTop:12,display: "block"}}>不填默认展示页面名称【邀请页】</span>
        </div>

        <div className="form-group">
          <label className="decoration-item-required">活动榜标题</label>
          <Input
            value={pageSet?pageSet.subTitle:''}
            onChange={this.handleContentChange}
            placeholder="请输入活动榜标题"
          />
        </div>
        <div className="form-group">
          <label>活动规则</label>
          <ItemRichEditor value={pageSet?pageSet.taskRule:''} onChange={this.handleRuleChange.bind(this)}/>
        </div>

      </div>
    );
  }
}
var getTaskPageDefaultSet = function () {
  return JSON.parse(JSON.stringify({
      title:'邀请页',
      subTitle:'晒单或点赞评论 送礼金',
      taskRule:''
  }));
};
export default {
  getTaskPageDefaultSet: getTaskPageDefaultSet,
  TaskPageSet: TaskPageSet,
};

const { Modal } = antd

import Picasa from "../../component/picasa";
import module from "../module/module";
const {Fragment} = React;
var {  ModuleSet } = module;
const getLaunchScreenPageDefaultSet = () => {
  return {
    imageUrl: '',
  };
};

class LaunchScreenPageSet extends ModuleSet {
  // 删除图片
  handleDeleteImage = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该开屏图片吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const { pageSet } = this.state.item;
        pageSet.imageUrl = '';
        this.refreshPageSet();
      }
    });
  };

  render() {
    let item = this.state.item;
    const {pageSet} = item;
    const hasImage = pageSet && pageSet.imageUrl;

    return (
       <Fragment>
         <div className="title">开机屏设置</div>
         <div className="notice-set">
           <div className="form-group">
              <span>
                建议图片尺寸为750px，宽高比1125 × 2000  （注意图片四周留出安全区域，在不同机型上边缘会被裁减隐藏掉，关键内容不要靠边），支持jpg. png. gif等流行图片格式，每张图片保持尺寸一致
              </span>
             <label style={{marginTop:12,display: "block"}}>开机屏图片</label>
             <div style={{ position: 'relative', display: 'inline-block' }}>
               <Picasa 
                 data={{
                   style : {width : '150px' , height : '75px' },
                   src : pageSet ? pageSet.imageUrl : ""
                 }} 
                 methods={{
                   updateSrc : (src) => {
                     pageSet.imageUrl = src;
                     this.refreshPageSet();
                   }
                 }} 
               />
               {hasImage && (
                 <div 
                   className="delete-icon" 
                   onClick={this.handleDeleteImage}
                   style={{
                     position: 'absolute',
                     top: '-10px',
                     right: '-10px',
                     width: '20px',
                     height: '20px',
                     borderRadius: '50%',
                     backgroundColor: 'rgba(0,0,0,0.5)',
                     color: '#fff',
                     display: 'flex',
                     alignItems: 'center',
                     justifyContent: 'center',
                     cursor: 'pointer',
                     fontSize: '14px'
                   }}
                 >
                   ×
                 </div>
               )}
             </div>
           </div>
         </div>
       </Fragment>
    );
  }
}

export default {
  LaunchScreenPageSet,
  getLaunchScreenPageDefaultSet
};

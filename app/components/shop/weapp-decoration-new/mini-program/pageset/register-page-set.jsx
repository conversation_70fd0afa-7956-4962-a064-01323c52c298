import Picasa from "../../component/picasa";
import module from "../module/module";
var {  ModuleSet } = module;
const { Input } = antd;

class RegisterPageSet extends ModuleSet {
  handleTitleChange = e => {
    const value = e.target.value;
    this.state.item.pageSet.title = value;
    this.refreshPageSet();
  };

  handleRegisterPriceChange = e => {
    const value = e.target.value;
    this.state.item.pageSet.registerPrice = value;
    this.refreshPageSet();
  };

  render() {
    let item = this.state.item;
    const {pageSet} = item;

    return (
      <div className="notice-set">

        <div className="form-group">
          <label><span className="required"></span>付费注册按钮引导文案</label>
          <div style={{display:'flex',alignItems:'center'}}>
          <Input
            value={pageSet?pageSet.registerPrice:""}
            onChange={this.handleRegisterPriceChange}
            style={{flex: 1}}
            placeholder="请输入"
          />
          <span style={{marginLeft:10}}>完成注册</span>
          </div>

          <label style={{marginTop:12,display: "block"}}>页面标题</label>
          <Input
            value={pageSet?pageSet.title:""}
            onChange={this.handleTitleChange}
            placeholder="请输入页面标题"
          />
          <span style={{marginTop:12,display: "block"}}>不填默认展示页面名称【注册】</span>
            <label style={{marginTop:12,display: "block"}}>顶部图片</label>
            <span style={{marginTop:12,display: "block"}}>尺寸：325*124</span>
            <Picasa data={{style : {width : '150px' , height : '75px' } , src : pageSet?pageSet.topPic:""}} methods={{
              updateSrc : (src) => {
                pageSet.topPic = src;
                this.refreshPageSet();
              }
            }} />
        </div>
      </div>
    );
  }
}
var getRegisterPageDefaultSet = function () {
  return JSON.parse(JSON.stringify({
      title:'注册',
  }));
};
export default {
  getRegisterPageDefaultSet: getRegisterPageDefaultSet,
  RegisterPageSet: RegisterPageSet,
};

import module from "../module/module";
import ItemRichEditor from "./item-rich-editor";
var {  ModuleSet } = module;
const { Input } = antd;

class LoginPageSet extends ModuleSet {
  handleTitleChange = e => {
    const value = e.target.value;
    this.state.item.pageSet.title = value;
    this.refreshPageSet();
  };




  render() {
    let item = this.state.item;
    const {pageSet} = item;

    return (
      <div className="notice-set">

        <div className="form-group">
          <label>页面标题</label>
          <Input
            value={pageSet?pageSet.title:""}
            onChange={this.handleTitleChange}
            placeholder="请输入页面标题"
          />
          <span style={{marginTop:12,display: "block"}}>不填默认展示页面名称【登录】</span>
        </div>


      </div>
    );
  }
}
var getLoginPageDefaultSet = function () {
  return JSON.parse(JSON.stringify({
      title:'登录',
  }));
};
export default {
  getLoginPageDefaultSet: getLoginPageDefaultSet,
  LoginPageSet: LoginPageSet,
};

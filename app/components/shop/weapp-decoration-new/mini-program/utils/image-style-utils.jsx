/**
 * 图片样式计算工具类
 * 用于计算图片容器和图片本身的样式
 */
class ImageStyleUtils {
    
    // 比例配置常量
    static ASPECT_RATIOS = {
        "16:9": 16/9,
        "4:3": 4/3,
        "3:2": 3/2,
        "1:1": 1/1,
        "2:1": 2/1
    };

    /**
     * 计算容器尺寸样式
     * @param {Object} imageSettings - 图片设置对象
     * @returns {Object} 容器样式对象
     */
    static getContainerStyle(imageSettings) {
        const { 
            fixedRatio, 
            aspectRatio, 
            imageHeight, 
            imageWidth, 
            customImageHeight, 
            widthMode, 
            heightMode 
        } = imageSettings;

        if (fixedRatio) {
            // 固定比例模式
            const ratio = ImageStyleUtils.ASPECT_RATIOS[aspectRatio] || 16/9;
            const width = imageHeight * ratio;

            return {
                width: `${width}px`,
                height: `${imageHeight}px`
            };
        } else {
            // 自定义宽高模式
            const style = {};

            // 宽度设置
            if (widthMode === "fixed") {
                style.width = `${imageWidth}px`;
            } else {
                style.width = 'auto';
            }

            // 高度设置
            if (heightMode === "fixed") {
                style.height = `${customImageHeight}px`;
            } else {
                style.height = 'auto';
            }

            return style;
        }
    }

    /**
     * 计算图片样式
     * @returns {Object} 图片样式对象
     */
    static getImageStyle() {
        return {
            width: '100%',
            height: '100%',
            visibility: 'visible',
            objectFit: 'cover'
        };
    }

    /**
     * 根据宽度和比例计算高度
     * @param {number} width - 宽度
     * @param {string} aspectRatio - 比例字符串
     * @returns {number} 计算出的高度
     */
    static calculateHeightFromWidth(width, aspectRatio) {
        const ratio = ImageStyleUtils.ASPECT_RATIOS[aspectRatio] || 16/9;
        return Math.round(width / ratio);
    }

    /**
     * 根据高度和比例计算宽度
     * @param {number} height - 高度
     * @param {string} aspectRatio - 比例字符串
     * @returns {number} 计算出的宽度
     */
    static calculateWidthFromHeight(height, aspectRatio) {
        const ratio = ImageStyleUtils.ASPECT_RATIOS[aspectRatio] || 16/9;
        return Math.round(height * ratio);
    }

    /**
     * 验证图片设置对象的完整性
     * @param {Object} imageSettings - 图片设置对象
     * @returns {boolean} 是否有效
     */
    static validateImageSettings(imageSettings) {
        if (!imageSettings || typeof imageSettings !== 'object') {
            return false;
        }

        const requiredFields = [
            'fixedRatio', 'aspectRatio', 'imageHeight', 
            'imageWidth', 'customImageHeight', 'widthMode', 'heightMode'
        ];

        return requiredFields.every(field => imageSettings.hasOwnProperty(field));
    }

    /**
     * 获取默认的图片设置
     * @returns {Object} 默认图片设置对象
     */
    static getDefaultImageSettings() {
        return {
            titleImage: "",
            fixedRatio: true,
            aspectRatio: "16:9",
            imageHeight: 100,
            imageWidth: 200,
            customImageHeight: 100,
            widthMode: "fixed",
            heightMode: "fixed"
        };
    }

    /**
     * 合并图片设置，确保所有必需字段都存在
     * @param {Object} userSettings - 用户提供的设置
     * @returns {Object} 合并后的完整设置
     */
    static mergeImageSettings(userSettings = {}) {
        const defaultSettings = ImageStyleUtils.getDefaultImageSettings();
        return {
            ...defaultSettings,
            ...userSettings
        };
    }
}

export default ImageStyleUtils;

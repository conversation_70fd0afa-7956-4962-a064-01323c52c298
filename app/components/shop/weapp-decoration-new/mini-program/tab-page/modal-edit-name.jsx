
import request from "../../../../utils/plugins/axios/request";
const {Fragment,useState,useEffect} = React;
const { Space,message,Input,Button} = antd;
const {EditOutlined} =icons;

export default function ModalEditName ({row,rowkey,reload}) {
    const [edit,setEdit] = useState(false)
    const [value,setValue] = useState(row[rowkey])

    useEffect(()=>{
        setValue(row[rowkey])
    },[row[rowkey]])
    return <Fragment>
        {
            !edit?
            <Space>
                {row[rowkey]}
                <EditOutlined style={{color:'var(--ant-primary-color)'}} onClick={()=>{
                    setEdit(true);
                }} />
            </Space>

            :
            <Space>
                <Input
                    value={value}
                    onChange={(e)=>{
                        setValue(e.target.value);
                    }}
                />
                <Button type='link' size='small' onClick={()=>{
                    row[rowkey] = value;
                    request({
                        url:'/mall-admin/api/wxappPages/popUpSave',
                        type:'post',
                        data:{
                          id: row.id,
                          name: value
                        },
                        success:()=>{
                            message.success("修改成功")
                            reload && reload()
                            setEdit(false)
                        }
                    })
                }}>保存</Button>
                <Button type='link' size='small' onClick={()=>{
                   setEdit(false)
                }}>取消</Button>
            </Space>
        }
    </Fragment>
}

import DesignPage from "../../design";


class Page extends React.Component {
  constructor(props) {
    super(props);

  }


  render() {

    return (
        <DesignPage  {...this.props}
          isInsertPage={true}
          pageUrl={"pages/task/task-center/index"}
          bgStyle={{
            height:'100%',
            backgroundImage: `url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/52980994880.png")`,
            backgroundSize: "cover",
            backgroundPosition: "top",
            position: "absolute",
            backgroundRepeat: "no-repeat",
            width: "375px",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          panePhoneWrapperStyle={{
            height: "210px",
            marginTop: "75px"
          }}
           iQsirnStyle={"unset-height"}
        />
    );
  }
}
export default Page;

import ModalEditName from "./modal-edit-name";
import modal from "../module/modal";
import request from "../../../../utils/plugins/axios/request";

const { Button, Space, Table, message, Modal: AntdModal, Input } = antd;
const { Modal, ModalSet } = modal;

// API 请求统一管理
const API = {
    // 获取弹窗列表
    async getModalList(params) {
        return request({
            url: `/mall-admin/api/wxappPages/searchPopUp`,
            needMask:true,
            method: "post",
            data: params,
        });
    },

    // 删除弹窗
    async deleteModal(params) {
        return request({
            url: `/mall-admin/api/wxappPages/deletePopUp`,
            needMask:true,
            method: "post",
            data: params,
        });
    },
    // 保存弹窗

    async saveModal(params) {
        return request({
            url: `/mall-admin/api/wxappPages/popUpSave`,
            needMask:true,
            method: "post",
            data: params,
        });
    },
};

// 默认配置
const DEFAULT_MODAL_CONFIG = {
    name: "modal",
    title: "弹窗",
    set: {
        name: "",
        title: "",
        content: "",
        imageUrlList: [],
        showCloseBtn: true,
        autoClose: false,
        autoCloseTime: 3,
        position: "bottom",
    },
};

// 表格列配置

class ModalPage extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            searchName: "", // 搜索名称
            modalList: [], // 弹窗列表
            currentModal: null, // 当前编辑的弹窗
            currentModalId: null, // 当前编辑的弹窗ID
        };
    }

    componentDidMount() {
        this.loadModalList();
    }

    // 加载弹窗列表
    async loadModalList() {
        const { searchName } = this.state;
        const params = {
            name: searchName,
        };

        try {
            const res = await API.getModalList(params);
            this.setState({ modalList: res });
        } catch (error) {
            console.error("加载弹窗列表失败:", error);
        }
    }

    // 搜索相关方法
    handleSearchTitleChange = e => {
        this.setState({ searchName: e.target.value });
    };

    handleSearch = () => {
        this.loadModalList();
    };

    handleResetSearch = () => {
        this.setState({ searchName: "" }, () => {
            this.loadModalList();
        });
    };

    // 弹窗编辑相关方法
    handleCreateModal = () => {
        this.setState({
            currentModal: JSON.parse(JSON.stringify(DEFAULT_MODAL_CONFIG)),
            currentModalId: null,
        });
    };

    handleEditModal = record => {
        this.setState({
            currentModal: {
                set:JSON.parse(record.dataJson)
            },
            currentModalId: record.id,
        });
    };

    async handleDeleteModal(record) {
        try {
            AntdModal.confirm({
            title: '确定要删除吗？',
            onOk: async () => {
                await API.deleteModal({id:record.id});
                await this.loadModalList();
              this.setState({currentModalId:null,currentModal:null });
            },
           })
        } catch (error) {
            console.error("删除弹窗失败:", error);
        }
    }

    handleUpdateModalConfig = updatedConfig => {
        this.setState({
            currentModal: updatedConfig,
        });
    };

    validateSaveData = () => {
        const { currentModal } = this.state;

        if(!currentModal.set.name){
          message.error("请输入弹窗名称");
          return false;
        }
        if(currentModal.set.position ==="bottom" && !currentModal.set.content){
            message.error("请输入弹窗内容");
            return false;
        }
       if(currentModal.set.position ==="center" && !currentModal.set.imageUrlList){
         message.error("请选择弹框图片");
        return false;
      }
        return true;
    }

    // 保存与取消
    handleSave = () => {
        const { currentModal, currentModalId } = this.state;

        if (!this.validateSaveData()) {
            return;
        }

        const saveData = {
            id: currentModalId,
            name: currentModal.set.name,
            dataJson: JSON.stringify(currentModal.set),
        };

        API.saveModal(saveData).then(res => {
            this.loadModalList();
           this.setState({currentModalId:null,currentModal:null });
        })
    };

    handleCancel = () => {
        this.setState({
            currentModal: JSON.parse(JSON.stringify(DEFAULT_MODAL_CONFIG)),
            currentModalId: null,
        });
    };

    render() {
        const { searchName, modalList, currentModal } = this.state;

        const TABLE_COLUMNS = [
            {
                title: "弹窗名称",
                dataIndex: "name",
                key: "name",
              width:"260",
              render: (text, record) => (
                    <ModalEditName row={record}  rowkey={"name"}  reload={()=>{
                      this.loadModalList();
                    }}/>
                  ),
            },
            {
                title: "操作",
                dataIndex: "action",
                key: "action",
                width:"110",
                render: (text, record) => (
                    <Space>
                        <a onClick={() => this.handleEditModal(record)}>编辑</a>
                        {/*<a onClick={() => this.handleDeleteModal(record)}>删除</a>*/}
                    </Space>
                ),
            },
        ];


        return (
            <div>
                <div className="bd">
                    <div className="main">
                        {/* 顶部操作栏 */}

                        <div className="panel-bd">
                            <div className="panel-module" style={{width: "350px"}}>
                              

                                {/* 搜索区域 */}
                                <div className="modal-search">
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                                        <div className="search-form-item" style={{ flex: 1, marginBottom: 0 }}>
                                            <Input
                                                placeholder="请输入弹窗名称"
                                                value={searchName}
                                                onChange={this.handleSearchTitleChange}
                                            />
                                        </div>
                                        <div className="search-actions">
                                            <Space>
                                                <Button type="primary" onClick={this.handleSearch}>搜索</Button>
                                                <Button onClick={this.handleResetSearch}>重置</Button>
                                            </Space>
                                        </div>
                                    </div>
                                </div>
                                {/* 头部按钮 */}
                                <div className="modal-header">
                                    <Space>
                                        <Button  onClick={this.handleCreateModal}>
                                            新增弹窗
                                        </Button>
                                    </Space>
                                </div>
                                {/* 表格区域 */}
                                <div className="modal-table">
                                    <Table
                                        pagination={false}
                                        size="small"
                                        columns={TABLE_COLUMNS}
                                        dataSource={modalList}
                                    />
                                </div>
                            </div>

                            {/* 预览区域 */}
                          {
                            currentModal ? <div className="panel-phone">
                              <Modal data={currentModal} />
                            </div>:null
                          }


                            {/* 配置区域 */}
                          {
                            currentModal ?
                              <div className="panel-module-set">
                                <Space style={{display: "flex", justifyContent: "flex-end", margin: "12px 0"}}>

                                  <Button type="primary" onClick={this.handleSave}>保存</Button>
                                </Space>
                                <div className="editor">
                                  <ModalSet data={currentModal} editSet={this.handleUpdateModalConfig}/>
                                </div>
                              </div>:null
                          }
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

// ReactDOM.render(<ModalPage />, document.getElementById("root"));
export default ModalPage;

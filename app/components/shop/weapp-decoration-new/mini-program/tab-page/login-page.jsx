import DesignPage from "../../design";


class Page extends React.Component {
  constructor(props) {
    super(props);

  }


  render() {

    return (
        <DesignPage  {...this.props}
                     pageUrl={"pages/login/home"}
                     isInsertPage={true}
                     bgStyle={{
                       height:'100%',
                       backgroundImage: `url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/87124045971.png")`,
                       backgroundSize: "contain",
                       backgroundPosition: "top",
                       position: "absolute",
                       backgroundRepeat: "no-repeat",
                       width: "375px",
                       top: "50%",
                       left: "50%",
                       transform: "translate(-50%, -50%)",
                     }}
                     panePhoneWrapperStyle={{
                       height: "600px",
                       marginTop: "45px"
                     }}
                     iQsirnStyle={"unset-height"}
        />
    );
  }
}
export default Page;

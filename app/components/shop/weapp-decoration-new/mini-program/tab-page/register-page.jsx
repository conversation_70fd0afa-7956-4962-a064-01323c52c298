import DesignPage from "../../design";


class Page extends React.Component {
  constructor(props) {
    super(props);

  }


  render() {

    return (
      <div>
        <DesignPage  {...this.props}
                     isInsertPage={true}
                     pageUrl={"pages/register-price/main"}
                     bgStyle={{
                       height:'800px',
                       backgroundImage: `url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/78054821907.png")`,
                       backgroundSize: "contain",
                       backgroundPosition: "top",
                       position: "absolute",
                       border: "2px solid #333",
                       backgroundRepeat: "no-repeat",
                       width: "375px",
                     }}
                     panePhoneWrapperStyle={{
                       height: "135px",
                       marginTop: "420px"
                     }}
                     iQsirnStyle={"unset-height"}
                     slot={(page)=>{
                      return <img src={page.pageSet?page.pageSet.topPic:""} style={{
                        position: "absolute",
                        width: "311px",
                        height: "80px",
                        top: "20px"
                      }}/>
                     }}

        />
      </div>
    );
  }
}
export default Page;


import PageEnum from "../../PageEnum";

const { Button, Table, Space, Modal, Form, Input, message, Select } = antd;
const { useState, useEffect } = React;
import request from "../../../../utils/plugins/axios/request";

const API = {
    getPageList: async (params) => {
        return await request({
            url: '/mall-admin/api/wxappPages/listCustomPages',
            needMask: true,
            method: 'post',
            data: params
        })
    },
    createPage: async (params) => {
        return await request({
            url: '/mall-admin/api/wxappPages/save',
            needMask: true,
            method: 'post',
            data: params
        })
    },
    deletePage: async (params) => {
        return await request({
            url: '/mall-admin/api/wxappPages/deleteCustomPage',
            needMask: true,
            method: 'post',
            data: params
        })
    },
    copyPage: async (params) => {
        return await request({
            url: '/mall-admin/api/wxappPages/copyCustomPage',
            needMask: true,
            method: 'post',
            data: params
        })
    }
};

const CustomPage = ({setIsCustomPage, setCustomPageConfig}) => {
    const [dataSource, setDataSource] = useState([]);
    const [newPageModalOpen, setNewPageModalOpen] = useState(false);

    const getPageList = async () => {
        const res = await API.getPageList();
        setDataSource(res);
    }

    useEffect(() => {
        getPageList()
    }, []);

    const columns = [
        {
            title: "页面名称",
            dataIndex: "pageName",
        },
        {
            title: "更新人",
            dataIndex: "updateBy",
        },
        {
            title: "更新时间",
            dataIndex: "updateTimeStr",
        },
        {
            title: "操作",
            dataIndex: "name",
            render: (_, record) => {
                return (
                    <Space>
                        <a onClick={() => handleEdit(record)}>编辑</a>
                        <SelectPageModal refreshFn={getPageList}  dataList={dataSource}>
                            复制
                        </SelectPageModal>
                        {/*<a onClick={() => handleDelete(record)}>删除</a>*/}
                    </Space>
                );
            },
        },
    ];

    const handleEdit = record => {
        setIsCustomPage(true)
        record.pageType= PageEnum.CUSTOM
        setCustomPageConfig(record)
     };
    const handleDelete = async (record) => {
        Modal.confirm({
            title: "确定要删除吗？",
            content: "删除后将无法恢复",
            onOk: () => {
                API.deletePage({ id: record.id }).then(() => {
                    getPageList()
                })
            },
        });
    };

    const handleNewPageModalOpen = () => {
        setNewPageModalOpen(true);
    };

    return (
        <div className="custom-page">
            <Button
                type="primary"
                onClick={() => {
                    handleNewPageModalOpen();
                }}
                style={{ marginBottom: 16 }}>
                新建页面
            </Button>
            <Table columns={columns} pagination={false} size="small" dataSource={dataSource} />
            <NewPageModal
                open={newPageModalOpen}
                onOk={pageName => {
                    API.createPage({
                        pageName,
                        pageType: 20,
                    }).then(() => {
                        getPageList()
                        setNewPageModalOpen(false)
                    })
                }}
                onCancel={() => {
                    setNewPageModalOpen(false);
                }}
            />
        </div>
    );
};

export default CustomPage;

const SelectPageModal = ({ refreshFn,  children, dataList }) => {
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);

    const onOk = async () => {
        const values = await form.validateFields();
        API.copyPage({
            id: values.id,
            pageName: values.pageName
        }).then(() => {
            refreshFn()
            setOpen(false)
        })

    };
    const onCancel = () => {
        setOpen(false);
        form.resetFields();
    };
    return (
        <div>
            <a onClick={() => setOpen(true)}>{children}</a>
            <Modal title={"复制页面"} open={open} onOk={onOk} onCancel={onCancel}>
                <Form form={form}>
                    <Form.Item label={"页面名称"} name="pageName" rules={[{ required: true, message: "请输入页面名称" }]}>
                        <Input placeholder="请输入页面名称" />
                    </Form.Item>
                    <Form.Item label="选择复制页面" name="id" rules={[{ required: true, message: "请选择页面" }]}>
                        <Select options={dataList.map(item => ({ label: item.pageName, value: item.id }))} />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

const NewPageModal = ({ open, onOk, onCancel }) => {
    const [form] = Form.useForm();

    useEffect(() => {
        if (!open) {
            form.resetFields();
        }
    }, [open, form]);

    const handleOk = async () => {
        const values = await form.validateFields();
        if (!values.name) {
            message.error("请输入页面名称");
            return;
        }
        onOk(values.name);
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal title={"新建页面"} open={open} onOk={handleOk} onCancel={handleCancel} okText="确认" cancelText="取消">
            <Form form={form}>
                <Form.Item label="页面名称" name="name" rules={[{ required: true, message: "请输入页面名称" }]}>
                    <Input placeholder="请输入页面名称" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

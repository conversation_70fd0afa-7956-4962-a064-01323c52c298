import DesignPage from "../../design";
const {Fragment} = React;
class LaunchScreen extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <DesignPage  {...this.props}
        isInsertPage={true}
        pageUrl={"pages/splash/index"}
        iQsirnStyle={"unset-height"}
        slot={(page)=>{
          return <div style={{
            position: "absolute",
            objectFit: "contain",
            width: "375px",
            height: "667px",
          }}> {page.pageSet && page.pageSet.imageUrl? <img src={page.pageSet.imageUrl} style={{
            objectFit: "contain",
            width: "100%",
            height: "100%",
          }}/>:null}</div>
        }}/>
    );
  }
}

export default LaunchScreen;

import {lib} from "../../../../common/react/utils/lib";

const {useState, useEffect, Fragment} = React;

const {message,Upload,Button} = antd;
const {UploadOutlined} = icons
export const ItemVideoButton = (props) => {
  /**
   * 主图
   */
  const [mainImages, setImages] = useState(Array(1).fill(null))
  useEffect(() => {
    const value = props.value;
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        mainImages[i] = value[i]
      }
      setImages([...mainImages])
    } else if (value === null) {
      setImages(Array(1).fill(null))
    }
  }, [props.value])

  const innerOnChange = (index, {file, fileList}) => {
    const {onChange} = props
    if (file.status === "uploading") {
      mainImages[index] = file
      setImages([...mainImages])
    } else if (file.status === "done") {
      mainImages[index] = file
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "removed") {
      mainImages[index] = null
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "error") {
      message.error("上传失败")
      setImages([])
    }
  }
  const base64ToFile = (base64, filename, mimeType) => {
    // 分离 Base64 数据和元信息
    const arr = base64.split(',');
    const dataPart = arr[arr.length > 1 ? 1 : 0];

    // 自动解析 MIME 类型（如果未指定）
    const metaPart = arr[0].match(/:(.*?);/);
    const finalMimeType = mimeType || (metaPart ? metaPart[1] : 'application/octet-stream');

    // 转换为字节数组
    const byteString = atob(dataPart);
    const buffer = new ArrayBuffer(byteString.length);
    const intArray = new Uint8Array(buffer);

    for (let i = 0; i < byteString.length; i++) {
      intArray[i] = byteString.charCodeAt(i);
    }

    // 生成 Blob 并转换为 File
    const blob = new Blob([buffer], { type: finalMimeType });
    return new File([blob], filename, {
      type: finalMimeType,
      lastModified: Date.now()
    });
  }
  const generateThumbnail = (file) => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const url = URL.createObjectURL(file);

      video.src = url;
      video.addEventListener('loadedmetadata', () => {
        // 设置视频时间点（这里取第1秒）
        video.currentTime = 1;
      });

      video.addEventListener('seeked', () => {
        const ctx = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        if(ctx){
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          const thumbnail = canvas.toDataURL('image/jpeg');
          canvas.toBlob(blob => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('生成缩略图失败'));
            }
            URL.revokeObjectURL(url);
          }, 'image/jpeg', 0.8);
          resolve(thumbnail);
        }
      });
    });
  };
  function uploadThumbnailFileRequest(
                                       file,onProgress) {
    return new Promise((resolve, reject) => {
      var data = new FormData();
      let suffix = file.name.split(".").reverse()[0].toLocaleLowerCase();
      var key = `${lib.generateNumberString(false,18)}_${parseInt(Math.random() * 1000)}.${suffix}`;
      data.append("name", "dev/" + key);
      data.append("key", key);
      data.append(
        "policy", "ewogICAgImV4cGlyYXRpb24iOiAiMjA0OS0xMi0zMVQwNTo0ODowNy4wMDBaIiwKICAgICJjb25kaXRpb25zIjogWwogICAgICAgIFsKICAgICAgICAgICAgImNvbnRlbnQtbGVuZ3RoLXJhbmdlIiwKICAgICAgICAgICAgMCwKICAgICAgICAgICAgMTA0ODU3NjAwMAogICAgICAgIF0KICAgIF0KfQ=="
      );
      data.append("OSSAccessKeyId", "LTAI5tNGqjjEfJ1n3xbV42v9");
      data.append("success_action_status", "200");
      data.append("signature", "oRnH+J0Ru/bPN/E1aAAO76P+DHg=");
      data.append("file", file);
      axios
        .request({
          url: "https://dante-img.oss-cn-hangzhou.aliyuncs.com",
          method: "POST",
          data: data,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          onUploadProgress: ({ total, loaded }) => {
            onProgress && onProgress({ percent: Math.round((loaded / total) * 100).toFixed(2) }, file)
          },
        })
        .then(json => {
          resolve({json, url:"https://dante-img.oss-cn-hangzhou.aliyuncs.com/" + key}) ;
        })
        .catch(reject)
})

  }
 function customRequest({
    data,
    file,
    filename,
    headers,
    onError,
    onProgress,
    onSuccess,
    withCredentials,
  }) {
    // eslint-disable-next-line no-undef
    uploadThumbnailFileRequest(file,onProgress)
      .then(async (response) => {
        file.url = response.url
        // 生成缩略图
        const thumbnailBlob = await generateThumbnail(file);
        const fileName =  `${file.name.replace(/\.[^/.]+$/, "")}_thumbnail.jpg`
        const thumbnailFile =base64ToFile(
          thumbnailBlob,
          fileName,
          'image/jpeg' // 可选参数
        );
        const thumbUrlResponse = await uploadThumbnailFileRequest(thumbnailFile)
        file.thumbUrl =thumbUrlResponse.url
        onSuccess(response.json, file)
      })
      .catch(onError)
    return {
      abort() {
        console.log("upload progress is aborted.")
      },
    }
  }
  const beforeUpload = async (file) => {
    let suffix = file.name.slice(file.name.lastIndexOf('.')).toLocaleLowerCase()
    if (![".mp4"].includes(suffix)) {
      message.error(`只能上传mp4格式的文件`)
      return Upload.LIST_IGNORE
    }
    if (file.size / 1024 / 1024 > 100) {
      message.error(`对不起，文件大小不能超过${100}M`)
      return Upload.LIST_IGNORE
    }
  }
  return <Upload
      maxCount={1}
      fileList={mainImages[0] ? [mainImages[0]] : []}
      customRequest={customRequest}
      beforeUpload={beforeUpload}
      onChange={(file) => {
        innerOnChange(0, file)
      }}
    >
    <Button icon={<UploadOutlined />}>上传</Button>
    </Upload>
}

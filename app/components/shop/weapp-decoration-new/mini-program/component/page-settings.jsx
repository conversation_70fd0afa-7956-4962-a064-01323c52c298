/**
 * 全局设置组件
 * 用于设置页面级别的配置，如背景色等
 */
 class PageSettings extends React.Component {

    render() {
        const { pageData, onChange } = this.props;
        const backgroundColor = (pageData && pageData.pageSet && pageData.pageSet.backgroundColor) || "#FFEEFF";

        return (
            <div className="page-settings">
                {/* 页面背景色设置 */}
                <div className="line">
                    <label className="decoration-item-required">页面背景色</label>
                    <div>
                        <input
                            type="color"
                            value={backgroundColor}
                            onChange={(e) => {
                                if (onChange) {
                                    onChange({
                                        ...pageData,
                                        pageSet: {
                                            ...(pageData && pageData.pageSet ? pageData.pageSet : {}),
                                            backgroundColor: e.target.value
                                        }
                                    });
                                }
                            }}
                            style={{
                                width: '50px',
                                height: '32px',
                                border: '1px solid #d9d9d9',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                        />
                        <span style={{ marginLeft: '10px', color: '#666' }}>{backgroundColor}</span>
                    </div>
                </div>
                  <div style={{
                        marginTop: '8px',
                        marginLeft: '24px',
                        fontSize: '12px',
                        color: '#999',
                        lineHeight: '1.4'
                    }}>
                        非标题部分，正文内容无组件填充部分都将展示当前颜色，默认#FFEEFF
                    </div>
            </div>
        );
    }
}

export default PageSettings;

.link-dialog{
    .dialog-hd{
        .nav-pills>li{
            margin-top:2px;
            a{
                line-height: 25px;
            }
        }
    }
    .dialog-bd{
        height: 460px;
        overflow: hidden;
        .pages{
            margin-top:20px;
        }
    }
    width: 820px;
    .list{
        display: flex;
        flex-direction: row ;
        flex-wrap: wrap;
        justify-content: flex-start;
        height: 356px;
    }
    .node{
        width:120px;
        height: 168px;
        background:#fff;
        margin:5px;
        position:relative;
        .selected{
            position:absolute;
            left: 0;
            right: 0;
            top:0;
            bottom:0;
            border:solid 3px #f30;
            z-index: 10;
        }
        .pic {
            width: 120px;
            height: 120px;
            box-sizing: border-box;
            overflow:hidden;
            background: url(#) no-repeat center center;
            background-size: cover;
            position:relative;
            .price{
                background:rgba(0 , 0 , 0 , 0.5);
                position: absolute;
                text-align: center;
                color:#fff;
                bottom: 0;
                width: 100%;
                height: 24px;
                line-height: 24px;
                font-size: 14px;
                font-weight: bold;
            }
        }
        .title{
            font-size: 12px;
            line-height: 1.5;
            height: 36px;
            overflow:hidden;
            padding:0 5px;
            margin-top:5px
        }
    }
    .pagination{
        margin-top:10px;
        .panel-page-size{
            display: none;
        }
        .panel-jump{
            margin-left: 10px;
        }
    }
}
.link-box {
    .dialog{
        display: block;
    }
    .btn.url{
        margin:0;
        display: block;
        float: left;
        width: 210px;
        position: relative;
    }
  .del {
    font-family: 'iconfont';
    font-size: 16px;
    color: #888;
    cursor: pointer;
    display: inline-block;
    margin: 0 12px;
    vertical-align: middle;
  }
}







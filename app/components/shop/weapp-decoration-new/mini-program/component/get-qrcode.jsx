import {lib} from "../../../../common/react/utils/lib";
const {useLayoutEffect,useRef} = React;

export default function PagePreviewQrCode({pageUrl,isCustomPage,customPageId}) {
  const domRef = useRef();
  useLayoutEffect(()=>{
    if(domRef.current!=null){
      const qrcode = new QRCode( domRef.current, {
        width : 200,
        height : 200
      });
      let url =pageUrl

      if(isCustomPage){
        url = "pages/customer-design-page/index"
      }
      let qrUrl = `${lib.getShareHost()}design-preview?storeId=${sessionStorage.shopId}&pageUrl=${url}${isCustomPage?`&pageId=${customPageId}`:""}`
      qrcode.makeCode(qrUrl);
    }
  },[])

  return <div id='qrcode' ref={domRef}></div>
}

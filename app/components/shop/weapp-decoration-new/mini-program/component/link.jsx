
import HotAreaDrawer, {RadioGroupNameMap} from "../module/hot-area-drawer";
const {Fragment} = React;
const {Button} = antd;

class Link extends React.Component{
    constructor(props){
        super(props);
        this.state = {
            pages : props.data.pages ,
            url : props.data.url ,
            showDialog : false
        }
    }
    componentWillReceiveProps(props){
        this.setState({
            pages : props.data.pages ,
            url : props.data.url
        })
    }
    confirm(url){
        this.setState({
            url : url ,
            showDialog : false
        })
        this.props.methods.updateUrl(url);
    }
    cancel(){
        this.setState({
            showDialog : false
        })
    }
    select(){
        this.setState({
            showDialog : true
        })
    }
    render(){
        var urlText = '';
       switch (this.state.url.radioKey){
         case RadioGroupNameMap.search:
           urlText = '已链接到：商品页';
           break;
         case RadioGroupNameMap.appoint:
           urlText = '已链接到：指定页面';
           break
         case RadioGroupNameMap.video:
           urlText = '已链接到：视频号';
           break
         case RadioGroupNameMap.customerService:
           urlText = '已链接到：客服号';
           break
         case RadioGroupNameMap.modal:
           urlText = '已链接到：弹窗';
           break
         case RadioGroupNameMap.marketingTools:
          urlText = '已链接到：营销活动';
          break;
         default:
           break
       }
        return (
            <div className='link-box'>
                {
                    this.state.showDialog ?
                      <HotAreaDrawer
                        open={ this.state.showDialog}
                        setOpen={(e) => this.setState({ showDialog: e })}
                        onSubmit={(radioKey, radioValue) => {
                          this.confirm({radioKey, radioValue})
                        }}
                        defaultData={{
                          radioKey: this.state.url.radioKey,
                          radioValue: this.state.url.radioValue
                        }}
                      />
                      : ''
                }
              {
                urlText? <Fragment><Button type={"link"} onClick={()=>this.select()}>{urlText}</Button> <div className='del' onClick={()=>{
                  this.confirm({radioKey:'', radioValue:null})
                }}>&#xe60d;</div></Fragment> :<Button onClick={()=>this.select()}>请选择</Button>
              }
            </div>
        )

    }
}
export default Link;

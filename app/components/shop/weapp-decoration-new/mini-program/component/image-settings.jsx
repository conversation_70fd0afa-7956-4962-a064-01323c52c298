import Picasa from "../../component/picasa";
const { Fragment } = React;
const { Modal, Switch } = antd;

/**
 * 图片设置组件
 * 包含图片上传、删除、尺寸配置等功能
 */
class ImageSettings extends React.Component {

    // 比例配置常量
    static ASPECT_RATIOS = {
        "16:9": 16/9,
        "4:3": 4/3,
        "3:2": 3/2,
        "1:1": 1/1,
        "2:1": 2/1
    };

    // 默认配置
    static DEFAULT_SETTINGS = {
        titleImage: "",
        fixedRatio: true, // 默认按比例
        aspectRatio: "16:9",
        imageHeight: 110, // 默认高度110
        imageWidth: 375, // 默认宽度375
        customImageHeight: 110,
        widthMode: "fixed", // 默认固定
        heightMode: "fixed", // 默认固定
        showHeight: true,
        tips:""
    };

    // 获取合并后的设置（用户设置 + 默认设置）
    getMergedSettings = () => {
        const { imageSettings, defaultSettings } = this.props;
        const finalDefaults = {
            ...ImageSettings.DEFAULT_SETTINGS,
            ...defaultSettings
        };
        return {
            ...finalDefaults,
            ...imageSettings
        };
    }

    // 删除图片的方法
    handleDeleteImage = () => {
        const { onDeleteImage } = this.props;
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该图片吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
                if (onDeleteImage) {
                    onDeleteImage();
                }
            }
        });
    }

    // 计算当前宽度值（固定比例模式下）
    calculateCurrentWidth = () => {
        const mergedSettings = this.getMergedSettings();
        const { aspectRatio, imageHeight } = mergedSettings;
        const ratio = ImageSettings.ASPECT_RATIOS[aspectRatio] || 16/9;
        return Math.round(imageHeight * ratio);
    }

    // 处理宽度变化（固定比例模式下）
    handleWidthChange = (newWidth) => {
        const { onChange, imageSettings } = this.props;
        const mergedSettings = this.getMergedSettings();
        const { aspectRatio, fixedRatio } = mergedSettings;

        // 确保输入值不为负数
        const validWidth = Math.max(0, newWidth);

        if (onChange) {
            if (fixedRatio) {
                // 只有在固定比例模式下才换算高度
                const ratio = ImageSettings.ASPECT_RATIOS[aspectRatio] || 16/9;
                const newHeight = Math.round(validWidth / ratio);
                onChange({
                    ...imageSettings,
                    imageHeight: newHeight
                });
            } else {
                // 非固定比例模式下，只更新宽度
                onChange({
                    ...imageSettings,
                    imageWidth: validWidth
                });
            }
        }
    }

    // 渲染图片上传区域
    renderImageUpload = () => {
        const { onImageUpload } = this.props;
        const mergedSettings = this.getMergedSettings();
        const { titleImage,tips } = mergedSettings;

        return (
            <div className="line navigation-pic">
                <label>标题图片</label>
                <div style={{ position: 'relative', display: 'inline-block', marginTop: '10px' }}>
                    <Picasa
                        data={{
                            style: { width: "150px", height: "75px" },
                            src: titleImage,
                        }}
                        methods={{
                            updateSrc: (src) => {
                                if (onImageUpload) {
                                    onImageUpload(src);
                                }
                            },
                        }}
                    />
                    {titleImage && (
                        <div
                            className="delete-icon"
                            onClick={this.handleDeleteImage}
                            style={{
                                position: 'absolute',
                                top: '-10px',
                                right: '-10px',
                                width: '20px',
                                height: '20px',
                                borderRadius: '50%',
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                color: '#fff',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                cursor: 'pointer',
                                fontSize: '14px'
                            }}
                        >
                            ×
                        </div>
                    )}
                </div>

            </div>
        );
    }

    // 渲染固定比例开关
    renderFixedRatioSwitch = () => {
        const { onChange, imageSettings } = this.props;
        const mergedSettings = this.getMergedSettings();
        const { fixedRatio } = mergedSettings;

        return (
            <div className="line">
                <label>
                    <span style={{ marginRight: '8px' }}>固定比例</span>
                </label>
                <Switch
                    checked={fixedRatio}
                    onChange={(checked) => {
                        if (onChange) {
                            onChange({
                                ...imageSettings,
                                fixedRatio: checked
                            });
                        }
                    }}
                    size="small"
                />
            </div>
        );
    }

    // 渲染固定比例模式的设置
    renderFixedRatioSettings = () => {
        const { onChange, imageSettings } = this.props;
        const mergedSettings = this.getMergedSettings();
        const { aspectRatio } = mergedSettings;

        return (
            <div>
                {/* 比例设置 */}
                <div className="line">
                    <label>比例设置</label>
                    <select
                        value={aspectRatio}
                        onChange={(e) => {
                            if (onChange) {
                                onChange({
                                    ...imageSettings,
                                    aspectRatio: e.target.value
                                });
                            }
                        }}
                        className="form-control"
                        style={{ width: '120px' }}
                    >
                        <option value="16:9">16:9</option>
                        <option value="4:3">4:3</option>
                        <option value="3:2">3:2</option>
                        <option value="1:1">1:1</option>
                        <option value="2:1">2:1</option>
                    </select>
                </div>

                {/* 宽度设置 */}
                <div className="line">
                    <label>宽度设置</label>
                    <input
                        type="number"
                        min="1"
                        max="800"
                        value={this.calculateCurrentWidth()}
                        onChange={(e) => {
                            const inputValue = e.target.value;
                            // 防止输入负数和无效值
                            if (inputValue === '' || inputValue === '0') {
                                return; // 不处理空值或0
                            }
                            const newWidth = Math.max(1, parseInt(inputValue) || 375);
                            this.handleWidthChange(newWidth);
                        }}
                        onKeyDown={(e) => {
                            // 阻止输入负号和其他非数字字符
                            if (e.key === '-' || e.key === '+' || e.key === 'e' || e.key === 'E') {
                                e.preventDefault();
                            }
                        }}
                        className="form-control"
                        placeholder="宽度"
                        style={{ width: '80px' }}
                    />
                    <span style={{ marginLeft: '5px', color: '#666' }}>px</span>
                </div>
            </div>
        );
    }

    // 渲染自定义模式的设置
    renderCustomSettings = () => {
        const { onChange, imageSettings } = this.props;
        const mergedSettings = this.getMergedSettings();
        const { widthMode, heightMode, imageWidth, customImageHeight,showHeight  } = mergedSettings;

        return (
            <div>
                {/* 宽度设置 */}
                { showHeight ? <div className="line">
                    <label>宽度设置</label>
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                      {/*<select*/}
                      {/*    value={widthMode}*/}
                      {/*    onChange={(e) => {*/}
                      {/*        if (onChange) {*/}
                      {/*            onChange({*/}
                      {/*                ...imageSettings,*/}
                      {/*                widthMode: e.target.value*/}
                      {/*            });*/}
                      {/*        }*/}
                      {/*    }}*/}
                      {/*    className="form-control"*/}
                      {/*    style={{ width: '100px' }}*/}
                      {/*>*/}
                      {/*    <option value="fixed">固定</option>*/}
                      {/*    <option value="auto">适应内容</option>*/}
                      {/*</select>*/}
                      {widthMode === "fixed" && (
                        <Fragment>
                          <input
                            type="number"
                            min="1"
                            max="800"
                            value={imageWidth}
                            onChange={(e) => {
                              const inputValue = e.target.value;
                              // 防止输入负数和无效值
                              if (inputValue === '' || inputValue === '0') {
                                return; // 不处理空值或0
                              }
                              const newWidth = Math.max(1, parseInt(inputValue) || 375);
                              if (onChange) {
                                onChange({
                                  ...imageSettings,
                                  imageWidth: newWidth
                                });
                              }
                            }}
                            onKeyDown={(e) => {
                              // 阻止输入负号和其他非数字字符
                              if (e.key === '-' || e.key === '+' || e.key === 'e' || e.key === 'E') {
                                e.preventDefault();
                              }
                            }}
                            className="form-control"
                            placeholder="宽度"
                            style={{ width: '80px' }}
                          />
                          <span style={{ color: '#666' }}>px</span>
                        </Fragment>
                      )}
                    </div>
                  </div>:null}

                {/* 高度设置 */}
                <div className="line">
                    <label>高度设置</label>
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                        {/*<select*/}
                        {/*    value={heightMode}*/}
                        {/*    onChange={(e) => {*/}
                        {/*        if (onChange) {*/}
                        {/*            onChange({*/}
                        {/*                ...imageSettings,*/}
                        {/*                heightMode: e.target.value*/}
                        {/*            });*/}
                        {/*        }*/}
                        {/*    }}*/}
                        {/*    className="form-control"*/}
                        {/*    style={{ width: '100px' }}*/}
                        {/*>*/}
                        {/*    <option value="fixed">固定</option>*/}
                        {/*    <option value="auto">适应内容</option>*/}
                        {/*</select>*/}
                        {heightMode === "fixed" && (
                            <Fragment>
                                <input
                                    type="number"
                                    min="1"
                                    max="500"
                                    value={customImageHeight}
                                    onChange={(e) => {
                                        const inputValue = e.target.value;
                                        // 防止输入负数和无效值
                                        if (inputValue === '' || inputValue === '0') {
                                            return; // 不处理空值或0
                                        }
                                        const newHeight = Math.max(1, parseInt(inputValue) || 110);
                                        if (onChange) {
                                            onChange({
                                                ...imageSettings,
                                                customImageHeight: newHeight
                                            });
                                        }
                                    }}
                                    onKeyDown={(e) => {
                                        // 阻止输入负号和其他非数字字符
                                        if (e.key === '-' || e.key === '+' || e.key === 'e' || e.key === 'E') {
                                            e.preventDefault();
                                        }
                                    }}
                                    className="form-control"
                                    placeholder="高度"
                                    style={{ width: '80px' }}
                                />
                                <span style={{ color: '#666' }}>px</span>
                            </Fragment>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    render() {
        const mergedSettings = this.getMergedSettings();
         const { fixedRatio,tips } = mergedSettings; // 暂时不使用

        return (
            <div>
                {/* 图片上传 */}
                {this.renderImageUpload()}
                {tips? <div style={{color:'#666',marginLeft:35,marginBottom:12}}>{tips}</div>:null}

                {/* 固定比例开关 - 暂时隐藏 */}
                {/* {this.renderFixedRatioSwitch()} */}

                {/* 尺寸设置 - 暂时只显示自定义设置 */}
                {/* {fixedRatio ? this.renderFixedRatioSettings() : this.renderCustomSettings()} */}
                {this.renderCustomSettings()}
            </div>
        );
    }
}

export default ImageSettings;


import commonSet from './common-set';
var {ToolBar , CommonSet , getDefaultCommonSet} = commonSet;
import Link from '../component/link';
import Picasa from '../../component/picasa';
import module from './module';
var {Module , ModuleSet} = module;
var defaultSet = {
    name : 'image' ,
    title : '单图',
    commonSet : getDefaultCommonSet(),
    set : {
        pic : '',
        url : {
          radioKey:'',
          radioValue:null
        }
    }
};

class Image extends Module{
    load(e){
        var item = this.state.item;
        item.set.width = e.currentTarget.width * 2;
        item.set.height = e.currentTarget.height * 2;
    }
    render(){
        var item = this.state.item;
        return (
            <div className={`image module ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
            style={{marginTop : item.commonSet.marginTop / 2 + 'px' , marginBottom : item.commonSet.marginBottom / 2 + 'px'}}>
              <ToolBar parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}  isSubModule={this.state.isSubModule} />
                <img src={item.set.pic || 'http://web.myazgo.com.cn/assets/store/img/diy/banner/01.png'} onLoad={this.load.bind(this)} />
            </div>
        )
    }
}

class ImageSet extends ModuleSet{
    render(){
        var item = this.state.item;
        return (
            <div className='image-set'>
                
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
               
                <div className='line line-pic'>
                    <label>图片</label>
                    <Picasa data={{style : {width : '150px' , height : '75px' } , src : item.set.pic}} methods={{
                        updateSrc : (src) => {
                            item.set.pic = src;
                            this.refreshSet();
                        }
                    }} />
                </div>
                {
                    this.props.imageSize?
                    <span style={{marginLeft:'65px',fontSize:'12px'}}>尺寸  <span style={{marginLeft:'12px'}}>
                        {this.props.imageSize}
                        </span></span>
                    :
                    null
                }
                
                <div className='line '>
                    <label>链接</label>
                    <Link data={{pages : this.state.pages  , url : item.set.url}} methods={{
                        updateUrl : (url) => {
                            item.set.url = url;
                            this.refreshSet();
                        }
                    }} />

                </div>
            </div>
        )
    }
}
var getImageDefaultSet = function(){
    return JSON.parse(JSON.stringify(defaultSet));
}
export default {
    Image : Image , ImageSet : ImageSet , getImageDefaultSet : getImageDefaultSet
}


import commonSet from "./common-set";
import Link from "../component/link";
import Picasa from "../../component/picasa";
import module from "./module";
var { Module, ModuleSet } = module;
var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
const { Select, Switch, Radio } = antd;
var defaultSet = {
    name: "guide",
    title: "导航组",
    commonSet: getDefaultCommonSet(),
    set: {
        type: 1,
        num: 5,
        layout: "tile", // 布局方式：tile-平铺，scroll-滚动
        ratio: "1:1", // 行高比例：全体设置
        showTitle: true, // 是否显示标题：全体设置
        list: [],
    },
};
for (var i = 0; i < 10; i++) {
    defaultSet.set.list.push({ icon: "", title: "我的分类", url: "" });
}

class Guide extends Module {
    render() {
        var item = this.state.item;
        // 为现有组件提供默认layout值
        if (!item.set.layout) {
            item.set.layout = 'tile';
        }
        var isScrollLayout = item.set.layout === 'scroll';

        // 渲染导航项
        var renderNodes = () => {
            // 为现有组件提供默认值
            if (!item.set.ratio) {
                item.set.ratio = '1:1';
            }
            if (item.set.showTitle === undefined) {
                item.set.showTitle = true;
            }

            return item.set.list.map((node, index) => {
                return (
                    <div
                        className={`node ${!item.set.showTitle ? 'no-title' : ''}`}
                        key={index}
                        data-ratio={item.set.ratio}>
                        <div
                            className="icon"
                            style={{
                                backgroundImage:
                                    "url(" +
                                    (node.icon ||
                                        "http://img.alicdn.com/tps/i4/TB19QYcabr1gK0jSZR0wu2P8XXa.png_300x300Q90s50.jpg_.webp") +
                                    ")",
                            }}></div>
                        {item.set.showTitle && <div className="title">{node.title}</div>}
                    </div>
                );
            });
        };

        return (
            <div
                className={`guide  ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                }}>
              <ToolBar parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}  isSubModule={this.state.isSubModule} />
                {isScrollLayout ? (
                    // 滚动布局：水平滚动容器
                    <div className="scroll-container">
                        <div className={"list list_" + item.set.num + " type_" + item.set.type + " layout_scroll"}>
                            {renderNodes()}
                        </div>
                    </div>
                ) : (
                    // 平铺布局：正常的多行显示
                    <div className={"list list_" + item.set.num + " type_" + item.set.type + " layout_tile"}>
                        {renderNodes()}
                    </div>
                )}
            </div>
        );
    }
}

class GuideSet extends ModuleSet {
    add() {
        var list = this.state.item.set.list;
        list.push({
            icon: "",
            title: "",
            url: "",
        });
        this.setState(this.state);
    }
    delete(index) {
        var list = this.state.item.set.list;
        list.splice(index, 1);
        this.setState(this.state);
    }
    render() {
        var self = this;
        var item = this.state.item;
        // 为现有组件提供默认值
        if (!item.set.layout) {
            item.set.layout = 'tile';
        }
        if (!item.set.ratio) {
            item.set.ratio = '1:1';
        }
        if (item.set.showTitle === undefined) {
            item.set.showTitle = true;
        }
        return (
            <div className="image-set">
                <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
                <div className="line">
                    <label>图标类型</label>
                    <Select
                        style={{ width: 120 }}
                        value={item.set.type}
                        onChange={(value) => {
                            item.set.type = value;
                            this.refreshSet();
                        }}>
                        <Select.Option value={1}>正常</Select.Option>
                        {/* 圆型选项只在1:1比例时显示 */}
                        {item.set.ratio !== '3:4' && (
                            <Select.Option value={2}>圆型</Select.Option>
                        )}
                    </Select>
                    {item.set.ratio === '3:4' && item.set.type === 2 && (
                        <div className="help-text" style={{color: '#ff6b6b', marginTop: '5px'}}>
                            圆型图标仅支持1:1比例
                        </div>
                    )}
                </div>
                <div className="line">
                    <label>每行</label>
                    <Select
                        style={{ width: 120 }}
                        value={item.set.num}
                        onChange={(value) => {
                            item.set.num = value;
                            this.refreshSet();
                        }}>
                        <Select.Option value={3}>3个</Select.Option>
                        <Select.Option value={4}>4个</Select.Option>
                        <Select.Option value={5}>5个</Select.Option>
                    </Select>
                </div>
                <div className="guide-line">
                     <div className="label">布局方式</div>
                    <div style={{display: 'inline-block', verticalAlign: 'top'}}>
                        <Radio.Group
                            value={item.set.layout}
                            onChange={(e) => {
                                item.set.layout = e.target.value;
                                this.refreshSet();
                            }}>
                            <Radio value="tile">平铺</Radio>
                            <Radio value="scroll">滚动</Radio>
                        </Radio.Group>
                        <div className="help-text">
                            {item.set.layout === 'scroll'
                                ? '滚动：当图片内容过多，超出单行显示区域时，会以水平滚动的方式展示剩余内容。'
                                : '平铺：多行网格布局，内容自动换行。'
                            }
                        </div>
                    </div>
                </div>
                <div className="guide-line">
                    <div className="label">行高比例</div>
                <div style={{display: 'inline-block', verticalAlign: 'top'}}>
                     <Select
                        style={{ width: 120 }}
                        value={item.set.ratio || '1:1'}
                        onChange={(value) => {
                            item.set.ratio = value;
                            // 当比例改为3:4时，自动将图标类型重置为正常
                            if (value === '3:4' && item.set.type === 2) {
                                item.set.type = 1;
                            }
                            this.refreshSet();
                        }}>
                        <Select.Option value="1:1">1:1</Select.Option>
                        <Select.Option value="3:4">3:4</Select.Option>
                    </Select>
                    <div className="help-text">
                        设置所有导航图标的行高比例，影响图标的显示高度。
                    </div>
                 </div>
                </div>
                <div className="guide-line">
                    <div className="label">是否有标题</div>
                    <div style={{display: 'inline-block', verticalAlign: 'top'}}>
                    <Switch
                        checked={item.set.showTitle !== false}
                        onChange={(checked) => {
                            item.set.showTitle = checked;
                            this.refreshSet();
                        }}
                    />
                    <div className="help-text">
                        控制是否显示导航图标下方的标题文字。
                    </div>
                    </div>
                </div>
                {item.set.list.map((node, index) => {
                    return (
                        <div className="group" key={index}>
                            <div className="group-op">
                                <div className="name">
                                    第{index + 1}组{" "}
                                    <div className="del" onClick={self.delete.bind(self, index)}>
                                        &#xe60d;
                                    </div>
                                </div>
                            </div>
                            <div className="line line-pic">
                                <label>图标</label>
                                <Picasa
                                    data={{ style: { width: "75px", height: "75px" }, src: node.icon }}
                                    methods={{
                                        updateSrc: src => {
                                            node.icon = src;
                                            this.refreshSet();
                                        },
                                    }}
                                />
                                
                            </div>

                            <div className="line">
                                <label>标题</label>
                                <input
                                    type="text"
                                    maxLength="5"
                                    value={node.title}
                                    className="form-control"
                                    placeholder="标题"
                                    onChange={e => {
                                        node.title = e.currentTarget.value;
                                        this.refreshSet();
                                    }}
                                />
                            </div>
                            <div className="line">
                                <label>链接</label>
                                <Link
                                    data={{ pages: self.state.pages, url: node.url }}
                                    methods={{
                                        updateUrl: url => {
                                            node.url = url;
                                            this.refreshSet();
                                        },
                                    }}
                                />
                            </div>
                        </div>
                    );
                })}
                <div className="btn btn-cool" onClick={this.add.bind(this)}>
                    添加一组
                </div>
            </div>
        );
    }
}
var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};
export default {
    Guide: Guide,
    GuideSet: GuideSet,
    getGuideDefaultSet: getDefaultSet,
};

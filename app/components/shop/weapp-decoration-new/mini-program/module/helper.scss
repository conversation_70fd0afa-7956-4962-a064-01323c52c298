.helper {
    .helper-content {
        background: #fff;
        padding: 10px 15px;

        .helper-box {
            display: flex;
            align-items: center;
            background-color: #ffefee;
            border-radius: 8px;
            padding: 12px 15px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #ff4d4f;
        }

        .helper-icon {
            width: 36px;
            height: 36px;
            margin-right: 10px;
            font-family: "iconfont";
            font-size: 26px;
            color: #ff4d4f;
        }

        .helper-text {
            flex: 1;

            .helper-title {
                font-size: 14px;
                font-weight: 500;
                color: #333;
                margin-bottom: 4px;
            }

            .helper-desc {
                font-size: 12px;
                color: #999;
            }
        }

        .helper-button {
            .add-button {
                display: inline-block;
                background-color: #ff4d4f;
                color: #fff;
                font-size: 13px;
                padding: 6px 12px;
                border-radius: 4px;
                text-decoration: none;

                &:hover {
                    background-color: #ff7875;
                }
            }
        }
    }
}

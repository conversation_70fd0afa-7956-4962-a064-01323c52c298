
import commonSet from './common-set';
import ItemSelect from '../../component/item-select';
import Picas<PERSON> from '../../component/picasa';
import module from './module';
var { Module, ModuleSet } = module;

var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;

import request from "../../../../utils/plugins/axios/request";

var defaultSet = {
    name: 'item-list',
    title: '商品模块',
    commonSet: getDefaultCommonSet(),
    set: {
        title: {
            type: 0,
            text: '',
            pic: ''
        },
        ids: []
    }
};
class ItemList extends Module {
  constructor(props){
    super(props);
    this.getItemListByIds();
  }
    componentWillReceiveProps(nextProps) {
      super.componentWillReceiveProps(nextProps)
        // 只有在数据发生变化时才更新状态和重新获取数据
      this.getItemListByIds();
    }


    getItemListByIds() {
        var self = this;
        if (this.state.item.set.ids.length == 0) {
            return;
        }

        request({
           url: '/mall-admin/api/items/news/search-in-shop',
            method: 'post',
            data: {
                shopId: window.shopId,
                ids: this.state.item.set.ids.join('_'),
                ignoreItemTag: true
            },
            needMask: false,
            success(json) {
                self.setState({
                    list: json.list
                });
            }
        })
    }

    render() {
        var item = this.state.item;
        var list = this.state.list || [];
        const isSubModule = this.state.isSubModule

        return (
            <div className={`item-list ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{ marginTop: item.commonSet.marginTop / 2 + 'px', marginBottom: item.commonSet.marginBottom / 2 + 'px',zIndex:isSubModule?200:100 }}>
                <ToolBar isSubModule={isSubModule} parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}/>

                {
                    (() => {
                        var title = item.set.title;
                        if (title.type == 1) {
                            return (
                                <div className='title'>
                                    <div className='text'>{title.text||title.title}</div>
                                    <div className='line'></div>
                                </div>
                            )
                        }
                        else if (title.type == 2) {
                            return (
                                <div className='title-img'>
                                    <img src={(title.pic||title.mainPic) + '?x-oss-process=image/resize,l_750'} />
                                </div>
                            )
                        }
                    })()
                }

                <div className='item-list-box'>
                    {
                        item.set.ids.length == 0 ? <div className='no-items'>请选择商品</div> : ''
                    }

                    {
                        list.map(function (item, index) {
                            return (
                                <div key={index} className='item'>
                                    <div className='pic' style={{ backgroundImage: 'url(' + item.mainPic + ')' }}>
                                    </div>
                                    <div className='title'>{item.name||item.title}</div>
                                    <div className='price'>￥{item.price / 100}</div>
                                </div>
                            )
                        })
                    }
                </div>
            </div>
        )
    }
}

class ItemListSet extends ModuleSet {
    render() {
        var self = this;
        var item = this.state.item;
        return (
            <div className='item-list-set'>
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
                <div className='line'>
                    <label>标题类型</label>
                    <select className="form-control short" value={item.set.title.type} onChange={
                        (e) => {
                            item.set.title.type = e.currentTarget.value;
                            this.refreshSet();
                        }
                    }>
                        <option value='0'>不显示</option>
                        <option value='1'>文字标题</option>
                        <option value='2'>图片标题</option>
                    </select>
                </div>
                {
                    item.set.title.type == 1 ?
                        <div className='line'>
                            <label>文字标题</label>
                            <input type="text" value={item.set.title.text} className="form-control" placeholder="文字标题" onChange={
                                (e) => {
                                    item.set.title.text = e.currentTarget.value;
                                    this.refreshSet();
                                }
                            } />
                        </div>
                        : ''
                }
                {
                    item.set.title.type == 2 ?
                        <div className='line line-pic'>
                            <label>图片标题</label>
                            <Picasa data={{ style: { width: '200px', height: '50px' }, src: item.set.title.pic }} methods={{
                                updateSrc: (src) => {
                                    item.set.title.pic = src;
                                    this.refreshSet();
                                }
                            }} />
                        </div>
                        : ''
                }

                <ItemSelect data={{ ids: item.set.ids }} methods={{
                    updateIds: (ids) => {
                        item.set.ids = ids;
                        this.refreshSet();
                    }
                }} />
            </div>
        )
    }
}
var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
}
export default {
    ItemList: ItemList, ItemListSet: ItemListSet, getItemListDefaultSet: getDefaultSet
}

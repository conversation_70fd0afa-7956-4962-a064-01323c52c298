.hot-area{
    .bg{
        background-repeat: no-repeat;
        background-size: 100% auto;
        position:relative;
        .rect{
            position: absolute;
            border: solid 2px #f30;
            cursor: pointer;
            background:rgba(0 , 0 , 0 , 0.5);
            outline: none;
            display: block;
            .index{
                position:absolute;
                width:30px;
                height: 30px;
                border:solid 2px #fff;
                left: 50%;
                top:50%;
                transform: translate(-50% , -50%);
                box-sizing:content-box;
                line-height: 30px;
                text-align: center;
                border-radius: 30px;
                font-size: 20px;
                color:#fff;
                user-select:none;
            }
        }
    }
    .none-set{
        font-size: 20px;
        text-align: center;
        line-height: 100px;
    }
}

.panel-module-set .editor .hot-area-set{
    .set{
        font-family: 'iconfont';
        width: 300px ;
        margin-top:50px;
        &:before{
            content: '\e656';
            font-size: 18px;
            margin-right: 10px;
            vertical-align: middle;
        }
    }
}

.hot-area-editor{
    width: 950px;
    height: 680px;
    .dialog-bd{
        height: 550px;
        position: relative;
        padding:0;
    }
    .bg{
        
        width: 0;
        height: 0;
        overflow:hidden;
        img{
            display: block;
            width: 375px;
        }
    }
    .draw{
        box-sizing: content-box;
        border:solid 2px #333;
        width: 375px;
        position:absolute;
        left: 20px;
        top:20px;
        overflow:hidden;
        min-height: 150px;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 100% auto;
        .rect{
            position: absolute;
            border: solid 2px #f30;
            cursor: pointer;
            background:rgba(0 , 0 , 0 , 0.5);
            outline: none;
            display: block;
            .index{
                position:absolute;
                width:30px;
                height: 30px;
                border:solid 2px #fff;
                left: 50%;
                top:50%;
                transform: translate(-50% , -50%);
                box-sizing:content-box;
                line-height: 30px;
                text-align: center;
                border-radius: 30px;
                font-size: 20px;
                color:#fff;
                user-select:none;
            }
            .forward{
                position:absolute;
                left: -1px;
                right:-1px;
                top:-1px;
                bottom:-1px;
                cursor: move;
                display: none;
                div{
                    position:absolute;
                    transform: translate(-50% , -50%);
                    padding:5px;
                    &:after{
                        display: block;
                        content:'';
                        background: #fff;
                        width: 6px;
                        height: 6px;
                    }
                }
                .lt{ left: 0; top:0; cursor: nw-resize;}
                .ct{ left: 50%; top:0; cursor: n-resize;}
                .rt{ left: 100%; top:0; cursor: ne-resize;}
                .lc{ left: 0; top:50%; cursor: w-resize;}
                .rc{ left: 100%; top:50%; cursor: e-resize;}
                .lb{ left: 0; top:100%; cursor: sw-resize;}
                .cb{ left: 50%; top:100%; cursor: s-resize;}
                .rb{ left: 100%; top:100%; cursor: se-resize;}
            }
        }
        .rect.active{
            .forward{
                display: block;
            }
        }
    }
    .draw.drag{
        cursor: crosshair;
        div{
            cursor: crosshair;
        }
    }
    .draw.move{
        cursor: move;
        div{
            cursor: move;   
        }
    }
    .draw.adjust.lt{cursor: nw-resize; div{cursor: nw-resize;}}
    .draw.adjust.ct{cursor: n-resize; div{cursor: n-resize;}}
    .draw.adjust.rt{cursor: ne-resize; div{cursor: ne-resize;}}
    .draw.adjust.lc{cursor: w-resize; div{cursor: w-resize;}}
    .draw.adjust.rc{cursor: e-resize; div{cursor: e-resize;}}
    .draw.adjust.lb{cursor: sw-resize; div{cursor: sw-resize;}}
    .draw.adjust.cb{cursor: s-resize; div{cursor: s-resize;}}
    .draw.adjust.rb{cursor: se-resize; div{cursor: se-resize;}}
    .draw-set{
        height: 550px;
        overflow-y: scroll;
        margin-left: 450px;
        padding:20px;
        .picasa{
            margin-left: 20px;
        }
        label{
            position:relative;
            .del{
                font-family: 'iconfont';
                font-size: 16px;
                color:#888;
                cursor: pointer;
                display: inline-block;
                margin-right: 12px;
                vertical-align: middle;
                &:hover{
                    color:#333;
                }
            }
        }
    }
}

.editor-panel .hot-area-set .out-set{
    label{
        position:relative;
        width: 120px;
        .del{
            font-family: 'iconfont';
            font-size: 16px;
            color:#888;
            cursor: pointer;
            display: inline-block;
            margin-right: 12px;
            vertical-align: middle;
            &:hover{
                color:#333;
            }
        }
    }

}







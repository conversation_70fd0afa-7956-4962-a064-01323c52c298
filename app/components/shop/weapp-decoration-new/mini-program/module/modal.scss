.weapp-decoration-modal {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color:#f2f2f2;

    .modal-content {
        position: relative;
        background-color: #fff;
        border-radius: 12px;
        padding: 20px 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        width: 85%;
        max-width: 320px;
        // margin: 0 auto;

        &.bottom {
            border-radius: 12px 12px 0 0;
            margin-top: auto;
        }

        &.center {
            border-radius: 12px;
            margin: auto;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
            color: #333;
        }
        .modal-line {
            width: 100%;
            height: 1px;
            background-color: #eee;
            margin: 10px 0;
        }

        .modal-body {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 15px;
            text-align: left;
            white-space: pre-line;

            .list-item {
                margin-bottom: 8px;
                padding-left: 18px;
                position: relative;
                text-indent: -18px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .modal-image {
            text-align: center;
            margin: 10px 0;

            img {
                max-width: 100%;
                border-radius: 4px;
            }
            .modal-image-close {
                position: absolute;
                bottom: -25px;
                left: 50%;
                transform: translateX(-50%);
                width: 30px;
                height: 30px;
                line-height: 30px;
                text-align: center;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.5);
                color: #fff;
                font-size: 20px;
                cursor: pointer;
                z-index: 10;
            }
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 24px;
            height: 24px;
            line-height: 22px;
            text-align: center;
            font-size: 20px;
            color: #999;
            cursor: pointer;
            border-radius: 50%;
            font-weight: 300;

            &:hover {
                background-color: #f5f5f5;
            }
        }
    }

    // 添加一个背景遮罩
    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }
}

.modal-set {
    .set-group {
        margin-bottom: 20px;
        padding: 0 15px;

        .set-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .set-item {
            margin-bottom: 12px;

            .label {
                font-size: 13px;
                color: #666;
                margin-bottom: 5px;
            }

            input[type="text"],
            input[type="number"],
            textarea {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 13px;

                &:focus {
                    border-color: #1890ff;
                    outline: none;
                }
            }

            textarea {
                min-height: 80px;
                resize: vertical;
            }

            .checkbox-item {
                display: flex;
                align-items: center;

                input[type="checkbox"] {
                    margin-right: 8px;
                }

                label {
                    font-size: 13px;
                    color: #666;
                }
            }

            .radio-group {
                display: flex;

                label {
                    margin-right: 15px;
                    display: flex;
                    align-items: center;
                    font-size: 13px;
                    color: #666;

                    input[type="radio"] {
                        margin-right: 5px;
                    }
                }
            }

            .upload-container {
                display: flex;
                margin-bottom: 10px;

                .upload-box {
                    flex: 1;
                    margin-right: 10px;
                }

                .upload-btn {
                    white-space: nowrap;
                    background-color: #f7f7f7;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 0 10px;
                    font-size: 12px;
                    color: #666;
                    height: 32px;
                    cursor: pointer;

                    &:hover {
                        background-color: #f0f0f0;
                    }
                }
            }

            .add-image-btn {
                width: 100%;
                background-color: #f7f7f7;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 0;
                font-size: 13px;
                color: #1890ff;
                cursor: pointer;

                &:hover {
                    background-color: #f0f0f0;
                }
            }
        }
    }
}

.modal-slider {
    position: relative;
    width: 100%;
    overflow: hidden;

    .slider-container {
        display: flex;
        transition: transform 0.3s ease;
        width: 100%;

        .slider-item {
            flex: 0 0 100%;
            width: 100%;

            img {
                width: 100%;
                height: auto;
                display: block;
            }
        }
    }

    .slider-dots {
        position: absolute;
        bottom: 10px;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;

        .slider-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            margin: 0 4px;
            cursor: pointer;

            &.active {
                background-color: #fff;
            }
        }
    }
}

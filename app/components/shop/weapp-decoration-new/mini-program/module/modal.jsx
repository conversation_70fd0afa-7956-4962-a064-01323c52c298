import Picasa from "../../component/picasa";
import module from "./module";
import Link from "../component/link";
const { Button,message} = antd;
var {Module, ModuleSet} = module;

class Modal extends Module {
  constructor(props) {
    super(props);
    // 在现有状态基础上添加轮播索引
    this.state = {
      ...this.state,
      currentSlide: 0,
    };
  }

  componentDidMount() {
    // 初始化轮播
    this.initSlider();
  }

  componentDidUpdate(prevProps) {
    // 检查图片列表是否发生变化
    const prevImages = prevProps.data.set.imageUrlList || [];
    const currentImages = this.props.data.set.imageUrlList || [];

    // 检查图片内容是否有变化
    let imagesChanged = false;

    // 长度不同，肯定有变化
    if (prevImages.length !== currentImages.length) {
      imagesChanged = true;
    } else {
      // 长度相同，检查每张图片的URL是否有变化
      for (let i = 0; i < currentImages.length; i++) {
        if ((prevImages[i] && prevImages[i].pic) !== (currentImages[i] && currentImages[i].pic)) {
          imagesChanged = true;
          break;
        }
      }
    }

    // 如果图片有变化，重新初始化轮播
    if (imagesChanged) {
      // 先清除现有定时器
      if (this.sliderTimer) {
        clearInterval(this.sliderTimer);
        this.sliderTimer = null;
      }
      // 重置当前轮播索引
      this.setState({currentSlide: 0}, () => {
        // 重新初始化轮播
        this.initSlider();
      });
    }
  }

  componentWillUnmount() {
    // 清除定时器
    if (this.sliderTimer) {
      clearInterval(this.sliderTimer);
      this.sliderTimer = null;
    }
  }

  // 初始化轮播的方法
  initSlider() {
    const imageList = this.props.data.set.imageUrlList || [];

    // 只有当有多张图片时才启动轮播
    if (imageList.length > 1) {
      this.startSlider();
    }
  }

  startSlider() {
    const imageCount = this.props.data.set.imageUrlList.length;

    this.sliderTimer = setInterval(() => {
      // 使用 setState 更新轮播索引
      this.setState(
        prevState => ({
          currentSlide: (prevState.currentSlide + 1) % imageCount,
        }),
        () => {
          // 在状态更新后更新 DOM
          this.updateSliderPosition();
        },
      );
    }, 3000); // 每3秒切换一次
  }

  // 更新轮播位置的方法
  updateSliderPosition() {
    const {currentSlide} = this.state;
    const container = document.querySelector(".slider-container");
    if (container) {
      container.style.transform = `translateX(-${currentSlide * 100}%)`;
    }

    const dots = document.querySelectorAll(".slider-dot");
    if (dots.length) {
      dots.forEach((dot, index) => {
        if (index === currentSlide) {
          dot.classList.add("active");
        } else {
          dot.classList.remove("active");
        }
      });
    }
  }

  // 点击指示点切换图片
  handleDotClick(index) {
    // 清除现有定时器
    if (this.sliderTimer) {
      clearInterval(this.sliderTimer);
      this.sliderTimer = null;
    }

    // 更新状态
    this.setState({currentSlide: index}, () => {
      // 更新轮播位置
      this.updateSliderPosition();
      // 重新启动轮播
      this.startSlider();
    });
  }

  render() {
    var item = this.state.item;
    const {set} = item;

    console.log('set', set);

    return (
      <div className={`weapp-decoration-modal module active`}>
        <div className={`modal-content ${set.position === "center" ? "center" : "bottom"}`}>
          {set.imageUrlList.length > 0 ? (
            <div className="modal-image">
              {set.imageUrlList.length > 0 && (
                <div className="modal-slider">
                  <div className="slider-container">
                    {set.imageUrlList.map((item, index) => {
                      console.log("set.picHeight",set.picHeight)
                      return (
                        <div className="slider-item" key={index}>
                          <img src={item.pic} alt="" style={{height:set.picHeight}} />
                        </div>
                      );
                    })}
                  </div>
                  {set.imageUrlList.length > 1 && (
                    <div className="slider-dots">
                      {set.imageUrlList.map((_, index) => (
                        <span
                          key={index}
                          className={`slider-dot ${
                            index === this.state.currentSlide ? "active" : ""
                          }`}
                          onClick={() => this.handleDotClick(index)}></span>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {set.showCloseBtn && <div className="modal-image-close">×</div>}
            </div>
          ) : (
            <div>
              <div className="modal-title">{set.title}</div>
              <div className="modal-line"></div>
              <div className="modal-body">
                {set.content.split("\n").map((line, index) => {
                  const isNumberedList = /^\d+\.\s/.test(line);
                  return (
                    <div key={index} className={isNumberedList ? "imageUrlList-item" : ""}>
                      {line}
                    </div>
                  );
                })}
              </div>
              {set.showCloseBtn && <div className="modal-close">×</div>}
            </div>
          )}
        </div>
      </div>
    );
  }
}

class ModalSet extends ModuleSet {
  constructor(props) {
    super(props);
    this.state = {
      item: props.data,
    };
  }

  handleChange(key, value) {
    var item = this.state.item;
    item.set[key] = value;
    this.setState({
      item: item,
    });
    this.props.editSet(item);
  }

  add() {
    var item = this.state.item;
    if ((item.set.imageUrlList||[]).length >= 10) {
      message.warning("最多只能添加10张图片");
      return;
    }
    var imageUrlList = [...item.set.imageUrlList]; // 创建新数组
    imageUrlList.push({
      pic: "",
      url: {},
      type: "pic",
    });

    // 更新整个 item 对象
    item.set.imageUrlList = imageUrlList;
    this.setState({item: {...item}});
    this.props.editSet({...item});
  }

  delete(index) {
    var item = this.state.item;
    var imageUrlList = [...item.set.imageUrlList]; // 创建新数组
    imageUrlList.splice(index, 1);

    // 更新整个 item 对象
    item.set.imageUrlList = imageUrlList;
    this.setState({item: {...item}});
    this.props.editSet({...item});
  }

  render() {
    var item = this.state.item;
    const {set} = item;

    return (
      <div className="modal-set">
        <div className="set-group">
          <div className="set-item">
            <label className="label">弹窗名称</label>
            <input
              type="text"
              value={set.name || ""}
              onChange={e => this.handleChange("name", e.target.value)}
              placeholder="注册福利"
            />
          </div>
        </div>
        <div className="set-group">
          <div className="set-title">展示位置</div>
        <div className="set-item">
          <div className="radio-group">
            <label>
              <input
                type="radio"
                name="position"
                checked={set.position !== "center"}
                onChange={() => this.handleChange("position", "bottom")}
              />
              底部（仅支持上传文本）
            </label>
            <label>
              <input
                type="radio"
                name="position"
                checked={set.position === "center"}
                onChange={() => this.handleChange("position", "center")}
              />
              中部（仅支持上传图片）
            </label>
          </div>
        </div>
        </div>
        {
          set.position !== "center" ?
            <div className="set-group">
              <div className="set-title">弹窗内容</div>
              <div className="set-item">
                <label className="label">标题</label>
                <input
                  type="text"
                  value={set.title || ""}
                  onChange={e => this.handleChange("title", e.target.value)}
                  placeholder="福利说明"
                />
              </div>
              <div className="set-item">
                <label className="label">内容</label>
                <textarea
                  value={set.content || ""}
                  onChange={e => this.handleChange("content", e.target.value)}
                  placeholder="注册299元券说明XXXX"
                />
              </div>
            </div>:
            <div className="set-group">
              <div className="line">
                <label>轮播高度</label>
                <input
                  type="range"
                  value={item.set.picHeight}
                  step="5"
                  onChange={this.input.bind(this, "picHeight")}
                  min="200"
                  max="750"
                />
                <div className="value">{item.set.height}px</div>
              </div>
              {item.set.imageUrlList.map((line, index) => {
                return (
                  <div className="group" key={index}>
                    <div className="group-op">
                      <div className="name">
                        第{index + 1}组
                        <div className="del" onClick={this.delete.bind(this, index)}>
                          &#xe60d;
                        </div>
                      </div>
                    </div>
                    <div className="image-set">
                      <div className="line line-pic">
                        <label>上传图片</label>
                        <Picasa
                          data={{
                            style: {width: "150px", height: "75px"},
                            src: line.pic,
                          }}
                          methods={{
                            updateSrc: src => {
                              // 创建新的图片列表数组
                              const newImageList = [...this.state.item.set.imageUrlList];
                              newImageList[index] = {
                                ...newImageList[index],
                                pic: src,
                              };

                              // 更新整个 item 对象
                              const newItem = {
                                ...this.state.item,
                                set: {
                                  ...this.state.item.set,
                                  imageUrlList: newImageList,
                                },
                              };

                              this.setState({item: newItem});
                              this.props.editSet(newItem);
                          }}}
                        />
                      </div>

                        <span style={{marginLeft:'60px'}}>尺寸 
                            <span style={{marginLeft:"10px"}}>4:3</span>
                        </span><div className='line '>
                        <label>链接1</label>
                        <Link data={{pages : {}  , url : line.url}} methods={{
                          updateUrl : (url) => {
                            const newImageList = [...this.state.item.set.imageUrlList];
                            newImageList[index] = {
                              ...newImageList[index],
                              url: url,
                            };

                            // 更新整个 item 对象
                            const newItem = {
                              ...this.state.item,
                              set: {
                                ...this.state.item.set,
                                imageUrlList: newImageList,
                              },
                            };

                            this.setState({item: newItem});
                            this.props.editSet(newItem);
                          }
                        }} />
                      </div>
                    </div>
                  </div>
                );
              })}
                <div className='add-img-footer'>
                  <Button type="primary" style={{width : "100%"}}  ghost  onClick={() => this.add()}>
                    添加图片（{item.set.imageUrlList.length||0}/10）
                  </Button>
                </div>

            </div>
        }

        <div className="set-group">
          <div className="set-title">显示设置</div>
          <div className="set-item">
            <div className="checkbox-item">
              <input
                type="checkbox"
                checked={set.showCloseBtn !== false}
                onChange={e => this.handleChange("showCloseBtn", e.target.checked)}
                id="showCloseBtn"
              />
              <label htmlFor="showCloseBtn">显示关闭按钮</label>
            </div>
          </div>
          <div className="set-item">
            <div className="checkbox-item">
              <input
                type="checkbox"
                checked={set.autoClose === true}
                onChange={e => this.handleChange("autoClose", e.target.checked)}
                id="autoClose"
              />
              <label htmlFor="autoClose">定时关闭</label>
            </div>
          </div>
          {set.autoClose && (
            <div className="set-item">
              <div className="label">定时关闭时长 (s)</div>
              <input
                type="number"
                value={set.autoCloseTime || 3}
                onChange={e => this.handleChange("autoCloseTime", parseInt(e.target.value) || 0)}
                min="1"
              />
            </div>
          )}

        </div>
      </div>
    );
  }
}


export default {
  Modal: Modal,
  ModalSet: ModalSet,
};

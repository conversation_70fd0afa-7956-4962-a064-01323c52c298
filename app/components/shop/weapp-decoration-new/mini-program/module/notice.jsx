import commonSet from "./common-set";
import module from "./module";
import Link from "../component/link";

const {Tooltip} = antd
var {Module, ModuleSet} = module;

var {ToolBar, CommonSet, getDefaultCommonSet} = commonSet;
var defaultSet = {
  name: "notice",
  title: "通知",
  commonSet: getDefaultCommonSet(),
  set: {
    title: "", // 公告标题
    content: "", // 公告内容
    hasLink: false, // 是否有链接
    links: [], // 链接列表
    style: "notice-low", // 公告样式
    url: {
      radioKey: "",
      radioValue: {},
    }
  },
};

class Notice extends Module {
  render() {
    var item = this.state.item;
    const {set} = item;

    return (
      <div
        className={`notice ${this.state.isSubModule ? "sub-module" : "module"} ${this.isActived()} ${set.style}`}
        style={{
          marginTop: item.commonSet.marginTop / 2 + "px",
          marginBottom: item.commonSet.marginBottom / 2 + "px",
        }}>
        <ToolBar parentIndex={this.state.parentIndex} index={this.state.index} layoutIndex={this.state.layoutIndex}
                 methods={this.props.methods} isSubModule={this.state.isSubModule}/>
        <div className="notice-content">
          {set.style === "notice-low" && (
            <div className="notice-low">
              <span className="notice-icon icon-feebas-new icon-feebas-icon_notice"></span>
              <div style={{marginLeft: "10px"}}>
                <div className="notice-title">{item.set.title}</div>
                <div className="notice-text">{item.set.content}</div>
              </div>
            </div>
          )}
          {set.style === "notice-mid" && (
            <div className="notice-mid">
              <span className="notice-icon icon-feebas-new icon-feebas-icon_notice"></span>
              <div style={{marginLeft: "10px"}}>
                <div className="notice-title">{item.set.title}</div>
                <div className="notice-text">{item.set.content}</div>
              </div>
            </div>
          )}
          {set.style === "notice-high" && (
            <div className="notice-high">
              <span className="notice-icon icon-feebas-new icon-feebas-icon_notice"></span>
              <div style={{marginLeft: "10px"}}>
                <div className="notice-title">{item.set.title}</div>
                <div className="notice-text">{item.set.content}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

class NoticeSet extends ModuleSet {
  handleTitleChange = e => {
    const value = e.target.value;
    this.state.item.set.title = value;
    this.refreshSet();
  };

  handleContentChange = e => {
    const value = e.target.value;
    this.state.item.set.content = value;
    this.refreshSet();
  };

  handleLinkToggle = e => {
    const checked = e.target.checked;
    this.state.item.set.hasLink = checked;
    this.refreshSet();
  };

  handleAddLink = () => {
    const {item} = this.state;
    const links = [...item.set.links, {url: "", text: ""}];
    this.state.item.set.links = links;
    this.refreshSet();
  };

  handleStyleChange = style => {
    this.state.item.set.style = style;
    this.refreshSet();
  };
  handleOptionChange = e => {
    const selectedOption = e.target.value;
    this.state.item.set.selectedOption = selectedOption;
    this.refreshSet();
  };

  render() {
    var item = this.state.item;
    const {set} = item;

    return (
      <div className="notice-set">
        <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule}/>

        <div className="form-group">
          <label className="decoration-item-required">公告标题</label>
          <input
            type="text"
            value={set.title}
            onChange={this.handleTitleChange}
            placeholder="请输入公告标题"
          />
        </div>

        <div className="form-group">
          <label className="decoration-item-required">公告内容</label>
          <span>公告内容将滚动循环播放</span>
          <textarea
            value={set.content}
            onChange={this.handleContentChange}
            placeholder="请输入公告内容"
            maxLength={30}
          />
          <span className="char-count">{set.content.length}/30</span>
        </div>
        <div className="form-group">
          <label>跳转链接</label>
          <div className='line '>
            <label style={{width: 'auto'}}>链接:</label>
            <Link data={{pages: this.state.pages, url: item.set.url}} methods={{
              updateUrl: (url) => {
                item.set.url = url;
                this.refreshSet();
              }
            }}/>
          </div>
        </div>

        <div className="form-group">
          <label>公告样式</label>
          <div className="style-options">

            <div
              className={`style-option ${set.style === "notice-low" ? "active" : ""}`}
              onClick={() => this.handleStyleChange("notice-low")}>
              <div className="notice-content">
                <div className="notice-low">
                  <span className="notice-icon icon-feebas-new icon-feebas-icon_notice"></span>
                  <div className="notice-title">店铺公告</div>
                </div>
              </div>
            </div>
            <Tooltip title={'建议用于“正向引导”场暴，例如促销/福利/直播预告/秒杀，引起注意'}>
            <div
              className={`style-option ${set.style === "notice-mid" ? "active" : ""}`}
              onClick={() => this.handleStyleChange("notice-mid")}>
              <div className="notice-content">
                <div className="notice-mid">
                  <span className="notice-icon icon-feebas-new icon-feebas-icon_notice"></span>
                  <div className="notice-title">店铺公告</div>
                </div>
              </div>
            </div>
            </Tooltip>
            <Tooltip title={'建议用于“警示”场暴。例如实发通知/物流延迅特殊殊说明。引起重视'}>
              <div
                className={`style-option ${set.style === "notice-high" ? "active" : ""}`}
                onClick={() => this.handleStyleChange("notice-high")}>
                <div className="notice-content">
                  <div className="notice-high">
                    <span className="notice-icon icon-feebas-new icon-feebas-icon_notice"></span>
                    <div className="notice-title">店铺公告</div>
                  </div>
                </div>
              </div>
            </Tooltip>

          </div>
        </div>
      </div>
    );
  }
}

var getDefaultSet = function () {
  return JSON.parse(JSON.stringify(defaultSet));
};

export default {
  Notice: Notice,
  NoticeSet: NoticeSet,
  getDefaultNoticeSet: getDefaultSet,
};

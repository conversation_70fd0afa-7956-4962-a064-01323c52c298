.notice {
  .notice-content {
    padding: 10px 15px;
    background: #f0f0f0;

    .notice-low {
      padding: 24px;
      height: 80px;
      display: flex;
      background: #FFFFFF; // 浅橙色背景
      color: #B3B3B7; // 橙色文字
      font-size: 14px;
      border-radius: 4px;

      .notice-icon {
        margin-right: 8px;
        font-size: 16px;
        line-height: 16px;
      }

      .notice-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .notice-close {
        margin-left: 12px;
        cursor: pointer;
        font-size: 16px;
        color: #B3B3B7;

        &:hover {
          color: #666;
        }
      }
    }

    .notice-mid {
      display: flex;
      background: #FFF2E0; // 浅橙色背景
      color: #FFA14B; // 橙色文字
    }

    .notice-high {
      display: flex;
      background: #FFEBF1; // 浅橙色背景
      color: #FF0050; // 橙色文字
    }

    .notice-mid, .notice-high, .notice-low {
      padding: 12px 24px;
      height: 80px;
    }
  }
}

.notice-set {
  padding: 10px 15px;

  .form-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 8px;
    }

    span {
      color: gray;
    }

    input[type="text"],
    textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .char-count {
      float: right;
      color: #999;
      font-size: 12px;
    }
  }

  .style-options {
    display: flex;
    gap: 10px;

    .style-option {
      background: #f0f0f0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      height: 100px;
      align-items: center;

      &.active {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
    .notice-content {

    }
    .notice-low {
      display: flex;
      background: #FFFFFF; // 浅橙色背景
      color: #B3B3B7; // 橙色文字
      font-size: 10px;
      align-items: center;
      border-radius: 4px;
      height: 30px;
      padding: 0 5px;

      .notice-icon {
        color: #B3B3B7; // 橙色文字
        font-size: 10px;
        margin-right: 8px;
        line-height: 16px;
      }

      .notice-text {
        flex: 1;

        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

    }

    .notice-mid {
      display: flex;
      font-size: 10px;
      background: #FFF2E0; // 浅橙色背景
      color: #FFA14B; // 橙色文字
      align-items: center;
      height: 30px;
      padding: 0 5px;
      .notice-icon {
        color: #FFA14B; // 橙色文字
        font-size: 10px;
        margin-right: 8px;
        line-height: 16px;
      }
    }

    .notice-high {
      font-size: 10px;
      display: flex;
      background: #FFEBF1; // 浅橙色背景
      color: #FF0050; // 橙色文字
      align-items: center;
      height: 30px;
      padding: 0 5px;
      .notice-icon {
        color: #FFA14B; // 橙色文字
        font-size: 10px;
        margin-right: 8px;
        line-height: 16px;
      }
    }
  }

  .link-list {
    margin-top: 10px;

    button {
      margin-top: 10px;
      padding: 5px 10px;
      background: #1890ff;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

@keyframes scroll {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(-100%);
  }
}

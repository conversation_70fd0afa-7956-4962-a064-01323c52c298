.layout-two {
  width: 100%;

  .layout-two-container {
    display: flex;
    justify-content: space-between;

    .layout-two-container {
      display: flex;
      justify-content: space-between;

      &-item {
        position: relative;
        margin: 3px;
        min-height: 90px;
      }

      > div {
        flex: 1; // 让两个div平均分配空间

        img {
          display: block; // 消除图片底部间隙
          width: 100%;
          height: auto;
        }
      }
    }
  }
}

.layout-two-set {
  .line {
    margin-bottom: 10px;

    label {
      display: inline-block;
      width: 60px;
    }

    input[type="number"] {
      width: 60px;
    }
  }
}

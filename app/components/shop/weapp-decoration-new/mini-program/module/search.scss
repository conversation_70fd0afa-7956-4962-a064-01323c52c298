.panel-phone {
    .search {
        padding: 10px;
        min-height: 10px;
        background: #fff;
        .search-content {
            display: flex;
            flex: 1;
            .search-box {
                width: 350px;
                height: 36px;
                border-radius: 36px;
                margin: 0 auto;
                background: #eee;
                line-height: 36px;
                font-size: 16px;
                box-sizing: border-box;
                padding-left: 50px;
                color: #999;
                position: relative;
            }
            .search-box:before {
                font-family: "iconfont";
                content: "\e61c";
                position: absolute;
                font-size: 20px;
                top: 0;
                left: 20px;
            }
            .search-icon-list {
                display: flex;
                flex-direction: row; // 水平方向排列
                justify-content: center; // 居中对齐
                align-items: center; // 垂直居中

                img {
                    display: block; // 消除图片间的空隙
                    margin: 0 5px;
                }
            }
        }
    }
}
.search-style-select {
    padding: 10px 15px;
    .style-option {
        margin-bottom: 15px;
        border: 1px solid #eee;
        padding: 10px;
        border-radius: 4px;

        &:hover {
            border-color: #3661ff;
        }

        input[type="radio"] {
            margin-right: 8px;
        }

        label {
            display: inline-block;
            margin-bottom: 10px;
        }

        .style-preview {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;

            img {
                width: 100%;
                height: auto;
                display: block;
            }
        }
    }
}
.add-img-footer {
  display: flex;
  padding: 0 36px;
}

const {Fragment} = React;


var defaultSet = {
    marginTop : 0 ,
    marginBottom : 0
}

class ToolBar extends React.Component{
    constructor(props){
        super(props);
        this.state = {
            isSubModule: props.isSubModule,
            parentIndex : props.parentIndex,
            index : props.index,
            layoutIndex : props.layoutIndex
        };
    }
    componentWillReceiveProps(props){
        this.setState({
            isSubModule: props.isSubModule,
            parentIndex : props.parentIndex,
            index : props.index,
            layoutIndex : props.layoutIndex
        });
    }
    edit(e){
        this.props.methods.edit(this.state.index,this.state.isSubModule, this.state.parentIndex,this.state.layoutIndex);
    }
    up(e){
        e.stopPropagation();
        this.props.methods.up(this.state.index,this.state.isSubModule,this.state.parentIndex,this.state.layoutIndex);
    }
    down(e){
        e.stopPropagation();
        this.props.methods.down(this.state.index,this.state.isSubModule,this.state.parentIndex,this.state.layoutIndex);
    }
    delete(e){
        e.stopPropagation();
        this.props.methods.delete(this.state.index,this.state.isSubModule,this.state.parentIndex,this.state.layoutIndex);
    }

    render(){
        return (
          <Fragment>
            <div className={this.props.isSubModule?"sub-tool":"tool"} onClick={this.edit.bind(this)}>
              {
                this.props.isSubModule ?
                  <Fragment>
                    <div className='up' onClick={this.up.bind(this)}>&#xe645;</div>
                    <div className='down' onClick={this.down.bind(this)}>&#xe644;</div>
                    <div className='delete' onClick={this.delete.bind(this)}>&#xe60d;</div>
                  </Fragment>:null
              }
            </div>
            {
              !this.props.isSubModule ?
                <div className={'tool-op'}>
                  <div className='up' onClick={this.up.bind(this)}>&#xe645;</div>
                  <div className='down' onClick={this.down.bind(this)}>&#xe644;</div>
                  <div className='delete' onClick={this.delete.bind(this)}>&#xe60d;</div>
                </div>:null
            }

          </Fragment>
        )
    }
}

class LayoutToolBar extends React.Component{
  constructor(props){
    super(props);
    this.state = {
      index : props.index,
      layoutIndex : props.layoutIndex,
    };
  }
  componentWillReceiveProps(props){
    this.setState({
      index : props.index,
      layoutIndex : props.layoutIndex
    });
  }
  edit(e){
    this.props.methods.edit(this.state.index);
  }
  up(e){
    e.stopPropagation();
    this.props.methods.up(this.state.index);
  }
  down(e){
    e.stopPropagation();
    this.props.methods.down(this.state.index);
  }
  delete(e){
    e.stopPropagation();
    this.props.methods.delete(this.state.index);
  }
  handleLayoutSelect(){
    this.props.methods.handleLayoutSelect(this.state.index,this.state.layoutIndex);
  }

  render(){
    return (
      <div className='layout-module-tool' onClick={this.handleLayoutSelect.bind(this)}>

      </div>
    )
  }
}
class CommonSet extends React.Component{
  constructor(props) {
    super(props)
    this.state = {
      item: props.data,
      pages: props.pages,
      designData: props.designData,
      isSubModule: props.isSubModule,
    }
  }
  componentWillReceiveProps(props) {
    this.setState({
      item: props.data,
      pages: props.pages,
      designData: props.designData,
      isSubModule: props.isSubModule,
    })
  }
    input(key , e){
        var commonSet = this.state.item.commonSet;
        commonSet[key] = e.currentTarget.value;
        this.props.editSet(this.state.item, this.state.isSubModule);
    }
    render(){
        return (
            <div className='common-set'>
                <div className='line'>
                    <label>上边距</label>
                    <input type="range" step='2' value={this.state.item.commonSet.marginTop} onChange={this.input.bind(this , 'marginTop')} min='0' max='50' />
                    <div className='value' >{this.state.item.commonSet.marginTop}px</div>
                </div>
                <div className='line'>
                    <label>下边距</label>
                    <input type="range" step='2' value={this.state.item.commonSet.marginBottom} onChange={this.input.bind(this , 'marginBottom')} min='0' max='50' />
                    <div className='value' >{this.state.item.commonSet.marginBottom}px</div>
                </div>
            </div>
        )
    }
}
var getDefaultCommonSet = function(){
    return JSON.parse(JSON.stringify(defaultSet));
}
export default {
    ToolBar : ToolBar , CommonSet : CommonSet , getDefaultCommonSet : getDefaultCommonSet,LayoutToolBar:LayoutToolBar
}

import commonSet from "./common-set";
import module from "./module";
var { Module, ModuleSet } = module;
var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
var defaultSet = {
    name: "helper",
    title: "小助手",
    commonSet: getDefaultCommonSet(),
    set: {
        title: "", // 小助手标题
        content: "", // 小助手内容
    },
};

class Helper extends Module {
    render() {
        var item = this.state.item;
        const { set } = item;
        return (
            <div
                className={`helper ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                }}>
              <ToolBar parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}  isSubModule={this.state.isSubModule} />
                <div className="helper-content">
                    <div className="helper-box">
                        <div className="helper-icon">&#xe600;</div>
                        <div className="helper-text">
                            <div className="helper-title">{set.title || "添加企业微信助手"}</div>
                            <div className="helper-desc">{set.content || "享受会员专属优惠"}</div>
                        </div>
                        <div className="helper-button">
                            <a href="javascript:void(0);" className="add-button">
                                去添加
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

class HelperSet extends ModuleSet {
    handleTitleChange = e => {
        const value = e.target.value;
        this.state.item.set.title = value;
        this.refreshSet();
    };

    handleContentChange = e => {
        const value = e.target.value;
        this.state.item.set.content = value;
        this.refreshSet();
    };

    render() {
        var item = this.state.item;
        const { set } = item;

        return (
            <div className="notice-set">
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />

                <div className="form-group">
                    <label>标题</label>
                    <input type="text" value={set.title} onChange={this.handleTitleChange} placeholder="请输入标题" />
                </div>

                <div className="form-group">
                    <label>内容</label>
                    <textarea value={set.content} onChange={this.handleContentChange} placeholder="请输入内容" />
                </div>
            </div>
        );
    }
}

var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};

export default {
    Helper: Helper,
    HelperSet: HelperSet,
    getHelperDefaultSet: getDefaultSet,
};

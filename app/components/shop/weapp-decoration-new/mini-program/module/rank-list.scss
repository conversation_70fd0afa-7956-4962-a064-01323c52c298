.rank-list {
  .rank-list-content {
    padding: 10px 15px;

    // 样式一 - 列表式
    .rank-list-style-one {
      width: 175px;
      background-color: #faf0e3;
      border-radius: 16px 16px 0 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .rank-header {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        // margin-bottom: 10px;
        padding-top: 15px;
        &-bg{
          text-align: center;
          background: linear-gradient( 180deg, #FFFFFF 0%, rgba(255,255,255,0.32) 100%);
          border-radius: 16px;
          opacity: 0.43;
          height: 70px;
          margin: 0 12px;
        }

        .rank-title {
          text-align: center;
          font-size: 16px;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 26px;
          text-stroke: 1px #894724;
          font-style: normal;
          text-transform: uppercase;
          -webkit-text-stroke: 1px #894724;
        }

        .rank-tag {
          font-size: 11px;
          color: #8b4513;
          display: flex;
          align-items: center;
          justify-content: center;
          gap:10px;

          .fire-icon,
          .arrow-icon {
            font-family: "iconfont";
            color: #8b4513;
          }
        }
      }

      .rank-items {
        margin-top: -15px;
        padding: 15px;
        border-radius: 16px 16px 0 0;
        background: #fff;

        .rank-item {
          display: flex;
          align-items: center;
          background: #fff;
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 10px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          position: relative;

          &:last-child {
            margin-bottom: 0;
          }

          .rank-num {
            position: absolute;
            top: -5px;
            left: -5px;
            width: 24px;
            height: 24px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            margin-right: 10px;
            z-index: 1;
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }

          &:nth-child(1) .rank-num {
            background-image: url("https://pic.nximg.cn/file/20230504/27715915_110500721101_2.jpg");
            // 如果没有图片，可以使用以下样式
            // background: #FF9800;
            // border-radius: 0 0 8px 0;
          }

          &:nth-child(2) .rank-num {
            background-image: url("https://pic.nximg.cn/file/20230504/27715915_110500721101_2.jpg");
            // 如果没有图片，可以使用以下样式
            // background: #9575CD;
            // border-radius: 0 0 8px 0;
          }

          &:nth-child(3) .rank-num {
            background-image: url("https://pic.nximg.cn/file/20230504/27715915_110500721101_2.jpg");
            // 如果没有图片，可以使用以下样式
            // background: #FF7043;
            // border-radius: 0 0 8px 0;
          }

          .rank-img {
            width: 60px;
            height: 60px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .rank-info {
            flex: 1;

            .rank-name {
              font-size: 14px;
              color: #e6a23c;
              margin-bottom: 6px;
              font-weight: 500;
              background-color: #fcf5e3;
              padding: 4px 6px;
              margin: 6px 0;
              border-radius: 4px;
              display: inline-block;
            }

            .rank-desc {
              font-size: 15px;
              color: #333;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }

    // 样式二 - 网格式
    .rank-list-style-two {
      background-color: #4169e1;
      border-radius: 16px 16px 0 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .rank-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 12px;

        .rank-title {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
        }

        .rank-tag {
          font-size: 14px;
          color: #fff;
          display: flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 12px;
          font-weight: 300;

          .fire-text {
            margin: 0 6px;
            font-weight: 400;
            font-size: 18px;
            color: #894724;
            line-height: 25px;
          }

          .fire-icon,
          .arrow-icon {
            font-family: "iconfont";
            color: #ffffff;
          }
        }
      }

      .rank-products {
        background-color: #ffffff;
        padding: 12px;
        border-radius: 20px 20px 0 0;

        .product-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .product-item {
            width: 32%;
            background: #fff;
            border-radius: 6px;
            // padding: 8px;
            position: relative;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            .product-top {
              padding: 8px 8px 0px 8px;

              .product-rank {
                position: absolute;
                top: 0;
                left: 0;
                width: 24px;
                height: 24px;
                background: #ffb74d;
                color: #fff;
                border-radius: 0 0 6px 0;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                z-index: 1;
              }

              &:nth-child(1) .product-rank {
                background: linear-gradient(135deg, #ff6b00, #ff9800);
              }

              &:nth-child(2) .product-rank {
                background: linear-gradient(135deg, #4169e1, #5a7ae2);
              }

              &:nth-child(3) .product-rank {
                background: linear-gradient(135deg, #ff9800, #ffb74d);
              }

              .product-img {
                width: 100%;
                height: 80px;
                display: flex;
                justify-content: center;
                margin-bottom: 8px;
                padding-top: 5px;
                position: relative;

                &:before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  height: 24px;
                  background: #f9f9f9;
                  border-radius: 6px 6px 0 0;
                  z-index: 0;
                }

                img {
                  height: 100%;
                  object-fit: contain;
                  position: relative;
                  z-index: 1;
                }
              }

              .product-info {
                text-align: center;

                .product-name {
                  font-size: 10px;
                  color: #ffcc30;
                  margin-bottom: 2px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  // font-weight: 500;
                  text-align: center;
                }
              }
            }

            .product-bottom {
              height: 30px;

              .product-desc {
                font-size: 12px;
                color: #666;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                background-color: #f3f5f4;
                line-height: 30px;
                border-radius: 6px;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
}

// 设置面板样式
.notice-set {
  .style-label {
    display: flex;
    align-items: center;
    margin-right: 15px;
    cursor: pointer;
  }

  .add-product-btn {
    background: #4169e1;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #5a7ae2;
    }
  }

  .required:after {
    content: "*";
    color: red;
    margin-left: 4px;
  }

  .rank-card-type {
    display: flex;
    flex-direction: column;
  }

  .rank-card-img {
    width: 130px;
  }
}


import commonSet from "./common-set";
import module from "./module";
import Picas<PERSON> from "../../component/picasa";
import {ItemVideoButton} from "../component/item-video-button";
var { Module, ModuleSet } = module;

var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
var defaultSet = {
    name: "video",
    title: "视频",
    commonSet: getDefaultCommonSet(),
    set: {
        height: 375,
        imgSrc: "",
        pic: "",
    },
};


class Video extends Module {
    render() {
        var item = this.state.item;
      return (
            <div
                className={`video ${this.state.isSubModule?"sub-module":"module"} ` + (this.isActived())}
                style={{
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                }}>
               <ToolBar parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}  isSubModule={this.state.isSubModule} />
                <video
                    style={{ height: item.set.height / 2 + "px" }}
                    src={
                        item.set.src ||
                        "http://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400"
                    }
                    autoPlay></video>
            </div>
        );
    }
}

class VideoSet extends ModuleSet {
    render() {
        var item = this.state.item;
        return (
            <div className="image-set">
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
                <div className="line">
                    <label>视频高度</label>
                    <input
                        type="range"
                        value={item.set.height}
                        step="5"
                        onChange={this.input.bind(this, "height")}
                        min="200"
                        max="1000"
                    />
                    <div className="value">{item.set.height}px</div>
                </div>
                <div className="line line-pic">
                    <label>视频封面</label>
                    <Picasa
                        data={{ style: { width: "150px", height: "75px" }, src: item.set.pic }}
                        methods={{
                            updateSrc: src => {
                                item.set.pic = src;
                                this.refreshSet();
                            },
                        }}
                    />
                </div>
                <div className="line">
                    <label>视频地址</label>
                    <input
                        type="text"
                        value={item.set.src}
                        onChange={this.input.bind(this, "src")}
                        className="form-control"
                        placeholder="视频地址（URL）"
                    />
                </div>
              <div className="line">
                <label>视频上传</label>
                <ItemVideoButton
                  value={[{url:item.set.src}]}
                  onChange={(value)=>{
                    item.set.pic = value[0].thumbUrl;
                    item.set.src = value[0].url;
                    this.refreshSet();
                  }}
                />
              </div>
            </div>
        );
    }
}
var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};
export default {
    Video: Video,
    VideoSet: VideoSet,
    getVideoDefaultSet: getDefaultSet,
};

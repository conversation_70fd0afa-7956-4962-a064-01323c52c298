
class Module extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      item: props.data,
      index: props.index,
      parentIndex: props.parentIndex,
      layoutIndex: props.layoutIndex,
      isSubModule: props.isSubModule,
      activeModule: props.activeModule,
      activeLayoutModule: props.activeLayoutModule,
      activeLayoutModuleIndex: props.activeLayoutModuleIndex
    }
  }
  componentWillReceiveProps(props) {
    this.setState({
      item: props.data,
      index: props.index,
      parentIndex: props.parentIndex,
      activeModule: props.activeModule,
      layoutIndex: props.layoutIndex,
      isSubModule: props.isSubModule,
      activeLayoutModule: props.activeLayoutModule,
      activeLayoutModuleIndex: props.activeLayoutModuleIndex
    })
  }
  isActived() {
    return this.state.isSubModule ? this.state.layoutIndex === this.state.activeLayoutModule && this.state.index === this.state.activeLayoutModuleIndex : this.state.index === this.state.activeModule
  }
}
class LayoutModule extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      item: props.data,
      index: props.index,
      parentIndex: props.parentIndex,
      layoutIndex: props.layoutIndex,
      activeModule: props.activeModule,
      activeLayoutModule: props.activeLayoutModule,
      activeLayoutModuleIndex: props.activeLayoutModuleIndex
    }
  }
  componentWillReceiveProps(props) {
    this.setState({
      item: props.data,
      index: props.index,
      parentIndex: props.parentIndex,
      layoutIndex: props.layoutIndex,
      activeModule: props.activeModule,
      activeLayoutModule: props.activeLayoutModule,
      activeLayoutModuleIndex: props.activeLayoutModuleIndex
    })
  }
}

class ModuleSet extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      item: props.data,
      pages: props.pages,
      designData: props.designData,
      isSubModule: props.isSubModule,
    }
  }
  componentWillReceiveProps(props) {
    this.setState({
      item: props.data,
      pages: props.pages,
      designData: props.designData,
      isSubModule: props.isSubModule,
    })
  }
  input(key, e) {
    const set = this.state.item.set
    set[key] = e.currentTarget.value
    this.props.editSet(this.state.item, this.state.isSubModule)
  }
  refreshSet() {
    this.props.editSet(this.state.item, this.state.isSubModule)
  }
  refreshPageSet() {
    this.props.editPageSet(this.state.item)
  }
}


export default {
  Module, ModuleSet, LayoutModule
}

.panel-phone {
  .module {
    position: relative;
    min-height: 100px;
  }
  .sub-module{
    position: relative;
    min-height: 100px;
    z-index: 101;
  }
  .sub-module:nth-child(2) .sub-tool, {
    .up {
      display: none;
    }
  }

  .sub-module:last-child .sub-tool{


    .down {
      display: none;
    }
  }
  .module:hover .tool,
  .module.true .tool{
    display: block;
    border: dashed 2px #06f;
  }
  .sub-module:hover .sub-tool,
  .sub-module.true .sub-tool {
    display: block;
    border: dashed 2px #06f;
  }
  .module:hover .tool-op,
  .module.true .tool-op {
    display: block;
  }
  .sub-module:hover .tool-op,
  .sub-module.true .tool-op {
    display: block;
  }
  .sub-tool{
    position: absolute;
    inset: 0;
    z-index: 102;
    display: none;
    cursor: pointer;
    div {
      font-family: "iconfont";
      font-size: 15px;
      color: #333;
      position: absolute;
      top: 2px;
      line-height: 36px;
    }
    .up {
      right: 50px;
    }

    .down {
      right: 30px;
    }

    .delete {
      right: 10px;
    }
  }
  .tool{
    position: absolute;
    inset: 0;
    z-index: 100;
    display: none;
    cursor: pointer;
    div {
      font-family: "iconfont";
      font-size: 15px;
      color: #333;
      position: absolute;
      top: 2px;
      line-height: 36px;
    }

    .up {
      right: 50px;
    }

    .down {
      right: 30px;
    }

    .delete {
      right: 10px;
    }
    &-op {
      display: none;
      position: absolute;
      right: -50px;

      div {
        font-family: "iconfont";
        font-size: 15px;
        color: #333;
        line-height: 16px;

        .up {
        }

        .down {
        }

        .delete {
        }
      }
    }


  }

  .tool-sub{
    position: absolute;
    inset: 0;
    display: none;
    cursor: pointer;
    z-index: 200;
  }
  .tool-sub:hover{
    display: block;
    border: dashed 2px #06f;
  }

  .layout-module-tool:hover {
    display: block;
    border: dashed 2px #D9001B;
  }

  .layout-module-tool {
    position: absolute;
    left: 1px;
    top: 1px;
    right: 1px;
    bottom: 1px;
    z-index: 100;
    display: block;
    border: dashed 2px #999;
    cursor: pointer;

    div {
      font-family: "iconfont";
      font-size: 25px;
      color: #eee;
      position: absolute;
      top: 6px;
      line-height: 36px;
    }

  }

  .module:nth-child(1) .tool-op, {
    .up {
      display: none;
    }
  }

  .module:last-child .tool-op{


    .down {
      display: none;
    }
  }
}

.panel-module-set .editor {
  .editor-panel {
    padding-top: 20px;
  }

  .dialog {
    input.form-control {
      float: none;
      width: 50px;
    }
  }

  .group {
    position: relative;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-top: 10px;

    .group-op {
      position: absolute;
      border: solid 1px #eee;
      width: 300px;
      left: 30px;
      top: 0;
      bottom: 20px;
      color: #888;

      .name {
        background: #fff;
        padding-left: 10px;
        padding-right: 10px;
        position: absolute;
        font-size: 14px;
        line-height: 16px;
        top: -8px;
        left: 20px;
      }

      .del {
        display: inline-block;
        font-family: "iconfont";
        font-size: 14px;
        margin-left: 10px;
      }

      .del:hover {
        color: #333;
        cursor: pointer;
      }
    }

    .line {
      z-index: 1;
      position: relative;
    }
  }

  .btn {
    display: block;
    width: 100px;
    margin: 0 auto;
  }

  .line {
    position: relative;
    height: 36px;
    line-height: 36px;
    overflow: hidden;
    margin-bottom: 20px;

    label {
      width: 100px;
      text-align: right;
      float: left;
      padding-right: 10px;
      box-sizing: border-box;
    }

    .form-control {
      width: 210px;
      float: left;
      position: relative;
      z-index: 1;
    }

    input[type="range"] {
      display: block;
      width: 120px;
      float: left;
      height: 34px;
    }

    .value {
      float: left;
      margin-left: 10px;
      font-size: 13px;
    }

    .tip {
      font-family: "iconfont";
      font-size: 20px;
      float: left;
      margin-left: 10px;
      color: #09f;
      cursor: help;
    }
  }

  .line-pic {
    height: 75px;

    label {
      line-height: 75px;
    }

    .picasa {
      float: left;
    }
  }
}

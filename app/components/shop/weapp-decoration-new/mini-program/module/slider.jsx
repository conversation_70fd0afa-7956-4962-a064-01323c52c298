
import commonSet from "./common-set";
import Link from "../component/link";
import Picasa from "../../component/picasa";
import module from "./module";
import video from "./video";
var { Module, ModuleSet } = module;

var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
var defaultSet = {
    name: "slider",
    title: "图片轮播",
    commonSet: getDefaultCommonSet(),
    set: {
        height: 375,
        list: [
            {
                pic: "",
                url: "",
                type: "video",
            },
        ],
    },
};

class Slider extends Module {
    componentDidMount() {
        this.state.sliderIndex = 0;
        this.timer = setInterval(() => {
            var length = this.state.item.set.list.length;
            if (length == 1) {
                return;
            }
            var sliderIndex = this.state.sliderIndex;
            sliderIndex++;
            sliderIndex %= length;
            this.setState({
                sliderIndex: sliderIndex,
            });
        }, 3000);
    }
    componentWillUnmount() {
        // clearInterval(this.timer);
    }
    render() {
        var item = this.state.item;
        var self = this;
        return (
            <div
                className={`slider ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{
                    height: parseInt(item.set.height / 2) + "px",
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                }}>
              <ToolBar parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}  isSubModule={this.state.isSubModule} />
              <div style={{overflow: "hidden"}}>
                <div
                    className="slider-box"
                    style={{
                        width: item.set.list.length * 375 + "px",
                        height: item.set.height / 2 + "px",
                        transform: "translate(" + this.state.sliderIndex * -375 + "px, 0)",
                    }}>
                    {item.set.list.map(function (line, index) {
                        return (
                            <div>
                                {line.type == "pic" ? (
                                    <div
                                        key={index}
                                        className="pic"
                                        style={{
                                            height: item.set.height / 2 + "px",
                                            backgroundImage: "url(" + line.pic + ")",
                                        }}
                                    />
                                ) : (
                                    <video
                                        className="pic"
                                        style={{ height: item.set.height / 2 + "px" }}
                                        src={
                                            line.url ||
                                            "http://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400"
                                        }></video>
                                )}
                            </div>
                        );
                    })}
                </div>
                </div>
            </div>
        );
    }
}

class SliderSet extends ModuleSet {
    add() {
        var list = this.state.item.set.list;
        list.push({
            pic: "",
            url: "",
            type: "pic",
        });
        this.setState(this.state.item);
    }
    addVideo() {
        var list = this.state.item.set.list;
        list.push({
            pic: "",
            url: "",
            type: "video",
        });
        this.setState(this.state.item);
    }
    delete(index) {
        var list = this.state.item.set.list;
        list.splice(index, 1);
        this.setState(this.state.item);
    }
    render() {
        var self = this;
        var item = this.state.item;
        return (
            <div className="slider-set">
              <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
                <div className="line">
                    <label>轮播高度</label>
                    <input
                        type="range"
                        value={item.set.height}
                        step="5"
                        onChange={this.input.bind(this, "height")}
                        min="200"
                        max="750"
                    />
                    <div className="value">{item.set.height}px</div>
                </div>
                {item.set.list.map((line, index) => {
                    return (
                        <div>
                            {line.type == "pic" ? (
                                <div className="group" key={index}>
                                    <div className="group-op">
                                        <div className="name">
                                            第{index + 1}组{" "}
                                            <div className="del" onClick={self.delete.bind(self, index)}>
                                                &#xe60d;
                                            </div>
                                        </div>
                                    </div>
                                    <div className="line line-pic">
                                        <label>图片</label>
                                        <Picasa
                                            data={{
                                                style: { width: "150px", height: "75px" },
                                                src: line.pic,
                                            }}
                                            methods={{
                                                updateSrc: src => {
                                                    line.pic = src;
                                                    this.refreshSet();
                                                },
                                            }}
                                        />
                                    </div>
                                    <span style={{marginLeft:'50px'}}>尺寸：宽750 高不固定自适应以配置高度为准</span>
                                    <div className="line">
                                        <label>链接</label>
                                        <Link
                                            data={{ pages: self.state.pages, url: line.url }}
                                            methods={{
                                                updateUrl: url => {
                                                    line.url = url;
                                                    this.refreshSet();
                                                },
                                            }}
                                        />
                                    </div>
                                </div>
                            ) : (
                                <div className="group" key={index}>
                                    <div className="group-op">
                                        <div className="name">
                                            第{index + 1}组{" "}
                                            <div className="del" onClick={self.delete.bind(self, index)}>
                                                &#xe60d;
                                            </div>
                                        </div>
                                    </div>
                                    <div className="image-set">
                                        <div className="line line-pic">
                                            <label>视频封面</label>
                                            <Picasa
                                                data={{
                                                    style: { width: "150px", height: "75px" },
                                                    src: line.pic,
                                                }}
                                                methods={{
                                                    updateSrc: src => {
                                                        line.pic = src;
                                                        this.refreshSet();
                                                    },
                                                }}
                                            />
                                        </div>
                                        <div className="line">
                                            <label>视频地址</label>
                                            <input
                                                type="text"
                                                value={line.url}
                                                onChange={e => {
                                                    line.url = e.target.value;
                                                    this.refreshSet();
                                                }}
                                                // onChange={this.input.bind(this, "url")}
                                                className="form-control"
                                                placeholder="视频地址（URL）"
                                            />
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}
                <div className="btn-box">
                    <div className="btn btn-cool" onClick={this.add.bind(this)}>
                        添加图片
                    </div>
                    <div className="btn btn-cool" onClick={this.addVideo.bind(this)}>
                        添加视频
                    </div>
                </div>
            </div>
        );
    }
}
var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};
export default {
    Slider: Slider,
    SliderSet: SliderSet,
    getSliderDefaultSet: getDefaultSet,
};

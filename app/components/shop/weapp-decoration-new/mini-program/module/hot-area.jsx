import commonSet from './common-set';
import module from './module';
import Picas<PERSON> from '../../component/picasa';
import dialog from '../../component/dialog';
import HotAreaDrawer, {RadioGroupNameMap} from './hot-area-drawer';

var { Module, ModuleSet } = module;
const { Button, Modal } = antd;

var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
var defaultSet = {
    name: 'hot-area',
    title: '热区',
    commonSet: getDefaultCommonSet(),
    set: {
        height: 0,
        pic: '',
        list: []
    }
};


class HotArea extends Module {

    render() {
        var item = this.state.item;
        return (
            <div className={`hot-area ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{ marginTop: item.commonSet.marginTop / 2 + 'px', marginBottom: item.commonSet.marginBottom / 2 + 'px' }}>
                <ToolBar parentIndex={this.state.parentIndex} index={this.state.index} layoutIndex={this.state.layoutIndex} methods={this.props.methods} isSubModule={this.state.isSubModule} />
                <div className='bg' style={{ backgroundImage: `url(${item.set.pic})`, height: item.set.height / 2 + 'px' }}>
                    {
                        item.set.list.map((node, index) =>
                            <div className='rect' key={index} style={{
                                left: node.left / 2 + 'px',
                                top: node.top / 2 + 'px',
                                width: node.width / 2 + 'px',
                                height: node.height / 2 + 'px'
                            }}>
                                <div className='index'>{index + 1}</div>
                            </div>
                        )
                    }
                </div>
                {item.set.pic == '' ? <div className='none-set'>请设置热区</div> : ''}

            </div>
        )
    }
}

class HotAreaSet extends ModuleSet {
    constructor(props) {
        super(props);
        this.state.showHotAreaEditor = false;
        this.state.showHotAreaDrawer = false;
        this.state.active = 0;
    }
    del(index) {
        var list = this.state.item.set.list;
        list.splice(index, 1);
        this.refreshSet();
    }
    getLinkText(url) {
      let urlText = '';
      switch (url.radioKey) {
        case RadioGroupNameMap.search:
          urlText = '已链接到：商品页';
          break;
        case RadioGroupNameMap.appoint:
          urlText = '已链接到：指定页面';
          break
        case RadioGroupNameMap.video:
          urlText = '已链接到：视频号';
          break
        case RadioGroupNameMap.customerService:
          urlText = '已链接到：客服号';
          break
        case RadioGroupNameMap.modal:
          urlText = '已链接到：弹窗';
          break
        default:
          break
    }
    return urlText
  }
    render() {
        var item = this.state.item;

        const { showHotAreaDrawer, active,isSubModule } = this.state;

        return (
            <div className='hot-area-set'>
                {
                    this.state.showHotAreaEditor ?
                        <HotAreaEditor data={{ pages: this.state.pages, isSubModule, set: item.set }} methods={{
                            confirm: (data) => {
                                item.set = data;
                                this.state.showHotAreaEditor = false;
                                this.refreshSet();
                            },
                            close: () => {
                                this.setState({
                                    showHotAreaEditor: false
                                })
                            }
                        }} />
                        : ''
                }
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
                <div className='out-set'>
                    {
                        item.set.list.map((item, index) =>{
                          let urlText = this.getLinkText(item.url)
                          return (<div className='line' key={index}>
                            <label>
                              <div className='del' onClick={this.del.bind(this, index)}>&#xe60d;</div>
                              链接{index + 1}
                            </label>
                            {/* <Link data={{pages : this.state.pages , url : item.url}} methods={{
                                    updateUrl : (url) => {
                                        item.url = url;
                                        this.refreshSet();
                                    }}}  /> */}
                            {
                              urlText?<Button type={"link"}  onClick={() => {
                                  this.setState({
                                    showHotAreaDrawer: true,
                                    active: index
                                  })
                                }}>{urlText}</Button> :
                                <Button onClick={() => {
                                  this.setState({
                                    showHotAreaDrawer: true,
                                    active: index
                                  })
                                }}>请选择</Button>
                            }
                          </div>)
                        }
                        )
                    }
                </div>
                <div className='btn btn-cool set' onClick={() => this.setState({ showHotAreaEditor: true })}>设置热区</div>
                <HotAreaDrawer
                    open={showHotAreaDrawer}
                    setOpen={(e) => this.setState({ showHotAreaDrawer: e })}
                    onSubmit={(radioKey, radioValue) => {
                      item.set.list[active].url={radioKey, radioValue}
                        this.refreshSet();
                        this.setState({
                            showHotAreaDrawer: false
                        })
                    }}
                    defaultData={{
                        radioKey: item.set.list.length&&item.set.list[active].url.radioKey,
                        radioValue: item.set.list.length&&item.set.list[active].url.radioValue
                    }}
                />
            </div>
        )
    }
}

class HotAreaDialog extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isSubModule:props.data.isSubModule,
            showHotAreaDrawer: false,
            pages: props.data.pages,
            pic: props.data.set.pic,
            list: [],
            height: props.data.set.height /props.data.isSubModule?1: 2,
            active: 0,
            type: '',
            offset: {},
            id: 'draw-' + new Date().getTime(),
            tip: false
        }
    }
    componentDidMount() {
        var el = document.getElementById(this.state.id);
        var [offsetLeft, offsetTop, offsetWidth, offsetHeight] = [0, 0, el.offsetWidth, el.offsetHeight];
        while (el) {
            offsetLeft += el.offsetLeft;
            offsetTop += el.offsetTop;
            el = el.offsetParent;
        }
        this.state.offset = {
            left: offsetLeft - 475,
            top: offsetTop - 340,
            width: offsetWidth,
            height: offsetHeight
        };
        var list = [];
        let radio = this.state.isSubModule? 1:2;

        this.props.data.set.list.map((item) => {
            var _item = {
                ...item,
                start: {
                    x: item.left /radio  + this.state.offset.left,
                    y: item.top / radio + this.state.offset.top
                },
                end: {
                    x: (item.left + item.width) / radio + this.state.offset.left,
                    y: (item.top + item.height) / radio + this.state.offset.top
                },
                url: item.url,
            }
            list.push(_item);
        })
       this.props.data.set
        this.setState({
            list: list,
            height:this.props.data.set.height /radio,
        })
    }

    select(index, e) {
        this.setState({
            active: index
        })
    }
    dragStart(e) {
        if (this.state.height == 0) {
          Modal.warning({
                icon: 'error',
                content: '请选设置背景图'
            })
            return;
        }

        var item = {
            start: {
                x: e.pageX,
                y: e.pageY
            },
            end: {
                x: e.pageX,
                y: e.pageY
            },
            url: ''
        };
        var list = this.state.list;
        list.push(item);
        this.state.active = list.length - 1;
        this.state.type = 'drag';
        this.setState(this.state);
    }
    draging(e) {
        if (this.state.active < 0 || this.state.type != 'drag') {
            return;
        }
        var item = this.state.list[this.state.active];
        item.end = {
            x: e.pageX,
            y: e.pageY
        }
        this.setState(this.state);
    }

    moveStart(e) {
        e.stopPropagation();
        if (this.state.active < 0) {
            return;
        }
        var item = this.state.list[this.state.active];
        item.moveStart = {
            x: e.pageX,
            y: e.pageY
        }
        this.state.type = 'move';
    }
    moving(e) {
        e.stopPropagation();
        if (this.state.active < 0 || this.state.type != 'move') {
            return;
        }
        var item = this.state.list[this.state.active];

        var [x, y] = [e.pageX - item.moveStart.x, e.pageY - item.moveStart.y];
        item.moveStart = {
            x: e.pageX,
            y: e.pageY
        }
        item.start.x += x;
        item.end.x += x;
        item.start.y += y;
        item.end.y += y;
        this.setState(this.state);
    }
    adjustStart(forward, e) {
        e.stopPropagation();
        if (this.state.active < 0) {
            return;
        }
        var item = this.state.list[this.state.active];
        item.adjustStart = {
            left: e.pageX,
            top: e.pageY
        }
        this.state.type = 'adjust';
        this.state.forward = forward;
    }
    adjusting(e) {
        e.stopPropagation();
        if (this.state.active < 0 || this.state.type != 'adjust') {
            return;
        }
        var item = this.state.list[this.state.active];
        var [x, y] = [e.pageX - item.adjustStart.left, e.pageY - item.adjustStart.top];
        item.adjustStart = {
            left: e.pageX,
            top: e.pageY
        }
        switch (this.state.forward) {
            case 'lt':
                item.start.x += x;
                item.start.y += y;
                break;
            case 'ct':
                item.start.y += y;
                break;
            case 'rt':
                item.end.x += x;
                item.start.y += y;
                break;
            case 'lc':
                item.start.x += x;
                break;
            case 'rc':
                item.end.x += x;
                break;
            case 'lb':
                item.start.x += x;
                item.end.y += y;
                break;
            case 'cb':
                item.end.y += y;
                break;
            case 'rb':
                item.end.x += x;
                item.end.y += y;
                break;
        }
        this.setState(this.state);
    }
    end(e) {
        if (this.state.type == 'drag') {
            $(e.currentTarget).find('button:last-child').focus();
        }
        this.setState({
            type: ''
        })
    }
    mouseMove(e) {
        if (this.state.active < 0) {
            return;
        }
        var item = this.state.list[this.state.active];
        switch (this.state.type) {
            case 'drag': this.draging(e); break;
            case 'move': this.moving(e); break;
            case 'adjust': this.adjusting(e); break;
        }
    }

    imgLoad(e) {
        var img = e.currentTarget;
        if (!this.state.tip) {
            return;
        }
        this.state.tip = false;
        var height = img.height; //目前布局只有两列布局，后边扩展再根据情况 按比缩小高度
       if (img.height < 150) {
            Modal.warning({
                content: '图片高度过小，屏幕宽度为750px,高度应该在300px - 1000px之间'
            })
            height = 150;
        }
        if (img.height > 500) {
            Modal.warning({
              content: <div>图片高度过大，屏幕宽度为750px,高度应该在300px - 1000px之间，热区图，超过1000px将被<span>无情的</span>截除</div>
            })
            height = 500;
        }
        this.setState({
            height: height
        })
    }
    keyUp(e) {
        if (e.keyCode == 46 || e.keyCode == 8) {
            var list = this.state.list;
            list.splice(this.state.active, 1);
            this.setState({
                list: list
            })
        }
    }
    del(index) {
        var list = this.state.list;
        list.splice(index, 1);
        this.setState({
            list: list
        })
    }
    confirm() {
        var list = [];
        let radio = this.state.isSubModule? 1:2;
        let cloneList = _.clone(this.state.list)
         cloneList.map((item) => {
            var _item = {
                left: (Math.min(item.start.x, item.end.x) - this.state.offset.left) * radio,
                top: (Math.min(item.start.y, item.end.y) - this.state.offset.top) * radio,
                width: Math.abs(item.end.x - item.start.x) * radio,
                height: Math.abs(item.end.y - item.start.y) * radio,
                url: item.url,

            }
            list.push(_item);
        })
        var data = {
            list: list,
            pic: this.state.pic,
            height: this.state.height * radio
        }
        this.props.methods.confirm(data);
    }
    render() {
        const { showHotAreaDrawer, active ,list} = this.state;
        return (
            <div className='hot-area-editor'>
                <HotAreaDrawer
                    open={showHotAreaDrawer}
                    setOpen={e => {
                        this.setState({ showHotAreaDrawer: e })
                    }}
                    onSubmit={(radioKey, radioValue) => {
                        this.state.list[active].url={radioKey, radioValue}
                        this.setState(this.state);
                        this.setState({
                            showHotAreaDrawer: false
                        })
                    }}
                    defaultData={{
                        radioKey: list.length>0 ? list[active] && list[active].url.radioKey : null,
                        radioValue: list.length>0 ?  list[active] && list[active].url.radioValue :null,
                    }}
                />
                <div className='dialog-hd'>热区图</div>
                <div className='dialog-bd'>
                    <div className='bg'><img src={this.state.pic} onLoad={this.imgLoad.bind(this)} /></div>
                    <div className={['draw', this.state.type, this.state.forward].join(' ')} id={this.state.id}
                        onMouseDown={this.dragStart.bind(this)}
                        onMouseMove={this.mouseMove.bind(this)}
                        onMouseUp={this.end.bind(this)}
                        onMouseLeave={this.end.bind(this)}
                        style={{ backgroundImage: `url(${this.state.pic})`, height: this.state.height + 'px' }}
                    >

                        {
                            this.state.list.map((item, index) =>
                                <button className={this.state.active == index && this.state.type != 'drag' ? 'rect active' : 'rect'} key={index}
                                    onMouseDown={this.moveStart.bind(this)}
                                    onClick={this.select.bind(this, index)}
                                    onKeyUp={this.keyUp.bind(this)}
                                    style={{
                                        left: Math.min(item.start.x, item.end.x) - this.state.offset.left + 'px',
                                        top: Math.min(item.start.y, item.end.y) - this.state.offset.top + 'px',
                                        width: Math.abs(item.end.x - item.start.x) + 'px',
                                        height: Math.abs(item.end.y - item.start.y) + 'px'
                                    }}>
                                    <div className='index' >{index + 1}</div>
                                    <div className='forward'>
                                        <div className='lt'
                                            onMouseDown={this.adjustStart.bind(this, 'lt')}
                                        ></div>
                                        <div className='ct'
                                            onMouseDown={this.adjustStart.bind(this, 'ct')}
                                        ></div>
                                        <div className='rt'
                                            onMouseDown={this.adjustStart.bind(this, 'rt')}
                                        ></div>
                                        <div className='lc'
                                            onMouseDown={this.adjustStart.bind(this, 'lc')}
                                        ></div>
                                        <div className='rc'
                                            onMouseDown={this.adjustStart.bind(this, 'rc')}
                                        ></div>
                                        <div className='lb'
                                            onMouseDown={this.adjustStart.bind(this, 'lb')}
                                        ></div>
                                        <div className='cb'
                                            onMouseDown={this.adjustStart.bind(this, 'cb')}
                                        ></div>
                                        <div className='rb'
                                            onMouseDown={this.adjustStart.bind(this, 'rb')}
                                        ></div>
                                    </div>
                                </button>
                            )
                        }
                    </div>
                    <div className='draw-set'>
                        <div className='line line-pic'>
                            <label>背景图</label>
                            <Picasa data={{ style: { width: '75px', height: '75px' }, src: this.state.pic }} methods={{
                                updateSrc: (src) => {
                                    this.setState({
                                        tip: true,
                                        pic: src
                                    })
                                }
                            }} />
                        </div>
                        {
                            this.state.list.map((item, index) =>
                                <div className='line' key={index}>
                                    <label>
                                        <div className='del' onClick={this.del.bind(this, index)}>&#xe60d;</div>
                                        链接{index + 1}
                                    </label>
                                    {/* <Link data={{ pages: this.state.pages, url: item.url }} methods={{
                                        updateUrl: (url) => {
                                            item.url = url;
                                            this.setState(this.state);
                                        }
                                    }} /> */}
                                    <Button onClick={() => {
                                        this.setState({
                                            showHotAreaDrawer: true,
                                            active: index
                                        })
                                    }}>请选择</Button>
                                </div>
                            )
                        }
                    </div>
                </div>
                <div className='dialog-ft'>
                    <div className='btn btn-default' onClick={this.props.methods.close}>取消</div>
                    <div className='btn btn-cool' onClick={this.confirm.bind(this)}>确定</div>
                </div>
            </div>
        )
    }
}
var HotAreaEditor = dialog(HotAreaDialog);

var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
}
export default {
    HotArea: HotArea, HotAreaSet: HotAreaSet, getHotAreaDefaultSet: getDefaultSet
}


import commonSet from "./common-set";
import module from "./module";
import <PERSON><PERSON><PERSON> from "../../component/picasa";
import Link from "../component/link";

const { Module, ModuleSet } = module;
const { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
const {message,Button} =antd
var defaultSet = {
    name: "search",
    title: "搜索",
    commonSet: getDefaultCommonSet(),
    set: {
        bg: "#fff",
        iconList: [],
        style: 1, // 1: 仅搜索, 2: 搜索+图片
    },
};

class Search extends Module {
    render() {
        var item = this.state.item;
        const isSubModule = this.state.isSubModule
        return (
            <div
                className={`search ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                    background: item.set.bg,
                }}>

                <ToolBar isSubModule={isSubModule} parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods} />
                <div className="search-content">
                  {
                    item.set.style === 3 &&
                    <div className="search-icon-list">
                      {item.set.iconList && item.set.iconList.map((icon, index) => {
                        let obj = {
                          src: icon.pic,
                        }
                        return <img {...obj} style={{width: "36px", height: "36px"}}/>
                      })}
                    </div>
                  }
                    <div className="search-box">搜索你想要的</div>
                  {
                    item.set.style === 2 &&
                    <div className="search-icon-list">
                      {item.set.iconList && item.set.iconList.map((icon, index) => {
                        let obj = {
                          src: icon.pic,
                        }
                        return <img {...obj} style={{width: "36px", height: "36px"}}/>
                      })}
                    </div>
                  }
                </div>
            </div>
        );
    }
}

class SearchSet extends ModuleSet {
    add() {
        var item = this.state.item;
        // 检查当前图标数量是否已达到最大值
        if (item.set.iconList.length >= 2) {
            message.warning("最多只能添加2张图片");
            return;
        }
        // 添加一个新的空图标到iconList数组
        item.set.iconList.push({
          url:{
            radioKey:"",
            radioValue:{},
          }
        });
        this.refreshSet();
    }

    delete(index) {
        var item = this.state.item;
        // 从iconList数组中删除指定索引的图标
        item.set.iconList.splice(index, 1);
        this.refreshSet();
    }

    render() {
        var item = this.state.item;
        return (
            <div className="search-set">
              <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
                {/*<div className="line setBga">*/}
                {/*    <label>背景色</label>*/}
                {/*    <input*/}
                {/*        type="text"*/}
                {/*        value={this.state.item.set.bg}*/}
                {/*        className="form-control"*/}
                {/*        onChange={e => {*/}
                {/*            this.state.item.set.bg = e.target.value;*/}
                {/*            this.refreshSet();*/}
                {/*        }}*/}
                {/*        placeholder="背景颜色"*/}
                {/*    />*/}
                {/*</div>*/}
                <div>
                    <div className="search-style-select">
                        <label>搜索样式</label>

                        <div className="style-option">
                            <input
                                type="radio"
                                name="searchStyle"
                                checked={item.set.style === 1}
                                onChange={() => {
                                    item.set.style = 1;
                                    this.refreshSet();
                                }}
                            />
                            <label>样式1（仅搜索）</label>
                            <div className="style-preview">
                                <img
                                    src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/53139887566.png"
                                />
                            </div>
                        </div>

                        <div className="style-option">
                            <input
                                type="radio"
                                name="searchStyle"
                                checked={item.set.style === 2}
                                onChange={() => {
                                    item.set.style = 2;
                                    this.refreshSet();
                                }}
                            />
                            <label>样式2（搜索+图片）</label>
                            <div className="style-preview">
                                <img
                                    src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/5313782485.png"
                                />
                            </div>
                        </div>
                        <div className="style-option">
                        <input
                          type="radio"
                          name="searchStyle"
                          checked={item.set.style === 3}
                          onChange={() => {
                            item.set.style = 3;
                            this.refreshSet();
                          }}
                        />
                        <label>样式3（图片+搜索）</label>
                        <div className="style-preview">
                          <img
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/5313609570.png"
                          />
                        </div>
                      </div>
                    </div>
                </div>
                {(item.set.style === 2||item.set.style === 3) && (
                    <div>
                        {item.set.iconList&&item.set.iconList.map((icon, index) => (
                            <div className="search-icon-item" key={index}>
                                <div style={{ display: "flex", justifyContent: "space-between", padding: "10px" }}>
                                    <span>图片 {index + 1}</span>
                                    <div className="del" onClick={() => this.delete(index)}>
                                        删除
                                    </div>
                                </div>
                                <div className="line line-pic">
                                    <label>图片</label>
                                    <Picasa
                                        data={{
                                            style: { width: "150px", height: "75px" },
                                            src: icon.pic,
                                        }}
                                        methods={{
                                            updateSrc: src => {
                                                item.set.iconList[index].pic = src;
                                                this.refreshSet();
                                            },
                                        }}
                                    />
                                </div>
                                 <span style={{marginLeft:'60px'}}>尺寸
                                    <span style={{marginLeft:"10px"}}>1:1</span>
                                </span>

                                <div className="line">
                                    <label>图片地址</label>
                                    <input
                                        type="text"
                                        value={icon.pic}
                                        className="form-control"
                                        onChange={e => {
                                            item.set.iconList[index].pic = e.target.value||"";
                                            this.refreshSet();
                                        }}
                                        placeholder="图片地址"
                                    />
                                </div>

                              <div className='line '>
                                <label>链接</label>
                                <Link data={{pages : this.state.pages, url : item.set.iconList[index].url}} methods={{
                                  updateUrl : (url) => {
                                    item.set.iconList[index].url =url
                                    this.refreshSet();
                                  }
                                }} />
                              </div>
                            </div>
                        ))}
                      <div className='add-img-footer'>
                        <Button type="primary" style={{width : "100%"}}  ghost  onClick={() => this.add()}>
                          添加图片（{item.set.iconList.length||0}/2）
                        </Button>
                      </div>
                    </div>
                )}
            </div>
        );
    }
}

var getSearchDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};
export default {
    Search: Search,
    SearchSet: SearchSet,
    getSearchDefaultSet: getSearchDefaultSet,
};

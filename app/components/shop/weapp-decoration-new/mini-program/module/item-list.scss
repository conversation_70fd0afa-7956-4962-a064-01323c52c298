.panel-phone{
    .item-list{
        >.title{
            height: 50px;

            .text{
                line-height: 50px;
                font-size: 20px;
                color:#333;
                padding:0 20px;
                background:transparent;
                position: absolute;
                z-index:1;
                left: 50%;
                transform : translate(-50% , 0);
            }

            .line{
                position: absolute;
                border-top:solid 1px #ddd;
                left: 20px;
                right: 20px;
                top: 25px;
            }
        }
        >.title-img{
            img{
                display: block;
                width:375px;
            }
        }
        .no-items{
            height: 100px;
            text-align: center;
            font-size: 22px;
            color:#333;
            line-height: 100px;
            font-weight: bold;
        }
        .item-list-box{
            display: flex;
            flex-direction: row ;
            flex-wrap: wrap;
            justify-content: flex-start;
            padding-left: 8px;
        }
        .item{
            width:175px;
            height: 260px;
            border-radius: 5px;
            overflow: hidden;
            background:#fff;
            margin-bottom:10px;
            margin-right: 8px;
            .pic {
                width: 175px;
                height: 175px;
                box-sizing: border-box;
                overflow:hidden;
                background: url(#) no-repeat center center;
                background-size: cover;

            }
            .title{
                margin:10px 10px 5px;
                height: 36px;
                line-height: 18px;
                font-size: 12px;
                overflow: hidden;
                color:#555;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            .price{
                color:#f30;
                display: block;
                font-size: 16px;
                line-height: 1.5;
                margin-left: 10px;
            }
        }
    }
}


.panel-module-set{
    .editor{
        .item-list-set{
            .line-pic{
                height: 50px;
                line-height: 50px;
                label{
                    line-height: 50px;
                }
            }
        }
      .weapp-decoration-new-dialog{
            .btn{
                display: inline-block;
                margin:0 20px;
            }
            .panel-jump{
                .btn{
                    width:50px;
                }
            }
        }

    }
}

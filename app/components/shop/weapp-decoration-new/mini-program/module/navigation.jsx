import commonSet from "./common-set";
import module from "./module";
import ImageSettings from "../component/image-settings";
import ImageStyleUtils from "../utils/image-style-utils";
const {Tooltip} = antd;
const { Module, ModuleSet } = module;
const { QuestionCircleOutlined } = icons;

const { ToolBar } = commonSet;
const defaultSet = {
    name: "navigation",
    title: "头部导航",
    set: {
        type: 1, // 标题类型 1:文字 2:图片
        name: "新建页面",
        title: "新建页面",
        shareMessage: "",
        backgroundColor: "#ffffff", // 背景色设置
        titleColor: "#000000", // 标题字体颜色设置
        titleImage: "", // 图片类型时的图片URL
        // 图片尺寸配置
        fixedRatio: false, // 固定比例开关（默认关闭）
        aspectRatio: "16:9", // 比例选项
        imageHeight: 86, // 高度设置（默认86）
        imageWidth: 375, // 宽度设置（默认375）
        customImageHeight: 86, // 自定义高度（默认86）
        // 宽高设置模式
        widthMode: "fixed", // 宽度模式：fixed(固定) / auto(适应内容)（默认固定）
        heightMode: "fixed", // 高度模式：fixed(固定) / auto(适应内容)（默认固定）
    },
};

// 通用的历史数据兼容性处理函数
const ensureDefaultValues = (item) => {
    // 确保 set 对象存在
    if (!item.set) {
        item.set = {};
    }

    // 获取默认值配置
    const defaults = defaultSet.set;

    // 只为缺失的属性设置默认值，保留现有值
    Object.keys(defaults).forEach(key => {
        if (item.set[key] === undefined) {
            // 对于 name 和 title，优先使用历史数据中的值
            if ((key === 'name' || key === 'title') && item.set[key]) {
                // 如果历史数据中已有值，保持不变
                return;
            }
            item.set[key] = defaults[key];
        }
    });

    return item;
};

class Navigation extends Module {

    // 计算容器尺寸 - 使用工具类
    getContainerStyle = (item) => {
        // 如果 ImageStyleUtils 可用，使用它；否则回退到原始实现
        if (typeof ImageStyleUtils !== 'undefined') {
            return ImageStyleUtils.getContainerStyle(item.set);
        }

        // 回退实现
        const { fixedRatio, aspectRatio, imageHeight, imageWidth, customImageHeight, widthMode, heightMode } = item.set;
        if (fixedRatio) {
            const ratios = { "16:9": 16/9, "4:3": 4/3, "3:2": 3/2, "1:1": 1/1, "2:1": 2/1 };
            const ratio = ratios[aspectRatio] || 16/9;
            const width = imageHeight * ratio;
            return { width: `${width}px`, height: `${imageHeight}px` };
        } else {
            const style = {};
            if (widthMode === "fixed") style.width = `${imageWidth}px`; else style.width = 'auto';
            if (heightMode === "fixed") style.height = `${customImageHeight}px`; else style.height = 'auto';
            return style;
        }
    }

    // 计算图片样式 - 使用工具类
    getImageStyle = () => {
        // 如果 ImageStyleUtils 可用，使用它；否则回退到原始实现
        if (typeof ImageStyleUtils !== 'undefined') {
            return ImageStyleUtils.getImageStyle();
        }

        // 回退实现
        return {
            width: '100%',
            height: '100%',
            visibility: 'visible',
            objectFit: 'cover'
        };
    }

    render() {
        var item = this.state.item;
        // 处理历史数据兼容性
        item = ensureDefaultValues(item);
        const { type, title, titleImage, backgroundColor, titleColor } = item.set;
        const containerStyle = this.getContainerStyle(item);
        const imageStyle = this.getImageStyle();

        return (
            <div className={"navigation module " + (this.state.index == this.state.activeModule)} style={{backgroundColor: backgroundColor}}>
                <ToolBar index={this.state.index} methods={this.props.methods} />
                <img className="bg" src="" />
                {type === 1 ? (
                    <div className="title" style={{color: titleColor}}>{title}</div>
                ) : (
                    <div className="title-image" style={containerStyle}>
                        {titleImage && <img src={titleImage} alt={title} style={imageStyle} />}
                    </div>
                )}
            </div>
        );
    }
}

class NavigationSet extends ModuleSet {

    // 处理图片设置变化
    handleImageSettingsChange = (newSettings) => {
        const item = this.state.item;
        console.log("handleImageSettingsChange",newSettings);

        Object.assign(item.set, newSettings);
        this.refreshSet();
    }

    // 处理图片上传
    handleImageUpload = (src) => {
        const item = this.state.item;
        item.set.titleImage = src;
        this.refreshSet();
    }

    // 处理图片删除
    handleImageDelete = () => {
        const item = this.state.item;
        item.set.titleImage = "";
        this.refreshSet();
    }

    render() {
        var item = this.state.item;
        // 处理历史数据兼容性
        item = ensureDefaultValues(item);
        const { type=1, title } = item.set;
        console.log("navigation-set",item.set);

        return (
            <div className="navigation-set">
                {/* 标题类型选择 */}
                <div className="line">
                    <label>标题类型</label>
                    <div style={{ display: 'flex'}}>
                        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                            <input
                                type="radio"
                                name="titleType"
                                checked={type === 1}
                                onChange={() => {
                                    item.set.type = 1;
                                    this.refreshSet();
                                }}
                                style={{ marginRight: '5px' }}
                            />
                            文字
                        </label>
                        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                            <input
                                type="radio"
                                name="titleType"
                                checked={type === 2}
                                onChange={() => {
                                    item.set.type = 2;
                                    this.refreshSet();
                                }}
                                style={{ marginRight: '5px' }}
                            />
                            图片
                        </label>
                    </div>
                </div>

                {/* 文字类型的设置 */}
                {type === 1 && (
                    <div>
                        <div className="line">
                            <label>页面标题  <Tooltip title={'页面标题，小程序中，页面显示的标题'}>
                                <QuestionCircleOutlined />
                            </Tooltip>
                            </label>
                            <input
                                maxLength="10"
                                type="text"
                                value={title}
                                onChange={this.input.bind(this, "title")}
                                className="form-control"
                                placeholder="页面标题"
                            />
                        </div>

                        {/* 字体颜色设置 */}
                        <div className="line">
                            <label>标题字体颜色</label>
                            <div style={{ marginTop: '8px' }}>
                                <input
                                    type="color"
                                    value={item.set.titleColor}
                                    onChange={(e) => {
                                        item.set.titleColor = e.target.value;
                                        this.refreshSet();
                                    }}
                                    style={{
                                        width: '50px',
                                        height: '32px',
                                        border: '1px solid #d9d9d9',
                                        borderRadius: '4px',
                                        cursor: 'pointer'
                                    }}
                                />
                                <span style={{ marginLeft: '10px', color: '#666' }}>{item.set.titleColor}</span>
                            </div>
                        </div>
                    </div>
                )}

                {/* 图片类型的设置 */}
                {type === 2 && (
                    <ImageSettings
                        imageSettings={item.set}
                        defaultSettings={{
                          tips: "图片尺寸宽固定375px,高不固定建议86px",
                          showHeight: false,
                        }}
                        onChange={this.handleImageSettingsChange}
                        onImageUpload={this.handleImageUpload}
                        onDeleteImage={this.handleImageDelete}
                    />
                )}

                {/* 背景色设置 */}
                <div className="line">
                    <label>背景色</label>
                    <div style={{ marginTop: '8px' }}>
                        <input
                            type="color"
                            value={item.set.backgroundColor}
                            onChange={(e) => {
                                item.set.backgroundColor = e.target.value;
                                this.refreshSet();
                            }}
                            style={{
                                width: '50px',
                                height: '32px',
                                border: '1px solid #d9d9d9',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                        />
                        <span style={{ marginLeft: '10px', color: '#666' }}>{item.set.backgroundColor}</span>
                    </div>
                </div>

            </div>
        );
    }
}
var getNavigationDefaultSet = function (id) {
    defaultSet.set.id = id;
    return JSON.parse(JSON.stringify(defaultSet));
};

export default {
    Navigation: Navigation,
    NavigationSet: NavigationSet,
    getNavigationDefaultSet: getNavigationDefaultSet,
};

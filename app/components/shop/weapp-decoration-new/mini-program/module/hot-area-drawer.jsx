const { useState, useEffect, useRef, useCallback, useMemo } = React;
const { Drawer, Radio, Card, Table, Input, Form, Button, Space, Typography, Select, Image, message } = antd;
const { FormSelect } = dtComponents

import request from "../../../../utils/plugins/axios/request";

const { Title } = Typography;
const API = {
  async getShopList(params) {
    return request({
      url: '/api/items/paging/v2?shopId=' + sessionStorage.shopId,
      needMask: true,
      data: params
    })
  },
  async getModalList(params) {
    return request({
      url: "/mall-admin/api/wxappPages/listPopUp",
      needMask: true,
      data: params
    })
  },
  async getCustomPageList(params) {
    return request({
      url: "/mall-admin/api/wxappPages/listCustomPages",
      needMask: true,
      data: params
    })
  },
  async getCustomServicPageList(params) {
    return request({
      url: "/mall-admin/api/weCom/KfAccount/list",
      needMask: true,
      data: params
    })
  },
  async getCustomPageUrl(params) {
    return request({
      url: "/mall-admin/api/weCom/KfAccount/getUrl",
      needMask: true,
      data: params
    })
  },
  // 获取营销活动的信息
  async getMarketingList(params) {
    return request({
      url: '/mall-admin/api/gb_activity/pageByMt?shopId=' + sessionStorage.shopId,
      needMask: true,
      data: params
    })
  },

}


export const RadioGroupNameMap = {
  search: 'goods',
  appoint: 'selectPage',
  modal: 'modal',
  customerService: 'customer_service',
  video: 'video',
  marketingTools: 'marketing_tools',
}
const HotAreaDrawer = ({ open, setOpen, defaultData, onSubmit }) => {
  const style = {
    display: "flex",
    flexDirection: "column",
    gap: 8,
  };
  const [form] = Form.useForm();
  const [selectedValue, setSelectedValue] = useState()
  const radioKey = Form.useWatch("radioKey", form)

  function setFormValue(defaultData) {
    form.setFieldsValue({
      [`${defaultData.radioKey}`]: defaultData.radioValue,
      radioKey: defaultData.radioKey
    })
  }

  useEffect(() => {
    if (defaultData) {
      setSelectedValue(defaultData.radioValue)
      setFormValue(defaultData)
    }
    return () => {
      setSelectedValue(null)
      form.resetFields()
    }
  }, [open, defaultData])

  const onClose = () => {
    setOpen(false);
  };

  const handleOk = () => {
    form.validateFields().then(values => {
      debugger
      onSubmit && onSubmit(values.radioKey, values[`${values.radioKey}`]);
      onClose();
    })
  };
  return (
    <Drawer title="设置热区" onClose={onClose} open={open} width={630} destroyOnClose={true} extra={
      <Space>
        <Button type="primary" onClick={() => handleOk()}>确定</Button>
        <Button onClick={onClose}>取消</Button>
      </Space>
    }>
      <div style={style}>
        <Form
          form={form}
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 12 }}>
          <Title level={5}>
            链接类型
          </Title>
          <Form.Item name={"radioKey"} wrapperCol={{ span: 24 }}>

            <Radio.Group wrap>
              <Space>
                <Radio value={RadioGroupNameMap.search}>
                  商品页
                </Radio>
                <Radio value={RadioGroupNameMap.appoint}>
                  指定页面
                </Radio>
                <Radio value={RadioGroupNameMap.video}>
                  视频号
                </Radio>
                <Radio value={RadioGroupNameMap.customerService}>
                  客服号
                </Radio>
                <Radio value={RadioGroupNameMap.modal}>
                  弹窗
                </Radio>
                <Radio value={RadioGroupNameMap.marketingTools}>
                  营销活动
                </Radio>
              </Space>
            </Radio.Group>

          </Form.Item>

          <div>
            <Title level={5}>
              链接配置
            </Title>
            {radioKey === RadioGroupNameMap.search ?
              <Form.Item name={RadioGroupNameMap.search} wrapperCol={{ span: 24 }}><Search /></Form.Item> : null}
            {radioKey === RadioGroupNameMap.appoint ?
              <AppiontPage /> : null}
            {radioKey === RadioGroupNameMap.video ? <VideoPage /> : null}
            {radioKey === RadioGroupNameMap.customerService ? <CustomerServicePage /> : null}
            {radioKey === RadioGroupNameMap.modal ?
              <Form.Item name={RadioGroupNameMap.modal} wrapperCol={{ span: 24 }}><ModalPage /></Form.Item> : null}
            {radioKey === RadioGroupNameMap.marketingTools ?
              <Form.Item
                name={RadioGroupNameMap.marketingTools} 
                wrapperCol={{ span: 24 }}
                rules={[
                  {
                      validator(rule, value) {
                        const { toolsId, activityId } = value || {}
                        if(!toolsId || !activityId || !activityId.length){
                          message.error(`请选择营销活动`);
                          return Promise.reject("")
                        }
                        console.log('value', value)
                          return Promise.resolve("")
                      }
                  }
              ]}
              ><MarketingTools /></Form.Item> : null}
          </div>
        </Form>
      </div>

    </Drawer>
  );
};

export default HotAreaDrawer;

const CustomerServicePage = () => {

  const [list, setList] = useState([])



  useEffect(() => {
    API.getCustomServicPageList().then(res => {
      if (res) {
        setList(res.account_list)
      }
    })
  }, [])

  return <div>
    <Form.Item
      label={"客服"}
      name={RadioGroupNameMap.customerService}>
      <Select
        showSearch
        style={{ width: '200px' }}>
        {
          list.map(item => <Select.Option value={item.open_kfid}>{item.name}</Select.Option>)
        }
      </Select>
    </Form.Item>
  </div>

}

const VideoPage = () => {
  return (
    <div>
      <Form.Item
        tooltip={'以“sph”开头的id，获取视频号ID的需要登录视频号助手，在首页可以查看自己的视频号ID。'}
        label={"视频号ID"}
        name={[RadioGroupNameMap.video, "number"]}
      >
        <Input
          placeholder="请输入视频号"
          style={{ width: "100%" }}
        />
      </Form.Item>
      <Form.Item
        label={"视频ID"}
        name={[RadioGroupNameMap.video, "id"]}
        tooltip={'获取视频的feedId需要登录视频号助手，在「动态管理」模块可以复制自己发表的每个视频对应的feedId。'}>
        <Input
          placeholder="请输入视频号ID"
          style={{ width: "100%" }}
        />
      </Form.Item>
    </div>
  )
}

const Search = ({ onChange, value }) => {
  const [form] = Form.useForm();

  const [shopList, setShopList] = useState([]);
  const [params, setParams] = useState({})
  const [pagination, setPagination] = useState({
    current: 1,
    size: 10,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([])


  useEffect(() => {
    value && setSelectedRowKeys(value || [])
  }, [value])

  const rowSelection = {
    type: "radio",
    rowKey: "id",
    selectedRowKeys: selectedRowKeys,

    onChange: (selectedRowKeys, _) => {
      setSelectedRowKeys(selectedRowKeys)
      onChange && onChange(selectedRowKeys);
    },
  };
  const initList = (data) => {
    API.getShopList(data).then(res => {
      setShopList(res.data);
      setPagination({
        size: res.size,
        total: res.total,
        current: res.current
      })
    })
  }

  useEffect(() => {
    initList(pagination)
  }, []);

  const onSearch = async () => {
    const values = form.getFieldsValue();
    if (values.outerSkuIds && values.outerSkuIds !== '') {
      values.outerSkuIds = [values.outerSkuIds]
    } else {
      delete values.outerSkuIds
    }
    initList({
      ...pagination,
      ...values
    })
  }
  const onCancel = () => {
    form.resetFields()
    initList({ current: 1, size: 10 })
    setParams({})
  }

  const columns = [
    {
      title: '图片', dataIndex: 'mainImage',width:70, align:'center', render: t => {
        return <Image src={t} style={{ width: '60px', height: '60px' }}></Image>
      }
    },
    { title: '名称', dataIndex: 'name' ,width:200},
  ]
  return (
    <div>
      <Form form={form}>
        <Form.Item label="商品名称" name="itemName">
          <Input placeholder="请输入商品名称" />
        </Form.Item>
        <Form.Item label="商品sku" name="outerSkuIds">
          <Input placeholder="请输入商品SKU" />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button onClick={onCancel}>重置</Button>
            <Button type="primary" onClick={onSearch}>搜索</Button>
          </Space>
        </Form.Item>
      </Form>
      <Table
        style={{ width: 450 }}
        dataSource={shopList}
        columns={columns}
        size="small"
        bordered
        rowKey="id"
        pagination={{
          pageSize: pagination.size,
          total: pagination.total,
          current: pagination.current,
          onChange: (page, pageSize) => {
            initList({
              ...params,
              current: page,
              size: pageSize
            })
          }
        }}
        rowSelection={rowSelection}
      />
    </div>
  );
}

const ModalPage = ({ onChange, value }) => {

  const [list, setList] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  useEffect(() => {
    if (Array.isArray(value)) {
      setSelectedRowKeys(value)
    }
  }, [value])

  const rowSelection = {
    type: "radio",
    rowKey: "id",
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys, _) => {
      setSelectedRowKeys(selectedRowKeys)
      onChange(selectedRowKeys);
    },
  };

  useEffect(() => {
    API.getModalList().then(res => {
      setList(res)
    })
  }, []);


  return (
    <div>
      <Table
        style={{ width: 450 }}
        dataSource={list}
        columns={[{ title: "名称", dataIndex: "name", key: "name" }]}
        size="small"
        rowKey="id"
        pagination={false}
        bordered
        rowSelection={rowSelection}
      />
    </div>
  );
}

const AppiontPage = () => {

  const defaultList = [
    { pageName: "首页", id: "home", pageType: "defalut" },
    { pageName: "分类页", id: "category", pageType: "defalut" },
    { pageName: "购物车", id: "shopCart", pageType: "defalut" },
    { pageName: "付费注册页", id: "register", pageType: "defalut" },
    { pageName: "登录引导页", id: "login", pageType: "defalut" },
    { pageName: "添加小助手", id: "helper", pageType: "defalut" },
    { pageName: "补贴任务页", id: "task", pageType: "defalut" },
  ]

  const [list, setList] = useState(defaultList)

  useEffect(() => {
    API.getCustomPageList().then(res => {
      const newRes = res.map(item => ({ pageName: item.pageName, id: item.id, pageType: 'custom' }))
      setList(v => [...v, ...newRes])
    })
  }, [])


  function SelectPage({ value, onChange }) {
    const [valueData, setValueData] = useState(value || {})
    const handleOnChange = (val, option) => {
      setValueData(option.data);
      onChange(option.data);
    }
    return <Select
      style={{ width: 200 }}
      value={valueData.id}
      showSearch={true}
      filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
      onChange={handleOnChange}
      onClick={(e) => e.preventDefault()}>
      {
        list.map(item => <Select.Option key={item.id} value={item.id} data={item}>{item.pageName}</Select.Option>)
      }
    </Select>
  }

  return (
    <Form.Item label={"页面列表"} name={"selectPage"}>
      <SelectPage />
    </Form.Item>
  );
};

// 营销活动
const MarketingTools = ({ onChange, value }) => {
  const [form] = Form.useForm();
  // 表格数据
  const [marketingList, setMarketingList] = useState([]);
  const [params, setParams] = useState({})
  const [toolList, setToolsList] = useState([])
  const [toolEnum, setToolEnum] = useState({})
  const [statusEnum , setStatusEnum] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    size: 10,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([])


  useEffect(() => {
    console.log('value', value)
    value && setSelectedRowKeys((value || {}).activityId || [])
  }, [value])

  const rowSelection = {
    type: "radio",
    rowKey: "id",
    selectedRowKeys: selectedRowKeys,

    onChange: (selectedRowKeys, selectedRows) => {
      console.log('selectedRowKeys', selectedRowKeys)
      setSelectedRowKeys(selectedRowKeys)
      onChange && onChange({
        toolsId:  selectedRows.length ? selectedRows[0].marketingToolId : '',
        activityId: selectedRowKeys
      });
    },
  };
  const initList = (data) => {
    if(!data.activityStatus){
      data.defaultStatus = [1, 2] // 未开始和进行中
    }
    API.getMarketingList(data).then(res => {
      setMarketingList(res.dataList);
      
      setPagination({
        size: (res.page || {}).pageSize,
        total: (res.page || {}).totalCount,
        current: (res.page || {}).currentPage
      })
    })
  }

  const getMarketingToolList = () => {
    request({
      url: '/mall-admin/api/marketing_tools/list',
      method: 'POST',
      data: {},
      success: (data) => {
        let arr = (data || []).filter(item => item.mtStatus == 1).sort((a, b) => a.mtOrder - b.mtOrder);
        setToolsList(arr)
        let tools = {}
        arr.forEach(item => {
          tools[item.id] = item.mtName;
        })
        setToolEnum(tools)
      }
    })
  }

  // 获取活动状态枚举
    const getEnum = (enumType, fn) => {
      request({
        url: '/mall-admin/api/gb_common/enum',
        method: 'POST',
        data: { enumType },
        success: (data) => {
          if (typeof fn == 'function') {
            fn(data)
          }
        }
      })
    }

  useEffect(() => {
    // 获取活动状态枚举
    getEnum('ActivityStatus', setStatusEnum);
    // 获取营销工具列表
    getMarketingToolList();
    initList(pagination)
  }, []);

  const onSearch = async () => {
    const values = form.getFieldsValue();
    initList({
      ...pagination,
      ...values
    })
  }
  const onCancel = () => {
    form.resetFields()
    initList({ current: 1, size: 10 })
    setParams({})
  }

  const columns = [
    {
      title: '活动类型', 
      dataIndex: 'marketingToolId',
      render: (_,  row,  index) => {
        return toolEnum[row.marketingToolId]
      }
    },
    { title: '活动名称', dataIndex: 'activityName' },
    { 
      title: '活动时间',
       dataIndex: 'time',
       render: (_,  row,  index) => {
        return `${moment(row.startTime).format("YYYY-MM-DD HH:mm")} ~ ${moment(row.endTime).format("YYYY-MM-DD HH:mm")}`
      }
    },
    { 
      title: '活动状态', 
      dataIndex: 'activityStatus',
      render: (_,  row,  index) => {
        return (statusEnum.find(item => item.id  == row.activityStatus) || {}).name || '';
      }
    },
  ]
  return (
    <div>
      <Form form={form}>
        <Form.Item
            label="活动类型"
            name={'marketingToolId'}
          >
            <Select
             allowClear
              options={toolList.map(item => {
                return {
                  value: item.id,
                  label: item.mtName
                }
              })}
            ></Select>
          </Form.Item>
        <Form.Item label="活动名称" name="activityName">
          <Input placeholder="请输入活动名称" />
        </Form.Item>
        <Form.Item
            label="活动状态"
            name={'activityStatus'}
          >
            <Select
              allowClear
              options={statusEnum.map(item => {
                return {
                  value: item.id,
                  label: item.name
                }
              }).filter(n => n.value == 1 || n.value == 2)}
            ></Select>
        </Form.Item>
        <Form.Item>
          <Space>
            <Button onClick={onCancel}>重置</Button>
            <Button type="primary" onClick={onSearch}>搜索</Button>
          </Space>
        </Form.Item>
      </Form>
      <Table
        style={{ width: 550 }}
        dataSource={marketingList}
        columns={columns}
        size="small"
        bordered
        rowKey="id"
        pagination={{
          pageSize: pagination.size,
          total: pagination.total,
          current: pagination.current,
          onChange: (page, pageSize) => {
            initList({
              ...params,
              current: page,
              size: pageSize
            })
          }
        }}
        rowSelection={rowSelection}
      />
    </div>
  );
}

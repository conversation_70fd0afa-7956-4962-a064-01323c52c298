import commonSet from "./common-set";
import module from "./module";
var { Module, ModuleSet } = module;
var { ToolBar, CommonSet, getDefaultCommonSet } = commonSet;
const { Cascader } = antd;
import request from "../../../../utils/plugins/axios/request";
var defaultSet = {
    name: "rank-list",
    title: "排行",
    commonSet: getDefaultCommonSet(),
    set: {
        title: "益生菌热卖榜",
        introduce: "2万+妈妈正在看",
        shopCategoryId: null, //类目id
        bgColor: "#E6E5FE",
        style: "style-one",
    },
};

const styleOneProductList = Array.from({ length: 3 }, (_, index) => ({
    id: index + 1,
    rank: index + 1,
    name: `3100位妈妈买过`,
    img: `https://pic.nximg.cn/file/20230504/27715915_110500721101_2.jpg`,
    desc: `拜奥益生菌`,
}));

const styleTwoProductListFirstLine = Array.from({ length: 3 }, (_, index) => ({
    id: index + 1,
    rank: index + 1,
    name: `3100位妈妈买过`,
    img: `https://pic.nximg.cn/file/20230504/27715915_110500721101_2.jpg`,
    desc: `拜奥益生菌`,
}));
const styleTwoProductListSecondLine = Array.from({ length: 3 }, (_, index) => ({
    id: index + 1,
    rank: index + 4,
    name: `3100位妈妈买过`,
    img: `https://pic.nximg.cn/file/20230504/27715915_110500721101_2.jpg`,
    desc: `拜奥益生菌`,
}));

class RankList extends Module {
    render() {
        var item = this.state.item;
        const { set } = item;
        return (
            <div
                className={`rank-list  ${this.state.isSubModule?"sub-module":"module"} ${this.isActived()}`}
                style={{
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                }}>
              <ToolBar parentIndex={this.state.parentIndex} index={this.state.index}  layoutIndex={this.state.layoutIndex } methods={this.props.methods}  isSubModule={this.state.isSubModule} />
                <div className="rank-list-content">
                    {set.style === "style-two" ? (
                        <div className="rank-list-style-two" style={{ backgroundColor: set.bgColor }}>
                            <div className="rank-header">
                                <div className="rank-title">{set.name || "益生菌热卖榜"}</div>
                                <div className="rank-tag">
                                    <span className="fire-icon">&#xe7a8;</span>
                                    <span className="fire-text">{set.introduce || "2万+妈妈正在逛"}</span>
                                    <span className="arrow-icon">&#xe617;</span>
                                </div>
                            </div>
                            <div className="rank-products">
                                <div className="product-row">
                                    {styleTwoProductListFirstLine.map((item, index) => {
                                        return (
                                            <div className="product-item">
                                                <div className="product-top">
                                                    <div className="product-rank">{item.rank}</div>
                                                    <div className="product-img">
                                                        <img src={item.img} />
                                                    </div>
                                                </div>
                                                <div className="product-bottom">
                                                    <div className="product-desc">{item.desc}</div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                                <div className="product-row">
                                    {styleTwoProductListSecondLine.map((item, index) => {
                                        return (
                                            <div className="product-item">
                                                <div className="product-top">
                                                    <div className="product-rank">{item.rank}</div>
                                                    <div className="product-img">
                                                        <img src={item.img} />
                                                    </div>
                                                </div>
                                                <div className="product-bottom">
                                                    <div className="product-desc">{item.desc}</div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="rank-list-style-one" style={{ backgroundColor: set.bgColor }}>
                            <div className="rank-header">
                              <div className={"rank-header-bg"}>
                                <div className="rank-title">{set.title || "益生菌热卖榜"}</div>
                                <div className="rank-tag">
                                  <img width={18} height={18} src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/29827141142.png"} className="arrow-icon"/>
                                  <span className="fire-text">{set.introduce || "2万+妈妈正在逛"}</span>
                                  <img width={18} height={18} src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/23229822501.png"} className="arrow-icon"/>
                                </div>
                              </div>
                            </div>
                            <div className="rank-items">
                                {styleOneProductList.map((item, index) => {
                                    return (
                                        <div className="rank-item">
                                            <div className="rank-num">{item.rank}</div>
                                            <div className="rank-img">
                                                <img src={item.img} alt="商品1" />
                                            </div>
                                            <div className="rank-info">
                                                <div className="rank-desc">{item.desc}</div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    }
}

const API = {
    async getCategoryList(params) {
        return request({
            url: `/api/shopCategory/default/tree/v2`,
            method: "POST",
            data: params,
        });
    },
};

class RankListSet extends ModuleSet {

    constructor(props) {
        super(props);
        this.state.categories = []
        this.state.selectedFirstLevel=undefined
        this.state.selectedSecondLevel=undefined
    }


    handleChange = (key, value) => {
        this.state.item.set[key] = value;
        this.refreshSet();
    };

    componentDidMount() {
        API.getCategoryList({
        }).then(res => {
            this.setState({ categories: res || [] });
        });
    }

    // 处理一级分类变化
    handleFirstLevelChange = (value) => {
        this.setState({
            selectedFirstLevel: value,
            selectedSecondLevel: undefined
        });
        this.handleChange("shopCategoryId", value);
    };

    // 处理二级分类变化
    handleSecondLevelChange = (value) => {
        this.setState({ selectedSecondLevel: value });
        this.handleChange("shopCategoryId", value);
    };
    render() {
        var item = this.state.item;
        const { set } = item;
        const { categories, selectedFirstLevel } = this.state;


        return (
            <div className="notice-set">
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />

                <div className="form-group">
                  <label className="decoration-item-required">排行榜名称</label>
                    <input
                        type="text"
                        value={set.title}
                        onChange={e => {
                            this.handleChange("title", e.target.value);
                        }}
                        placeholder="请输入标题"
                    />
                </div>

                <div className="form-group">
                    <label className="decoration-item-required">排行榜介绍</label>
                    <textarea
                        value={set.introduce}
                        onChange={e => {
                            this.handleChange("introduce", e.target.value);
                        }}
                        placeholder="请输入内容"
                    />
                </div>
                <div className="form-group">
                   <label className="decoration-item-required">排行因素｜商品分类</label>
                    <div style={{ display: 'flex' }}>
                         <Cascader
                            options={categories}
                            changeOnSelect
                            allowClear={false}
                            placeholder="请选择分类"
                            value={ this.state.item.set.shopCategoryId}
                            fieldNames={{ label: 'name', value: 'id' }}
                            onChange={this.handleFirstLevelChange}
                            style={{ width: '150px' }}
                      />
                    </div>
                </div>
                <div className="form-group">
                  <label className="decoration-item-required">背景色</label>
                    <input
                        type="color"
                        value={set.bgColor || "#E6E5FE"}
                        onChange={e => {
                            this.handleChange("bgColor", e.target.value);
                        }}
                        placeholder="请选择背景色"
                    />
                </div>
                <div className="form-group">
                  <label className="decoration-item-required">布局选择</label>
                    <div style={{ display: "flex", gap: "10px" }}>
                      <div>
                        <label className="style-label">
                          <input
                            type="radio"
                            value="style-one"
                            name="style"
                            style={{ marginRight: "12px" }}
                            checked={set.style === "style-one"}
                            onChange={e => {
                              this.handleChange("style", e.target.value);
                            }}
                          />
                          样式1
                        </label>
                        <img  className={"rank-card-img"} src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/21938026819.png"}/>
                      </div>
                      <div>
                        <label className="style-label">
                            <input
                                type="radio"
                                value="style-two"
                                name="style"
                                style={{ marginRight: "12px" }}
                                checked={set.style === "style-two"}
                                onChange={e => {
                                    this.handleChange("style", e.target.value);
                                }}
                            />
                            样式2
                        </label>
                      <img className={"rank-card-img"} src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/21938025688.png"}/>
                      </div>
                    </div>
                </div>
            </div>
        );
    }
}

var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};

export default {
    RankList: RankList,
    RankListSet: RankListSet,
    getRankListDefaultSet: getDefaultSet,
};

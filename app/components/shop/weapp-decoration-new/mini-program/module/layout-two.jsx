
import commonSet from "./common-set";
import module from "./module";
import Pi<PERSON><PERSON> from "../../component/picasa";

var { LayoutModule, ModuleSet } = module;

var { ToolBar,LayoutToolBar, CommonSet, getDefaultCommonSet } = commonSet;
var defaultSet = {
    name: "layout-two",
    title: "两列布局",
    commonSet: getDefaultCommonSet(),
    modules:[[],[]],
    set: {
        leftPic: "",
        rightPic: "",
        leftUrl: "",
        rightUrl: "",
        gap: 0, // 两列之间的间距
    },
};

class LayoutTwo extends LayoutModule {
    loadLeft(e) {
        var item = this.state.item;
        item.set.leftWidth = e.currentTarget.width * 2;
        item.set.leftHeight = e.currentTarget.height * 2;
    }

    loadRight(e) {
        var item = this.state.item;
        item.set.rightWidth = e.currentTarget.width * 2;
        item.set.rightHeight = e.currentTarget.height * 2;
    }

    render() {
        var item = this.state.item;
        let activeModule=this.state.activeModule
        const parentIndex = this.state.index
       const activeLayoutModuleIndex = this.state.activeLayoutModuleIndex
       const activeLayoutModule = this.state.activeLayoutModule
       const methods= this.props.methods
        return (
            <div
                className={"layout-two module " + (this.state.index == this.state.activeModule)}
                style={{
                    marginTop: item.commonSet.marginTop / 2 + "px",
                    marginBottom: item.commonSet.marginBottom / 2 + "px",
                }}>
                <ToolBar index={this.state.index} methods={methods} isSubModule={false} />
                <div className="layout-two-container" style={{ gap: item.set.gap / 2 + "px" }}>
                    <div className={"layout-two-container-item"} style={{ background: item.set.bg, width: "50%" }}>
                      <LayoutToolBar index={this.state.index} layoutIndex={0} methods={methods} />
                      {(item.modules[0]||[]).map(function (item1, index) {
                        const renderModuleUtils =require( "../../utils").default
                        return renderModuleUtils.renderModule(item1,parentIndex, index,0,activeModule,methods,activeLayoutModule, activeLayoutModuleIndex,true);
                      })}
                    </div>
                    <div  className={"layout-two-container-item"} style={{ background: item.set.bg, width: "50%" }}>
                      <LayoutToolBar index={this.state.index} layoutIndex={1} methods={methods} />
                      {(item.modules[1]||[]).map(function (item1, index) {
                        const renderModuleUtils =require( "../../utils").default
                        return renderModuleUtils.renderModule(item1, parentIndex,index,1,activeModule,methods,activeLayoutModule, activeLayoutModuleIndex,true);
                      })}
                    </div>
                </div>
            </div>
        );
    }
}

class LayoutTwoSet extends ModuleSet {
    render() {
        var item = this.state.item;
        return (
            <div className="layout-two-set">
               <CommonSet data={item} editSet={this.props.editSet} isSubModule={this.props.isSubModule} />
               {/* <div className="line setBga">
                    <label>背景</label>
                    <input
                        type="text"
                        value={item.set.bg}
                        className="form-control"
                        onChange={e => {
                            item.set.bg = e.target.value;
                            this.refreshSet();
                        }}
                        placeholder="背景颜色"
                    />
                </div>

                <div className="line line-pic">
                    <label>左侧图片</label>
                    <Picasa
                        data={{ style: { width: "150px", height: "75px" }, src: item.set.leftPic }}
                        methods={{
                            updateSrc: src => {
                                item.set.leftPic = src;
                                this.refreshSet();
                            },
                        }}
                    />
                </div>
                <div className="line line-pic">
                    <label>右侧图片</label>
                    <Picasa
                        data={{ style: { width: "150px", height: "75px" }, src: item.set.rightPic }}
                        methods={{
                            updateSrc: src => {
                                item.set.rightPic = src;
                                this.refreshSet();
                            },
                        }}
                    />
                </div>*/}
            </div>
        );
    }
}
var getDefaultSet = function () {
    return JSON.parse(JSON.stringify(defaultSet));
};
export default {
    LayoutTwo: LayoutTwo,
    LayoutTwoSet: LayoutTwoSet,
    getLayoutTwoDefaultSet: getDefaultSet,
};

.guide {
  min-height: 30px!important; // 设置预览module的最小高度

  .list {
    background: #fff;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;

    // 平铺布局添加底部边距
    &.layout_tile {
      padding-bottom: 10px;
    }
    .node {
      box-sizing: border-box;
      padding: 10px 5px 0; // 上10px，左右5px，下0px
      display: flex;
      flex-direction: column;
      align-items: center; // 水平居中
      text-align: center; // 文本居中

      .icon {
        width: 100%; // 图片宽度占node的100%
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
        // 1:1比例时的高度等于宽度
        aspect-ratio: 1 / 1; // 使用CSS aspect-ratio属性保持1:1比例
      }

      // 3:4比例时的图标高度调整
      &[data-ratio="3:4"] .icon {
        aspect-ratio: 3 / 4; // 使用CSS aspect-ratio属性保持3:4比例
      }

      .title {
        line-height: 25px;
        height: 25px;
        font-size: 14px;
        overflow: hidden;
        margin-top: 5px;
        width: 100%; // 确保标题占满宽度
      }
    }
  }

  // 滚动容器样式
  .scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    position: relative;
    z-index: 1;
    width: 100%; // 确保容器有固定宽度

    // 隐藏滚动条
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .list.layout_scroll {
      display: flex;
      flex-wrap: nowrap;
      width: 100%; // 滚动布局也占满容器宽度
      padding-bottom: 10px; // 滚动布局也添加底部边距

      .node {
        flex-shrink: 0; // 防止节点被压缩
        // 滚动布局下也按每行数量平分宽度，继承.list_4和.list_5的宽度设置
        // 不再使用固定宽度，而是使用百分比宽度
      }
    }
  }

  // 根据每行数量平分宽度（平铺和滚动布局都适用）
  .list_3 .node {
    width: 33.333%; // 每行3个，每个占33.333%
  }
  .list_4 .node {
    width: 25%; // 每行4个，每个占25%
  }
  .list_5 .node {
    width: 20%; // 每行5个，每个占20%
  }
  // 圆型图标只在1:1比例时生效
  .type_2 .node:not([data-ratio="3:4"]) .icon {
    border-radius: 50%; // 使用百分比实现完美圆形
  }
  &-line {
  display: flex;
  margin-bottom: 20px;
    .label {
      width: 100px;
      text-align: right;
      padding-right: 10px;
      box-sizing: border-box;
    }
    .help-text {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
      max-width: 216px;
    }
  }
}

// 提高权重以覆盖 .panel-phone .module 的样式
.panel-phone .guide.module {
  min-height: 30px !important;
}

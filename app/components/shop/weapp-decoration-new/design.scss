.bd {

  .page-title {
    position: relative;
    padding-bottom: 0px;

    .btn {
      margin-left: 0;
      margin-right: 20px;
      line-height: 28px;
      padding-left: 25px;
      padding-right: 25px;

      span {
        font-size: 20px;
        margin-right: 5px;
      }

      position: relative;
    }

    .btn:first-child,
    .btn:last-child {
      padding-right: 25px;
    }

    .btn .delete {
      font-family: "iconfont";
      font-size: 12px;
      color: #999;
      display: inline-block;
      margin-left: 10px;
    }

    .btn .delete:hover {
      color: #000;
    }

    .btn-active {
      background-image: linear-gradient(135deg, #09f, #06f);
      color: #fff;
      border-color: #06f;

      .delete {
        color: #ccc;
      }

      .delete:hover {
        color: #fff;
      }
    }

    .btn-active:hover {
      background-image: linear-gradient(135deg, #0bf, #07f);
      color: #fff;
      border-color: #07f;
    }

    .tools {
      right: 0;
      text-align: center;
      position: absolute;

      .btn {
        margin: 0 10px;
      }

      top: 0px;
    }
  }

  .panel-bd {
    height:calc(100% - 50px);
    display: flex;
    width: 100%;
    flex-direction: row;
    -webkit-box-pack: justify;
    justify-content: space-between;
  }

  .panel-module {
    background: #fff;
    width: 300px;
    height: 100%;
    border-radius: 10px;
    overflow: scroll;
    padding-bottom: 24px;

    > .title {
      height: 50px;
      line-height: 50px;
      padding-left: 20px;
      font-size: 16px;
      border-bottom: solid 1px #eee;
      color: #333;
    }

    > .content {
      height: 100%;
      display: flex;
      flex-direction: column;

      .subTitle {
        margin-left: 15px !important;
        margin-top: 24px;
        font-weight: 700;
      }
    }

    .module {
      width: 80px;
      height: 80px;
      border: solid 1px #ccc;
      background: #eee;
      float: left;
      box-sizing: border-box;
      padding-top: 10px;
      margin: 15px 0 0 15px;
      cursor: pointer;

      &:first-child {
        .icon {
          font-size: 35px;
        }
      }

      .name {
        font-size: 14px;
        text-align: center;
        margin-top: 5px;
      }

      .icon {
        font-family: "iconfont";
        font-size: 22px;
        text-align: center;
        line-height: 36px;
      }
      .icon-feebas-new {
        display: block;
        font-size: 22px;
        text-align: center;
        line-height: 36px;
      }
    }
  }

  .panel-phone-wrapper {
    flex: 1 1 0%;
    overflow-y: auto;
    background: #f2f2f2;
    height: calc(100vh - 200px);
  }

  .iQsirn {
    margin: 20px auto;
    width: 375px;
    min-height: calc(-130px + 100vh);
    background: #f2f2f2;

    &.unset-height {
      min-height: unset;
      box-shadow: unset;
    }
  }

  .panel-phone {
    width: 375px;
    border-radius: 10px;
    background: #fff;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .panel-module-set {
    width: 360px;
    min-height: 600px;
    border-radius: 10px;
    overflow: scroll;
    background: #fff;
    box-sizing: border-box;
    padding-bottom: 20px;
    height: calc(100vh - 200px);

    .editor {
      > .title {
        height: 50px;
        line-height: 50px;
        padding-left: 20px;
        font-size: 16px;
        border-bottom: solid 1px #eee;
        color: #333;
      }
    }
  }

  .decoration-item-required:before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
}


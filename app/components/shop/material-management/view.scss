// 素材管理页面样式
.material-management {
  height: 100%;
 

  // 自定义菜单项样式
  .custom-menu-item {
    width: 100%;
  }


 
  .ant-layout-content {
    .ant-card {
      transition: all 0.3s ease;
      border-radius: 8px;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .ant-card-cover {
        border-radius: 8px 8px 0 0;
        overflow: hidden;

        img {
          transition: transform 0.3s ease;
        }

        &:hover img {
          transform: scale(1.05);
        }
      }

      .ant-card-actions {
        background-color: #fafafa;
        border-top: 1px solid #f0f0f0;

        .ant-btn-link {
          &.ant-btn-dangerous {
            color: #ff4d4f;

            &:hover {
              color: #ff7875;
            }
          }
        }
      }
    }
  }

  // 上传区域样式
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    transition: border-color 0.3s ease;

    &:hover {
      border-color: #1890ff;
    }

    .ant-upload-drag-icon {
      margin-bottom: 16px;
    }

    .ant-upload-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
    }

    .ant-upload-hint {
      color: #999;
      font-size: 14px;
    }
  }

  // 搜索框样式
  .ant-input-search {
    .ant-input {
      border-radius: 6px;
    }

    .ant-btn {
      border-radius: 0 6px 6px 0;
    }
  }

 

  // 响应式设计
  @media (max-width: 768px) {
    .ant-layout-sider {
      width: 200px !important;
      min-width: 200px !important;
    }

    .ant-layout-content {
      padding: 16px !important;
    }

    .ant-card {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 576px) {
    .ant-layout-sider {
      width: 100% !important;
      min-width: 100% !important;
      position: absolute;
      z-index: 1000;
      height: 100vh;
    }

    .search-upload-area {
      flex-direction: column;
      gap: 16px;

      .ant-input-search {
        width: 100% !important;
      }
    }
  }
}
.material-management-upload-modal{
  .ant-upload.ant-upload-drag{
    height: 481px
  }
}
 

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .ant-spin {
    .ant-spin-text {
      margin-top: 12px;
      color: #666;
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;

  .empty-icon {
    font-size: 64px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    color: #999;
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 14px;
    color: #ccc;
  }
}

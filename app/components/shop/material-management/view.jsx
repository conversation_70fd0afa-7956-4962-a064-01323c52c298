import request from "../../utils/plugins/axios/request";
import CustomMenuItem from "./components/CustomMenuItem";
import BatchMoveModal from "./components/BatchMoveModal";
import CreateFolderModal from "./components/CreateFolderModal";
import RenameFolderModal from "./components/RenameFolderModal";
import UploadManager from "./components/UploadManager";

const { useEffect } = React;

const { useState, useRef } = React;
const { Layout, Menu, Button, message, Modal, Space, Image, Tooltip } = antd;
const { FolderOutlined, PictureOutlined, AppstoreOutlined } = icons;


// 导入组件
const { Sider } = Layout;
const { SearchList, Spa, SpaConfigProvider } = dtComponents;



// 主组件
function MaterialManagement() {
  const [selectedMenuKey, setSelectedMenuKey] = useState();
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const uploadManagerRef = useRef(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [openKeys, setOpenKeys] = useState([]);
  const [batchMoveModalVisible, setBatchMoveModalVisible] = useState(false);
  const [createFolderModalVisible, setCreateFolderModalVisible] =
    useState(false);
  const [renameFolderModalVisible, setRenameFolderModalVisible] =
    useState(false);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [currentMoveOperation, setCurrentMoveOperation] = useState(null);
  const searchListRef = useRef();
  const [menuTree, setMenuTree] = useState([]);
  const currentIdRef = useRef(null);
  useEffect(() => {
    getMenuTree();
  }, []);

  const handleMenuSelect = ({ item, key }) => {
    setSelectedMenuKey(key);
  };

  // 处理上传按钮点击
  const handleUploadClick = () => {
    if(!selectedMenuKey){
      message.error("请先创建或选中目录再上传图片");
      return;
    }
    // 检查是否有正在进行的上传任务
    if (uploadManagerRef.current && uploadManagerRef.current.hasAnyUploads && uploadManagerRef.current.hasAnyUploads()) {
      Modal.confirm({
        title: '有未完成的上传任务',
        content: '您已有上传任务，是否结束原上传任务，再次上传？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
         uploadManagerRef.current.stopAllUploads();
        }
      });
      return;
    }
    setUploadModalVisible(true);
  };

  // 处理选中新创建的文件夹
  const handleSelectNewFolder = (folder) => {
    getMenuTree()
    // 选中新创建的文件夹
    setSelectedMenuKey(folder.id);
    setCurrentFolder(folder)
  };

  // 展示图片函数
  const materialNameFn = (row) => {
    const { materialName, materialUrl } = row;
    return (
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Image
          width={48}
          height={48}
          preview={{
            src: materialUrl,
          }}
          src={`${materialUrl}?x-oss-process=image/resize,l_90`}
          style={{ objectFit: "cover", borderRadius: "4px" }}
        />
        <Tooltip title={materialName} placement="topLeft">
          <div
            style={{
              flex: 1,
              lineHeight: "20px",
              margin: "0 12px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {materialName}
          </div>
        </Tooltip>
      </div>
    );
  };

  // 格式化更新时间
  const updatedTimeFn = (row) => {
    if (!row.updatedTime) return "-";
    return moment(row.updatedTime).format("YYYY-MM-DD HH:mm:ss");
  };

  // 格式化创建时间
  const createdTimeFn = (row) => {
    if (!row.createdTime) return "-";
    return moment(row.createdTime).format("YYYY-MM-DD HH:mm:ss");
  };

  // 格式化文件大小
  const fileSizeFn = (row) => {
    const fileSize = row.fileSize;
    if (!fileSize || fileSize === 0) return "-";

    // 如果已经是格式化的字符串（如 "2.5MB"），直接返回
    if (typeof fileSize === 'string' && /^[\d.]+\s*(B|KB|MB|GB)$/i.test(fileSize)) {
      return fileSize;
    }

    // 如果是数字，按字节处理
    const bytes = typeof fileSize === 'string' ? parseFloat(fileSize) : fileSize;
    if (isNaN(bytes)) return "-";

    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';

    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = (bytes / Math.pow(1024, i)).toFixed(2);

    return `${parseFloat(size)} ${sizes[i]}`;
  };

  const getConfig = async function () {
    const data = await axios.get(
      "https://maria.yang800.com/api/data/v2/1013/856"
    );
    return data.data.data;
  };

  // 批量操作通用处理函数
  const handleBatch = ({ title, content, requestFunction, type, id }) => {
    let ids = [];
    if (type === "batch") {
      if (selectedItems.length === 0) {
        return message.error("请勾选数据");
      }
      ids = selectedItems;
    } else {
      ids = [id];
    }

    Modal.confirm({
      title: title,
      content: content,
      onOk: () => {
        return requestFunction(ids);
      },
    });
  };

  // 批量删除
  const batchDelete = ({ id, type }) => {
    const isBatch = type === "batch";
    handleBatch({
      title: isBatch ? "批量删除" : "删除",
      content: "确认删除文件？\n删除素材不会影响已使用场景中的展示",
      type: type,
      id: id,
      requestFunction: (ids) => {
        // 调用删除图片列表API
        return new Promise((resolve, reject) => {
          request({
            url: "/mall-admin/api/material_group/deleteMaterial",
            method: "POST",
            data: {
              idList: ids,
            },
            success: (res) => {
              console.log("删除图片成功:", res);
              message.success("删除成功");
              searchListRef.current.load();
              setSelectedItems([]);
              resolve(res);
            },
            fail: (error) => {
              console.error("删除图片失败:", error);
              message.error("删除失败");
              reject(error);
            },
          });
        });
      },
    });
  };

  // 批量移动
  const batchMove = ({ id, type }) => {
    const isBatch = type === "batch";
    if (isBatch && selectedItems.length === 0) {
      return message.error("请勾选数据");
    }
    // 存储当前操作的类型和ID，用于移动确认时使用
    setCurrentMoveOperation({ type, id, isBatch });
    setBatchMoveModalVisible(true);
  };

  // 刷新数据的通用方法
  const handleRefresh = () => {
    // 刷新列表数据
    if (searchListRef.current) {
      searchListRef.current.load();
    }
    // 清空选中项
    setSelectedItems([]);
  };

  const getMenuTree = () => {
    request({
      url: "/mall-admin/api/material_group/listMaterialGroupTree",
      method: "POST",
      data: {},
      success: (data) => {
        setMenuTree(data.children);
        if (Array.isArray(data.children) && data.children.length > 0) {
          const childrenElement = data.children[0];
          if (
            Array.isArray(childrenElement.children) &&
            childrenElement.children.length > 0
          ) {
            setOpenKeys([`${childrenElement.type}`]);
            const id = childrenElement.children[0].id;
            setSelectedMenuKey(id);
            currentIdRef.current = id;
            setCurrentFolder(childrenElement.children[0]);
          }
        }
      },
    });
  };
  useEffect(() => {
    if (selectedMenuKey) {
      setTimeout(()=>{
        searchListRef.current.changeImmutable({ groupId: selectedMenuKey });
      },100)
    }
  }, [selectedMenuKey]);

  return (
    <Layout
      className="material-management"
      style={{ height: "100%",  background: "transparent" }}
    >
      {/* 左侧菜单 */}
      <Sider width={250} style={{overflow: "auto"}}>
        <Menu
          mode="inline"
          selectedKeys={[`${selectedMenuKey}`]}
          onOpenChange={(keys) => {
            setOpenKeys(keys);
          }}
          openKeys={openKeys}
          style={{ height: "100%", borderRight: 0 }}
          onSelect={handleMenuSelect}
          items={menuTree.map((menuItem) => ({
            key: menuItem.type,
            icon: <AppstoreOutlined />,
            label: (
              <CustomMenuItem
                item={menuItem}
                isSelected={selectedMenuKey === menuItem.key}
                onCreateFolder={(parentKey) => {
                  setCurrentFolder(parentKey);
                  setCreateFolderModalVisible(true);
                }}
                onRename={(item) => {
                  setCurrentFolder(item);
                  setRenameFolderModalVisible(true);
                }}

              />
            ),
            children: (menuItem.children || []).map((item) => ({
              key: item.id,
              label: (
                <CustomMenuItem
                  item={item}
                  isSelected={selectedMenuKey === item.id}
                  onCreateFolder={(parentKey) => {
                    setCurrentFolder(parentKey);
                    setCreateFolderModalVisible(true);
                  }}
                  onRename={(item) => {
                    setCurrentFolder(item);
                    setRenameFolderModalVisible(true);
                  }}
                  onSelect={(item)=>setCurrentFolder(item)}
                  onRefresh={handleRefresh}
                  onDelete={getMenuTree}
                />
              ),
            })),
          }))}
        />
      </Sider>

      {/* 右侧内容区域 */}
      <Layout style={{ marginLeft: "24px", background: "#fff" }}>
        <SearchList
          ref={searchListRef}
          scrollMode={"tableScroll"}
          paginationConfig={{ size: "default", showPosition: "bottom" }}
          onTableSelected={(ids, rows) => {
            setSelectedItems([...ids]);
          }}
          searchConditionConfig={{
            size: "middle",
          }}
          preventLoadProcess={() => {
            if (!currentIdRef.current) {
              return true;
            }
            return false;
          }}
          getConfig={getConfig}
          renderLeftOperation={() => {
            return (
              <Space>
                <Button onClick={() => batchDelete({ type: "batch" })}>
                  批量删除
                </Button>
                <Button onClick={() => batchMove({ type: "batch" })}>
                  批量移动
                </Button>
              </Space>
            );
          }}
          renderRightOperation={() => {
            return (
              <Button
                type="primary"
                onClick={handleUploadClick}
              >
                上传图片
              </Button>
            );
          }}
          tableCustomFun={{
            materialNameFn: materialNameFn,
            updatedTimeFn: updatedTimeFn,
            createdTimeFn: createdTimeFn,
            fileSizeFn: fileSizeFn,
            operateFn: (row) => {
              return (
                <Space>
                   <Button
                    type="link"
                    size="small"
                    onClick={() => batchMove({ id: row.id, type: "single" })}
                  >
                    移动至
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => batchDelete({ id: row.id, type: "single" })}
                  >
                    删除
                  </Button>

                </Space>
              );
            },
          }}
        />
      </Layout>

      {/* 上传管理器 */}
      <UploadManager
        ref={uploadManagerRef}
        uploadModalVisible={uploadModalVisible}
        setUploadModalVisible={setUploadModalVisible}
        selectedCategory={selectedMenuKey}
        currentFolder={currentFolder}
        onRefresh={handleRefresh}
      />

      {/* 批量移动弹框 */}
      <BatchMoveModal
        visible={batchMoveModalVisible}
        onCancel={() => setBatchMoveModalVisible(false)}
        selectedCount={selectedItems.length}
        selectedItems={selectedItems}
        isBatch={
          (currentMoveOperation && currentMoveOperation.isBatch) || false
        }
        currentMoveOperation={currentMoveOperation}
        currentFolderId={selectedMenuKey} // 传递当前目录ID
        onRefresh={handleRefresh}
      />

      {/* 新建文件夹弹框 */}
      <CreateFolderModal
        visible={createFolderModalVisible}
        onCancel={() => setCreateFolderModalVisible(false)}
        parentFolder={currentFolder}
        onRefresh={handleRefresh}
        onSelectNewFolder={handleSelectNewFolder}
      />

      {/* 重命名文件夹弹框 */}
      <RenameFolderModal
        visible={renameFolderModalVisible}
        onCancel={() => setRenameFolderModalVisible(false)}
        currentFolder={currentFolder}
        onRefresh={()=>{
          getMenuTree();
          handleRefresh();
        }}
      />
    </Layout>
  );
}

// 主组件
ReactDOM.render(
  <SpaConfigProvider
    spa={
      new Spa({
        _openPage: () => {},
        request: {
          request: (params) => {
            const obj = Object.assign(params, {
              method: "post",
            });
            request(obj);
          },
        },
      })
    }
  >
    <MaterialManagement />
  </SpaConfigProvider>,
  document.getElementById("material-management-container")
);

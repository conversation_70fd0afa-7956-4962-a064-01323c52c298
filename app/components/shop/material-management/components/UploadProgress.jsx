import request from "../../../utils/plugins/axios/request";
import { uploadToOSS, getImageResolution } from './uploadUtils';

const { useState } = React;
const { Modal, Button, Table, Progress, Spin, message, Tooltip, Image } = antd;

// 上传进度组件
function UploadProgress({
  visible,
  onClose,
  uploadList,
  onRefresh,
  onUpdateProgress,
  onUpdateStatus,
  onSetImageUrl,
                          selectedCategory
}) {
  const [collapsed, setCollapsed] = useState(false);

  const uploadingCount = uploadList.filter(item => item.status === 'uploading').length;
  const pendingCount = uploadList.filter(item => item.status === 'pending').length;
  // 使用第一个上传项的total字段作为总数，如果没有则使用列表长度
  const totalCount = uploadList.length > 0 && uploadList[0].total ? uploadList[0].total : uploadList.length;
  const completedCount = uploadList.filter(item => item.status === 'success').length;
  const failedCount = uploadList.filter(item => ['upload_error', 'save_error', 'name_error'].includes(item.status)).length;

  // 检查是否所有任务都已完成（成功或失败）
  const allCompleted = uploadingCount === 0 && pendingCount === 0;

  // 提交图片信息到后端
  const submitImageToBackend = async (imageData, uploadId) => {
    try {
      // 这里应该调用实际的后端API
      const materialList=[
        {
          materialName: imageData.name,
          materialType:1,
          materialUrl: imageData.url,
          size: imageData.resolution,
          fileSize: imageData.size,
        }
      ]
      request({
        url: '/mall-admin/api/material_group/addMaterial',
        data: {materialList,groupId: selectedCategory },
        success: (res) => {

          // 更新上传状态为成功
          if (onUpdateStatus) {
            onUpdateStatus(uploadId, 'success', null);
          }

          // 刷新列表数据
          if (onRefresh) {
            onRefresh();
          }
        },
        fail: (error) => {
          if (onUpdateStatus) {
            onUpdateStatus(uploadId, 'save_error', 'backend_save');
          }
        }
      })
    } catch (error) {
      console.error('保存到后端失败:', error);
      if (onUpdateStatus) {
        onUpdateStatus(uploadId, 'save_error', 'backend_save');
      }
    }
  };



  // 重试上传
  const handleRetry = (uploadItem) => {
    if (!uploadItem.file) {
      message.error('无法重试，文件信息丢失');
      return;
    }

    // 文件名错误不能重试
    if (uploadItem.failureType === 'name_too_long') {
      message.error('文件名超过50字符，无法重试');
      return;
    }

    // 重置状态
    if (onUpdateStatus) {
      onUpdateStatus(uploadItem.id, 'uploading', null);
    }
    if (onUpdateProgress) {
      onUpdateProgress(uploadItem.id, 0);
    }

    // 根据失败类型决定重试策略
    if (uploadItem.failureType === 'backend_save') {
      // 如果是后端保存失败，直接重试保存
      retryBackendSave(uploadItem);
    } else {
      // OSS上传失败，重新上传到OSS
      retryOSSUpload(uploadItem);
    }
  };

  // 重试后端保存
  const retryBackendSave = async (uploadItem) => {
    if (uploadItem.imageUrl) {
      // 如果有图片URL，获取分辨率后直接保存
      const resolution = await getImageResolution(uploadItem.imageUrl);
      const imageData = {
        name: uploadItem.file.name,
        url: uploadItem.imageUrl,
        resolution: resolution,
        size: uploadItem.file.size,
        category: uploadItem.category
      };
      await submitImageToBackend(imageData, uploadItem.id);
    } else {
      // 如果没有图片URL，重新上传到OSS
      retryOSSUpload(uploadItem);
    }
  };

  // 重试OSS上传
  const retryOSSUpload = (uploadItem) => {
    uploadToOSS(uploadItem.file, ({ percent }) => {
      if (onUpdateProgress) {
        onUpdateProgress(uploadItem.id, percent);
      }
    }, null) // 重试时不需要注册取消函数
    .then(async ({ imageUrl }) => {
      // 保存图片URL
      if (onSetImageUrl) {
        onSetImageUrl(uploadItem.id, imageUrl);
      }

      // 获取分辨率并保存到后端
      const resolution = await getImageResolution(imageUrl);
      const imageData = {
        name: uploadItem.file.name,
        url: imageUrl,
        resolution: resolution,
        size: uploadItem.file.size,
        category: uploadItem.category
      };
      await submitImageToBackend(imageData, uploadItem.id);
    })
    .catch((error) => {
      console.error('重试上传到OSS失败:', error);
      if (onUpdateStatus) {
        onUpdateStatus(uploadItem.id, 'upload_error', 'oss_upload');
      }
      message.error('重试OSS上传失败');
    });
  };

  const floatingButtonStyle = {
    position: 'fixed',
    right: '20px',
    bottom: '20px',
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    backgroundColor: '#1890ff',
    color: 'white',
    border: 'none',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    fontSize: '20px',
    zIndex: 1000
  };

  if (!visible || uploadList.length === 0) {
    return null;
  }

  if (collapsed) {
    return (
      <div style={floatingButtonStyle} onClick={() => setCollapsed(false)}>
        {uploadingCount > 0 ? <Spin size="small" /> : '📁'}
        {uploadingCount > 0 && (
          <div style={{
            position: 'absolute',
            top: '-5px',
            right: '-5px',
            backgroundColor: '#ff4d4f',
            color: 'white',
            borderRadius: '50%',
            padding: '2px 6px',
            fontSize: '12px',
            minWidth: '20px',
            textAlign: 'center'
          }}>
            {uploadingCount}
          </div>
        )}
      </div>
    );
  }

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {uploadingCount > 0 && <Spin size="small" style={{ marginRight: 8 }} />}
            <span>
              {allCompleted
                ? `上传完成 ${completedCount}张成功，${failedCount}张失败`
                : `正在上传图片(${completedCount + failedCount}/${totalCount})`
              }
            </span>
          </div>
          <div>
            <Button type="text" onClick={() => setCollapsed(true)} style={{ marginRight: 8 }}>
              ➖
            </Button>
            <Button type="text" onClick={() => {
              onClose();
            }}>
              ✕
            </Button>
          </div>
        </div>
      }
      open={visible}
      footer={null}
      closable={false}
      width={500}
      style={{ position: 'fixed', right: 20, bottom: 20, top: 'auto', left: 'auto', margin: 0 }}
      bodyStyle={{ maxHeight: '400px', overflowY: 'auto' }}
    >
      <Table
        dataSource={uploadList}
        pagination={false}
        size="small"
        rowKey="id"
        columns={[
          {
            title: '文件名',
            dataIndex: 'name',
            key: 'name',
            width: '35%',
            render: (name, record) => {
              // 生成图片预览URL
              const imageUrl = record.file ? URL.createObjectURL(record.file) : null;

              return (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  {imageUrl && (
                    <Image
                      width={32}
                      height={32}
                      src={imageUrl}
                      style={{ objectFit: 'cover', borderRadius: '4px' }}
                      preview={false}
                    />
                  )}
                  <Tooltip title={name} placement="topLeft">
                    <span style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '200px',
                      display: 'inline-block'
                    }}>
                      {name}
                    </span>
                  </Tooltip>
                </div>
              );
            }
          },
          {
            title: '大小',
            dataIndex: 'size',
            key: 'size',
            width: '20%',
            render: (size) => size || '-'
          },
          {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: '45%',
            render: (status, record) => {
              if (status === 'pending') {
                return <span style={{ color: '#666' }}>等待上传</span>;
              } else if (status === 'uploading') {
                return (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Progress percent={record.progress} size="small" style={{ flex: 1, marginRight: 8 }} />
                  </div>
                );
              } else if (status === 'success') {
                return <span style={{ color: '#52c41a' }}>上传成功</span>;
              } else if (status === 'name_error') {
                return <span style={{ color: '#ff4d4f' }}>文件名超过50字符</span>;
              } else if (status === 'upload_error') {
                return (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span style={{ color: '#ff4d4f' }}>上传失败</span>
                    <Button type="link" size="small" onClick={() => handleRetry(record)} style={{ padding: 0 }}>
                      重试
                    </Button>
                  </div>
                );
              } else if (status === 'save_error') {
                return (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span style={{ color: '#ff4d4f' }}>保存失败</span>
                    <Button type="link" size="small" onClick={() => handleRetry(record)} style={{ padding: 0 }}>
                      重试
                    </Button>
                  </div>
                );
              }
              return status;
            }
          }
        ]}
      />
    </Modal>
  );
}

export default UploadProgress;

import request from "../../../utils/plugins/axios/request";

const { useState } = React;
const { Button, Dropdown, Modal, message, Tooltip } = antd;
const { FolderOutlined, PlusOutlined } = icons;
// 自定义菜单项组件
function CustomMenuItem({ item, isSelected,onSelect, onCreateFolder, onRename, onDelete }) {
  const [isHovered, setIsHovered] = useState(false);

  // 内部处理删除文件夹
  const handleDeleteFolder = (folderKey) => {
    Modal.confirm({
      title: "删除文件夹",
      content: "确认删除文件夹？\n 删除素材不会影响已使用场景中的展示",
      onOk: async () => {
        try {
          // 调用删除目录分组API
          await new Promise((resolve, reject) => {
            request({
              url: '/mall-admin/api/material_group/deleteMaterialGroup',
              method: 'POST',
              data: {
                groupId: folderKey
              },
              success: (res) => {
                console.log('删除文件夹成功:', res);
                resolve(res);
              },
              fail: (error) => {
                console.error('删除文件夹失败:', error);
                reject(error);
              }
            });
          });

          message.success("删除成功");


          if (onDelete) {
            onDelete();
          }
        } catch (error) {
          message.error("删除失败");
          console.error('删除失败:', error);
        }
      }
    });
  };

  // 创建下拉菜单项
  const dropdownItems = [
    {
      key: 'rename',
      label: '重命名',
      onClick: () => onRename(item)
    },
    {
      key: 'delete',
      label: '删除',
      onClick: () => handleDeleteFolder(item.id),
      danger: true
    }
  ];

  return (
    <div
      className={`custom-menu-item ${isSelected ? 'selected' : ''}`}
      style={{
        position: 'relative',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => onSelect(item)}
    >
      <Tooltip title={item.groupName} placement="topLeft">
        <span style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          maxWidth: '150px',
          display: 'inline-block'
        }}>
          {item.groupName}
        </span>
      </Tooltip>
      {/* 操作按钮 */}

        <div className="menu-actions" style={{ display: 'flex', gap: '4px' }}>
          {/* 创建子文件夹按钮 */}
          {item.level===1 && (
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              style={{
                minWidth: '24px',
                height: '24px',
                padding: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              onClick={(e) => {
                e.stopPropagation();
                onCreateFolder(item.id);
              }}
              title="创建子文件夹"
            />
          )}

          {/* 更多操作按钮 */}
          {isHovered && item.canEdit && (
            <Dropdown
              menu={{ items: dropdownItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                size="small"
                icon="⋯"
                style={{
                  minWidth: '24px',
                  height: '24px',
                  padding: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onClick={(e) => e.stopPropagation()}
                title="更多操作"
              />
            </Dropdown>
          )}
        </div>
    </div>
  );
}

export default CustomMenuItem;

import request from "../../../utils/plugins/axios/request";

const { Modal, Form, message } = antd;
const { DTInput } = dtComponents;

// 新建文件夹弹框组件
function CreateFolderModal({ visible, onCancel, onSelectNewFolder }) {
  const [form] = Form.useForm();

  // 内部处理创建文件夹
  const handleCreateFolder = async (values) => {
    try {
      // 调用新增目录API
      const result = await new Promise((resolve, reject) => {
        request({
          url: '/mall-admin/api/material_group/addMaterialGroup',
          method: 'POST',
          data: {
            type:"DECORATION",
            groupName: values.folderName
          },
          success: (res) => {
            console.log('创建文件夹成功:', res);
            resolve(res);
          },
          fail: (error) => {
            console.error('创建文件夹失败:', error);
            reject(error);
          }
        });
      });

      message.success("创建文件夹成功");
      form.resetFields();
      onCancel(); // 关闭弹框
      // 选中新创建的文件夹
      if (onSelectNewFolder && result) {
        onSelectNewFolder(result);
      }
    } catch (error) {
      console.error('创建文件夹失败:', error);
    }
  };

  const handleOk = () => {
    form.validateFields()
      .then(values => {
        handleCreateFolder(values);
      })
      .catch(info => {
        console.log('表单验证失败:', info);
      });
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="新建文件夹"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
    >
      <Form
        form={form}
        name="createFolderForm"
      >
        <Form.Item
          label="文件夹名称"
          name="folderName"
          rules={[
            { required: true, message: '请输入文件夹名称' },
            { whitespace: true, message: '文件夹名称不能为空' },
            { max: 50, message: '文件夹名称不能超过50个字符' }
          ]}
        >
          <DTInput
            trimMode="all"
            placeholder="请输入文件夹名称"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default CreateFolderModal;

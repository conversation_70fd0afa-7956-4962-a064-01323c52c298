const  { useEffect, useRef }= React;


const REQUIRED_IMAGE_STYLES = {opacity: 0, maxWidth: '100%'};

// List of props that should be filtered out from image element
const cropperProps = [
    'dragMode', 'enable', 'scaleX', 'scaleY', 'zoomTo', 'rotateTo',
    'ready', 'onInitialized', 'checkCrossOrigin', 'checkOrientation',
    'modal', 'guides', 'center', 'highlight', 'background', 'autoCrop',
    'autoCropArea', 'movable', 'rotatable', 'scalable', 'zoomable',
    'zoomOnTouch', 'zoomOnWheel', 'wheelZoomRatio', 'cropBoxMovable',
    'cropBoxResizable', 'toggleDragModeOnDblclick', 'minCanvasWidth',
    'minCanvasHeight', 'minCropBoxWidth', 'minCropBoxHeight', 'minContainerWidth',
    'minContainerHeight', 'build', 'built', 'cropstart', 'cropmove',
    'cropend', 'crop', 'zoom'
];

const cleanImageProps = (imageProps) =>
    cropperProps.reduce((acc, key) => {
        const {[key]: _, ...rest} = acc;
        return rest;
    }, imageProps);

const applyDefaultOptions = (cropper, options = {}) => {
    const {enable = true, scaleX = 1, scaleY = 1, zoomTo = 0, rotateTo} = options;
    enable ? cropper.enable() : cropper.disable();
    cropper.scaleX(scaleX);
    cropper.scaleY(scaleY);
    rotateTo !== undefined && cropper.rotateTo(rotateTo);
    zoomTo > 0 && cropper.zoomTo(zoomTo);
};

/**
 * sourced from: https://itnext.io/reusing-the-ref-from-forwardref-with-react-hooks-4ce9df693dd
 */
const useCombinedRefs = (...refs) => {
    const targetRef = useRef(null);

    React.useEffect(() => {
        refs.forEach((ref) => {
            if (!ref) return;

            if (typeof ref === 'function') {
                ref(targetRef.current);
            } else {
                ref.current = targetRef.current;
            }
        });
    }, [refs]);

    return targetRef;
};

const ReactCropper = React.forwardRef(({...props}, ref) => {
    const {
        dragMode = 'crop',
        src,
        style,
        className,
        crossOrigin,
        scaleX,
        scaleY,
        enable,
        zoomTo,
        rotateTo,
        alt = 'picture',
        ready,
        onInitialized,
        ...rest
    } = props;
    const defaultOptions = {scaleY, scaleX, enable, zoomTo, rotateTo};
    const innerRef = useRef(null);
    const combinedRef = useCombinedRefs(ref, innerRef);

    /**
     * Invoke zoomTo method when cropper is set and zoomTo prop changes
     */
    useEffect(() => {
        if (combinedRef.current && combinedRef.current.cropper && typeof zoomTo === 'number') {
            combinedRef.current.cropper.zoomTo(zoomTo);
        }
    }, [props.zoomTo]);

    /**
     * re-render when src changes
     */
    useEffect(() => {
        if (combinedRef.current && combinedRef.current.cropper && typeof src !== 'undefined') {
            combinedRef.current.cropper.reset().clear().replace(src);
        }
    }, [src]);

    useEffect(() => {
        if (combinedRef.current !== null) {
            const cropper = new Cropper(combinedRef.current, {
                dragMode,
                ...rest,
                ready: (e) => {
                    if (e.currentTarget !== null) {
                        applyDefaultOptions(e.currentTarget.cropper, defaultOptions);
                    }
                    ready && ready(e);
                },
            });
            onInitialized && onInitialized(cropper);
        }

        /**
         * destroy cropper on un-mount
         */
        return () => {
            if (combinedRef.current && combinedRef.current.cropper) {
                combinedRef.current.cropper.destroy();
            }
        };
    }, [combinedRef]);

    const imageProps = cleanImageProps({...rest, crossOrigin, src, alt});

    return (
        <div style={style} className={className}>
            <img {...imageProps} style={REQUIRED_IMAGE_STYLES} ref={combinedRef} />
        </div>
    );
});

export {ReactCropper, applyDefaultOptions};

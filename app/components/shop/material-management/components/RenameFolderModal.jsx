import request from "../../../utils/plugins/axios/request";

const { Modal, Form, message } = antd;
const { DTInput } = dtComponents;

// 重命名文件夹弹框组件
function RenameFolderModal({ visible, onCancel, currentFolder, onRefresh }) {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (visible && currentFolder) {
      form.setFieldsValue({
        folderName: currentFolder.groupName,
      });
    }
  }, [visible, currentFolder, form]);

  // 内部处理重命名文件夹
  const handleRenameFolder = async (values) => {
    try {
      // 调用重命名API
      await new Promise((resolve, reject) => {
        request({
          url: "/mall-admin/api/material_group/renameMaterialGroup",
          method: "POST",
          data: {
            groupId: currentFolder.id,
            name: values.folderName,
          },
          success: (res) => {
            console.log("重命名成功:", res);
            resolve(res);
          },
          fail: (error) => {
            console.error("重命名失败:", error);
            reject(error);
          },
        });
      });

      message.success("重命名成功");
      form.resetFields();
      onCancel(); // 关闭弹框

      // 刷新菜单数据
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error("重命名失败:", error);
    }
  };

  const handleOk = () => {
    form
      .validateFields()
      .then((values) => {
        handleRenameFolder(values);
      })
      .catch((info) => {
        console.log("表单验证失败:", info);
      });
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="重命名文件夹"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
    >
      <Form form={form} name="renameFolderForm">
        <Form.Item
          label="文件夹名称"
          name="folderName"
          rules={[
            { required: true, message: "请输入文件夹名称" },
            { whitespace: true, message: "文件夹名称不能为空" },
            { max: 50, message: "文件夹名称不能超过50个字符" },
          ]}
        >
          <DTInput trimMode="all" placeholder="请输入文件夹名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default RenameFolderModal;

const { useState, Fragment, forwardRef, useImperativeHandle } = React;
const { Modal } = antd;
import UploadModal from './UploadModal';
import UploadProgress from './UploadProgress';

// 上传管理器组件 - 统一管理上传相关的状态和逻辑
const UploadManager = forwardRef(({
  uploadModalVisible,
  setUploadModalVisible,
  selectedCategory,
  currentFolder,
  onRefresh
}, ref) => {
  const [uploadProgressVisible, setUploadProgressVisible] = useState(false);
  const [uploadList, setUploadList] = useState([]);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [pendingUploadAction, setPendingUploadAction] = useState(null);
  const [activeRequests, setActiveRequests] = useState(new Map()); // 存储活跃的上传请求


  // 检查是否有正在进行的上传任务
  const hasActiveUploads = () => {
    return uploadList.some(item => ['uploading', 'pending'].includes(item.status));
  };

  // 检查是否有未完成的上传任务（排除已成功的）
  const hasAnyUploads = () => {
    return uploadList.some(item => item.status !== 'success');
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    hasAnyUploads,
    hasActiveUploads,
    stopAllUploads
  }));

  // 处理上传开始
  const handleUploadStart = (uploadItem) => {
    setUploadList(prev => prev.concat([uploadItem]));
    setUploadProgressVisible(true);
  };

  // 更新上传进度
  const updateUploadProgress = (uploadId, progress) => {
    setUploadList(prev => prev.map(item =>
      item.id === uploadId ? Object.assign({}, item, { progress: progress }) : item
    ));
  };

  // 更新上传状态
  const updateUploadStatus = (uploadId, status, failureType) => {
    const actualFailureType = failureType !== undefined ? failureType : null;
    setUploadList(prev => prev.map(item =>
      item.id === uploadId ? Object.assign({}, item, { status: status, failureType: actualFailureType }) : item
    ));
  };
  // 设置上传项的图片URL
  const setUploadImageUrl = (uploadId, imageUrl) => {
    setUploadList(prev => prev.map(item =>
      item.id === uploadId ? Object.assign({}, item, { imageUrl: imageUrl }) : item
    ));
  };



  // 处理取消函数注册
  const handleRegisterCancel = (uploadId, cancelFunction) => {
    setActiveRequests(prev => {
      const newMap = new Map(prev);
      if (cancelFunction === null) {
        newMap.delete(uploadId);
      } else {
        // 添加活跃请求
        newMap.set(uploadId, cancelFunction);
      }
      return newMap;
    });
  };

  // 清空上传进度
  const handleClearProgress = () => {
    setUploadList([]);
    setUploadProgressVisible(false);
    setActiveRequests(new Map());
  };

  // 停止所有上传任务
  const stopAllUploads = () => {
    // 取消所有活跃的上传请求
    activeRequests.forEach((cancelFunction, uploadId) => {
      try {
        if (typeof cancelFunction === 'function') {
          cancelFunction(); // 调用取消函数
        }
      } catch (error) {
        console.warn(`取消上传任务 ${uploadId} 失败:`, error);
      }
    });

    // 清空所有状态
    handleClearProgress()
  };

  // 处理确认弹框的确认操作
  const handleConfirmOk = () => {
    stopAllUploads();
    setConfirmModalVisible(false);
    if (pendingUploadAction) {
      pendingUploadAction();
      setPendingUploadAction(null);
    }
  };

  // 处理确认弹框的取消操作
  const handleConfirmCancel = () => {
    setConfirmModalVisible(false);
    setPendingUploadAction(null);
  };

  // 处理上传弹框关闭
  const handleUploadModalCancel = () => {
    if (hasActiveUploads()) {
      // 如果有正在进行的上传任务，显示确认弹框
      setConfirmModalVisible(true);
      setPendingUploadAction(() => () => {
        setUploadModalVisible(false);
      });
    } else {
      setUploadModalVisible(false);
      // 关闭上传弹框时清空进度
      setUploadProgressVisible(false);
    }
  };

  // 处理进度弹框关闭
  const handleProgressClose = () => {
    if (hasActiveUploads()) {
      // 如果有正在进行的上传任务，显示确认弹框
      setConfirmModalVisible(true);
      setPendingUploadAction(() => () => {
        setUploadProgressVisible(false);
      });
    } else {
      setUploadProgressVisible(false);
      handleClearProgress();
    }
  };



  return (
    <Fragment>
      {/* 上传图片弹框 */}
      <UploadModal
        visible={uploadModalVisible}
        onCancel={handleUploadModalCancel}
        selectedCategory={selectedCategory}
        currentFolder={currentFolder}
        onUploadStart={handleUploadStart}
        onRefresh={onRefresh}
        onUpdateProgress={updateUploadProgress}
        onUpdateStatus={updateUploadStatus}
        onSetImageUrl={setUploadImageUrl}
        onRegisterCancel={handleRegisterCancel}
      //  onClearProgress={handleClearProgress}
      />

      {/* 上传进度弹框 */}
      <UploadProgress
        visible={uploadProgressVisible}
        onClose={handleProgressClose}
        uploadList={uploadList}
        onRefresh={onRefresh}
        onUpdateProgress={updateUploadProgress}
        onUpdateStatus={updateUploadStatus}
        onSetImageUrl={setUploadImageUrl}
        selectedCategory={selectedCategory}
        // onClearProgress={handleClearProgress}
      />

      {/* 确认弹框 */}
      <Modal
        title="有未完成的上传任务"
        open={confirmModalVisible}
        onOk={handleConfirmOk}
        onCancel={handleConfirmCancel}
        okText="确定"
        cancelText="取消"
      >
        <p>您已有上传任务，是否结束原上传任务，再次上传？</p>
      </Modal>
    </Fragment>
  );
});

export default UploadManager;

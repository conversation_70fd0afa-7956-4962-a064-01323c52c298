const { Mo<PERSON>, <PERSON>, But<PERSON>, message, Row, Col, Typography } = antd;
const { <PERSON>agger } = antd.Upload;
const { useState, useRef, Fragment } = React;
import { getImageResolution, uploadToOSS, validateFile } from "./uploadUtils";
import request from "../../../utils/plugins/axios/request";
import { ReactCropper } from "./react-cropper";
const {ExclamationCircleOutlined} =icons

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return "0 B";

  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = (bytes / Math.pow(1024, i)).toFixed(2);

  return `${parseFloat(size)} ${sizes[i]}`;
};


// 上传图片弹框组件
function UploadModal({
  visible,
  onCancel,
  selectedCategory,
  currentFolder,
  onUploadStart,
  onRefresh,
  onUpdateProgress,
  onUpdateStatus,
  onRegisterCancel,
}) {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [currentCropIndex, setCurrentCropIndex] = useState();
  const [showCropper, setShowCropper] = useState(false);
  const [cropBoxData, setCropBoxData] = useState({ width: 0, height: 0 });
  const [hasPendingCrop, setHasPendingCrop] = useState(false);
  const [showCropWarning, setShowCropWarning] = useState(false);
  const cropperRef = useRef(null);

  // 重置状态
  const resetState = () => {
    setSelectedFiles([]);
    setCurrentCropIndex(0);
    setShowCropper(false);
    setCropBoxData({ width: 0, height: 0 });
    setHasPendingCrop(false);
    setShowCropWarning(false);
  };
  const handleZoom = (event) => {
      setHasPendingCrop(true);
  };
  // 处理裁剪框变化
  const handleCropBoxChange = (event) => {
    if (event.detail && event.detail.width && event.detail.height) {
      setCropBoxData({
        width: Math.round(event.detail.width),
        height: Math.round(event.detail.height),
      });
    }
    if(event.type === "cropend"){
      setHasPendingCrop(true);
    }
  };

  // 处理模态框关闭
  const handleCancel = () => {
    resetState();
    onCancel();
  };



  // 提交图片信息到后端
  const submitImageToBackend = async (imageData, uploadId) => {
    try {
      // 这里应该调用实际的后端API
      await new Promise((resolve, reject) => {
        const materialList = [
          {
            materialName: imageData.name,
            materialType: 1,
            materialUrl: imageData.url,
            size: imageData.resolution,
            fileSize: imageData.size,
          },
        ];
        request({
          url: "/mall-admin/api/material_group/addMaterial",
          data: { materialList, groupId: selectedCategory },
          success: (res) => {
            resolve(res);
          },
          fail: (error) => {
            reject(error);
          },
        });
      });

      // console.log('保存图片到后端:', imageData);

      // 更新上传状态为成功
      if (onUpdateStatus) {
        onUpdateStatus(uploadId, "success", null);
      }

      // 不在这里刷新列表，等所有上传完成后统一刷新
    } catch (error) {
      console.error("保存到后端失败:", error);
      // 更新上传状态为后端保存失败
      if (onUpdateStatus) {
        onUpdateStatus(uploadId, "save_error", "backend_save");
      }
    }
  };

  // 处理文件选择
  const handleFileSelect = (fileList) => {
    const validFiles = [];

    fileList.forEach((file) => {
      const validation = validateFile(file);
      if (validation.valid) {
        const fileUrl = URL.createObjectURL(file);
        if (validFiles.length < 50) {
          validFiles.push({
            id: Date.now() + "_" + Math.random().toString(36).substring(2, 11),
            file: file,
            name: file.name,
            size: file.size,
            url: fileUrl,
            originalUrl: fileUrl,
            cropped: false,
          });
        }
      }
    });

    if (validFiles.length > 0) {
      setSelectedFiles(validFiles);
      setShowCropper(true);
      setCurrentCropIndex(0);
      setHasPendingCrop(false);
      setShowCropWarning(false);
    }
  };

  // 处理裁剪应用
  const handleCropApply = () => {
    console.log(" updatedFiles[currentCropIndex]", )
    if (cropperRef.current && cropperRef.current.cropper) {
      const canvas = cropperRef.current.cropper.getCroppedCanvas({
        "fillColor": "transparent"
      });
      const originalFile = selectedFiles[currentCropIndex].file
      canvas.toBlob((blob) => {
        const croppedUrl = URL.createObjectURL(blob);
        const updatedFiles = [...selectedFiles];
        const originalFile = updatedFiles[currentCropIndex];
        // 为裁剪后的blob设置文件名
        const croppedBlob = new File([blob], originalFile.name, {
          type: blob.type,
        });

        updatedFiles[currentCropIndex] = {
          ...updatedFiles[currentCropIndex],
          url: croppedUrl,
          croppedBlob: croppedBlob,
          croppedSize: blob.size,
          croppedResolution: `${canvas.width}*${canvas.height}`,
          cropped: true,
        };
        setSelectedFiles(updatedFiles);
        setHasPendingCrop(false);
        setShowCropWarning(false);
      }, originalFile.type||"image/jpeg");
    }
  };

  // 处理裁剪还原
  const handleCropRestore = () => {
    const updatedFiles = [...selectedFiles];
    updatedFiles[currentCropIndex] = {
      ...updatedFiles[currentCropIndex],
      url: updatedFiles[currentCropIndex].originalUrl,
      croppedBlob: null,
      croppedSize: null,
      croppedResolution: null,
      cropped: false,
    };
    setSelectedFiles(updatedFiles);
    setHasPendingCrop(false);
    setShowCropWarning(false);
  };

  // 处理图片切换
  const handleImageSwitch = (index) => {
    if (hasPendingCrop && index !== currentCropIndex) {
      setShowCropWarning(true);
      return;
    }
    setCurrentCropIndex(index);
    setShowCropWarning(false);
  };

  // 删除文件
  const handleFileRemove = (index) => {
    const updatedFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(updatedFiles);

    if (updatedFiles.length === 0) {
      setShowCropper(false);
      setHasPendingCrop(false);
      setShowCropWarning(false);
    } else if (currentCropIndex >= updatedFiles.length) {
      setCurrentCropIndex(updatedFiles.length - 1);
    }
  };

  // 处理最终上传
  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      message.warning("请先选择图片");
      return;
    }

    // 关闭模态框
    handleCancel();

    const totalFiles = selectedFiles.length;

    // 先将所有文件添加到上传列表中，状态为pending
    const uploadItems = selectedFiles.map((fileData) => {
      const uploadId =
        Date.now() + "_" + Math.random().toString(36).substring(2, 11);
      const fileToUpload = fileData.croppedBlob || fileData.file;

      // 校验文件名长度
      const isValidFileName = fileData.name.length <= 50;

      return {
        id: uploadId,
        name: fileData.name,
        size: formatFileSize(fileToUpload.size),
        resolution: "",
        status: isValidFileName ? "pending" : "name_error",
        progress: 0,
        category: selectedCategory,
        file: fileToUpload,
        failureType: isValidFileName ? null : "name_too_long",
        total: totalFiles,
        fileData: fileData,
      };
    });

    // 批量添加到上传列表
    uploadItems.forEach((item) => {
      onUploadStart(item);
    });

    // 开始批量并发上传（只上传校验通过的文件）
    const validItems = uploadItems.filter((item) => item.status === "pending");

    // 批量并发上传所有有效文件
    const uploadPromises = validItems.map(async (uploadItem) => {
      // 更新状态为uploading
      if (onUpdateStatus) {
        onUpdateStatus(uploadItem.id, "uploading", null);
      }

      try {
        // 第一步：上传到OSS
        const uploadPromise = uploadToOSS(
          uploadItem.file,
          ({ percent }) => {
            // 更新上传进度
            if (onUpdateProgress) {
              onUpdateProgress(uploadItem.id, percent);
            }
          },
          (cancelFunction) => {
            // 注册取消函数到父组件
            if (onRegisterCancel) {
              onRegisterCancel(uploadItem.id, cancelFunction);
            }
          }
        );

        const { imageUrl } = await uploadPromise;

        // OSS上传成功，获取图片分辨率和文件大小
        // 如果是裁剪后的文件，使用裁剪后的尺寸和大小
        const fileData = uploadItem.fileData;
        const resolution =
          fileData.cropped && fileData.croppedResolution
            ? fileData.croppedResolution
            : await getImageResolution(imageUrl);

        const imageData = {
          name: fileData.name,
          url: imageUrl,
          resolution: resolution,
          size: uploadItem.file.size,
          category: selectedCategory,
        };

        // 第二步：保存到后端
        await submitImageToBackend(imageData, uploadItem.id);

        // 上传成功，移除活跃请求
        if (onRegisterCancel) {
          onRegisterCancel(uploadItem.id, null); // 传null表示移除
        }

        return { success: true, uploadId: uploadItem.id };
      } catch (error) {
        console.error("上传失败:", error);
        // 上传失败，移除活跃请求
        if (onRegisterCancel) {
          onRegisterCancel(uploadItem.id, null); // 传null表示移除
        }
        // 更新上传状态为失败
        if (onUpdateStatus) {
          onUpdateStatus(uploadItem.id, "upload_error", "oss_upload");
        }

        return { success: false, uploadId: uploadItem.id, error };
      }
    });

    // 等待所有上传完成（不管成功还是失败）
    try {
      const results = await Promise.allSettled(uploadPromises);

      // 检查是否有成功的上传
      const hasSuccessfulUploads = results.some(
        (result) =>
          result.status === "fulfilled" && result.value && result.value.success
      );

      // 如果有成功的上传，刷新列表页面
      if (hasSuccessfulUploads && onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error("批量上传过程中出现错误:", error);
    }
  };

  return (
    <Modal
      title="上传图片"
      className="material-management-upload-modal"
      open={visible}
      maskClosable={false}
      onCancel={handleCancel}
      footer={
        showCropper
          ? [
              <Button key="cancel" onClick={handleCancel}>
                取消
              </Button>,
              <Button key="upload" type="primary" onClick={handleUpload}>
                上传图片 ({selectedFiles.length})
              </Button>,
            ]
          : null
      }
      width={950}
    >
      <Fragment>
        <div style={{ marginBottom: 16 }}>
          <span style={{ marginRight: 8 }}>
            上传至：{(currentFolder && currentFolder.groupName) || "根目录"}
          </span>
        </div>
        {!showCropper ? (
          <Dragger
            multiple
            style={{ height: 481 }}
            accept=".jpg,.jpeg,.png,.webp"
            showUploadList={false}
            beforeUpload={(_, fileList) => {
              handleFileSelect(fileList);
              return false; // 阻止自动上传
            }}
          >
            <p className="ant-upload-drag-icon">
              <Button type="primary">选择图片</Button>
            </p>
            <p className="ant-upload-text">
              单张图片不允许超过10M，最多选择50张
            </p>
            <p className="ant-upload-hint">支持图片jpg/png/jpeg/webp</p>
          </Dragger>
        ) : (
          <div style={{ display: "flex", height: 481 }}>
            {/* 左侧九宫格 */}
            <div style={{ width: "40%" }}>
              {/* 警告提示 */}
              { showCropWarning &&(
                <div
                  style={{
                    color: "#ff4d4f",
                    padding: "8px 12px",
                    marginBottom: 8,
                    fontSize: 14,
                    textAlign: "left",
                  }}
                >
                  <ExclamationCircleOutlined />请先完成当前图片裁剪再切换下一张图片
                </div>
              )}
             <div style={{ height:showCropWarning? "calc(100% - 50px)":'100%',  paddingRight: 16, overflowY: "auto" }}>

              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: 8,
                }}
              >
                {selectedFiles.map((file, index) => (
                  <div
                    key={file.id}
                    style={{
                      position: "relative",
                      aspectRatio: "1",
                      border:
                        currentCropIndex === index
                          ? "2px solid #1890ff"
                          : "1px solid #d9d9d9",
                      borderRadius: 4,
                      overflow: "hidden",
                      cursor: "pointer",
                    }}
                    onClick={() => handleImageSwitch(index)}
                  >
                    <img
                      src={file.url}
                      alt={file.name}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                    {file.cropped && (
                      <div
                        style={{
                          position: "absolute",
                          top: 2,
                          left: 2,
                          background: "#52c41a",
                          color: "white",
                          fontSize: 10,
                          padding: "1px 4px",
                          borderRadius: 2,
                        }}
                      >
                        已裁剪
                      </div>
                    )}
                    <Button
                      type="text"
                      size="small"
                      danger
                      style={{
                        position: "absolute",
                        top: 2,
                        right: 2,
                        minWidth: "auto",
                        width: 20,
                        height: 20,
                        padding: 0,
                        background: "rgba(0,0,0,0.5)",
                        color: "white",
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFileRemove(index);
                      }}
                    >
                      ×
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            </div>

            {/* 右侧裁剪区域 */}
            <div style={{ width: "60%", paddingLeft: 16 }}>
              {selectedFiles[currentCropIndex] && (
                <Fragment>
                  <div
                    style={{
                      marginBottom: 8,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "left",
                    }}
                  >
                    <div
                      style={{ display: "flex", gap: 8, alignItems: "center" }}
                    >
                      <Typography.Title
                        level={5}
                        style={{ marginBottom: 0, marginRight: 8 }}
                      >
                        裁剪
                      </Typography.Title>
                      <Button type={"primary"} onClick={handleCropApply}>
                        应用裁剪
                      </Button>
                      <Button onClick={handleCropRestore}>恢复</Button>
                    </div>
                    <div style={{ fontSize: 14, color: "#666" }}>
                      尺寸: 宽{cropBoxData.width} 高{cropBoxData.height}
                    </div>
                  </div>
                  <div style={{ height: 400, border: "1px solid #d9d9d9" }}>
                    <ReactCropper
                      ref={cropperRef}
                      src={selectedFiles[currentCropIndex].originalUrl}
                      style={{ height: "100%", width: "100%" }}
                      guides={true}
                      viewMode={0}
                      dragMode="move"
                      scalable={true}
                      cropBoxMovable={true}
                      cropBoxResizable={true}
                      autoCropArea={0.8}
                      zoom={handleZoom}
                      crop={handleCropBoxChange}
                      cropend={handleCropBoxChange}
                      ready={(e) => {
                        // 初始化时获取裁剪框尺寸
                        if (e.currentTarget && e.currentTarget.cropper) {
                          const cropBoxData =
                            e.currentTarget.cropper.getCropBoxData();
                          setCropBoxData({
                            width: Math.round(cropBoxData.width),
                            height: Math.round(cropBoxData.height),
                          });
                        }
                      }}
                    />
                  </div>
                </Fragment>
              )}
            </div>
          </div>
        )}
      </Fragment>
    </Modal>
  );
}

export default UploadModal;

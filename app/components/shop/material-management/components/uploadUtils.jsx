import { lib } from "../../../common/react/utils/lib";
import request from "../../../utils/plugins/axios/request";


// OSS直传上传函数 - 支持取消
export function uploadToOSS(file, onProgress, onCancel) {
  let cancelTokenSource;

  const promise = new Promise((resolve, reject) => {
    // 处理文件名，如果是blob则使用默认扩展名
    const fileName = file.name || 'image.jpg';
    const suffix = fileName.split(".").reverse()[0].toLowerCase();
    const key = `miniShop/material-management/${lib.generateNumberString(false, 18)}_${parseInt(Math.random() * 1000)}.${suffix}`;

    // 先获取OSS上传配置
    request({
      url: '/mall-admin/api/oss/post-signature',
      method: 'POST',
      data: {
        bucket: "dante-minshop",
        region: "cn-hangzhou",
        maxFileSize: file.size || 10485760, // 10MB
        uploadDir: key
      },
      success: function (ossConfig) {
        // 使用API返回的配置创建FormData
        const formData = new FormData();
        formData.append("success_action_status", "200");
        formData.append("policy", ossConfig.policy);
        formData.append("x-oss-signature", ossConfig.signature);
        formData.append("x-oss-signature-version", ossConfig.version);
        formData.append("x-oss-credential", ossConfig.xossCredential);
        formData.append("x-oss-date", ossConfig.xossDate);
        formData.append("key", key);
        formData.append("x-oss-security-token", ossConfig.securityToken);
        formData.append("file", file);

        // 创建取消令牌
        cancelTokenSource = axios.CancelToken.source();

        axios.request({
          url: `https://${ossConfig.host}`,
          method: "POST",
          data: formData,
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          cancelToken: cancelTokenSource.token,
          onUploadProgress: ({ total, loaded }) => {
            onProgress && onProgress({ percent: Math.round((loaded / total) * 100) });
          },
        })
        .then(() => {
          const imageUrl = `https://dante-minshop.yang800.com/${key}`;
          resolve({ imageUrl });
        })
        .catch((error) => {
          if (axios.isCancel(error)) {
            reject(new Error('上传已取消'));
          } else {
            reject(error);
          }
        });
      },
      fail: (error) => {
        reject(new Error('获取上传配置失败: ' + error.message));
      }
    });
  });

  // 添加取消方法
  promise.cancel = () => {
    console.log("用户取消上传");
    if (cancelTokenSource) {
      cancelTokenSource.cancel('用户取消上传');
    }
  };

  // 如果提供了取消回调，注册它
  if (onCancel && typeof onCancel === 'function') {
    onCancel(promise.cancel);
  }

  return promise;
}



// 获取图片分辨率
export function getImageResolution(imageUrl) {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = function() {
      resolve(`${this.width}*${this.height}`);
    };
    img.onerror = function() {
      resolve('未知');
    };
    img.src = imageUrl;
  });
}

// 文件验证
export function validateFile(file) {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type);
  if (!isValidType) {
    return { valid: false, message: '只支持 JPG/PNG/JPEG/WEBP 格式的图片' };
  }

  const isValidSize = file.size / 1024 / 1024 < 10;
  if (!isValidSize) {
    return { valid: false, message: '图片大小不能超过 10MB' };
  }

  return { valid: true };
}

import request from "../../../utils/plugins/axios/request";

const { useState, useEffect } = React;
const { Modal, Tree, message } = antd;

// 批量移动弹框组件
function BatchMoveModal({
  visible,
  onCancel,
  selectedCount,
  isBatch,
  selectedItems = [],
  onRefresh,
  currentMoveOperation,
  currentFolderId // 新增：当前所在目录ID
}) {
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);

  // 设置默认值
  const batchMode = isBatch !== undefined ? isBatch : false;

  // 获取目录树数据
  const getTreeData = async () => {
    setLoading(true);
    try {
      await new Promise((resolve, reject) => {
        request({
          url: '/mall-admin/api/material_group/listMaterialGroupTree',
          method: 'POST',
          data: {},
          success: (data) => {
            // 转换数据格式为 Tree 组件需要的格式
            const convertToTreeData = (nodes, isFirstLevel = false) => {
              if (!Array.isArray(nodes)) return [];

              return nodes.map(node => {
                // 禁用条件：
                // 1. 一级目录（isFirstLevel为true）
                // 2. 当前目录（node.id === currentFolderId）
                const isDisabled = isFirstLevel || node.id == currentFolderId;

                return {
                  title: node.groupName || node.label,
                  key: node.id,
                  disabled: isDisabled,
                  children: node.children ? convertToTreeData(node.children, false) : []
                };
              });
            };

            const formattedData = convertToTreeData(data.children || [], true);

            setTreeData([...formattedData]);
            resolve(data);
          },
          fail: (error) => {
            console.error('获取目录树失败:', error);
            reject(error);
          }
        });
      });
    } catch (error) {
      console.error('获取目录树失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 当弹框显示时获取数据
  useEffect(() => {
    if (visible) {
      getTreeData();
      setSelectedFolder(null); // 重置选中状态
    }
  }, [visible]);

  // 内部处理移动确认
  const handleMoveConfirm = async (targetFolder) => {
    try {
      // 准备移动的文件ID列表
      let idList = [];
      if (batchMode) {
        idList = selectedItems;
      } else if (currentMoveOperation && currentMoveOperation.id) {
        idList = [currentMoveOperation.id];
      }
      if (idList.length === 0) {
        message.error("没有选择要移动的文件");
        return;
      }

      // 调用移动图片的目录API
      await new Promise((resolve, reject) => {
        request({
          url: '/mall-admin/api/material_group/moveMaterial',
          method: 'POST',
          data: {
            idList: idList,
            groupId: targetFolder
          },
          success: (res) => {
            resolve(res);
          },
          fail: (error) => {
            reject(error);
          }
        });
      });

      message.success("移动成功");
      setSelectedFolder(null);
      onCancel(); // 关闭弹框

      // 刷新列表
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('移动失败:', error);
    }
  };

  const handleOk = () => {
    if (!selectedFolder) {
      message.error('请选择目标文件夹');
      return;
    }
    handleMoveConfirm(selectedFolder);
  };

  const handleCancel = () => {
    setSelectedFolder(null);
    onCancel();
  };

  return (
    <Modal
      title={batchMode ? "批量移动" : "移动"}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={400}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          加载目录中...
        </div>
      ) : (
        <Tree
          treeData={treeData}
          defaultExpandAll
          height={400}
          onSelect={(selectedKeys, info) => {
            // 检查选中的节点是否被禁用
            if (selectedKeys.length > 0 && !info.node.disabled) {
              setSelectedFolder(selectedKeys[0]);
            }
          }}
          selectedKeys={selectedFolder ? [selectedFolder] : []}
        />
      )}
    </Modal>
  );
}

export default BatchMoveModal;

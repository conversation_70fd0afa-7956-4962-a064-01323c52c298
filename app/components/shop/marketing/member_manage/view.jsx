
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import { getParam } from "../../../items/seller/add-edit-module/util"
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,Descriptions} = antd;
import TableModal from "../../../common/react/components/table-modal";
import SecretInfo from "../external-crowd-manage/secret-info"
const modalTableColumns = [
    {
        title: '用户手机号码',
        dataIndex: 'phone',
        key: 'phone',
         render:(text,record,index)=>{
                    return <SecretInfo content={text} type={'phone'} />
                }
    },
    {
        title: '用户ID',
        dataIndex: 'userId',
        key: 'userId',
    },
    {
        title: '注册时间',
        dataIndex: 'createdTimeDesc',
        key: 'createdTimeDesc',
    },
];

const outModalTableColumns = [
    {
        render:(_,record,index)=>{
            return index+1
        },
        title:'序号',
    },
    {
        key:'outUserId',
        dataIndex:'outUserId',
        title:'企微员工ID',
    },
    {
        key:'customerName',
        dataIndex:'customerName',
        title:'用户微信名称',
    },
    {
        key:'remark',
        dataIndex:'remark',
        title:'用户微信备注',
    },
];
export const Index = () => {
    const searchListRef = useRef()
    const [modalShow,setModalShow]= useState(false)
    const [outModalShow,setOutModalShow] = useState(false)
    const [param,setParam] = useState({})
    
    const getConfig = async () => {
        const data = await new Promise((resolve, reject) => {
          $.ajax({
            url: "https://maria.yang800.com/api/data/v2/940/765",
            success: (res) => {
              resolve(res)
            }
          })
        })
        return data.data;
      };
    return <Fragment>
        <SearchList
            searchConditionConfig={{
                size: "middle",
            }}
            paginationConfig={{size: "small",showPosition:'bottom'}}
            scrollMode="tableScroll"
            ref={searchListRef}
            getConfig={getConfig}
            
            tableCustomFun={{
                // name:(row)=>{
                //     return <a href={`/seller/add-edit-module?id=${row.id}`}  target="_blank"></a>
                // }
                phoneFn:(row)=>{
                    return <SecretInfo content={row.phone} type={'phone'} />
                },
                renderNum:(row)=>{
                    return <a onClick={()=>{
                        setModalShow(true);
                        setParam(row)
                    }}>{row.invitedCount||0}</a>
                },
                outUserIdFn:(row)=>{
                    return (<a onClick={()=>{
                        setOutModalShow(true);
                        setParam(row);
                        // 
                    }}>{row.outUserId}</a>)
                }
            }}
            renderModal={()=>{
                return <Fragment>
                    <TableModal
                        title={"详情"}
                        // searchConfigs={}
                        tableColumn={modalTableColumns}
                        closeFn={()=>{
                            setModalShow(false);
                        }}
                        header={<Descriptions title={`已邀请${param? param.invitedCount:0}名用户`}></Descriptions>}
                        show={modalShow}
                        dataUrl={'/mall-admin/api/family/register/invitedUserPage'}
                        searchParams={{invitedBy:param? param.userId:null}}
                        scroll={{
                            x:400,
                            y:400
                        }}
                        modalWidth={700}
                    />
                    <TableModal 
                        title={'详情'}
                        show={outModalShow}
                        tableColumn={outModalTableColumns}
                        closeFn={()=>{
                            setOutModalShow(false);
                        }}
                        dataUrl={'/mall-admin/api/family/getPagedWeChatEmployees'}
                        searchParams={{outUserId:param? param.outUserId:null}}
                        scroll={{
                            x:400,
                            y:400
                        }}
                        modalWidth={700}
                    />

                </Fragment>
            }}
        />
    </Fragment>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("member-manage"));
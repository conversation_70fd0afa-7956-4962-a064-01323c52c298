
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message} = antd;
import TableModal from "../../../common/react/components/table-modal";
import SecretInfo from "../external-crowd-manage/secret-info";
const modalSearchConfigs = [
    {
        type: 'INPUT',
        labelKey: 'userId',
        labelName: '小程序用户ID',
    },
    {
        type: 'INPUT',
        labelKey: 'customerOutUserId',
        labelName: '微信ID',
    },
    {
        type: 'INPUT',
        labelKey: 'remarkMobiles',
        labelName: '用户手机号',
    },
    {
        type: 'DATERANGE',
        labelKey: 'outCreateTime',
        searchValue: ['outCreateTimeStart','outCreateTimeEnd'],
        labelName: '添加时间',
    },
    {
        type: 'SELECT',
        labelKey: 'status',
        labelName: '当前状态',
        url:'/mall-admin/api/family/customer/statusList',
        list:[],
    },
    {
        type:'INPUT',
        labelKey:'customerName',
        labelName:'用户微信名称',
    },
    {
        type:'INPUT',
        labelKey:'remark',
        labelName:'用户微信备注',
    },
];
const modalTableColumns = [
    {
        title: '小程序用户ID',
        dataIndex: 'userId',
        key: 'userId',
    },
    {
        title: '微信ID',
        dataIndex: 'customerOutUserId',
        key: 'customerOutUserId',
    },
    {
        title: '用户手机号',
        dataIndex: 'remarkMobilesDesc',
        key: 'remarkMobilesDesc',
        render:(text,record,index)=>{
            return <SecretInfo content={text} type={'phone'} />
        }
    },
    {
        key:'customerName',
        dataIndex:'customerName',
        title:'用户微信名称',
    },
    {
        key:'remark',
        dataIndex:'remark',
        title:'用户微信备注',
    },
    // {
    //     title: '微信ID',
    //     dataIndex: 'customerOutUserId',
    //     key: 'customerOutUserId',
    // },
    {
        title: '添加时间',
        dataIndex: 'outCreateTimeDesc',
        key: 'outCreateTimeDesc',
    },
    {
        title: '当前状态',
        dataIndex: 'statusDesc',
        key: 'statusDesc',
    },

];

const modalTableOutColumns = [
    {
        render:(_,record,index)=>{
            return index+1
        },
        title:'序号',
    },
    {
        key:'outUserId',
        dataIndex:'outUserId',
        title:'企微员工ID',
    },
    {
        key:'customerName',
        dataIndex:'customerName',
        title:'用户微信名称',
    },
    {
        key:'remark',
        dataIndex:'remark',
        title:'用户微信备注',
    },
]
export const Index = () => {
    const searchListRef = useRef()
    const [modalShow,setModalShow] = useState(false);
    const [outModalShow,setOutModalShow] = useState(false);
    const [params,setParams] = useState({});
    const getConfig = async () => {
        const data = await new Promise((resolve, reject) => {
          $.ajax({
            url: "https://maria.yang800.com/api/data/v2/942/767",
            success: (res) => {
              resolve(res)
            }
          })
        })
        return data.data;
      };
    return <Fragment>
        <SearchList
            searchConditionConfig={{
                size: "middle",
            }}
            paginationConfig={{size: "small",showPosition:'bottom'}}
            scrollMode="tableScroll"
            ref={searchListRef}
            getConfig={getConfig}
            tableCustomFun={{
                customerCountFn:(row)=>{
                    return <a onClick={()=>{
                        setModalShow(true);
                        setParams({
                            outUserId:row.outUserId
                        })
                    }}>{row.customerCount||0}</a>
                },
                outUserIdFn:(row)=>{
                    return <a onClick={()=>{
                        setOutModalShow(true);
                        setParams({
                            outUserId:row.outUserId
                        })
                    }}>{row.outUserId||0}</a>
                }
            }}
            renderModal={()=>{
                return <Fragment>
                    <TableModal
                        title={"详情"}
                        searchConfigs={modalSearchConfigs}
                        tableColumn={modalTableColumns}
                        closeFn={()=>{
                            setModalShow(false);
                        }}
                        show={modalShow}
                        dataUrl={'/mall-admin/api/family/user/customer/page'}
                        searchParams={params}
                    />
                    <TableModal
                        title={"详情"}
                        searchConfigs={null}
                        tableColumn={modalTableOutColumns}
                        closeFn={()=>{
                            setOutModalShow(false);
                        }}
                        show={outModalShow}
                        dataUrl={'/mall-admin/api/family/getPagedWeChatEmployees'}
                        searchParams={params}
                    />
                </Fragment>
            }}
        />
    </Fragment>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("external-contacts-manage"));


const { Spa, SpaConfigProvider,DTEditForm } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message} = antd;
const {ReloadOutlined} = icons
export default ({load,id})=>{
    const ref = useRef(null);
    const [open,setOpen] = useState(false);

    const onOk = ()=>{
        // /mall-admin/api/family/bulletScreen/add
        ref.current.form.validateFields().then(values=>{
            request({
                url:'/mall-admin/api/family/bulletScreen/add',
                method:'POST',
                data:{
                    userName:values.userName,
                    outGroupId:values.outGroupId,
                    content:values.content,
                    avatarPath:values.avatarPath&&
                    values.avatarPath[0]?values.avatarPath[0].url:'',
                }
            }).then(res=>{
                message.success("添加成功")
                setOpen(false)
                load();
            })
        })

    }

    const autoCreate = ()=>{
        // /mall-admin/api/family/bulletScreen/randomCreate
        request({
            url:'/mall-admin/api/family/bulletScreen/randomCreate',
            method:'POST',
        }).then(res=>{
            console.log("res:",res)
            ref.current.form.setFieldsValue({
                userName:res.userName,
                outGroupId:res.outGroupId,
                content:res.content,
                avatarPath:[{url:res.avatarPath}],
            })
        })
    }

    useEffect(()=>{
        console.log(ref)
        if(!open){
            ref.current && ref.current.form.resetFields();
        }
    },[open])

    return (<Fragment>
        <Button style={{
            margin:'20px'
        }} type={'primary'} onClick={()=>{
            setOpen(true)
        }}>
            新增
        </Button>
        <Modal
            title="弹幕信息"
            open={open}
            onOk={onOk}
            okText="确认"
            cancelText="取消"
            onCancel={()=>{
                setOpen(false)
            }}
        >

            <DTEditForm
                layout={{mode: "appoint",colNum: 1, }}
                ref={ref}
                configs={{
                    base:{
                        title:<div style={{width:'432px'}}>
                            基础信息
                            <Button icon={<ReloadOutlined />} style={{float:'right'}} onClick={()=>{
                                autoCreate()
                            }}>随机生成</Button>
                        </div>,
                        configs:[
                            {
                                type:'INPUT',
                                fProps:{
                                    name:'userName',
                                    label:'用户名称',
                                    rules:[{required:true,message:'请输入用户名称'},{max:10,type:'string',message:'最长10个字符'}]
                                },

                            },{
                                type:'SELECT',
                                fProps:{
                                    name:'outGroupId',
                                    label:'用户所在群',
                                    rules:[{required:true,message:'请选择用户所在群'}]
                                },
                                list:[],
                                dataUrl:'/mall-admin/api/family/bulletScreen/groupList'
                            },
                            {
                                type:'FILE',
                                fProps:{
                                    name:'avatarPath',
                                    label:'用户头像',
                                    extra: "支持png,jpg,jpeg格式，最多不超过5M",
                                    // labelCol: { span: 6 },
                                    rules:[{required:true,message:'请选择用户头像'}]
                                },
                                cProps: {
                                    // listType: "text",
                                    maxCount: 1,
                                    size: 5,
                                    accept: ".jpg,.jpeg,.png",
                                },
                            }
                        ]
                    },
                    content:{
                        title:'弹幕信息',
                        configs:[{
                            type:'TEXTAREA',
                            fProps:{
                                // label: "申请人",
                                name:'content',
                                rules:[{
                                    required:true,
                                    message: '请输入弹幕信息'
                                },{
                                    max:100,message:"最长100字符",type:'string'
                                }],
                                wrapperCol:{span:24}
                            },
                        }]
                    }
                }}
            />
        </Modal>

    </Fragment>)
}

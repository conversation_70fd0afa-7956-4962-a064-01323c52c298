
const { <PERSON>, Spa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>ist,DTEditForm } = dtComponents
import request from "../../../utils/plugins/axios/request";
import BarrageMadal from "./barrage-madal";
import EditModal from "./edit-modal";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,Space,Image,Descriptions} = antd;

const Index = () => {
    const searchListRef = useRef()
    const [editBol,setEditBol] = useState(false);
    const [editRow,setEditRow] = useState();
    const ref = useRef();
    const getConfig = async () => {
        const data = await new Promise((resolve, reject) => {
            $.ajax({
                url: "https://maria.yang800.com/api/data/v2/946/771",
                success: (res) => {
                    resolve(res);
                }
            })
        })
        return data.data;
    };

    const setWoolData = (values)=>{
        request({
            url:'/mall-admin/api/family/group/decoration/submit',
            data:{
                ...values
            },
        }).then(res=>{
            console.log("res:",res)
            message.success("福利转化页设置成功")
        })
    }

    const getData = ()=>{
        request({
            url:'/mall-admin/api/family/group/decoration/detail',
            method:'POST',
            data:{ },
        }).then(res=>{
            console.log("res:",res)
            ref.current.form.setFieldsValue({
                ...res,
                file:res.imgPath?[{url:res.imgPath}]:[]
            })
        })
    }

    useEffect(()=>{
        getData()
    },[])

    return <div  className="barrage-manage">
        <BarrageMadal load={()=>{
            searchListRef.current.load();
        }} />
      <div style={{height:'460px'}}>
        <SearchList
            searchConditionConfig={{
                size: "middle",
            }}
            paginationConfig={{size: "small",showPosition:'bottom'}}
            scrollMode="tableScroll"
            ref={searchListRef}
            getConfig={getConfig}
            tableCustomFun={{
                avatarPathFn:(row)=>{
                    return <Image src={row.avatarPath} style={{width:'60px',height:'60px'}} />
                },
                indexFn:(row,index)=>{
                    return index+1
                },
                operateFn:(row)=>{
                    return <Space>
                        <Button type='link' onClick={()=>{
                            Modal.confirm({
                                title:'确认',
                                content:'确定删除吗？',
                                onOk:()=>{
                                    request({
                                        url:'/mall-admin/api/family/bulletScreen/remove',
                                        data:{
                                            id:row.id,
                                        }
                                    }).then(res=>{
                                        message.success("删除成功");
                                        searchListRef.current.load();
                                    })
                                }
                            })
                        }}>删除</Button>
                        <Button type='link' onClick={()=>{
                            setEditBol(true);
                            setEditRow(row)
                        }}>编辑</Button>
                    </Space>
                }
            }}
        />
      </div>
        <EditModal load={()=>{
            searchListRef.current.load();

        }}
             open={editBol}
             closeFn={()=>{
                setEditBol(false);
             }}
             row={editRow}
        />
        <div style={{padding:'20px 30px',borderTop:'1px solid #ccc'}}>
        <Space>
                <Descriptions title="福利说明" style={{width:"100px",marginLeft:'50px'}} items={[]} ></Descriptions>
                <Button type="primary" style={{marginBottom:"20px"}} onClick={()=>{
                    console.log(ref.current.form)
                    ref.current.form.validateFields().then(values=>{
                        console.log(values);
                        // values.file
                        // values.imgPath = values.file[0].url;
                        values.imgPath  = values.file[0]?values.file[0].url:'',
                        delete values.file
                        setWoolData(values)
                    })
                }}>保存</Button>
            </Space>

            <DTEditForm
                ref={ref}
                layout={{
                    mode: "appoint",
                    colNum: 2,
                }}
                configs={[{
                    type:'TEXTAREA',
                    fProps:{
                        label:'引导文案',
                        name:'content',
                        rules:[{required:true,message:'请输入引导文案'},{
                            max:100,message:"最长100字符",type:'string'
                        }]
                    },
                    cProps:{
                        placeholder:'以;号换行展示',
                        style:{
                            minHeight:'200px'
                        }
                    }
                },{
                    type:'FILE',
                    fProps:{
                        label:'图文页',
                        name:'file',
                        extra: "支持png,jpg,jpeg格式，最多不超过20M",
                        labelCol: { span: 6 },
                        // rules:[{required:true,message:'请选择用户头像'}]
                    },
                    cProps: {
                        // listType: "text",
                        maxCount: 1,
                        size: 20,
                        accept: ".jpg,.jpeg,.png",
                    },
                }]}

            />
        </div>

    </div>

}
export default Index;

const {Row,Col,Slider,InputNumber} = antd;
const {useState,useEffect,useRef,Fragment} = React;
// const InputNumber = Input
const IntegerStep = ({value,onChange,min,max,disabled}) => {
    const [inputValue, setInputValue] = useState(value);
  
    const onNewChange = (newValue) => {
      setInputValue(newValue);
      onChange(newValue)
    };
  
    return (
      <Row>
        <Col span={12}>
          <Slider
            min={min}
            max={max}
            disabled={disabled}
            onChange={onNewChange}
            step={1}
            value={typeof inputValue === 'number' ? inputValue : 0}
          />
        </Col>
        <Col span={4}>
          <InputNumber
            min={min}
            max={max}
            style={{ margin: '0 16px' }}
            disabled={disabled}
            value={inputValue}
            onChange={onNewChange}
          />
        </Col>
      </Row>
    );
  };

  export default IntegerStep
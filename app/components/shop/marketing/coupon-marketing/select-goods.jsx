import AddGoodsModal from "./add-goods-modal";

const {useState,useEffect,useRef,Fragment} = React;
const {Button,Space,Table,Input,InputNumber,Row,Col,message, Modal, Tooltip} = antd;
const {QuestionCircleOutlined} = icons;
import request from "../../../utils/plugins/axios/request";



const SelectGoods = ({
    value=[],
    configs,
    onChange,
    type,
    pageMethod,
    maxLimit,
}) => {
    const [goods,setGoods] = useState([]);
    const [open,setOpen] = useState(false);
    const [discount,setdiscount] = useState();
    const [quantity,setquantity] = useState();
    const [thresholdAmount,setthresholdAmount] = useState();
    const [thresholdDeductionAmount,setthresholdDeductionAmount] = useState();
    const [deductionAmount,setdeductionAmount] = useState();
    const [selectItem,setSelectItem] = useState([])
    const once = useRef(false)
    const isDisabled = (key)=>{
        if(pageMethod==='edit'){
            if(key!=='quantity'){
                return true
            } else{
                return false
            }
        } else if(pageMethod ==='look'){
            return true;
        } else {
            return false
        }
    }
    const columns1 = [
        {
            title:'商品信息',
            render:(row,_,index)=> (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src={row.mainImage} style={{ width: '50px', heigth: '50px' }} />
                    <div style={{marginLeft: '10px'}}>
                        <div>{row.name}</div>
                        <div>商品ID{row.id}</div>
                        <div>原价：{row.highPrice / 100}</div>
                    </div>
                </div>)
        },
        {
            title:'券折扣',
            // dataIndex
            render:(row,_,index)=> <Input disabled={isDisabled('discount')} value={row.discount} addonBefore='¥'  onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].discount = e.currentTarget.value;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:<Tooltip title="该商品维度上最大可领取的优惠券的数量,每个商品券发放量独立">
                    <span style={{verticalAlign:"middle", marginRight:"6px"}}>券发放量</span>
                    <QuestionCircleOutlined style={{ verticalAlign: "middle" }} />
                </Tooltip>,
            // dataIndex
            render:(row,_,index)=> <Input disabled={isDisabled('quantity')} max={1000} value={row.quantity} addonBefore='¥'  onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].quantity = e.currentTarget.value;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:'每人限领',
            // dataIndex
            render:(row,_,index)=> <InputNumber max={maxLimit} disabled={maxLimit===1 || isDisabled('limitNum')} value={row.limitNum} addonAfter='张' onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].limitNum = e;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:'操作',
            render: (row,_,index) => <Fragment>
                <Button  type='link'  disabled={isDisabled('btn')} onClick={()=>{
                    goods.splice(index,1)
                    setGoods([...goods]);
                    onChange([...goods]);
                    selectItem.splice(index,1);
                    setSelectItem([...selectItem])
                }}>删除</Button>
            </Fragment>
        }
    ]

    const columns2 = [
        {
            title:'商品信息',
            render:(row,_,index)=>(
                 <div style={{ display: 'flex', alignItems: 'center' }}>
                 <img src={row.mainImage} style={{ width: '50px', heigth: '50px' }} />
                 <div style={{marginLeft: '10px'}}>
                     <div>{row.name}</div>
                     <div>商品ID{row.id}</div>
                     <div>原价：{row.highPrice / 100}</div>
                 </div>
             </div>)
        },
        {
            title:'券满减金额',
            // dataIndex
            render:(row,_,index)=> (
                <Space style={{padding:'10px 0'}}>
                    满
                    <Input disabled={isDisabled('thresholdAmount')} addonBefore="¥" value={row.thresholdAmount} onChange={(e)=>{
                        // setthresholdAmount(e.currentTarget.value)
                        console.log("goods:",goods)
                        goods[index].thresholdAmount = e.currentTarget.value;
                        setGoods([...goods]);
                        onChange([...goods])
                    }}/>
                    减
                    <Input disabled={isDisabled('thresholdDeductionAmount')}  addonBefore="¥" value={row.thresholdDeductionAmount}  onChange={(e)=>{
                         console.log("goods:",goods)
                         goods[index].thresholdDeductionAmount = e.currentTarget.value;
                         setGoods([...goods]);
                         onChange([...goods])
                    }}/>
                </Space>
            )
        },
        {
            title:<Tooltip title="该商品维度上最大可领取的优惠券的数量,每个商品券发放量独立">
                    <span style={{verticalAlign:"middle", marginRight:"6px"}}>券发放量</span>
                    <QuestionCircleOutlined style={{ verticalAlign: "middle" }} />
                </Tooltip>,
            // dataIndex
            render:(row,_,index)=> <Input  disabled={isDisabled('quantity')} max={1000}  value={row.quantity} addonBefore='¥'  onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].quantity = e.currentTarget.value;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:'每人限领',
            // dataIndex
            render:(row,_,index)=> <InputNumber max={maxLimit} disabled={maxLimit===1 || isDisabled('limitNum')} value={row.limitNum} addonAfter='张' onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].limitNum = e;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:'操作',
            render: (row,_,index) => <Fragment>
                <Button  type='link'  disabled={isDisabled('btn')} onClick={()=>{
                     goods.splice(index,1)
                     setGoods([...goods]);
                     onChange([...goods]);
                     selectItem.splice(index,1);
                     setSelectItem([...selectItem])
                }}>删除</Button>
            </Fragment>
        }
    ]

    const delHandle = (index)=>{
        Modal.confirm({
            title: '提示',
            content: '确认删除？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                goods.splice(index,1)
                setGoods([...goods]);
                onChange([...goods]);
                selectItem.splice(index,1);
                setSelectItem([...selectItem])
            },
        })
    }
    const columns3 = [
        {
            title:'商品信息',
            render:(row)=>(    
                 <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src={row.mainImage} style={{ width: '50px', heigth: '50px' }} />
                    <div style={{marginLeft: '10px'}}>
                        <div>{row.name}</div>
                        <div>商品ID{row.scopeValue}</div>
                        <div>原价：{row.highPrice / 100}</div>
                    </div>
                </div>
            )
        },
        {
            title:'券立减金额',
            // dataIndex
            render:(row,_,index)=> <Input  disabled={isDisabled('deductionAmount')} value={row.deductionAmount} addonBefore='¥'  onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].deductionAmount = e.currentTarget.value;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            // title:'券发放量',
           title: <Tooltip title="该商品维度上最大可领取的优惠券的数量,每个商品券发放量独立">
                    <span style={{verticalAlign:"middle", marginRight:"6px"}}>券发放量</span>
                    <QuestionCircleOutlined style={{ verticalAlign: "middle" }} />
                </Tooltip>,
            // dataIndex
            render:(row,_,index)=> <Input  disabled={isDisabled('quantity')}  max={1000}  value={row.quantity} addonBefore='¥'  onChange={(e)=>{
                // goods.splice(index,1)
                goods[index].quantity = e.currentTarget.value;
                console.log( goods[index]);
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:'每人限领',
            // dataIndex
            render:(row,_,index)=> <InputNumber max={maxLimit} disabled={maxLimit===1 || isDisabled('limitNum')} value={row.limitNum} addonAfter='张' onChange={(e)=>{
                // goods.splice(index,1)
                console.log(row,index);
                goods[index].limitNum = e;
                setGoods([...goods]);
                onChange([...goods])
            }} />
        },
        {
            title:'操作',
            render: (row,_,index) => <Fragment>
                <Button type='link' disabled={isDisabled('btn')} onClick={()=>{
                  delHandle(index)
                }}>删除</Button>
            </Fragment>
        }
    ]
    

    const getList = (itemIds)=>{
        setSelectItem(itemIds.map((item)=>{
            return {scopeValue:item}
        }))
        request({
            url:'/mall-admin/api/items/list',
            method:'post',
            data:{
                itemIds:itemIds||[]
            }
        }).then(res=>{
            value.map((item,index)=>{
               let data = res.find(i => i.id == item.scopeValue)
                if(data) {
                    item.name = data.name;
                    item.mainImage = data.mainImage;
                    item.highPrice = data.highPrice;
                }  
            })
            setGoods(value)
        })
    }

    const columnsObj = {
        "DISCOUNT":columns1,
        "DIRECT_REDUCTION":columns3,
        "FULL_REDUCTION":columns2
    }

    const batchSet = (type)=>{
        switch (type) {
            case "DISCOUNT":
                // if(pageMethod)
                if(!discount && ['add'].includes(pageMethod)){
                    return message.error("请输入券折扣")
                }
                if(!/^(0\.\d|[1-9](\.\d)?)$/.test(discount) && ['add'].includes(pageMethod)){
                    return message.error("折扣只能输入大于0小于10，且最多一位小数的正数")
                }
                if(!quantity && ['add','edit'].includes(pageMethod)){
                    return message.error("请输入发放量")
                }
                if(!/^(10000|[1-9]\d{0,3})$/.test(quantity)){
                    return message.error("发放量只能输入最大10000正整数")
                }
                goods.map((item)=>{
                    item.quantity = quantity;
                    if(['add'].includes(pageMethod)){
                        item.discount = discount; 
                    }
                })
                setGoods([...goods]);
                onChange([...goods]);
                break;
            case "DIRECT_REDUCTION":
                if(!deductionAmount && ['add'].includes(pageMethod)){
                    return message.error("请输入立减面额")
                }
                // {
                //     validator(_,value){
                //         if(!value) return Promise.resolve('');
                //         if(!/^(?!0$)[1-9]\d{0,4}(\.\d)?$/.test(value)) 
                //             return Promise.reject("请输入1至99999之间的数字，只允许保留小数点后一位");
                //         if(value > 99999) 
                //             return Promise.reject("请输入1至99999之间的数字，只允许保留小数点后一位");
                //         return Promise.resolve("")
                //     }
                // }
                if(!/^(?!0$)[1-9]\d{0,4}(\.\d)?$/.test(deductionAmount) && ['add'].includes(pageMethod)){
                    return message.error("请输入1至99999之间的数字，只允许保留小数点后一位")
                }
                if(deductionAmount > 99999){
                    return message.error("请输入1至99999之间的数字，只允许保留小数点后一位")
                }
                if(!quantity && ['add','edit'].includes(pageMethod)){
                    return message.error("请输入发放量")
                }
                if(!/^(10000|[1-9]\d{0,3})$/.test(quantity)){
                    return message.error("发放量只能输入最大10000正整数")
                }
                goods.map((item)=>{
                    item.quantity = quantity;
                    if( ['add'].includes(pageMethod)){
                        item.deductionAmount = deductionAmount;
                    }
                    
                })
                setGoods([...goods]);
                onChange([...goods]);

                break;
            case "FULL_REDUCTION":
                if(!thresholdAmount && ['add'].includes(pageMethod)){
                    return message.error("请输入券满额")
                }
                if(!/^([1-9]\d{0,4}|[1-9])$/.test(thresholdAmount) && ['add'].includes(pageMethod)){
                    return message.error("满额只能输入不大于99999的正整数")
                }
                if(!thresholdDeductionAmount && ['add'].includes(pageMethod)){
                    return message.error("请输入券减额")
                }
                if(!/^([1-9]\d{0,4}|[1-9])$/.test(thresholdDeductionAmount) && ['add'].includes(pageMethod)){
                    return message.error("减额只能输入不大于99999的正整数")
                }
                if(+thresholdDeductionAmount >= +thresholdAmount && ['add'].includes(pageMethod)){
                    return message.error("券满额必须大于券减额")
                }
                if(!quantity && ['add',"edit"].includes(pageMethod)){
                    return message.error("请输入发放量")
                }
                if(!/^(10000|[1-9]\d{0,3})$/.test(quantity)){
                    return message.error("发放量只能输入最大10000正整数")
                }
                goods.map((item)=>{
                    item.quantity = quantity;
                    if( ['add'].includes(pageMethod)){
                        item.thresholdAmount = thresholdAmount;
                        item.thresholdDeductionAmount = thresholdDeductionAmount;   
                    }
                   
                })
                setGoods([...goods]);
                onChange([...goods]);
                break;
            default:
                break;
        }
    }

    // 一键清空列表
    const clearListHandle = ()=>{
        Modal.confirm({
            title: '提示',
            content: '确认一键清空列表吗？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                setGoods([]);
                onChange([]);
                setSelectItem([]);
            },
        })
    }

    useEffect(()=>{
        // getList();
        if(value && value.length>0 && value[0].type === 'PRODUCT' && !once.current){
            getList(value.map((item)=>item.scopeValue))
            once.current = true
        } 
    },[value])

    return (<Fragment>
        <Space>
            <Button type="primary" disabled={ pageMethod !=='add'} onClick={()=>{
                setOpen(true);
            }}>添加商品</Button>
            已选数量{value?value.length:0}个
        </Space>
        <br />
        <div style={{display:'flex',flexDirection:'row',alignItems:'center',
            justifyContent:'space-between',
            width:'100%'
        }}>
            <div>
                {
                    type === "DISCOUNT" &&<Space style={{padding:'10px 0'}}>
                        <Input
                            disabled={isDisabled('discount')}
                            placeholder="批量输入折扣"
                            addonAfter="折" value={discount} onChange={(e)=>setdiscount(e.currentTarget.value)} 
                        />
                        <Input  
                            addonAfter="张"
                            placeholder="批量输入发放量"
                            disabled={isDisabled('quantity')}
                            value={quantity}
                            onChange={(e)=>setquantity(e.currentTarget.value)}
                        />
                        <Button disabled={!goods || goods.length===0 || pageMethod ==='look'} type="primary" onClick={()=>{
                            batchSet(type)
                        }}>批量填充</Button>
                    </Space>
                }
                
                {
                    type === "DIRECT_REDUCTION" &&<Space style={{padding:'10px 0'}}>
                        <Input  
                            disabled={isDisabled('deductionAmount')}
                            placeholder="批量输入立减金额"
                            addonBefore="¥" value={deductionAmount} 
                            onChange={(e)=>setdeductionAmount(e.currentTarget.value)}
                        />
                        <Input
                            addonAfter="张"
                            placeholder="批量输入发放量"
                            disabled={isDisabled('quantity')}
                            value={quantity}
                            onChange={(e)=>setquantity(e.currentTarget.value)}/>
                        <Button  disabled={!goods || goods.length===0 || pageMethod ==='look'}  type="primary" onClick={()=>{
                            batchSet(type)
                        }}>批量填充</Button>
                    </Space>
                }
                {
                    type === "FULL_REDUCTION" &&<Space style={{padding:'10px 0'}}>
                        满
                        <Input  
                            addonBefore="¥"
                            value={thresholdAmount} 
                            disabled={isDisabled('thresholdAmount')}
                            placeholder="批量输入券满额"
                            onChange={(e)=>setthresholdAmount(e.currentTarget.value)}
                        />
                        减
                        <Input
                            addonBefore="¥"
                            disabled={isDisabled('thresholdDeductionAmount')}
                            placeholder="批量输入券减额"
                            value={thresholdDeductionAmount}
                            onChange={(e)=>setthresholdDeductionAmount(e.currentTarget.value)}
                        />
                        <Input
                            addonAfter="张"
                            value={quantity} 
                            disabled={isDisabled('quantity')}
                            placeholder="批量输入发放量"
                            onChange={(e)=>setquantity(e.currentTarget.value)}
                        />
                        <Button  disabled={!goods || goods.length===0  || pageMethod ==='look'}  type="primary" onClick={()=>{
                            batchSet(type)
                        }}>批量填充</Button>
                    </Space>
                }  
            </div>
            
            <Button type="primary" disabled={isDisabled("btn")} onClick={clearListHandle}>一键清空列表</Button>
        </div>
      
        <AddGoodsModal 
            open={open}
            closeFn={(selects)=>{
                setOpen(false);
                if(selects && selects.length){
                   let arr =  selects.map((item)=>{
                        return {
                            scopeValue: item.id,
                            limitNum: maxLimit || 1,
                            name: item.name,
                            mainImage: item.mainImage,
                            highPrice: item.highPrice,
                            thresholdDeductionAmount: item.thresholdDeductionAmount,
                            deductionAmount: item.deductionAmount,
                            discount: item.discount,
                            thresholdAmount: item.thresholdAmount
                        }
                    })
                    setGoods([...goods,...arr])
                    onChange([...goods,...arr])
                    setSelectItem([...goods,...arr])
                }
            }}
            selectItem={selectItem}
        />
        <Table 
            dataSource={goods}
            columns={columnsObj[type] ||[]}
            pagination={false}
        />
    </Fragment>)
}

export default SelectGoods
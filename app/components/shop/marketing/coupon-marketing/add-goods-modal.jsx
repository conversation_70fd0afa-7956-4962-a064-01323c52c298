import request from "../../../utils/plugins/axios/request";

const {useState,useEffect,useRef,Fragment,useMemo} = React;
const {Button,Space,Modal,Table,Input,Row,Col,message} = antd;
// import {}
const {CopyOutlined} = icons;
// import copy from "../../../common/react/utils/copy-to-clipboard";
import copy from "../../../common/react/utils/copy-to-clipboard"
export default ({
    open,
    closeFn,
    selectItem,
})=>{
    const [dataSource,setDataSource] = useState([]);
    const [searchValue,setSearchValue] = useState('');
    const [selectedRows,setSelectedRows]= useState([]);
    const [selectedIdList,setSelectedIdList] = useState([]);
    const [pagination,setPagination] = useState({
        pageSize:10,
        size:1,
    })
    const [rowSelection,setrowSelection] = useState({
        onChange: (selectedIdList, selectedRows) => {
            setSelectedRows(selectedRows)
            setSelectedIdList(selectedIdList)
        },
        getCheckboxProps: (record) => {
            return {
                disabled: (selectItem||[]).map((item)=>String(item.scopeValue)).includes(String(record.id)),
                name: record.name,
            }
        },
        selectedRowKeys: selectedIdList,
        fixed: true,
        preserveSelectedRowKeys: true,
    })
    const getData = (pagination)=>{
        // api/items/paging/v2?shopId=179
        request({
            url:'/api/items/paging/v2?shopId=' + sessionStorage.shopId,
            method:'POST',
            data:{
                current:  pagination.page,
                size: pagination.pageSize,
                idOrName: searchValue
            }
        }).then(res=>{
            console.log("res:",res);
            setDataSource(res.data);
            setPagination({
                pageSize:res.size,
                total:res.total,
                current:res.current
            })
        })
    }

    const onOk = ()=>{
        if(selectedRows.length===0){
            return message.error("请选择商品")
        }
        closeFn(selectedRows)
    }

    useEffect(()=>{
        if(open){
            setPagination({
                pageSize:10,
                page:1,
            })
            // setSelectedIdList([...selectItem])
            getData({
                pageSize:10,
                page:1,
            })
            setrowSelection({
                onChange: (selectedIdList, selectedRows) => {
                    console.log(selectedIdList, selectedRows)
                    setSelectedRows(selectedRows)
                    setSelectedIdList(selectedIdList)
                },
                getCheckboxProps: (record) => {
                    return {
                        disabled: (selectItem||[]).map((item)=>String(item.scopeValue)).includes(String(record.id)),
                        name: record.name,
                    }
                },
                fixed: true,
                preserveSelectedRowKeys: true,
            })
        } else {
            setDataSource([]);
            setSearchValue(null);
            setSelectedRows([]);
            setSelectedIdList([])
        }
    },[open])
    return (<Modal
        title="全部商品"
        open={open}
        onCancel={()=>{
            closeFn();
        }}
        onOk={()=>{
            onOk()
        }}
        width={1000}
        okText={'选择'}
        cancelText={"取消"}
    >
        <Space style={{paddingBottom:'10px'}}>
            <Input.Search
                value={searchValue}
                placeholder={'请输入商品ID/名称'}
                onChange={(e)=>{
                    setSearchValue(e.currentTarget.value)
                }}
                onSearch={(e)=>{
                    console.log(e)
                    setSearchValue(e);
                    getData({
                        pageSize:10,
                        page:1,
                    })
                }}
            />
        </Space>

       <Table
            dataSource={dataSource}
            columns={[
                // { title: '全选', key: 'select', render: (row) => <Checkbox checked={row.checked} />, },
                { title: '商品名称/ID',render:(row)=>{
                    // return (<Row>
                    //     <Col> <img src={row.mainImage} style={{width:'50px',heigth:'50px'}}/>
                    //     </Col>
                    //    <Col>
                    //    <div>
                    //         <div>{row.name}</div>
                    //         <div>商品ID{row.id}</div>
                    //     </div>
                    //    </Col>

                    // </Row>)
                    return <div style={{display:'flex',flexDirection:'row',alignItems:'center'}}>
                        <img src={row.mainImage} style={{height:'50px',width:'50px',display:'flex'}} />
                        <div style={{marginLeft:'10px'}} onClick={()=>{
                            copy(row.id)
                            message.success("复制成功")
                        }}>
                            <div>{row.name||'商品名称'}</div>
                            <div>ID:{row.id}<CopyOutlined /> </div>
                        </div>
                    </div>
                }},
                { title: '商品原价', render:(row)=>{
                    return (<div>
                        {row.price/100}
                    </div>)
                }},
                { title: '商品库存', dataIndex: 'stockQuantity', key: 'stockQuantity' },
            ]}
            rowSelection={{
                selectedRowKeys:selectedIdList,
                ...rowSelection
            }}
            pagination={{
                ...pagination,
                onChange(page,pageSize){
                    console.log(page,pageSize)
                    getData({
                        page,
                        pageSize
                    })
                }
            }}
            rowKey="id"
            scroll={{
                x:1000,
                y:400
            }}
        />
    </Modal>)
}

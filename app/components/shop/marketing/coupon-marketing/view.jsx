
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,Tabs,Form,Input,Radio,Descriptions,DatePicker,Space,Select,Slider, Tooltip} = antd;
import UserTypeSelector from "./UserTypeSelector"
import SelectGoods from "./select-goods"
import ShowScale from "./show-scale";
import SliderInput from "./slider-input"
import {valitorItems,createAcitivityName,createDefaultTime} from "../coupon_main/common"
const { RangePicker } = DatePicker;
const {QuestionCircleOutlined} = icons;

const formTailLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 14, offset: 3 },
};
export const Index = () => {
    let type = __lib__.getParam("type");
    let id = __lib__.getParam("id")
    let fromPage =  __lib__.getParam("fromPage");
    const addBol = type === 'add';
    const lookBol = type === 'look';
    const editBol = type === 'edit';
    const isFromGroup = fromPage === 'group';

    const [form] = Form.useForm();
    const configs = [
        { id: "SHOP_PURCHASER", label: '拼团购成团成员', description: '用户参与了拼团购类型的营销活动，并开团成功' },
        { id: "PRODUCT_PURCHASER", label: '分享购成团成员',method:'PRODUCT', description: '用户参与了分享购类型的营销活动，并开团成功' },
    ]
    const userScope = Form.useWatch('userScope', form);
    const method = Form.useWatch('method',form)
    const range = Form.useWatch('type',form)
    const useType = Form.useWatch('usageTimeType',form)

    const idsList = useRef();

    const onFinish = (values)=>{
        if(values.customUseTime){
            values.customUseStartTime = values.customUseTime[0].valueOf();
            values.customUseEndTime = values.customUseTime[1].valueOf();
            delete values.customUseTime
        }
        // const scopeList = [];
        if(values.type === 'SHOP'){
            values.scopeList = [
                {
                    type:'SHOP',
                    method:values.method,
                    deductionAmount:values.deductionAmount,
                    discount:values.discount,
                    thresholdAmount:values.thresholdAmount,
                    thresholdDeductionAmount:values.thresholdDeductionAmount,
                    quantity:values.quantity,
                    limitNum:values.limitNum,
                    id:editBol?idsList.current[0]:null
                }
            ]
        } else{
            values.scopeList && values.scopeList.map((item,index)=>{
                // item.scopeValue = item.scopeValue;
                //  type:'SHOP',

                item.type = values.type;
                item.method = values.method;
                if(editBol){
                    item.id = idsList.current[index];
                }
              
            })
            console.log('values.scopeList:',values.scopeList)
        }
        values.type = "RE_PURCHASE" // 复购券
        if(editBol){
            values.id = id;
        }
        request({
            url:addBol?'/mall-admin/api/couponActivity/create':'/mall-admin/api/couponActivity/update',
            method: 'POST',
            data:{...values},
            success:(res)=>{
                message.success(addBol?"创建成功":'保存成功')
                setTimeout(()=>{
                    // history.back();
                   // window.close();
                   location.href = "/seller/marketing/coupon-main?tabIndex=item-2"
                },1000)
            }
        })
    }

    const getDetail = ()=>{
        request({
            url: `/mall-admin/api/couponActivity/detail?id=${id}`,
            method:'GET',
            success(res){
                if(res.customUseStartTime || res.customUseEndTime){
                    res.customUseTime = [moment(res.customUseStartTime),moment(res.customUseEndTime)]
                    delete res.customUseStartTime
                    delete res.customUseEndTime
                }
                // 店铺券时，数据处理
                if(res.scopeList && res.scopeList[0] && res.scopeList[0].type === 'SHOP'){
                    res = {...res,
                        type:res.scopeList[0].type,
                        method:res.scopeList[0].method,
                        deductionAmount:res.scopeList[0].deductionAmount,
                        discount:res.scopeList[0].discount,
                        thresholdAmount:res.scopeList[0].thresholdAmount,
                        thresholdDeductionAmount:res.scopeList[0].thresholdDeductionAmount,
                        quantity:res.scopeList[0].quantity,
                        limitNum:res.scopeList[0].limitNum,
                    }
                } else {
                    res.type = "PRODUCT"
                    res.method = res.scopeList[0].method;
                }
                idsList.current = res.scopeList.map((item)=>item.id);

                form.setFieldsValue({...res})
            }
        })
    }

    useEffect(()=>{
        if(editBol || lookBol){
            getDetail()
        }
    },[])
        

    return <Fragment>
        
        <Form 
            form={form} 
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 14 }}
            onFinish={onFinish}
            // ini
            // SHOP_PURCHASER
            initialValues={addBol?{
                userScope:'SHOP_PURCHASER',
                usageTimeType:'LIME',
                type:'SHOP',
                method:'DIRECT_REDUCTION',
                name: createAcitivityName("RE_PURCHASE"),
                customUseTime: createDefaultTime(),
                limitNum:30
            }:{}}
        >
            <Descriptions title="基础规则" items={[]} />
            <Form.Item label={'人群选择'} name='userScope'  wrapperCol={{span:22}}
              rules={[{required:true,message:'人群选择是必选的'}]}
            >
                <UserTypeSelector configs={configs}  disabled={editBol || lookBol || isFromGroup} onChange={(val)=>{
                    let type = configs.filter(item=>item.id===val)[0].method;
                    if(type){
                        form.setFieldsValue({type:type})
                    }
                }}/>
            </Form.Item>
            <Form.Item label={'人群规模'} name='userScope'>
                <ShowScale configs={configs} />
            </Form.Item>
            <Form.Item label={'活动名称'} name='name'
                rules={[{required:true,message:'活动名称是必填的'}]}
            >
                <Input  maxLength={30} showCount  disabled={editBol || lookBol} />
            </Form.Item>
            <Form.Item label={'使用时间'} name='usageTimeType'
                rules={[{required:true,message:'使用时间是必选的'}]}
            >
                <Radio.Group
                    options={[
                        { value: "LIME", label: '限制有效天数' },
                        { value: "CUSTOM", label: '自定义时间' },
                    ]}
                    disabled={editBol || lookBol}
                    ></Radio.Group>
            </Form.Item>
            {
                useType === "LIME" &&  
                <div style={{display:'flex',flexDirection:'row',alignItems:'center',alignItems:'center',
                    marginLeft:'12.4%'
                }}>
                    <Form.Item  name='validDays' {...formTailLayout}
                          rules={[{required:true,message:'有效天数是必填的'},
                            {
                                pattern:/^[1-9]\d{0,2}$/,
                                message:'有效天数只能输入不大于999的正整数'
                            },
                        ]}
                    >
                        <Input style={{width:'100px',marginRight:'10px'}} addonAfter="天"  disabled={editBol || lookBol} />
                    </Form.Item>
                    <div style={{marginBottom:'24px',marginLeft:'10px'}}>可使用</div> 
                </div>
            }
           {
                useType === "CUSTOM" &&  <Form.Item {...formTailLayout} name={'customUseTime'}
                    rules={[
                        {required:true,message:'使用时间是必选的'},
                        {
                            validator(rule,value){
                                if(!value){
                                    return Promise.resolve("")
                                }
                                if(value.length!==2){
                                    return Promise.reject("使用时间是必选的")
                                }
                                const start =value[0].valueOf();
                                const end = value[1].valueOf();
                                const tamp = end - start;
                                if((tamp / 1000) > 999 * 24 * 60 * 60){
                                    return Promise.reject("时间最大跨度999天");
                                }
                                return Promise.resolve("")
                            }
                        }
                    ]}
                >
                    <RangePicker showTime  disabled={editBol || lookBol} />
                </Form.Item>
            }
            <Descriptions title="优惠设置" items={[]} />
            <Form.Item label="优惠范围" name='type'
             rules={[{required:true,message:'优惠范围是必选的'}]}
            >
                <Radio.Group
                    options={["SHOP_PURCHASER","RECLAIMED_USER","SHOP_NEW_USER","ALL_USER"].includes(userScope)?[
                        {
                            value: "SHOP", label:
                                <Tooltip title="全店铺通用指无需选择特定的商品,店铺所有商品都参加">
                                    <span style={{ display: "flex", alignItems: "center" }}>
                                        全店通用券
                                        <QuestionCircleOutlined style={{ marginLeft: "4px" }}/>
                                    </span>
                                </Tooltip>
                        },
                        { value: "PRODUCT", label: '商品券'},
                    ]:[ { value: "PRODUCT", label: '商品券'},]}
                    disabled={editBol || lookBol}
                    onChange={(e)=>{
                        if(e.target.value === "SHOP"){
                            form.setFieldsValue({scopeList:[]})
                        }
                    }}
                ></Radio.Group>
            </Form.Item>
            <Form.Item label={'优惠方式'} name='method'
                rules={[{required:true,message:'优惠方式是必选的'}]}
            >
                <Radio.Group
                    options={[
                        { value: "DIRECT_REDUCTION", label: '立减' },
                        { value: "FULL_REDUCTION", label: '满减' },
                        { value: "DISCOUNT", label: '折扣' },
                    ]}
                    disabled={editBol || lookBol} 
                   
                    ></Radio.Group>
            </Form.Item>
            {
               range==='SHOP' && method === "DIRECT_REDUCTION" && <Fragment>
                    <Form.Item label="立减面额" name="deductionAmount" wrapperCol={{span:10}}
                         rules={[{required:true,message:'立减面额是必填的'},
                        {
                            validator(_,value){
                                if(!value) return Promise.resolve('');
                                if(!/^(?!0$)[1-9]\d{0,4}(\.\d)?$/.test(value)) 
                                    return Promise.reject("请输入1至99999之间的数字，只允许保留小数点后一位");
                                if(value > 99999) 
                                    return Promise.reject("请输入1至99999之间的数字，只允许保留小数点后一位");
                                return Promise.resolve("")
                            }
                        }]}
                    >
                        <Input addonBefore="¥"  disabled={editBol || lookBol} />
                    </Form.Item>
                    <Form.Item label="发放量" name="quantity" extra='优惠券创建后，发放量只能增加不能减少'
                        wrapperCol={{span:10}}
                        rules={[{required:true,message:'发放量是必填的'},
                            {
                                pattern:/^(10000|[1-9]\d{0,3})$/,
                                message:'只能输入最大10000正整数'
                            },
                        ]}
                    >
                        <Input addonAfter="张"  disabled={lookBol} />
                    </Form.Item>
                    <Form.Item label="每人限领" name="limitNum" extra='1-30张'
                        wrapperCol={{span:8}}
                        rules={[{required:true,message:'每人限领是必填的'},{
                            pattern:/^[1-9]\d*$/,
                            message:'只能输入正整数'
                        }]}
                    >
                        <SliderInput
                            min={1}
                            max={30}
                            // onChange={onChange}
                            disabled={editBol || lookBol} 
                            value={typeof inputValue === 'number' ? inputValue : 0}
                        />
                    </Form.Item>
                </Fragment>
            }
            {/* {满减 } */}
            {
               range==='SHOP' && method === "FULL_REDUCTION"  &&
                <Fragment> 
                    <Form.Item label="满减面额" required={true}  >
                        <Space aligns='center'>
                            满
                            <Form.Item
                                name="thresholdAmount"
                                noStyle
                                rules={[
                                    {
                                        pattern:/^([1-9]\d{0,4}|[1-9])$/,
                                        message:'满额只能输入不大于99999的正整数'
                                    },
                                    {
                                        validator(_,value){
                                            const thresholdAmount = form.getFieldValue("thresholdAmount")
                                            const thresholdDeductionAmount = form.getFieldValue("thresholdDeductionAmount")
                                            if(!thresholdAmount){
                                                return Promise.reject('满额是必填的') 
                                            }
                                            if(Number(thresholdAmount)<=Number(thresholdDeductionAmount)){
                                                console.log(form.getFieldError('thresholdDeductionAmount').length)
                                                if(form.getFieldError('thresholdDeductionAmount').length){
                                                    // form.validateFields(['thresholdAmount'])
                                                    return Promise.resolve("")
                                                }
                                                return Promise.reject('满额必须大于减额') 
                                            } else {
                                                if(form.getFieldError('thresholdDeductionAmount').length){
                                                    form.validateFields(['thresholdDeductionAmount'])
                                                }
                                            }
                                            return Promise.resolve("")
                                        }
                                    }
                                ]}
                                  
                                style={{ display: 'inline-block', width: '70px' }}
                            >
                                <Input addonBefore="¥" placeholder="请输入"  disabled={editBol || lookBol}  />
                            </Form.Item>
                            减
                            <Form.Item
                                name="thresholdDeductionAmount"
                                noStyle
                                rules={[
                                    {
                                        pattern:/^([1-9]\d{0,4}|[1-9])$/,
                                        message:'减额只能输入不大于99999的正整数'
                                    },{
                                        validator(_,value){
                                            const thresholdAmount = form.getFieldValue("thresholdAmount")
                                            const thresholdDeductionAmount = form.getFieldValue("thresholdDeductionAmount")
                                            
                                            if(!thresholdDeductionAmount){
                                                return Promise.reject('减额是必填的') 
                                            }
                                            if(Number(thresholdAmount)<=Number(thresholdDeductionAmount)){
                                                if(form.getFieldError('thresholdAmount').length){
                                                    // form.validateFields(['thresholdAmount'])
                                                    return Promise.resolve("")
                                                }
                                                return Promise.reject('满额必须大于减额') 
                                            } else {
                                                if(form.getFieldError('thresholdAmount').length){
                                                    form.validateFields(['thresholdAmount'])
                                                }
                                            }
                                            return Promise.resolve("")
                                        }
                                    }
                                ]}
                                style={{ display: 'inline-block', width: '70px' , margin: '0 8px' }}
                            >
                                <Input  addonBefore="¥" placeholder="请输入"  disabled={editBol || lookBol}  />
                            </Form.Item>   
                        </Space>
                        
                    </Form.Item>
                    <Form.Item label="发放量"  wrapperCol={{span:8}} name="quantity" extra='优惠券创建后，发放量只能增加不能减少'
                        rules={[
                            {required:true,message:'发放量是必填的'},
                            {
                                pattern:/^(10000|[1-9]\d{0,3})$/,
                                message:'只能输入最大10000正整数'
                            },
                        ]}
                    >
                        <Input addonAfter="张" disabled={lookBol}  />
                    </Form.Item>
                    <Form.Item label="每人限领"  wrapperCol={{span:8}} name="limitNum" extra="1-30张"
                        rules={[{required:true,message:'每人限领是必填的'},{
                            pattern:/^[1-9]\d*$/,
                            message:'只能输入正整数'
                        }]}
                    >
                        <SliderInput
                            min={1}
                            max={30}
                            // onChange={onChange}
                            disabled={editBol || lookBol} 
                            value={typeof inputValue === 'number' ? inputValue : 0}
                        />
                    </Form.Item>
                </Fragment>
            }
            {
               range==='SHOP' && method === "DISCOUNT" && <Fragment>
                    <Form.Item label="折扣" name='discount' wrapperCol={{span:8}}
                        rules={[{required:true,message:'折扣是必填的'},{
                            pattern: /^(0\.\d|[1-9](\.\d)?)$/,
                            message:'折扣只能输入大于0小于10，且最多一位小数的正数'
                        }]}
                     >
                        <Input addonAfter="折"  disabled={editBol || lookBol} />
                    </Form.Item>
                    <Form.Item label="发放量" wrapperCol={{span:8}} name='quantity' extra='优惠券创建后，发放量只能增加不能减少'
                        rules={[
                            {required:true,message:'发放量是必填的'},
                            {
                                pattern:/^(10000|[1-9]\d{0,3})$/,
                                message:'只能输入最大10000正整数'
                            },
                        ]}
                    >
                        <Input addonAfter="张"  disabled={lookBol} />
                    </Form.Item>
                    <Form.Item label="每人限领" wrapperCol={{span:8}} name='limitNum' extra="1-30张"
                        rules={[{required:true,message:'每人限领是必填的'},{
                            pattern:/^[1-9]\d*$/,
                            message:'只能输入正整数'
                        }]}
                    >
                        <SliderInput
                            min={1}
                            max={30}
                            disabled={editBol || lookBol}
                            value={typeof inputValue === 'number' ? inputValue : 0}
                        />
                    </Form.Item>
                </Fragment>
            }
           {
            range ==='PRODUCT' &&  <Form.Item label={'选择商品'} name='scopeList'
                wrapperCol={{span:22}}
                required={true}
                rules={[{
                        validator(rule,value){
                            if(!value){
                                return Promise.resolve("请添加商品")
                            }
                            if(value.length===0){
                                return Promise.reject("请添加商品")
                            }
                            const errors = valitorItems(value,method);
                            if(errors.length === 0){
                                return Promise.resolve("")
                            } else {
                                const str = errors.join("；");
                                return Promise.reject(str)
                            }
                        }
                    }
                ]}
            >
                <SelectGoods type={method}  maxLimit={30} pageMethod={type}/>
            </Form.Item>
           }
            {
                (editBol || addBol) && <Form.Item>
                    <Button type="primary" htmlType="submit">{type==='add'?'创建':'保存'}</Button>
                </Form.Item>
            }
        </Form>
    </Fragment>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("coupon-re-shop"));
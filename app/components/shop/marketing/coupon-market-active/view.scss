.user-type-selector {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .user-type-card {
        border: 1px solid #ccc;
        padding: 15px;
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }
    
    .user-type-card.selected {
        background-color: #e0f7fa;
        border-color: #00bcd4;
    }
    
    .user-type-card h3 {
        margin: 0;
        font-size: 18px;
    }
    
    .user-type-card p {
        margin: 5px 0 0;
        font-size: 14px;
        color: #666;
    }
  }
  
  
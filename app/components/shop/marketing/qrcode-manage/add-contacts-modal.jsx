import request from "../../../utils/plugins/axios/request";

const {useState,useEffect,useRef,Fragment} = React;
const { Modal, Tabs, Table, Input, Button, Radio,Checkbox,Row,Space,message }= antd;
const {PlusCircleFilled} = icons;
const { TabPane } = Tabs;



const SelectContactsModal = ({onAdd}) => {
    const [visible, setVisible] = useState(false);
    const [tabKey, setTabKey] = useState('1');
    const [searchText, setSearchText] = useState('');
    const [outUserId,setOutUserId] = useState();
    const [outUserName,setOutUserName] = useState();
    const [outUserId1,setOutUserId1] = useState();
    const [outUserName1,setOutUserName1] = useState();
    const [allList,setAllList] = useState([]);
    const [selectList,setSelectList] = useState([]);
    const [total,setTotal] = useState(null)
    const [autoChecked,setAutoChecked] = useState(false);
    const [selectedRows,setSelectedRows]= useState([]);
    const [selectedIdList,setSelectedIdList] = useState([])
    const selects = useRef([]);
    const [pagination,setPagination] = useState({
        pageSize:10,
        size:1,
    })
    const showModal = () => {
        setVisible(true);
    };

    const handleOk = () => {
        // if(s)
        if(!selectList.length){
            return message.error("请选择数据并去已选页面填写必要数据")
        }
        for(let i=0;i<selectList.length;i++){
            if(!selectList[i].dailyCustomerLimit && selectList[i].dailyCustomerLimit!==0){
                return message.error(`第${i+1}项请添加今日添加上限`)
            }
            if(selectList[i].dailyCustomerLimit >= 100000000){
                return message.error(`第${i+1}项不允许输入超过8位的数字`)
            }
        }
        // setVisible(false);
        onAdd(selectList);
        handleCancel();
    };

    const handleCancel = () => {
        setVisible(false);
        setAllList([]);
        setSelectList([]);
        setSelectedIdList([])
        setSelectedRows([])
        setOutUserId(null);
        setOutUserId1(null);
        setTabKey('1')
    };

    const handleSearch = (pages,) => {
        let defaultPage = pages ||{
            current: 1,
            pageSize: 20,
        };
       
        fetch(defaultPage, {
            outUserId,
        });
        // 执行搜索逻辑
    };

    const handleReset = () => {
        // setSearchText('');
        setOutUserId('');
        setOutUserName('');
        // 重置搜索条件
        fetch({
            current: 1,
            pageSize: 20,
        }, {
            outUserId: null,
        });
    };

    const fetch = (pages,searchs)=>{
        request({
            url:'/mall-admin/api/family/user/qr/external/page',
            method:'post',
            data:{
                currentPage: pages.current,
                pageSize: pages.pageSize,
                ...searchs
            }
        }).then(res=>{
            setAllList(res.dataList);
            setPagination({
                current: res.page.currentPage,
                total: res.page.totalCount,
                pageSize: res.page.pageSize,
                showTotal: total => `共${total}条`,
            })
        })
    }

    let rowSelection = {
        onChange: (selectedIdList, selectedRows) => {
            setSelectedRows(selectedRows)
            setSelectedIdList(selectedIdList)
        },
        selectedRowKeys:selectedIdList,
        fixed: true,
        preserveSelectedRowKeys: true,
    }

    const handleSearch1 = ()=>{
        // selects.current = selectList
        const arr = selects.current.filter(item=>{
            return item.outUserId.includes(outUserId1)
        })
        setSelectList(arr);
    }

    const handleReset1 = ()=>{
        setOutUserId1('');
        setOutUserName1('');
        setSelectList([...selects.current]);
    }

    useEffect(()=>{
        if(visible){
            fetch({current:1,pageSize:10},{})
        }
      
    },[visible])

    return (
        <Fragment>
            <Button type="primary" onClick={showModal}>
                添加企微员工
            </Button>
            <Modal
                title="选择企微员工"
                visible={visible}
                width={1000}
                okText={'确定'}
                cancelText={'取消'}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <h3>已选择企微员工数量：{selectList.length}</h3>
                <Tabs activeKey={tabKey} onChange={(key) =>{
                    setTabKey(key)
                    if(key==2){
                        const arr = selectedRows.filter(item=>{
                            return !selectList.map(item=>item.outUserId).includes(item.outUserId)
                        }).map(item=>{
                            console.log("item:",item)
                            item.dailyCustomerLimit = 0;
                            return item;
                        })
                        setSelectList([...selectList,...arr]);
                        selects.current = [...selectList,...arr]
                    }
                }}>
                    <TabPane tab="待选" key="1">
                        <Row>
                            <Space>
                                <Input
                                    placeholder="企微员工ID"
                                    value={outUserId}
                                    onSearch={handleSearch}
                                    onChange={(e)=>{
                                        setOutUserId(e.currentTarget.value)
                                    }}
                                    style={{ marginBottom: 8 }}
                                />
                                <Button type="primary" onClick={()=>{
                                    handleSearch()
                                }}>
                                查询
                                </Button>
                                <Button onClick={handleReset}>
                                重置
                                </Button>  
                            </Space>
                            
                        </Row>
                    
                        <Table
                            dataSource={allList}
                            columns={[
                                // { title: '全选', key: 'select', render: (row) => <Checkbox checked={row.checked} />, },
                                { title: '企微员工ID', dataIndex: 'outUserId', key: 'outUserId' },
                                // { title: '企微员工名称', dataIndex: 'contactName', key: 'contactName' },
                                { title: '今日添加客户数', dataIndex: 'countToDay', key: 'countToDay' },
                                { title: '添加客户总数', dataIndex: 'customerCount', key: 'customerCount' },
                            ]}
                            rowSelection={rowSelection}
                            rowKey="outUserId"
                            pagination={{
                                ...pagination,
                                onChange(page,pageSize){
                                    fetch({
                                        current:page,
                                        pageSize
                                    },{
                                        outUserId,
                                        outUserName
                                    })
                                },
                                
                            }}
                        />
                    </TabPane>
                    <TabPane tab="已选" key="2">
                        {/* 已选项卡内容 */}
                        <Row>
                            <Space>
                                 <Input
                                    placeholder="企微员工ID"
                                    value={outUserId1}
                                    // onSearch={handleSearch}
                                    onChange={(e)=>{
                                        setOutUserId1(e.currentTarget.value)
                                    }}
                                    style={{ marginBottom: 8 }}
                                />
                                <Button type="primary" onClick={handleSearch1}>
                                查询
                                </Button>
                                <Button type="primary" onClick={handleReset1}>
                                重置
                                </Button>
                            </Space>
                        </Row>
                    
                        <Table
                            dataSource={selectList}
                            selectedRowKeys={'outUserId'}
                            columns={[
                                // { title: '全选', key: 'select', render: () => <Radio />, },
                                { title: '企微员工ID', dataIndex: 'outUserId', key: 'outUserId' },
                                // { title: '企微员工名称', dataIndex: 'outUserName', key: 'contactName' },
                                { title: '今日添加客户数', dataIndex: 'countToDay', key: 'countToDay' },
                                { title: '添加客户总数', dataIndex: 'customerCount', key: 'customerCount' },
                                { title: <span >
                                    今日添加上限
                                    <Button disabled={autoChecked} icon={<PlusCircleFilled />}
                                        onClick={()=>{
                                            if(!selectList[0]){
                                                return message.error("请添加已选数据")
                                            }
                                            if(!selectList[0].dailyCustomerLimit){
                                                return message.error("请给第一项的今日上限填值")
                                            }
                                            selectList.map((item)=>{
                                                item.dailyCustomerLimit = selectList[0].dailyCustomerLimit
                                            })
                                            setSelectList([...selectList])
                                        }}
                                        style={{color:autoChecked?'#ccc':'#1890ff',marginLeft:'8px',border:'none'}} 
                                    >

                                    </Button>
                                </span>,
                                render: (row) => {
                                    console.log("row:",row)
                                    return (
                                        <Input disabled={autoChecked} 
                                            value={row.dailyCustomerLimit} 
                                            onChange={(e)=>{
                                                // setSelectList()
                                                // row.dailyCustomerLimit = parseInt(e.target.value) || null;
                                                row.dailyCustomerLimit = parseInt(e.target.value).toString() === "NaN"?null:parseInt(e.target.value)
                                                setSelectList([...selectList])
                                            }} 
                                        />
                                    )
                                }
                               },
                            ]}
                        />
                        <Row>
                            <Space style={{paddingTop:'10px'}}>
                                <Checkbox  
                                    onChange={(e)=>{
                                        setAutoChecked(e.target.checked)
                                    }}
                                    checked={autoChecked}
                                />
                                自动分配客户添加上限:
                                <Input value={total}
                                    disabled={!autoChecked}
                                    onChange={(e)=>{
                                        const val = parseInt(e.currentTarget.value) || null
                                        if(!val){
                                            setTotal(null);
                                        }
                                        if(e.currentTarget.value)
                                        setTotal(parseInt(val).toString() === "NaN"?null:parseInt(val))
                                        if(parseInt(val) === NaN) return;
                                        const a = Math.floor((val||0)/selectList.length);
                                        if(parseInt(val)>= 100000000){
                                            return message.error("不允许输入超过8位的数字")
                                        }
                                        if(a<1){
                                            return
                                        }
                                        const b = Math.floor((val||0)%selectList.length);
                                        selectList.map((item)=>{
                                            item.dailyCustomerLimit = a;
                                        })
                                        selectList[selectList.length-1].dailyCustomerLimit = a+b;
                                        setSelectList([...selectList])
                                    }}
                                />
                            </Space>
                            
                        </Row>
                    </TabPane>
                </Tabs>
            </Modal>
        </Fragment>
    );
};

export default SelectContactsModal;
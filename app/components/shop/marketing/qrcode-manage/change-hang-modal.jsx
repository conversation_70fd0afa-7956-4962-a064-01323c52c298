
const { Spa, SpaConfig<PERSON>rovider,
DTEditForm } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message} = antd;
const {ReloadOutlined} = icons
export default ({open,closeFn,rows,currentIndex,maxLength})=>{
    const ref = useRef(null);
    // const [open,setOpen] = useState(false);

    const onOk = ()=>{
        // /mall-admin/api/family/bulletScreen/add

        ref.current.form.validateFields().then(values=>{
            const {sort} = values;
            console.log("values:",values)
            const element = rows.splice(currentIndex, 1)[0]; // splice返回被删除元素的数组
            // 将元素插入到第b个位置
            rows.splice(sort-1, 0, element);
            request({
                url:'/mall-admin/api/family/user/qr/refreshSort',
                method:'POST',
                data:{
                    data:rows
                }
            }).then(res=>{
                // ref.current.form.reset()
                message.success("修改成功")
                closeFn(true);
            })
        })
       
    }

    useEffect(()=>{
        console.log(ref)
    },[])

    return (<Fragment>
        <Modal
            title="调整行数"
            open={open}
            onOk={onOk}
            okText="确认"
            cancelText="取消"
            onCancel={()=>{
                // setOpen(false)
                closeFn()
            }}
        >
         
            <DTEditForm 
                layout={{mode: "appoint",colNum: 1, }}
                ref={ref}
                configs={[
                        {
                            type:'INPUT',
                            fProps:{
                                name:'sort',
                                label:'调整至',
                                required:true,
                                rules:[{
                                    pattern:/^[1-9]\d*$/,message:'请输入正整数'
                                },{
                                    validator(_,value){
                                        if(!value){
                                            return Promise.resolve("请输入调整行数")
                                        }
                                    //    pattern:/^[1-9]\d{0,2}$/
                                        if(value<=0){
                                            return Promise.reject("不可小于第一行")
                                        }
                                        if(value>maxLength){
                                            return Promise.reject("不可大于当前最大行")
                                        }
                                        return Promise.resolve("")
                                    }
                                }]
                            },
                            cProps:{
                                addonAfter:"行"
                            }
                        }
                ]}
            />
        </Modal>
        
    </Fragment>)
}
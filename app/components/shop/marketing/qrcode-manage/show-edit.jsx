
import request from "../../../utils/plugins/axios/request";
const {useRef, Fragment,useState,useEffect} = React;
const { Space,Switch,message,Modal,Input,Button} = antd;
const {DTEditForm, Spa, SpaConfigProvider} = dtComponents;
const {DownOutlined,EditOutlined} =icons;

export default ({row,rowkey,load,dataSource}) => {
    const [edit,setEdit] = useState(false)
    const [value,setValue] = useState(row[rowkey])

    useEffect(()=>{
        setValue(row[rowkey])
    },[row[rowkey]])
    return <Fragment>
        {
            !edit?
            <Space>
                {row[rowkey]}
                <EditOutlined style={{color:'blue'}} onClick={()=>{
                    setEdit(true);
                }} />
            </Space>
          
            :
            <Space>
                <Input 
                    value={value} 
                    onChange={(e)=>{
                        setValue(e.target.value);
                    }} 
                />
                <Button type='link' size='small' onClick={()=>{
                    // if(row[rowkey])
                    // if(value<0){
                    //     return message.error("必须是大于等于0")
                    // }
                    // if(!/^\d+$/.test(value)){
                    //     return message.error("必须是大于等于0的整数")
                    // }
                    // console.log(row.customerCountDay)
                    if(!/^\d*$/.test(value)){
                        return message.error("请输入整数")
                    }
                    if(value < row.customerCountDay){
                        return message.error("必须大于等于当日添加客户数")
                    }
                    if(value > 100000){
                        return message.error("每日客户添加上限不可大于100000")
                    }
                    row[rowkey] = value;
                    request({
                        url:'/mall-admin/api/family/user/qr/updateLimit',
                        type:'post',
                        data:{
                            id: row.id,
                            dailyCustomerLimit: value
                        },
                        success:()=>{
                            message.success("修改成功")
                            load && load()
                            setEdit(false)
                        }
                    })
                }}>保存</Button>
                <Button type='link' size='small' onClick={()=>{
                   setEdit(false)
                }}>取消</Button>
            </Space>
        }
    </Fragment>
}
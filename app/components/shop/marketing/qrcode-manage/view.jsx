
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import { getParam } from "../../../items/seller/add-edit-module/util"
import request from "../../../utils/plugins/axios/request";
// import "./view.less"
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Input,Table,Switch,Modal,message,Space,Descriptions} = antd
import UpdatePrice from "./show-edit"
import AddContactsModal from "./add-contacts-modal"
import ChangeHangModal from "./change-hang-modal";
export const Index = () => {
    const searchListRef = useRef()
    const [changeModal,setChangeModal] = useState(false);
    const [dataSource,setDataSource] = useState([])
    const [currentIndex,setcurrentIndex] = useState(0);
    const [total,setTotal] = useState({
        countDailyCustomerLimitInToDay:0,
        countToDay:0
    });
  const [height, setHeight] = useState(0);

  useEffect(() => {
    const updateHeight = () => {
      if (searchListRef.current) {
        setHeight(searchListRef.current.offsetHeight);
      }
    };

    const observer = new ResizeObserver(updateHeight);
    if (searchListRef.current) {
      observer.observe(searchListRef.current);
    }

    window.addEventListener("resize", updateHeight); // 监听窗口变化

    return () => {
      observer.disconnect();
      window.removeEventListener("resize", updateHeight);
    };
  }, []);
  console.log("height",height)
    const getData = ()=>{
        request({
            url:'/mall-admin/api/family/user/qr/info',
            method:'POST',
        }).then(res=>{
            setTotal(res);
        })
    }

      useEffect(()=>{
        getData();
        load()
      },[])

    const load = ()=>{
        request({
            url:'/mall-admin/api/family/user/qr/page',
            method:'POST',
            data:{}
        }).then(res=>{
            // setTotal(res);
            setDataSource(res);
        })
    }

    const deleteQrcode= (id)=>{
        request({
            url:'/mall-admin/api/family/user/qr/delete',
            data:{
                id,
            },
            method:'post'
        }).then(()=>{
            message.success("删除成功");
            // searchListRef.current.load();
            load();
        })
    }
    const tableColumn = [
        {
            title: '行号',
            // dataIndex: 'customerUserId',
            // key: 'customerUserId',
            render:(row,_,index)=>{
                return index + 1
            },
            width:80,
        },
        {
            title:'企微员工ID',
            dataIndex: 'outUserId',
            key: 'outUserId',
            width:150,
        },
        {
            title: '企微员工名称',
            dataIndex: 'outUserName',
            key: 'outUserName',
            width:120,
        },
        {
            title: '每日添加客户上限',
            // dataIndex: 'dailyCustomerLimit',
            // key: 'dailyCustomerLimit',
            width:260,
            render:(row)=>{
                return <Fragment>
                    <UpdatePrice
                        row={row}
                        dataSource={dataSource}
                        rowkey="dailyCustomerLimit"
                        load={() => {
                            load()
                        }}
                    />
                </Fragment>
            }
        },
        {
            title: '当日添加客户数',
            dataIndex: 'customerCountDay',
            key: 'customerCountDay',
            width:150,
        },
        {
            title: '累计添加客户数',
             width:150,
            dataIndex: 'customerCountTotal',
            key: 'customerCountTotal',
        },
        {
            title: '操作',
            width:180,
            fixed:'right',
            // dataIndex: 'customerCountTotal',
            // key: 'customerCountTotal',
            render:(row,_,index)=>{
                return <Fragment>
                    <Button type='link' onClick={()=>{
                        setChangeModal(true)
                        setcurrentIndex(index)
                    }}>调整行数</Button>
                    <Button type='link' onClick={()=>{
                        Modal.confirm({
                            title:'确认',
                            content:'确定移除吗？',
                            onOk(){
                               deleteQrcode(row.id)
                            },
                            okText:'确定',
                            cancelText:'取消'
                        })

                    }}>移除</Button>
                </Fragment>
            }
        },
    ]

    const add = (datas)=>{
        // if()
        request({
            url:'/mall-admin/api/family/user/qr/batchAdd',
            method:'post',
            data:{
                dataList: datas.map((item)=>{
                    return {id:item.id,dailyCustomerLimit:item.dailyCustomerLimit}
                })
            },
            needMask:true
        }).then(res=>{
            load();
            if(res.failOutUserIds.length > 0) {
                Modal.confirm({
                    title:'以下微信ID无法创建二维码,请检查',
                    content:(<Fragment>
                        <div>企微员工ID:</div>
                        {res.failOutUserIds.map(item=><div>{item}</div>)}
                        </Fragment>),
                        okText:'确认',
                        cancelText:'取消'
                })
            } else {
                message.success('添加成功')
            }
        })
    }
    return <Fragment>
        <div style={{display:'flex',flexDirection:'column',height:'100%'}}>
          <div>
            <div>
                <Descriptions title="企微员工添加"></Descriptions>
            </div>
            <div style={{marginBottom:12}}>
                <AddContactsModal onAdd={(datas)=>{
                    add(datas)
                }}  />
            </div>
          </div>
      <div style={{flex:1,overflow:'hidden'}} ref={searchListRef}>
         <Table
                columns={tableColumn}
                dataSource={dataSource}
                scroll={{
                    x:1000,
                    y:height-105
                }}
            />
            <ChangeHangModal
                rows={[...dataSource]}
                open={changeModal}
                maxLength={dataSource.length}
                currentIndex={currentIndex}
                closeFn={(loadBol)=>{
                    if(loadBol){
                        load();
                    }
                    setChangeModal(false);
                }}
            />
      </div>
      </div>
    </Fragment>
}



ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("qrcode-manage"));


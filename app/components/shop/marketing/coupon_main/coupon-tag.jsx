const { Spa, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,Tag} = antd;
// NOT_STARTED("未开始"),
// IN_EFFECT("生效中"),
// OBSOLETE("已作废"),
// EXPIRED("已过期"),
const enums = {
    NOT_STARTED:{
        text:'未开始',
        color:'#666',
        bg:"#f5f5f5"
    },
    IN_EFFECT:{
        text:'生效中',
        color:'#4caf50',
        bg:"#e8f5e9"
    },
    OBSOLETE:{
        text:'已作废',
        color:'#f44336',
        bg:"#ffebee"
    },
    EXPIRED:{
        text:'已过期',
        color:'#9e9e9e',
        bg:"#eee"
    }
}

export default ({status})=>{
    return <Tag color={enums[status].bg} style={{color:enums[status].color}}>{enums[status].text}</Tag>
}
const valitorItems = (items, method, filterQuantity) => {
    let errors = [];
    items.map((item, index) => {
        let str = ''
        let bol = false;
        switch (method) {
            case "DISCOUNT":
                str = `第${index + 1}项商品`

                if (item.discount) {
                    if (!/^(0\.\d|[1-9](\.\d)?)$/.test(item.discount)) {
                        // list.push(`第${index}项商品折扣不符合规范`)
                        str += "，折扣只能输入大于0小于10且最多一位小数的正数"
                        bol = true
                    }

                } else {
                    str += "，折扣未填写"
                    bol = true
                }
                if (!filterQuantity) {
                    if (item.quantity) {
                        if (!/^(10000|[1-9]\d{0,3})$/.test(item.quantity)) {
                            // list.push(`第${index}项商品折扣不符合规范`)
                            str += "，发放量是不可大于10000的正整数"
                            bol = true
                        }
                    } else {
                        str += "，发放量未填写"
                        bol = true
                    }
                }

                if (bol) {
                    errors.push(str);
                }


                break;
            case "FULL_REDUCTION":
                // /^([1-9]\d{0,4}|[1-9])$/
                str = `第${index + 1}项商品`
                if (item.thresholdAmount) {
                    if (!/^([1-9]\d{0,4}|[1-9])$/.test(item.thresholdAmount)) {
                        // list.push(`第${index}项商品满额不符合规范`)
                        str += `，满额只能输入不大于99999的正整数`
                        bol = true
                    }
                } else {
                    str += "，满额未填写"
                    bol = true
                }

                if (item.thresholdDeductionAmount) {
                    if (!/^([1-9]\d{0,4}|[1-9])$/.test(item.thresholdDeductionAmount)) {
                        // list.push(`第${index}项商品满额不符合规范`)
                        str += `，减额只能输入不大于99999的正整数`
                        bol = true
                    }
                } else {
                    str += "，减额未填写"
                    bol = true
                }

                if (/^([1-9]\d{0,4}|[1-9])$/.test(item.thresholdAmount) && /^([1-9]\d{0,4}|[1-9])$/.test(item.thresholdDeductionAmount)) {
                    if (+item.thresholdAmount <= +item.thresholdDeductionAmount) {
                        str += "，满额必须大于减额";
                        bol = true
                    }


                }
                // /^([1-9]\d{0,4}|[1-9])$/
                if (!filterQuantity) {
                    if (item.quantity) {
                        if (!/^(10000|[1-9]\d{0,3})$/.test(item.quantity)) {
                            // list.push(`第${index}项商品折扣不符合规范`)
                            str += "，发放量是不可大于10000的正整数"
                            bol = true
                        }
                    } else {
                        str += "，发放量未填写"
                        bol = true
                    }
                }

                if (bol) {
                    errors.push(str);
                }
                break;
            case "DIRECT_REDUCTION":
                // /^([1-9]\d{0,4}|[1-9])$/
                str = `第${index + 1}项商品`
                // rules={[{required:true,message:'立减面额是必填的'},{
                //     pattern:/^(?!0$)[1-9]\d{0,4}(\.\d)?$/,
                //     message:'请输入1至99999之间的数字，只允许保留小数点后一位'
                // }]}
                if (item.deductionAmount) {
                    console.log("item.deductionAmount:", item.deductionAmount)
                    // if (!/^(?!0$)[1-9]\d{0,4}(\.\d)?$/.test(item.deductionAmount)) {
                    if (!/^(?!0$)[1-9]\d{0,4}(\.\d)?$/.test(item.deductionAmount)) {
                        str += "，立减面额是1至99999之间的数字，只允许保留小数点后一位"
                        bol = true
                    }
                    if (item.deductionAmount > 99999) {
                        str += "，立减面额是1至99999之间的数字，只允许保留小数点后一位"
                        bol = true
                    }
                } else {
                    str += "，立减面额未填写"
                    bol = true
                }
                if (!filterQuantity) {
                    if (item.quantity) {
                        if (!/^(10000|[1-9]\d{0,3})$/.test(item.quantity)) {
                            // list.push(`第${index}项商品折扣不符合规范`)
                            str += "，发放量是不可大于10000的正整数"
                            bol = true
                        }
                    } else {
                        str += "，发放量未填写"
                        bol = true
                    }

                }
                if (bol) {
                    errors.push(str);
                }
                break;
            default:
                break;
        }
    })

    return errors;
}

const createAcitivityName = (type) => {
    const obj = {
        "RE_PURCHASE": "复购券",
        "NEW_CUSTOMER": "新客券",
        "SINGLE_PRODUCT": "单品券",
        "ASSISTANCE": "助力裂变券",
        "MARKETING_ACTIVITY": "营销活动券"
    }
    const timeStr = moment(new Date().getTime()).format("YYYYMMDD  HH:mm");
    return `${obj[type]}  ${timeStr}`
}

const createDefaultTime = () => {
    // 获取当前时间
    const now = moment();

    // 开始时间：当前时间延后30分钟
    const startTime = now.add(30, 'minutes');

    // 结束时间：开始时间往后推半年
    const endTime = moment(startTime).add(6, 'months');
    return [startTime, endTime]
}
module.exports = {
    valitorItems,
    createAcitivityName,
    createDefaultTime
}
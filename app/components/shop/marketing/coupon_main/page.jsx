
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,Tag,Space} = antd;
import copy from "../../../common/react/utils/copy-to-clipboard";
import couponTag from "./coupon-tag";
import CouponTag from "./coupon-tag";
{/* <CopyOutlined /> */}
// import {}
const {CopyOutlined} = icons
const Index = () => {
    const searchListRef = useRef()

    const getConfig = async () => {
        const data = await new Promise((resolve, reject) => {
            $.ajax({
                url: "https://maria.yang800.com/api/data/v2/948/773",
                success: (res) => {
                    resolve(res);
                }
            })
        })
        return data.data;
    };

    
    const urls = {
        'RE_PURCHASE': "/seller/marketing/coupon-re-shop",
        'NEW_CUSTOMER': "/seller/marketing/coupon-new-custom",
        'SINGLE_PRODUCT': "/seller/marketing/coupon-single-product",
        'ASSISTANCE': "/seller/marketing/coupon-assist",
        'MARKETING_ACTIVITY': "/seller/marketing/coupon-market-active"
    }
    const linkUrl = (row,type)=>{
        // type
        let url= urls[row.type] + "?type=" + type + "&id=" + row.id;
        location.href = url;
    }

    const cancel = (row)=>{
        Modal.confirm({
            title:'作废提醒',
            content:<Fragment>
                <div>
                    确定作废这个优惠券?
                </div>
                <div>失效后，买家无法再领取该优惠券；你也不能继续编辑优惠方式；买家之前已领到的优惠券，在有效期内还能继续使用</div>
            </Fragment>,
            okText:'确定',
            cancelText:'取消',
            onOk:()=>{
                request({
                    url:'/mall-admin/api/couponActivity/update',
                    method:'post',
                    data:{
                        id:row.id,
                        invalid:true
                    },
                    success:()=>{
                        message.info('作废成功');
                        searchListRef.current.load();
                    }
                })
            }
        })
        
    }

    return <Fragment>
        <SearchList
            searchConditionConfig={{
                size: "middle",
            }}
            paginationConfig={{size: "small",showPosition:'bottom'}}
            scrollMode="tableScroll"
            ref={searchListRef}
            getConfig={getConfig}
            tableCustomFun={{
                infoFn: (row) => <Fragment>
                    <div>
                        <div>{row.name} </div>
                        <div>活动ID:{row.id} <CopyOutlined onClick={()=>{
                            copy(row.id);
                            message.success("复制成功")
                        }} /></div>
                        <Tag color="blue">{row.typeDesc}</Tag>
                    </div>
                </Fragment>,
                timeFn: (row)=> <Fragment>
                    <div>
                        {
                            row.type !== 'MARKETING_ACTIVITY'?
                            <div>
                                <Tag color="blue" border={false}>领</Tag>
                                <span>{moment(row.startTime).format("YYYY-MM-DD" + " HH:mm:ss")}~{moment(row.endTime).format("YYYY-MM-DD" + " HH:mm:ss")}</span>
                            </div>
                            :null
                        }
                        
                        <div>
                            <Tag color="gold" border={false}>用</Tag>
                            {
                                row.usageTimeType === "SAME" && 
                                <span>{moment(row.startTime).format("YYYY-MM-DD" + " HH:mm:ss")}~{moment(row.endTime).format("YYYY-MM-DD" + " HH:mm:ss")}</span>
                            }
                            {
                                row.usageTimeType === "LIME" && <span>领券开始{row.validDays}天有效</span>
                            }
                            {
                                row.usageTimeType === "CUSTOM" &&
                                <span>{moment(row.customUseStartTime).format("YYYY-MM-DD" + " HH:mm:ss")}~{moment(row.customUseEndTime).format("YYYY-MM-DD" + " HH:mm:ss")}</span>
                            }
                        </div>
                    </div>
                </Fragment>,
                statusFn: (row)=>{
                    return <CouponTag status={row.status} />
                },
                operateFn:(row)=>{
                    return <Space>
                        {
                           row.type!=="MARKETING_ACTIVITY"&&  ['NOT_STARTED','IN_EFFECT'].includes(row.status) &&<Button type='link' onClick={()=>{
                                linkUrl(row,'edit')
                            }}>编辑</Button>
                        }
                        {
                            ['NOT_STARTED','IN_EFFECT'].includes(row.status) && <Button type='link' onClick={()=>{
                                cancel(row);
                            }}>作废</Button>
                        }
                         {
                            ['OBSOLETE','EXPIRED'].includes(row.status) && <Button type='link' onClick={()=>{
                                linkUrl(row,'look')
                            }}>查看</Button>
                        }
                       
                    </Space>
                },
                numFn:(row)=>{
                    if( row.scopeList[0].type === 'PRODUCT'
                        ||row.type === "ASSISTANCE"
                    ){
                        return <div style={{color:'#ccc'}}>请展开查看</div>
                    } else {
                        return `${row.receiveQuantity||"-"}/${row.quantity||"-"}`
                    }
                },
                contentFn:(row)=>{
                    // row.methodDesc + "\n"+ row.couponScopeDesc
                    if( row.type === "ASSISTANCE"
                    ){
                        return <div style={{color:'#ccc'}}>请展开查看</div>
                    } else {
                        return <div>
                            {row.methodDesc}
                            <br />{row.couponScopeDesc}
                        </div>
                    }
                }
            }}
            renderExpandRow={
            (row,expanded)=>{
                // 新客券-商品新客&复购券-商品复购
                if(row.scopeList && row.scopeList[0].type === 'PRODUCT'){
                    return (
                        <Table 
                            dataSource={row.scopeList}
                            pagination={false}
                            columns={[
                                {
                                    title:'优惠商品',
                                    width:250,
                                    render:(row,_)=>{
                                        return (<div style={{display:'flex',flexDirection:'row',alignItems:'center'}}>
                                            <img src={row.mainPic} style={{height:'50px',width:'50px',display:'flex'}} />
                                            <div style={{marginLeft:'10px'}} onClick={()=>{
                                                copy(row.scopeValue)
                                                message.success("复制成功")
                                            }}>
                                                <div>{row.goodName||'商品名称'}</div>
                                                <div>ID:{row.scopeValue}<CopyOutlined /> </div>
                                            </div>
                                        </div>)
                                    }
                                },{
                                    title:'优惠内容',
                                    width:150,
                                    render:(row)=>{
                                        return (<div>
                                            {/* {row.method==="DIRECT_REDUCTION" &&`立减${row.deductionAmount}元`}
                                            {row.method==="FULL_REDUCTION" &&`满${row.thresholdAmount}元减${row.thresholdDeductionAmount}`}
                                            {row.method==="DISCOUNT" &&`${row.discount}折`} */}
                                            {row.couponScopeDesc}
                                        </div>)
                                    }
                                },{
                                    title:'领取量/发放量',
                                    render:(row)=>{
                                        return (<div>
                                            {row.receiveQuantity}/{row.quantity}
                                        </div>)
                                    }
                                }  ,{
                                    title:'每人限领',
                                    dataIndex:'limitNum',
                                    key:'limitNum'
                                },
                                {
                                    title:'券ID',
                                    render:(row)=>{
                                        return <div onClick={()=>{
                                            copy(row.id);
                                            message.success("复制成功")
                                        }}>{row.id}<CopyOutlined /> </div>
                                    }
                                },
                            ]}
                        />)
                }
                if(row.type === "ASSISTANCE"){
                    return (
                        <Table 
                            dataSource={row.scopeList}
                            pagination={false}
                            columns={[
                                {
                                    title:'优惠项',
                                    width:250,
                                    render:(row,_)=>{
                                        return (<div>
                                            {row.scopeValue === 'sharer' && "分享者优惠"}
                                            {row.scopeValue === 'shared' && "被分享者优惠"}
                                            {row.method==="FULL_REDUCTION" && <div style={{color:'#ccc',fontSize:'12px'}}>店铺满减券</div>}
                                            {row.method==="DIRECT_REDUCTION" && <div style={{color:'#ccc',fontSize:'12px'}}>店铺直减券</div>}
                                            
                                        </div>)
                                    }
                                },{
                                    title:'优惠内容',
                                    width:'150',
                                    render:(row)=>{
                                        return (<div>
                                            {/* {row.method==="DIRECT_REDUCTION" &&`立减${row.deductionAmount}元`}
                                            {row.method==="FULL_REDUCTION" &&`满${row.thresholdAmount}元减${row.thresholdDeductionAmount}`}
                                            {row.method==="DISCOUNT" &&`${row.discount}折`} */}
                                            {row.couponScopeDesc}
                                        </div>)
                                    }
                                },{
                                    title:'领取量/发放量',
                                    render:(row)=>{
                                        return (<div>
                                            {row.receiveQuantity}/{row.quantity}
                                        </div>)
                                    }
                                }
                                ,{
                                    title:'每人限领',
                                    dataIndex:'limitNum',
                                    key:'limitNum'
                                },
                                {
                                    title:'券ID',
                                    render:(row)=>{
                                        // console.log("row.id",row)
                                        return <div onClick={()=>{
                                            copy(row.id);
                                            message.success("复制成功")
                                        }}>{row.id}<CopyOutlined /> </div>
                                    }
                                },
                            ]}
                        />)
                }
                return null;
                
            }
        }
        />
    </Fragment>

}
export default Index


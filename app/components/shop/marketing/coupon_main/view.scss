.coupon-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    // background-color: #c3c3c3;
    .btn-blue{
        color: #007bff;
        border-color: #007bff;
        border-radius: 8px;
    }
    .coupon-item {
        background: white;
        position: relative;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 20px;
        padding-bottom: 60px;
        display: flex;
        align-items: center;
        box-shadow: 0 0 5px #e0e6ec ;
        // margin: 20px;
        .item-content{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .item-icon{
                width: 60px;
                border-radius: 30px;
                height: 60px;
                background-color: #b2d0f2;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
            }
            .item-info{
                .coupon-item-name{
                    font-size: 20px;
                    line-height: 30px;
                    color: #333;
                    font-weight: bold;
                }
                .coupon-item-extra{
                    font-size: 12px;
                    line-height: 20px;
                    color: #999;
                    margin-top: 6px;
                }
            }
        }
        .coupon-item-btn{
            position: absolute;
            right: 20px;
            top: 20px;
            border: 1px solid #1677FF;
            border-radius: 6px;
            background-color: #fff;
            padding: 2px 12px;
            color: #1677FF;
        }
    }
    
    .icon {
        font-size: 40px;
        margin-right: 20px;
    }
    
    .content {
        flex-grow: 1;
    }
    
    .new-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
    }
    
    .new-button:hover {
        background: #005ce6;
    }
  }
.component-qjf-container{
    // height: max-content;
    background-color: #fff;
    border-radius: 8px;
    padding: 24px;
    height: calc(100% - 100px);
}
.qjf-coupon-create{
    height: max-content !important;
}
  
  
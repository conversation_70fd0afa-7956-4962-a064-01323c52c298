
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,Tabs, Image} = antd
const {ReloadOutlined,ApartmentOutlined,UserAddOutlined,TagOutlined,PlusOutlined} = icons;
import Page from "./page"


export const Index = () => {
    const [activeIndex,setActiveIndex] = useState("item-1");
    const list = [
        {url:'/seller/marketing/coupon-re-shop?type=add',
            name:'复购券',
            // icon: <ReloadOutlined style={{color:'#0290de',fontSize:'24px',fontWeight:800}} />,
            icon: <Image src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/99200299146.png" style={{width:'60px',height:'60px'}} />,
            msg:'针对已购买用户发放的优惠券，提高用户复购率和忠诚度'
        },
        {url:'/seller/marketing/coupon-new-custom?type=add',
            name:'新客券',
            icon: <Image src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/99202402606.png" style={{width:'60px',height:'60px'}} />,
            msg:'专属新用户的首单优惠券，快速吸引新客下单转化'
        },
        {url:'/seller/marketing/coupon-single-product?type=add',
            name:'单品券',
            icon: <Image src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/99129485144.png" style={{width:'60px',height:'60px'}} />,
            msg:'专属新用户的首单优惠券，快速吸引新客下单转化'
        },
        {url:'/seller/marketing/coupon-assist?type=add',
            name:'助力裂变券',
            icon: <Image src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/99198253630.png" style={{width:'60px',height:'60px'}} />,
            msg:'通过分享助力获得的优惠券，实现社交裂变营销'
        },
        {url:'/seller/marketing/coupon-market-active?type=add',
            name:'营销活动券',
            icon: <Image src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/99129485144.png" style={{width:'60px',height:'60px'}} />,
            msg:'针对特定营销活动的专属优惠券，打通运营全链路'
        }
    ]

    useEffect(()=>{
        const str = __lib__.getParam("tabIndex") || "item-1";
        setActiveIndex(str)
    },[])
    return <div style={{height:'100%'}}>
        <Tabs activeKey={activeIndex}
            onChange={(e)=>{
                

                setActiveIndex(e);
            }}
             className="coupon-manage-tab"
        >
            <Tabs.TabPane tab="新建优惠券" key="item-1">
                {/* 活动类型卡片 */}
                <div className="coupon-container">
                    {
                        list.map((item)=>(
                            <a href={item.url} target="_blank" className="coupon-item">
                                <div className="item-content">
                                    <div className="item-icon">
                                    {/* <i className="fas fa-redo text-blue-600 text-2xl"></i> */}
                                    {item.icon}
                                        {/* <ReloadOutlined style={{color:'#0290de',fontSize:'16px',fontWeight:800}} /> */}
                                    </div>
                                    <div className="item-info">
                                        <div className="coupon-item-name">{item.name}</div>
                                        <div className="coupon-item-extra">{item.msg}</div>
                                    </div>
                                </div>
                                <button className="coupon-item-btn">
                                    <PlusOutlined />
                                    新建
                                </button>
                            </a>
                        ))
                    }
                </div>
            </Tabs.TabPane>
            <Tabs.TabPane tab="管理优惠券" key="item-2">
                <Page />
            </Tabs.TabPane>
        </Tabs>
    </div>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("coupon-main"));
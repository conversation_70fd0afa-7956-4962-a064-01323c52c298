.marketing-tools-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .tools-item {
        background: white;
        position: relative;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding-top: 20px;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 5px #e0e6ec;

        // margin: 20px;
        .item-content {
            padding-left: 20px;
            display: flex;
            flex-direction: row;
            align-items: center;

            .item-icon {
                width: 60px;
                border-radius: 30px;
                height: 60px;
                background-color: #b2d0f2;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
            }

            .item-info {
                .coupon-item-name {
                    font-size: 20px;
                    line-height: 30px;
                    color: #333;
                    font-weight: bold;
                }

            }
        }

        .coupon-item-extra {
            flex-grow: 1;
            margin-top: 10px;
            width: 100%;
            font-size: 12px;
            line-height: 20px;
            padding: 6px 20px 6px;
            color: #999;
            background: #f7f8fa;
        }

        .coupon-item-btn {
            position: absolute;
            right: 0;
            top: 0;
            border: 1px solid #1677FF;
            border-radius: 0 8px 0 0;
            background-color: #fff;
            padding: 2px 12px;
            color: #1677FF;
        }
    }
}
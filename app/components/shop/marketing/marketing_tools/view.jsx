
const { Spa, SpaConfigProvider, SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const { useState, useEffect, useRef, Fragment } = React;
const { Modal, Image } = antd

export const Index = () => {

    const [toolList, setToolsList] = useState([])

    // 页面枚举
    const UrlEnum = {
        1: '/seller/marketing/marketing-tools-group-list', // 拼团购
        2: '/seller/marketing/marketing-tools-share-list', // 分享购
        3: '', // 砍一刀
    }

    const getMarketingToolList = () => {
        request({
            url: '/mall-admin/api/marketing_tools/list',
            method: 'POST',
            data: {},
            success: (data) => {
                let arr = (data || []).filter(item => item.mtStatus == 1).sort((a, b) => a.mtOrder - b.mtOrder);
                setToolsList(arr)
            }
        })
    }


    const goPage = (activityId) => {
        let url = UrlEnum[activityId]
        if(!activityId) return
        if(url){
            window.open(`${url}?marketingToolId=${activityId}`)
        } else {
            Modal.error({
                title: '功能暂未开放',
                okText: '确认'
              });
        }
    }

    useEffect(() => {
        // 获取营销工具列表
        getMarketingToolList();
    }, [])
    return <div style={{ height: '100%' }}>
        {/* 活动类型卡片 */}
        <div className="marketing-tools-container">
            {
                toolList.map((item) => (
                    <a  className="tools-item" key={item.id} onClick={
                        () => { goPage(item.id) }
                    }>
                        <div className="item-content">
                            <Image className="item-icon" src={item.mtPic} preview={false}></Image>
                            <div className="item-info">
                                <div className="coupon-item-name">{item.mtName}</div>
                            </div>
                        </div>
                        <div className="coupon-item-extra">{item.mtDes}</div>
                        {
                            item.mtIntroduction && <button className="coupon-item-btn">{item.mtIntroduction}</button>
                        }
                    </a>
                ))
            }
        </div>
    </div>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("marketing-tools-main"));
import AddGoodsModal from "./add-goods-modal";
const { useState, useEffect, useRef, Fragment } = React;
const { Button, Space, Table, Input, InputNumber, Row, Col, message, Modal, Tooltip, Switch, Form } = antd;
const { QuestionCircleOutlined } = icons;
import { parseSpecData, calculateRowSpans, generateCombinations } from './spec'
import request from "../../../utils/plugins/axios/request";



const SelectGoods = ({
    value = [],
    configs,
    onChange,
    pageMethod,
    maxLimit,
    form,
    requiredDom,
    hideBtns,
}) => {
    const addBol = pageMethod === 'add';
    const [goods, setGoods] = useState([]);
    const [open, setOpen] = useState(false);
    const [groupPrice, setGroupPrice] = useState();
    const [quantity, setquantity] = useState();
    // 子表格展开的keys
    const [expandedRowKeys, setExpandedRowKeys] = useState([])

    const goodsList = Form.useWatch('goodsList', form);

    const once = useRef(false)

    // 更多信息
    const moreDom = <span style={{ color: '#999' }}>详情请展开查看</span>;

    // 扩展列
    const expandRow = (bool, row) => {
        if (bool) {
            setExpandedRowKeys([...expandedRowKeys, row.itemId])
        } else {
            const idx = expandedRowKeys.findIndex(m => m === row.itemId);
            const copy = [...expandedRowKeys];
            copy.splice(idx, 1);
            setExpandedRowKeys(copy)
        }
    }

    // 表格列配置
    const columns = [
        {
            title: '商品信息',
            width: 360,
            render: (row, _, index) => (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src={row.mainImage} style={{ width: '50px', heigth: '50px' }} />
                    <div style={{ marginLeft: '10px' }}>
                        <div>{row.name}</div>
                        <div>商品ID：{row.itemId}</div>
                    </div>
                </div>)
        },
        {
            title: '原销售价',
            width: 150,
            render: (row, _, index) => (
                <span>{`¥${row.lowPrice}${row.lowPrice == row.highPrice ? '' : ' ~ ¥' + row.highPrice}`}</span>)
        },
        {
            title: requiredDom('拼团价'),
            require: true,
            width: 150,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    return <Form.Item
                        name={['goodsList', index, 'skuList', 0, 'gbPrice']}
                        rules={[
                            {
                                required: true, message: '请填写拼团价'
                            },
                            {
                                type: 'number',
                                min: 0.01,
                                message: '拼团价需大于0元'
                            }
                        ]}>
                        <InputNumber
                            disabled={row.disabled}
                            addonAfter="元"
                            min={0}
                            max={row.highPrice}
                            precision={2}
                        />
                    </Form.Item>
                }
            }
        },
        {
            title: '可售库存',
            width: 120,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return <span>{ row.stockQuantity || 0 }</span>
                } else {
                    return <span>{ (row.skuList[0] || {}).stockQuantity || 0 }</span>
                }
                    
            }
        },
        {
            title: requiredDom('活动库存'),
            width: 150,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    let disabledFlag = row.disabled || (!addBol && !row.canEditStock)
                    let maxObj = !disabledFlag ? { max: row.stockQuantity } : {};
                    return <Form.Item
                        name={['goodsList', index, 'skuList', 0, 'skuStock']}
                        rules={[
                            { required: true, message: '请填写活动库存' },
                            {
                                type: 'number',
                                min: 1,
                                message: '活动库存需大于0'
                            }
                        ]}>
                        <InputNumber
                            disabled={disabledFlag}
                            min={0}
                            {...maxObj}
                            precision={0}
                        />
                    </Form.Item>
                }
            }
        },
        {
            title: '限购数量',
            width: 150,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    let max = (row.skuList[0] || {}).orderQuantityLimit;
                    return <Form.Item name={['goodsList', index, 'skuList', 0, 'skuLimit']}>
                        <InputNumber
                            disabled={row.disabled}
                            min={0}
                            {...(max ? { max } : {})}
                            precision={0}
                        />
                    </Form.Item>
                }
            }
        },
        {
            title: '是否参与拼团',
            width: 120,
            render: (_, row, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    return <Form.Item name={['goodsList', index, 'skuList', 0, 'joinFlag']} valuePropName="checked" >
                        <Switch
                            disabled={row.disabled}
                            checked={row['joinFlag']}
                            onClick={(val) => {
                                Modal.confirm({
                                    content: '单规格SPU必须参团，如不参团请删除该SPU',
                                    onOk: () => changeSubByField(row.itemId, 0, 'joinFlag', true),
                                    okText: '确认',
                                    cancelButtonProps: { style: { display: 'none' } }
                                });
                            }}
                        />
                    </Form.Item>

                }
            },
        },
        {
            title: '可用福豆',
            width: 120,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    if ((row.skuList[0] || {}).joinFlag) {
                        return <Form.Item name={['goodsList', index, 'skuList', 0, 'isFudou']} valuePropName="checked">
                            <Switch disabled={row.disabled} />
                        </Form.Item>
                    } else {
                        return '-'
                    }
                }
            }
        },
        {
            title: '可用福卡',
            width: 120,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    if ((row.skuList[0] || {}).joinFlag) {
                        return <Form.Item name={['goodsList', index, 'skuList', 0, 'isLijing']} valuePropName="checked" >
                            <Switch disabled={row.disabled} />
                        </Form.Item>
                    } else {
                        return '-'
                    }
                }
            }
        },
        {
            title: '可用优惠券',
            width: 120,
            render: (row, _, index) => {
                if (row.skuList && row.skuList.length > 1) {
                    return moreDom
                } else {
                    if ((row.skuList[0] || {}).joinFlag) {
                        return <Form.Item name={['goodsList', index, 'skuList', 0, 'isYouhui']} valuePropName="checked" >
                            <Switch disabled={row.disabled} />
                        </Form.Item>
                    } else {
                        return '-'
                    }
                }
            }
        },
        {
            title: '操作',
            width: 120,
            fixed: 'right',
            render: (row, _, index) => <Fragment>
                <Button type='link' disabled={row.disabled} onClick={() => {
                    Modal.confirm({
                        title: '提示',
                        content: '是否确认删除，再次选择不保留上次提交内容',
                        okText: '确认',
                        cancelText: '取消',
                        onOk: () => {
                            goods.splice(index, 1)
                            form.setFieldValue('goodsList', [...goods])
                        },
                    })

                }}>删除</Button>
            </Fragment>
        }
    ]

    // 选择商品数据后根据规格生成子表格数据
    const dealSpecsList = (data) => {
        // const comonParams = {
        //     joinFlag: true,
        //     canDou: false,
        //     canGift: false,
        //     canConpon: false,
        //     groupPrice: 0,
        //     quantity: 0,
        //     buyNum: 0
        // }
        const comonParams = {}
        data.forEach((item, index) => {
            if (item.itemSpecificationParams && item.itemSpecificationParams.length > 0) {
                const parsedSpecs = parseSpecData(item.itemSpecificationParams);
                const rawData = generateCombinations(parsedSpecs, comonParams);
                let skuList = calculateRowSpans(rawData, parsedSpecs);
                item.skuList = skuList.map(m => {
                    let cur = item.skus.find(n => n.specDetails.sort().join('') === m.specDetails.sort().join(''))
                    return {
                        ...cur,
                        ...m,
                        id: cur.id,
                    }
                });
            } else {
                data[index] = {
                    ...comonParams,
                    ...item,
                    skuList: item.skus
                }
            }
        })
        return [...data]
    }

    // 嵌套表格渲染函数
    const expandedRowRender = (record, idx) => {
        if (record.skuList && record.skuList.length > 1) {
            // 当前行key
            let key = record.itemId;

            const specDetails = record.specDetails;

            const addCols = specDetails.map((item, index) => {
                return {
                    title: item.name,
                    dataIndex: item.name,
                    fixed: 'left',
                    width: 360 / (specDetails.length || 1),
                    render: (value, _, index) => {
                        const span = record['skuList'][index][`${item.name}Span`] || 0;
                        return { children: value, props: { rowSpan: span } };
                    }
                }
            })

            // 是否可编辑
            const disabledFlag = record.disabled

            // 嵌套表格列配置
            const cols = [
                ...addCols,
                {
                    title: '原销售价（元）',
                    dataIndex: 'price',
                    key: 'price',
                    width: 150,
                },
                {
                    title: requiredDom('拼团价（元）'),
                    key: 'gbPrice',
                    width: 150,
                    render: (_, row, index) => {
                        if (row.joinFlag) {
                            return <Form.Item
                                name={['goodsList', idx, 'skuList', index, 'gbPrice']}

                                rules={[
                                    { required: true, message: '请填写拼团价' },
                                    {
                                        type: 'number',
                                        min: 0.01,
                                        message: '拼团价需大于0元'
                                    }
                                ]}>
                                <InputNumber
                                    disabled={disabledFlag}
                                    addonAfter="元"
                                    min={0}
                                    max={row.price}
                                    precision={2}
                                />
                            </Form.Item>
                        } else {
                            return '-'
                        }

                    },
                },
                {
                    title: '可售库存',
                    dataIndex: 'stockQuantity',
                    key: 'stockQuantity',
                    width: 120,
                },
                {
                    title: requiredDom('活动库存（件）'),
                    dataIndex: 'skuStock',
                    key: 'skuStock',
                    width: 150,
                    render: (_, row, index) => {
                        let maxObj = !disabledFlag ? { max: row.stockQuantity } : {};
                        if (row.joinFlag) {
                            return <Form.Item
                                name={['goodsList', idx, 'skuList', index, 'skuStock']}
                                rules={[
                                    { required: true, message: '请填写活动库存' },
                                    {
                                        type: 'number',
                                        min: 1,
                                        message: '活动库存需大于0'
                                    }
                                ]}>
                                <InputNumber
                                    disabled={disabledFlag || (!addBol && !record.canEditStock)}
                                    min={0}
                                    {...maxObj}
                                    precision={0}
                                />
                            </Form.Item>
                        } else {
                            return '-'
                        }


                    },
                },
                {
                    title: '限购数量',
                    dataIndex: 'skuLimit',
                    key: 'skuLimit',
                    width: 150,
                    render: (_, row, index) => {
                        let maxObj = !disabledFlag &&  row.orderQuantityLimit ? { max: row.orderQuantityLimit } : {};
                        return <Form.Item name={['goodsList', idx, 'skuList', index, 'skuLimit']} >
                            <InputNumber
                                min={0}
                                disabled={disabledFlag}
                                precision={0}
                                {...maxObj}
                            />
                        </Form.Item>

                    },
                },
                {
                    title: '是否参与拼团',
                    dataIndex: 'field',
                    width: 120,
                    key: 'field',
                    render: (_, row, index) => (
                        <Form.Item name={['goodsList', idx, 'skuList', index, 'joinFlag']} valuePropName="checked">
                            <Switch
                                disabled={disabledFlag}
                                onChange={(val) => {
                                    if ((record.skuList || []).filter(item => item.joinFlag).length === 1 && !val) {
                                        Modal.confirm({
                                            content: '多规格SPU至少保留一个SKU参与活动，如不参团请删除SPU',
                                            onOk: () => changeSubByField(key, index, 'joinFlag', true),
                                            okText: '确认',
                                            cancelButtonProps: { style: { display: 'none' } }
                                        });
                                    } else {
                                        changeSubByField(key, index, 'joinFlag', val)
                                    }

                                }}
                            />
                        </Form.Item>
                    ),
                },
                {
                    title: '可用福豆',
                    dataIndex: 'isFudou',
                    key: 'isFudou',
                    width: 120,
                    render: (_, row, index) => {
                        if (row.joinFlag) {
                            return <Form.Item name={['goodsList', idx, 'skuList', index, 'isFudou']} valuePropName="checked">
                                <Switch disabled={disabledFlag} />
                            </Form.Item>

                        } else {
                            return '-'
                        }
                    }
                },
                {
                    title: '可用福卡',
                    dataIndex: 'isLijing',
                    key: 'isLijing',
                    width: 120,
                    render: (_, row, index) => {
                        if (row.joinFlag) {
                            return <Form.Item name={['goodsList', idx, 'skuList', index, 'isLijing']} valuePropName="checked">
                                <Switch disabled={disabledFlag} />
                            </Form.Item>
                        } else {
                            return '-'
                        }
                    }
                },
                {
                    title: '可用优惠券',
                    dataIndex: 'isYouhui',
                    key: 'isYouhui',
                    width: 120,
                    render: (_, row, index) => {
                        if (row.joinFlag) {
                            return <Form.Item name={['goodsList', idx, 'skuList', index, 'isYouhui']} valuePropName="checked">
                                <Switch disabled={disabledFlag} />
                            </Form.Item>
                        } else {
                            return '-'
                        }
                    }
                },

            ];

            let totalWidth = cols.reduce((total, current) => total + current.width, 0)
            return <Table
                columns={cols}
                dataSource={record.skuList}
                scroll={{
                    x: totalWidth + 'px',
                }}
                pagination={false} />;
        } else {
            return null;
        }

    }

    // 开关改变事件
    const changeSubByField = (key, index, name, val) => {
        let goodsCopy = [...goods];
        let item = goodsCopy.find(item => item.itemId === key);
        if (item.skuList && item.skuList.length > 0) {
            item.skuList[index][name] = val;
        } else {
            item[name] = val;
        }
        form.setFieldValue('goodsList', goodsCopy)
        setGoods(goodsCopy)
    }



    // 获取商品及其对应的sku信息
    const getSkusByIds = async (itemIds) => {
        return await request({
            url: '/mall-admin/api/gb_item/skusByItems',
            method: 'post',
            data: {
                itemIds: itemIds || []
            }
        })
    }

    // 批量填充
    const batchSet = () => {
        // 拼团价和库存的批量填充
        if (groupPrice || quantity) {
            let groupPriceNum = parseFloat(groupPrice)
            let quantityNum = parseInt(quantity)
            let extra = {}

            // 填充数据
            if (groupPriceNum == 0) {
                return message.error("拼团价需大于0元")
            } else {
                if(groupPriceNum != NaN){
                    extra['gbPrice'] = groupPriceNum;
                }
            }
            if (quantityNum == 0) {
                return message.error("库存需大于0")
            } else {
                if(quantityNum != NaN){
                    extra['skuStock'] = quantityNum;
                }
            }

            let goodsList = form.getFieldValue('goodsList')
            for (let i = 0, len = goodsList.length; i < len; i++) {
                goodsList[i].skuList = (goodsList[i].skuList || []).map(item => {
                    let extraSelf = {}
                    if (item.joinFlag) {
                        // 规格自身的过滤
                        // 拼团价
                        if (extra['gbPrice'] && extra['gbPrice'] <= parseFloat(item.price)) {
                            extraSelf['gbPrice'] = extra['gbPrice']
                        }
                        // 库存
                        if (extra['skuStock'] && extra['skuStock'] <= parseInt(item.stockQuantity)) {
                            extraSelf['skuStock'] = extra['skuStock']
                        }
                    }

                    return {
                        ...item,
                        ...extraSelf
                    }
                })
            }
            form.setFieldValue('goodsList', [...goodsList])
        }

    }

    // 一键清空列表
    const clearListHandle = () => {
        Modal.confirm({
            title: '提示',
            content: '确认一键清空列表吗？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                form.setFieldValue('goodsList', [])
            },
        })
    }

    useEffect(() => {
        let curGoods = form.getFieldValue('goodsList') || [];
        setGoods([...curGoods])
    }, [goodsList])


    // 商品信息验证
    const valitorShops = (items, field) => {
        let errors = [];
        console.log('items', items)

        for (let i = 0, len = items.length; i < len; i++) {
            let shop = items[i];
            let bol = false;
            let str = ''
            for (let j = 0, len2 = (shop.skuList || []).length; j < len2; j++) {
                let item = shop.skuList[j]; // 规格
                if (item.joinFlag) {
                    str += `${str !== '' ? '；' : ''}第${i + 1}项商品，第${j + 1}个规格`;
                    let gbPrice = item.gbPrice;

                    // 拼团价的效验
                    // if (gbPrice) {
                    //     if (gbPrice > item.price) {
                    //         str += "，拼团价不得超过商品原销售价"
                    //         bol = true
                    //     }
                    // } else if (gbPrice === 0) {
                    //     str += "，拼团价需大于0"
                    //     bol = true
                    // } else {
                    //     str += "，拼团价未填写"
                    //     bol = true
                    // }
                    if(!gbPrice){
                        str += "，拼团价未填写"
                        bol = true
                    }
                    // 库存的效验
                    let skuStock = item.skuStock;
                    // if (skuStock) {
                    //     if (skuStock > item.stockQuantity) {
                    //         str += "，库存不得超过商品原库存"
                    //         bol = true
                    //     }
                    // } else if (skuStock === 0) {
                    //     str += "，库存需大于0"
                    //     bol = true
                    // } else {
                    //     str += "，库存未填写"
                    //     bol = true
                    // }
                    if (!skuStock) {
                        str += "，库存未填写"
                        bol = true
                    }
                }

            }
            if (str !== '') {
                str += '\n'
            }
            if (bol) {
                errors.push(str);
            }
        }
        console.log('errors', errors)
        return errors;
    }

    return (<Fragment>
        <Space>
            <Button type="primary" onClick={() => {
                setOpen(true);
            }}>添加商品</Button>
            已选商品{goodsList ? goodsList.length : 0}件
        </Space>
        <br />
        {
            !hideBtns && <div style={{
                display: 'flex', flexDirection: 'row', alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%'
            }}>
                <div>
                    <Space style={{ padding: '10px 0' }}>

                        <Tooltip title="拼团价需要低于原销售价，超过将不填充" placement="bottom">
                            <InputNumber
                                min={0}
                                precision={2}
                                style={{ width: '200px' }}
                                placeholder="请输入SKU拼团价"
                                value={groupPrice}
                                onChange={(val) => setGroupPrice(val)}
                            />

                        </Tooltip>
                        {
                            addBol && <Tooltip title="活动库存需要低于可售库存，超过将不填充" placement="bottom">
                                <InputNumber
                                    placeholder="请输入活动库存"
                                    min={0}
                                    precision={0}
                                    style={{ width: '200px' }}
                                    value={quantity}
                                    onChange={(val) => setquantity(val)}
                                />

                            </Tooltip>
                        }


                        <Button disabled={!goods || goods.length === 0 || pageMethod === 'look'} type="primary" onClick={() => {
                            batchSet()
                        }}>批量填充</Button>
                    </Space>

                </div>

                <Button type="primary" onClick={clearListHandle}>一键清空列表</Button>
            </div>
        }

        <AddGoodsModal
            open={open}
            closeFn={async (selects) => {
                setOpen(false);
                if (selects && selects.length) {
                    const ids = selects.map(item => item.id);
                    let skusData = await getSkusByIds(ids)
                    let dealSkusData = dealSpecsList(skusData)
                    let arr = dealSkusData.map((cur, curIdx) => {
                        let beforeItem = (goods || []).find(m => m.itemId === (cur.item || {}).id);
                        const { item, skuList } = cur;
                        return {
                            itemId: item.id,
                            name: item.name,
                            mainImage: item.mainImage,
                            stockQuantity: item.stockQuantity, // 可售库存
                            lowPrice: item.lowPrice / 100,
                            highPrice: item.highPrice / 100,
                            specDetails: cur.itemSpecificationParams,
                            disabled: beforeItem ? beforeItem.disabled : false,
                            canEditStock: beforeItem ? beforeItem.canEditStock : true, // 是否可编辑库存
                            skuList: beforeItem ? beforeItem.skuList : skuList.map(m => {
                                let orderQuantityLimit = parseInt((m.extraMap || {}).orderQuantityLimit || 0);
                                let skuOrderSplitLine = parseInt((m.extraMap || {}).skuOrderSplitLine || 0);
                                let qLimit = Math.min(orderQuantityLimit, skuOrderSplitLine);
                                return {
                                    ...m,
                                    price: m.price / 100,
                                    orderQuantityLimit: qLimit,
                                    joinFlag: true,
                                    isFudou: false,
                                    isLijing: false,
                                    isYouhui: false,
                                    stockQuantity: m.stockQuantity,
                                }
                            })
                        }
                    })
                    form.setFieldValue('goodsList', [...arr])
                    setExpandedRowKeys([...arr].filter(m => m.skuList && m.skuList.length > 1).map(n => n.itemId))
                }
            }}
            selectItem={goods}
        /> <Form.Item name='goodsList'
            rules={[
                {
                    validator(rule, value) {
                        if (value && Array.isArray(value) && value.length > 0) {
                            const errors = valitorShops(value);
                            if (errors.length === 0) {
                                return Promise.resolve("")
                            } else {
                                return Promise.reject("请检查填写的商品信息")
                                // const str = errors.join("；");
                                // return Promise.reject(str)
                            }
                        } else {
                            return Promise.reject('请选择商品')
                        }
                    }
                }
            ]
            }
        >
            <Table
                dataSource={goods}
                columns={columns}
                style={{
                    marginTop: hideBtns ? '10px' : '0'
                }}
                className='goods-table'
                rowKey='itemId'
                scroll={{ y: 500, x: columns.reduce((total, current) => total + current.width, 0) }}
                pagination={false}
                sticky
                expandable={{
                    expandedRowRender,
                    rowExpandable: (record) => record.skuList && record.skuList.length > 1,
                    expandRowByClick: true,
                    rowKey: 'itemId',
                    expandedRowKeys,
                    onExpand: expandRow,
                }}
            />
        </Form.Item>
    </Fragment>)
}

export default SelectGoods
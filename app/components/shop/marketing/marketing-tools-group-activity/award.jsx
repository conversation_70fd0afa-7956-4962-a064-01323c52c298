const { useState, useEffect, useRef, Fragment } = React;
const { InputNumber, Form, Radio, Select } = antd;
const { FormSelect } = dtComponents

const Award = ({
  name,
  form,
  couponList,
  disabled,
  type,
  userScope
}) => {
  // 输入框、下拉框长度
  const formCol = {
    labelCol: { span: 4 },
    wrapperCol: { span: 10 }
  }

  const isShowRequired = type !== 'look';

  const isHasAward = Form.useWatch(['award', name], form);
  const awardType = Form.useWatch(['award', name + 'type'], form);

  // 有无奖励的改变事件
  const onHasChange = (e) => {
    let award = form.getFieldValue('award')
    if (e.target.value) {
      form.setFieldsValue({
        award: {
          ...award,
          [name]: 1,
          [name + 'type']: 0,
          [name + 'num']: '',
          [name + 'couponName']: [],
          [name + 'expireDay']: '',
          [name + 'time']: '',
        }
      })
    } else {
      form.setFieldsValue({
        award: {
          ...award,
          [name]: 0,
        }
      })
    }
    validAward()
  }

  const validAward = () => {
    form.validateFields(['award'])
  }

  return <Form.Item label=''
  >
    <Form.Item name={['award', name]} rules={[{ required: true, message: '请选择活动奖励' }]}>
      <Radio.Group
        disabled={disabled}
        defaultValue={0}
        options={[
          { value: 0, label: '无奖励' },
          { value: 1, label: '有奖励' },
        ]}
        onChange={onHasChange}
      ></Radio.Group>
    </Form.Item>
    {
      isHasAward ? (
        <div className="common-wrap">
          <Form.Item
            label="奖励类型"
            name={['award', name + 'type']}
            {...formCol}
            required={isShowRequired}
            rules={[{ required: true, message: '请选择奖励类型' }]}
            onChange={validAward} 
          >
            <Select
              disabled={disabled}
              defaultValue={0}
              options={[
                { value: 0, label: '福豆' },
                { value: 1, label: '福卡' },
                { value: 2, label: '优惠券' },
              ]}
            ></Select>
          </Form.Item>
          {
            (awardType == 0 || awardType == 1) && <Form.Item
              label={`奖励额度${awardType == 0 ? '（个）' : '（元）'}`}
              name={['award', name + 'num']}
              required={isShowRequired}
              {...formCol}
              rules={[{ required: true, message: '请填写奖励额度' }]}
              help={awardType == 0 ? '10福豆 = 1元' : '1福卡 = 1元'}
            >
              <InputNumber disabled={disabled} style={{ width: '100%' }} min={0} className={"attr-item-input"} precision={awardType === 0 ? 0 : 2} onChange={validAward}  />
            </Form.Item>
          }
          {
            awardType == 0 && <Form.Item
              label="福豆有效期天（天）"
              name={['award', name + 'expireDay']}
              required={isShowRequired}
              {...formCol}
              rules={[
                { required: true, message: '请填写福豆有效期' },
                {
                  type: 'number',
                  min: 0.01,
                  message: '福豆有效期需大于0'
                }
              ]}
            >
              <InputNumber disabled={disabled} style={{ width: '100%' }} min={0} className={"attr-item-input"} precision={0} onChange={validAward}  />
            </Form.Item>
          }
          {
            awardType == 2 && <Form.Item
              label="优惠券名称"
              name={['award', name + 'couponName']}
              rules={[{ required: true, message: '请选择优惠券' }]}
              {...formCol}
              options={couponList}
              required={isShowRequired}
            >
              {
                (couponList || []).length == 0 ? (
                  <div>当前暂无符合拼团活动的优惠券，<a style={{ color: '#1890ff' }} onClick={() => {
                    window.open(`/seller/marketing/coupon-market-active?type=add&userScope=${userScope}`, '_self');
                  }}>去创建</a></div>
                ) : (
                  <Select
                    disabled={disabled}
                    options={couponList}
                    mode="multiple"
                    onChange={validAward} 
                  ></Select>
                )
              }
            </Form.Item>
          }
          {/* {
            awardType == 3 && (
              <FormSelect
                fProps={{
                  label: '优惠券名称',
                  name: ["item", "brandId"],
                  rules:  [{required: true, message: '请输入优惠券名称'}] ,
                }}
                url="/api/itemBrand/drop/down/box"
              />
            )
          } */}

          <Form.Item
            label={'发放时间'}
            required={isShowRequired}
            {...formCol}
          >
            确认收货
            <Form.Item noStyle name={['award', name + 'time']}
              rules={[{ required: true, message: '请填写发放时间' }]}
            >
              <InputNumber disabled={disabled} style={{ margin: '0 10px' }} min={0} className={"attr-item-input"} precision={0} onChange={validAward} />
            </Form.Item>
            h后
          </Form.Item>
        </div>
      ) : null
    }
  </Form.Item >
};

export default Award;
import { lib } from "../../../../common/react/utils/lib";
const parseSpecData = (valueSpec) => valueSpec
    .filter((spec) => spec)
    .map((spec) => ({
        id: spec.id,
        name: spec.name,
        itemSpecificationDetailParams: (spec.itemSpecificationDetailParams || []).filter((v) => v && v.name) // 过滤无 name 的值
    }))
    .filter((spec) => spec.itemSpecificationDetailParams.length > 0) // 过滤无有效值的规格

// 合并计算逻辑
const calculateRowSpans = (data, specs) => {
    return data.map((record, index) => {
        const spans = {};
        specs.forEach((spec, specIndex) => {
            const field = spec.name;
            const isLastSpec = specIndex === specs.length - 1;
            if (isLastSpec) {
                // 最后一列不合并，设置跨度为1
                spans[`${field}Span`] = 1;
            } else if (index === 0 || record[field] !== data[index - 1][field]) {
                let count = 1;
                for (let i = index + 1; i < data.length; i++) {
                    if (data[i][field] === record[field]) count++;
                    else break;
                }
                spans[`${field}Span`] = count;
            } else {
                spans[`${field}Span`] = 0;
            }
        });
        if (!record.id) {
            record.id = lib.generateNumberString(false, 18)
        }
        return { ...record, ...spans };
    });
};
// 计算规格组合（笛卡尔积）
const generateCombinations = (parsedSpecs, comonParams) => {
    if (!parsedSpecs || parsedSpecs.length === 0) {
        return []
    }
    const valuesArray = parsedSpecs.map((s) => s.itemSpecificationDetailParams);
    const combinations = valuesArray.reduce(
        (acc, curr) => acc.flatMap((a) => curr.map((b) => [...a, b])),
        [[]]
    );
    return combinations.map((comb, index) => {
        return {
            ...comonParams,
            specDetails: parsedSpecs.map((s, i) => comb[i].frontId),
            ...Object.fromEntries(parsedSpecs.map((s, i) => [s.name, comb[i].name])),
            // price: "",
        }
    });
};

export { parseSpecData, calculateRowSpans, generateCombinations }
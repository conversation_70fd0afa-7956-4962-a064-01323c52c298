
const { <PERSON>, SpaConfig<PERSON><PERSON><PERSON>, SearchList, DTUploaderFile } = dtComponents
import request from "../../../utils/plugins/axios/request";
const { useState, useEffect, useRef, Fragment, useCallback } = React;
import { parseSpecData, calculateRowSpans, generateCombinations } from './spec'
const { Button, InputNumber, Switch, Modal, message, Tabs, Form, Input, Radio, Descriptions, DatePicker, Space, Select, Slider, Tooltip } = antd;
import SelectGoods from "./select-goods"
const { RangePicker } = DatePicker;
const { PlusOutlined } = icons
import { roleEnum, baseAwardInfo, userScopeEnum } from './enum'
import customRequest from "../../../common/react/upload/custom-request";
import Award from './award'



export const Index = () => {
    // 当前营销活动ID
    const marketingToolId = __lib__.getParam("marketingToolId");
    const type = __lib__.getParam("type");
    const id = __lib__.getParam("activityId")
    const status = __lib__.getParam("activityStatus")
    const addBol = type === 'add';
    const editBol = type === 'edit';
    const onlyEditGoods = useRef(false)

    const tableLayout = {
        labelCol: { span: 3 },
        wrapperCol: { span: 19 }
    }

    // 活动图片
    const [activityImages, setActivityImages] = useState([])

    // 组团规则
    const [ruleImages, setRuleImages] = useState([])

    const [form] = Form.useForm();

    // 活动范围
    const [range, setRange] = useState(null);

    // 可进行中活动编辑->sku数组备份
    const editSkusCopy = useRef([])

    // 生成必填符号
    const requiredDom = (text) => {
        return <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{
                display: 'inline-block',
                marginRight: '4px',
                color: '#ff4d4f',
                fontSize: '14px',
            }}>*</span>{text}
        </div>
    }

    // 是否可以转换为数字 （排除布尔值和空字符串）
    function isStrictlyNumeric(value) {
        return !isNaN(parseFloat(value)) && isFinite(value) && 
               (typeof value === 'number' || (typeof value === 'string' && value.trim() !== ''));
      }

    // 请求活动奖励优惠券列表
    const queryCouponList = () => {
        request({
            url: `/mall-admin/api/couponActivity/markingActivityList`,
            method: 'POST',
            data: {
                userScope: userScopeEnum[marketingToolId],
                status: 'IN_EFFECT'
            },
            success(res) {
                if (!res) return
                setCouponList(res.map(item => {
                    return {
                        value: item.id,
                        label: item.name
                    }
                }))
            }
        })
    }
    // 活动奖励优惠券列表
    const [couponList, setCouponList] = useState([])
    // 活动奖励tabs配置
    const awardConfig = [
        {
            key: '1',
            label: '团长',
            children: <Award name={roleEnum.GROUP} form={form} couponList={couponList} disabled={onlyEditGoods.current} userScope={userScopeEnum[marketingToolId]} />,
        },
        {
            key: '2',
            label: '团员',
            children: <Award name={roleEnum.MEMBER} form={form} couponList={couponList} disabled={onlyEditGoods.current} userScope={userScopeEnum[marketingToolId]} />,
        }
    ];


    // 表单提交事件
    const onFinish = (values) => {
        if (addBol) {
            if (values.startTime < moment().startOf('minute').valueOf()) {
                form.setFieldValue('time', [])
                return message.error('活动开始时间小于当前时间');
            }
        }
        // 活动时间
        values.startTime = values.time[0].valueOf();
        values.endTime = values.time[1].valueOf();
        delete values.time;

        // 活动奖励
        let rewardList = [];
        let award = values.award;
        [roleEnum.GROUP, roleEnum.MEMBER].map((m, mIdx) => {
            let roleVal = mIdx + 1;
            if (award[m]) { // 有奖励
                let extra = {};
                let id = award[m + 'id'];
                let num = award[m + 'num'];
                let time = award[m + 'time']
                let expireDay = award[m + 'expireDay']
                let couponArr = award[m + 'couponName']
                switch (award[m + 'type']) {
                    case 0: // 福豆
                        extra = {
                            fudouFlag: 1,
                            fukaFlag: 0,
                            couponFlag: 0,
                            fudouNum: num,
                            fudouExpireDay: expireDay,
                            fudouDelayHour: time,
                        }
                        break;
                    case 1: // 福卡
                        extra = {
                            fudouFlag:0,
                            fukaFlag: 1,
                            couponFlag: 0,
                            fukaNum: num,
                            fukaDelayHour: time
                        }
                        break;
                    case 2: // 优惠券
                        extra = {
                            fudouFlag:0,
                            fukaFlag: 0,
                            couponFlag: 1,
                            couponIds: couponArr,
                            couponDelayHour: time
                        }
                        break;
                }
                rewardList.push({
                    ...extra,
                    id: id,
                    rewardRange: roleVal,
                    shopId: sessionStorage.shopId,
                })
            }
        })
        values.rewardList = rewardList;
        delete values.award

        // sku数据处理
        let goodsList = values.goodsList || [];
        let skuList = [];
        goodsList.forEach(goods => {
            goods.skuList.forEach(sku => {
                // 参与拼团活动
                if (sku.joinFlag) {
                    skuList.push({
                        id: sku.curId,
                        itemId: goods.itemId,
                        skuId: sku.id,
                        gbPrice: sku.gbPrice * 100,
                        skuLimit: sku.skuLimit,
                        isFudou: sku.isFudou ? 1 : 0,
                        isLijing: sku.isLijing ? 1 : 0,
                        isYouhui: sku.isYouhui ? 1 : 0,
                        joinFlag: sku.joinFlag ? 1 : 0,
                        skuStock: sku.skuStock,
                        shopId: sessionStorage.shopId,
                    })
                }
            })
        })
        values.skuList = skuList;

        if (!skuList.length) {
            return message.error('未选择参与拼团的商品');
        }
        delete values.goodsList;

        // 活动图片
        values.activityPics = JSON.stringify((values.activityPics || []).map(item => item.url))
        // 活动规则
        values.activityRules = (values.activityRules || []).length ? values.activityRules[0].url : '';

        let params = {
            ...values,
            marketingToolId,
            autoSuccess: values.autoSuccess ? 1 : 2, // 是否自动成团
        }

        let url = '';
        if (addBol) {
            url = '/mall-admin/api/gb_activity/save'
        } else {
            // 修改非进行中活动
            if (!onlyEditGoods.current) {
                params.id = parseInt(id);
                let skuIdsArr = params.skuList.map(item => item.skuId)
                params.removeSkuIdList = (editSkusCopy.current || []).filter(m => !skuIdsArr.includes(m.id)).map(n => n.id)
                url = '/mall-admin/api/gb_activity/updateActivityBeforeStart'
            } else { // 修改进行中活动
                console.log('value', values)
                params = {
                    id,
                    marketingToolId,
                    skuList: params.skuList,
                    startTime: values.startTime,
                    endTime: values.endTime,
                }
                url = '/mall-admin/api/gb_activity/updateActivityAfterStart'
            }
        }

        console.log('请求数据', params)

        request({
            url,
            method: 'POST',
            data: params,
            needMask: true,
            success: (res) => {
                message.success(addBol ? "创建成功" : '保存成功')
                setTimeout(() => {
                    location.href = `/seller/marketing/marketing-tools-group-list?marketingToolId=${marketingToolId}`
                }, 1000)
            }
        })
    }

    // 上传按钮样式
    const picUploadButton = () => {
        return <div>
            <PlusOutlined />
            <div style={{ marginTop: 8, color: "rgba(0, 0, 0, 0.65)" }}>上传</div>
        </div>
    }

    // 处理接口数据
    const dealData = (info) => {
        let baseInfo = info.gbActivityInfoDTO || {};
        // 对时间进行处理
        if (baseInfo.startTime || baseInfo.endTime) {
            baseInfo.time = [moment(baseInfo.startTime), moment(baseInfo.endTime)]
            delete baseInfo.startTime
            delete baseInfo.endTime
        }
        // 对图片进行处理
        let activityPics = [];
        try {
            activityPics = JSON.parse(baseInfo.activityPics || '').map(m => { return { url: m } })
        } catch (e) {
            activityPics = []
        }
        // 对是否自动成团进行处理
        baseInfo.autoSuccess = baseInfo.autoSuccess == 1;

        let activityRules = baseInfo.activityRules ? [{ url: baseInfo.activityRules }] : []
        baseInfo.activityPics = activityPics;
        baseInfo.activityRules = activityRules;
        setActivityImages(activityPics)
        setRuleImages(activityRules)

        // 对奖励信息进行处理
        let award = { ...baseAwardInfo }
        info.gbActivityConfigRewardDTOS.forEach(item => {
            let role = ''
            switch (item.rewardRange) {
                case 1:
                    role = roleEnum.GROUP
                    break;
                case 2:
                    role = roleEnum.MEMBER
                    break;
            }
            if (role) {
                if (item.fudouFlag || item.fukaFlag || item.couponFlag) {
                    award[role] = 1;
                    award[role + 'id'] = item.id; 
                    // 奖励福豆
                    if (item.fudouFlag) {
                        award[role + 'type'] = 0;
                        award[role + 'num'] = item.fudouNum;
                        award[role + 'expireDay'] = item.fudouExpireDay || '';
                        award[role + 'time'] = item.fudouDelayHour;
                    }
                    // 奖励福卡
                    if (item.fukaFlag) {
                        award[role + 'type'] = 1;
                        award[role + 'num'] = item.fukaNum;
                        award[role + 'time'] = item.fukaDelayHour;
                    }
                    // 奖励优惠券
                    if (item.couponFlag) {
                        award[role + 'type'] = 2;
                        award[role + 'couponName'] = (item.couponIds || []).map(m => parseInt(m));
                        award[role + 'time'] = item.couponDelayHour;
                    }
                }
            }
        })

        // 对活动列表信息进行处理
        editSkusCopy.current = (info.gbItemSkusResps || []).reduce((pre, cur) => {
            return [...pre, ...(cur.skus)];
        }, [])
        let goods = (info.gbItemSkusResps || []).map(cur => {
            const { item, skus } = cur;
            const parsedSpecs = parseSpecData(cur.itemsSpecificationList);
            const rawData = generateCombinations(parsedSpecs, {});
            let specList = calculateRowSpans(rawData, parsedSpecs);
            return {
                itemId: item.id,
                name: item.name,
                mainImage: item.mainImage,
                stockQuantity: item.stockQuantity,
                lowPrice: item.lowPrice / 100,
                highPrice: item.highPrice / 100,
                specDetails: cur.itemsSpecificationList || [],
                disabled: onlyEditGoods.current, // 正在进行中的团原先的商品不能编辑
                skuList: skus.map(n => {
                    let specInfo = specList.find(spec => spec.specDetails.sort().join('') == n.specDetails.sort().join(''))
                    let orderQuantityLimit = parseInt(JSON.parse(n.extra || '{}').orderQuantityLimit || 0);
                    let skuOrderSplitLine = parseInt(JSON.parse(n.extra || '{}').skuOrderSplitLine || 0);
                    let qLimit = Math.min(orderQuantityLimit, skuOrderSplitLine);
                    return {
                        ...specInfo,
                        ...(n.gbActivityConfigSkuDTO || {}),
                        gbPrice: (n.gbActivityConfigSkuDTO || {}).gbPrice ? (n.gbActivityConfigSkuDTO || {}).gbPrice / 100 : '',
                        ...n,
                        stockQuantity: n.skuStockQuantity, // 可售库存
                        skuStock: n.stockQuantity,// 活动库存
                        curId: (n.gbActivityConfigSkuDTO || {}).id || '',
                        price: n.price / 100,
                        orderQuantityLimit: qLimit,
                    }
                    
                })
            }
        })
        console.log('goods', goods)

        return {
            ...baseInfo,
            award: {
                ...award
            },
            goodsList: goods
        };
    }

    // 查询活动信息
    const getDetail = () => {
        request({
            url: `/mall-admin/api/gb_activity/detail`,
            method: 'POST',
            data: {
                id
            },
            needMask: true,
            success(res) {
                // 判断活动是否在进行中
                if (res.gbActivityInfoDTO && res.gbActivityInfoDTO.activityStatus !== 1) {
                    onlyEditGoods.current = true;
                }
                let data = dealData(res);
                console.log('活动信息', data)
                form.setFieldsValue({ ...data })
            }
        })
    }

    // 活动时间限制只能选择当前时间后
    const disabledDate = (current) => {
        // 获取当前时间（不包括毫秒）
        const now = moment().startOf('minute'); // 或者使用 .startOf('minute') 来包括当前分钟

        // 禁用29天范围
        // const tooLate = dates[0] && current.diff(dates[0], 'days') > 7;
        let bolRange = range && range[0] ? current > moment(range[0]).add(29, 'days') : false;

        // 禁用过去的时间
        return (current && current < now) || bolRange;
    };

    const onDateChange = (dates, dateStrings) => {
        setRange(dates)
    }


    useEffect(() => {
        // 便捷和查看查询活动信息
        if (editBol) {
            getDetail()
        }
        // 查询优惠券列表
        if (editBol || addBol) {
            queryCouponList();
        }
    }, [])


    return <Fragment>
        <Form
            form={form}
            {...tableLayout}
            onFinish={onFinish}
            initialValues={
                {
                    award: {
                        ...baseAwardInfo,
                    },
                    autoSuccess: false,
                }
            }

        >
            <Form.Item label={'活动名称'} name='activityName' wrapperCol={{ span: 10 }}
                rules={[{ required: true, message: '请填写活动名称' }]}
                extra="该名称将展示在活动页标题"
            >
                <Input placeholder="请输入" maxLength={10} showCount disabled={onlyEditGoods.current} />
            </Form.Item>

            <Form.Item label={'活动说明'} name='activityDes' wrapperCol={{ span: 10 }}
                rules={[{ required: true, message: '请填写活动说明' }]}
                extra="该名称将展示在活动页说明"
            >
                <Input placeholder="请输入" maxLength={30} showCount disabled={onlyEditGoods.current} />
            </Form.Item>
            <Form.Item
                label={`活动图片（${activityImages.length}/5）`}
                name="activityPics"
                valuePropName="activityPics"
                help="请保持上传图片宽度和高度一致，否则将影响用户查看体验"
                getValueFromEvent={e => {
                    if (Array.isArray(e)) {
                        return e;
                    }
                    return e && e.fileList;
                }}
                rules={[{ required: true, message: "请上传活动图片" }]}>
                <DTUploaderFile
                    maxCount={5}
                    accept={[".jpg", ".jpeg", ".png"]}
                    disabled={onlyEditGoods.current}
                    listType={"picture-card"}
                    fileList={activityImages}
                    customRequest={customRequest}
                    uploadButton={picUploadButton("上传")}
                    onChange={({ file, fileList }) => {
                        setActivityImages([...fileList])
                    }}
                />
            </Form.Item>
            <Form.Item
                label="组团规则"
                name="activityRules"
                valuePropName="activityRules"
                getValueFromEvent={e => {
                    if (Array.isArray(e)) {
                        return e;
                    }
                    return e && e.fileList;
                }}>
                <DTUploaderFile
                    maxCount={1}
                    accept={[".jpg", ".jpeg", ".png"]}
                    disabled={onlyEditGoods.current}
                    listType={"picture-card"}
                    fileList={ruleImages}
                    uploadButton={picUploadButton("上传")}
                    onChange={({ file, fileList }) => {
                        setRuleImages([...fileList])
                    }}
                />
            </Form.Item>
            <Form.Item label="成团人数" name="activityNum"
                wrapperCol={{ span: 8 }}
                help="成团人数应在2~30之间"
                rules={[{ required: true, message: '请填写成团人数' }]}
            >
                <InputNumber min={2} max={30} style={{ width: '90px' }} precision={0} disabled={onlyEditGoods.current} />
            </Form.Item>
            {
                marketingToolId == '2' && <Form.Item label="支付时限" name="paymentDeadline"
                    wrapperCol={{ span: 8 }}
                    rules={[
                        { required: true, message: '请填写支付时限' },
                        {
                            type: 'number',
                            min: 0.01,
                            message: '支付时限需大于0'
                        }]}
                >
                    <InputNumber min={0} style={{ width: '141px' }} precision={0} disabled={onlyEditGoods.current} addonAfter="分钟" />
                </Form.Item>
            }
            <Form.Item
                label="活动时间"
                name='time'
                help="时间范围不能超过29天"
                rules={[{ required: true, message: '请选择活动时间' }]}
            >
                <RangePicker disabled={onlyEditGoods.current} format="YYYY-MM-DD HH:mm" showTime={{ format: 'HH:mm' }} placeholder={['开始时间', '结束时间']} disabledDate={disabledDate} onCalendarChange={onDateChange} />
            </Form.Item>
            <Form.Item
                label="是否自动成团"
                name='autoSuccess'
                valuePropName="checked"
                help="开启自动成团后，到达活动结束时间未达成成团人数也可自动成团"
                rules={[{ required: true, message: '请选择是否自动成团' }]}
            >
                <Switch disabled={onlyEditGoods.current} />
            </Form.Item>
            <Form.Item label={'活动奖励'} name="award" required={true}
                rules={[
                    {
                        validator(rule, value) {
                            let award = value;
                            let roleArr = [roleEnum.GROUP, roleEnum.MEMBER]
                            for (let i = 0, len = roleArr.length; i < len; i++) {
                                let role = roleArr[i];
                                let roleName = ['团长', '团员'][i];
                                if (award[role]) { // 有奖励
                                    let type = award[role + 'type'];
                                    let num = award[role + 'num'];
                                    let time = award[role + 'time']
                                    let couponArr = award[role + 'couponName'] || []
                                    if (((type === 0 || type === 1) && !isStrictlyNumeric(num)) || !isStrictlyNumeric(time)) { // 福豆和福卡
                                        return Promise.reject(`请填写${roleName}奖励信息`)
                                    } else if (type === 2 && couponArr.length == 0) {
                                        return Promise.reject(`请填写${roleName}奖励信息`)
                                    }
                                }
                            }
                            return Promise.resolve("")
                        }
                    }
                ]}
            >
                <span></span>
                <Tabs
                    type="card"
                    destroyInactiveTabPane={false}
                    defaultActiveKey="1"
                    items={awardConfig}
                />
            </Form.Item>
            <Form.Item label={'选择商品'}
                required={true}
            >
                <SelectGoods
                    hideBtns={onlyEditGoods.current}
                    pageMethod={type}
                    requiredDom={requiredDom}
                    form={form}
                    parseSpecData={parseSpecData}
                    generateCombinations={generateCombinations}
                    calculateRowSpans={calculateRowSpans}
                />
            </Form.Item>
            {
                (editBol || addBol) && <Form.Item>
                    <Button type="primary" onClick={() => { form.submit() }}>{type === 'add' ? '创建' : '保存'}</Button>
                </Form.Item>
            }
        </Form>
    </Fragment >

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                method: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("marketing-tools-group-activity"));
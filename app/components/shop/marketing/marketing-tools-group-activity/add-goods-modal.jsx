import request from "../../../utils/plugins/axios/request";

const { useState, useEffect } = React;
const { Button, Modal, Table, Input, Row, Col, message, Form, Select } = antd;
const { useRowSelection } = dtHooks;
const { CopyOutlined } = icons;
const { FormSelect } = dtComponents
import copy from "../../../common/react/utils/copy-to-clipboard"
export default ({
    open,
    closeFn,
    selectItem,
}) => {
    const [searchForm] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    const [pagination, setPagination] = useState({
        pageSize: 10,
        size: 1,
    })
    const { rowSelection, selectedRows, setSelectedRows } = useRowSelection({
        rowKey: 'id',
        preserveSelectedRowKeys: true,
    })

    // 品牌列表
    const [brandList, setBrandList] = useState([])

    // 请求品牌列表
    const queryBrandList = () => {
        request({
            url: `/api/itemBrand/drop/down/box`,
            method: 'POST',
            success(res) {
                if (!res) return
                setBrandList(res.map(item => {
                    return {
                        value: item.id,
                        label: item.name
                    }
                }))
            }
        })
    }

    // 请求表格数据
    const getData = (pagination) => {
        let formData = searchForm.getFieldsValue();
        let params = {
            itemName: (formData.itemName || '').trim(),
            itemId: (formData.itemId || '').trim(),
            itemStatus: formData.itemStatus,
            brandId: formData.brandId
        }
        request({
            url: '/mall-admin/api/gb_item/Page',
            method: 'POST',
            needMask: true,
            data: {
                shopId: sessionStorage.shopId,
                currentPage: pagination.page,
                pageSize: pagination.pageSize,
                ...params
            }
        }).then(res => {
            let data = res.dataList || []
            let page = res.page || {}
            setDataSource(data.map(item => {
                return {
                    ...(item.itemDTO || {}),
                    selected: item.selected,
                    unSelectReason: item.unSelectReason,
                }
            }));
            setPagination({
                pageSize: page.pageSize,
                total: page.totalCount,
                current: page.currentPage
            })
        })
    }



    const onOk = () => {
        if (selectedRows.length === 0) {
            return message.error("请选择商品")
        }
        closeFn(selectedRows)
    }

    useEffect(() => {
        if (open) {
            // 查询品牌列表
            queryBrandList()
            setSelectedRows(selectItem.map(item => {
                return { id: item.itemId }
            }));
            setPagination({
                pageSize: 10,
                page: 1,
            })
            getData({
                pageSize: 10,
                page: 1,
            })
        } else {
            setDataSource([]);
            searchForm.resetFields();
        }
    }, [open])
    return (<Modal
        title="全部商品"
        open={open}
        onCancel={() => {
            closeFn();
        }}
        onOk={() => {
            onOk()
        }}
        width={1000}
        okText={'选择'}
        cancelText={"取消"}
    >
        <Form
            form={searchForm}
            layout="inline"
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
        >
            <Row span={24} style={{ width: '100%' }}>
                <Col span={6}>
                    <Form.Item label={'商品名称'} name='itemName'>
                        <Input />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label={'商品ID'} name='itemId'>
                        <Input />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item
                        label="商品状态"
                        name='itemStatus'
                    >
                        <Select
                            allowClear
                            options={[
                                { value: 1, label: '已上架' },
                                { value: -1, label: '已下架' }
                            ]}
                        ></Select>
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item
                        label="品牌"
                        name='brandId'
                    >
                        <Select
                            allowClear
                            options={brandList}
                        ></Select>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
        <div style={{ display: 'flex', columnGap: "10px", margin: '10px 0' }}>
            <Button type="primary" onClick={() => { getData({ pageSize: 10, page: 1 }) }}>搜索</Button>
            <Button onClick={() => { searchForm.resetFields(); }}>重置</Button>
        </div>
        <Table
            rowSelection={{
                ...rowSelection,
                getCheckboxProps: (record) => {
                    return {
                        disabled: (selectItem || []).map((item) => String(item.itemId)).includes(String(record.id)) || record.selected,
                        name: record.name,
                    }
                },
            }}
            dataSource={dataSource}
            rowKey="id"
            scroll={{
                y: 400
            }}
            pagination={{
                ...pagination,
                onChange(page, pageSize) {
                    getData({
                        page,
                        pageSize
                    })
                }
            }}
            columns={[
                {
                    title: '商品名称/ID', render: (row) => {
                        return <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                            <img src={row.mainImage} style={{ height: '50px', width: '50px', display: 'flex' }} />
                            <div style={{ marginLeft: '10px' }} onClick={() => {
                                copy(row.id)
                                message.success("复制成功")
                            }}>
                                <div>{row.name || '商品名称'}</div>
                                <div>商品ID:{row.id}<CopyOutlined /> </div>
                            </div>
                        </div>
                    }
                },
                {
                    title: '商品原价', render: (row) => {
                        return (<div>{`¥${row.lowPrice / 100}${row.lowPrice == row.highPrice ? '' : ' ~ ¥' + row.highPrice / 100}`}
                        </div>)
                    }
                },
                {
                    title: '商品库存', width: 150, dataIndex: 'stockQuantity', key: 'stockQuantity'
                },
                {
                    title: '不可参与原因', dataIndex: 'unSelectReason', key: 'unSelectReason'
                },
            ]}
        />
    </Modal>)
}

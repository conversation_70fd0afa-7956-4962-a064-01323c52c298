
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import TableModal from "../../../common/react/components/table-modal";
import { getParam } from "../../../items/seller/add-edit-module/util"
import request from "../../../utils/plugins/axios/request";
import SecretInfo from "./secret-info";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message} = antd;

export const Index = () => {
    const searchListRef = useRef()
    const [modalShow,setModalShow] = useState(false);
    const [search,setSearch] = useState({})
    // const

    const getConfig = async () => {
        const data = await new Promise((resolve, reject) => {
            $.ajax({
                url: "https://maria.yang800.com/api/data/v2/941/766",
                success: (res) => {
                    resolve(res)
                }
            })
        })
        return data.data;
    };

    return <Fragment>
        <SearchList
            searchConditionConfig={{
                size: "middle",
            }}
            paginationConfig={{size: "small",showPosition:'bottom'}}
            scrollMode="tableScroll"
            ref={searchListRef}
            getConfig={getConfig}

            tableCustomFun={{
                outMemberCountFn:(row)=>{
                    return <a onClick={()=>{
                        let obj ={
                            ...row,
                            outUserType: "2",
                            isAdmin:false
                        }
                        setModalShow(true);
                        setSearch(obj);
                    }}>{row.outCustomerCount||0}</a>
                },
                outCustomerCountFn:(row)=>{
                    return <a onClick={()=>{
                        // let obj ={
                        //   outUserType: "2",
                        //   isAdmin:false
                        // }
                        setModalShow(true);
                        setSearch({...row});
                    }}>{row.outMemberCount||0}</a>
                }
            }}
            renderModal={()=>{
                return <Fragment>
                    <TableModal
                        title={"详情"}
                        searchConfigs={modalSearchConfigs}
                        tableColumn={modalTableColumns}
                        closeFn={()=>{
                            setModalShow(false);
                        }}
                        show={modalShow}
                        dataUrl={'/mall-admin/api/family/group/member/page'}
                        searchParams={search}
                    />

                </Fragment>
            }}
        />
    </Fragment>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("external-crowd-manage"));
// 用户ID	文本录入，精确搜索	弹窗内
// 微信ID	文本录入，支持模糊搜索	弹窗内
// 用户手机号	文本录入，支持模糊搜索	弹窗内
// 用户名称	文本录入，支持模糊搜索	弹窗内
// 用户类型	单选下拉，枚举为：客户 / 企业内部员工	弹窗内
// 是否为群管理员	单选下拉，枚举为：是/否	弹窗内
// 入群时间	时间区间，A时间~B时间区间内命中数据，格式为年月日 时分秒	弹窗内
// 入群方式	单选下拉，枚举为：邀请入群、链接入群、扫码入群	弹窗内

const modalSearchConfigs = [
    {
        type: 'INPUT',
        labelKey: 'familyUserId',
        labelName: '小程序用户ID',
    },
    {
        type: 'INPUT',
        labelKey: 'outUserId',
        labelName: '微信ID',
    },
    {
        type: 'INPUT',
        labelKey: 'familyUserPhone',
        labelName: '用户手机号',
       
    },
    {
        type: 'INPUT',
        labelKey: 'outUserName',
        labelName: '用户名称',
    },
    {
        type: 'SELECT',
        labelKey: 'outUserType',
        labelName: '用户类型',
        list: [],
        url:'/mall-admin/api/family/groupMemberTypeList'
    },
    {
        type: 'SELECT',
        labelKey: 'isAdmin',
        labelName: '是否为群管理员',
        list: [{
            id: true,
            name: '是'
        }, {
            id: false,
            name: '否'
        }],
    },
    {
        type: 'DATERANGE',
        labelKey: 'outJoinTime',
        searchValue:["outJoinTimeStart","outJoinTimeEnd"],
        labelName: '入群时间',
    },
    {
        type: 'SELECT',
        labelKey: 'outJoinScene',
        labelName: '入群方式',
        list:[],
        url:'/mall-admin/api/family/joinSceneList'
    },
]

const modalTableColumns = [
    {
        title: '小程序用户ID',
        dataIndex: 'familyUserId',
        key: 'familyUserId',
    },
    {
        title: '微信ID',
        dataIndex: 'outUserId',
        key: 'outUserId',
    },
    {
        title: '用户手机号',
        dataIndex: 'outUserPhone',
        key: 'outUserPhone',
        with:200,
        render:(text,record,index)=>{
            return <SecretInfo content={text} type={'phone'} />
        }
    },
    {
        title: '用户名称',
        dataIndex: 'outUserName',
        key: 'outUserName',
    },
    {
        title: '用户类型',
        dataIndex: 'outUserTypeDesc',
        key: 'outUserTypeDesc',
    },
    {
        title: '是否为群管理员',
        // dataIndex: 'f',
        // key: 'f',
        render:(row)=>{
            return row.isAdmin?'是':'否'
        }
    },
    {
        title: '入群时间',
        dataIndex: 'outJoinTimeDesc',
        key: 'outJoinTimeDesc',
    },
    {
        title: '入群方式',
        dataIndex: 'outJoinSceneDesc',
        key: 'outJoinSceneDesc',
    },
    {
        title: '邀请人ID',
        dataIndex: 'outUserInvitorId',
        key: 'outUserInvitorId',
    },
]

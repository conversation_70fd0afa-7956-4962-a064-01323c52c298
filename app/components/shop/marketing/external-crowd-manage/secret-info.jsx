const { <PERSON>, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message} = antd;
const {EyeOutlined,EyeInvisibleOutlined} = icons;
export default ({content,type})=>{
    const [text,setText] = useState('');
    const [eye,setEye] = useState(false);
    const secretInfo = ()=>{
        switch (type) {
            case "phone":
                // setText('手机号');
                content && setText(content.replace(/^(\d{3})\d+(\d{2})$/, '$1****$2'))
                break;
        
            default:
                break;
        }
    }
    useEffect(()=>{
        secretInfo(type);
    },[content,type])
    return (<Fragment>
        <div className="secret-info">
            <div className="secret-info-title">{text}

                {
                    text? <span style={{marginLeft:"10px"}} onClick={()=>{
                                    setEye(!eye);
                                    // secretInfo(type);
                                    if(!eye){
                                        setText(content)
                                    }else{
                                        secretInfo(type);
                                    }
                                }}>
                                    {
                                        !eye?
                                        <EyeOutlined style={{color:"blue"}}/>:
                                        <EyeInvisibleOutlined />
                                    }
                                </span> 
                    :''
                }
               
            </div>
           
        </div>
    </Fragment>)
}
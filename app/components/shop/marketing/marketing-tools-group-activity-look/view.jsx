import request from "../../../utils/plugins/axios/request";
import { roleEnum, baseAwardInfo, userScopeEnum } from '../marketing-tools-group-activity/enum'
import { parseSpecData, calculateRowSpans, generateCombinations } from '../marketing-tools-group-activity/spec'
const { useRef, useState, Fragment, useEffect } = React;
const { Space, Radio, Table, Switch, Form, Image, Button, Row, Col, Card, Tabs, Select, InputNumber } = antd;
const { Spa, SpaConfigProvider } = dtComponents;

const GroupActivityLook = () => {

  const id = __lib__.getParam("activityId")

  const marketingToolId = __lib__.getParam("marketingToolId");

  const [form] = Form.useForm();

  const [labelSpan, contentSpan, totalSpan] = [3, 19, 24]

  // 更多信息
  const moreDom = <span style={{ color: '#999' }}>详情请展开查看</span>;

  const awardTypeEnum = {
    0: '福豆',
    1: "福卡",
    2: '优惠券'
  }

  // 活动信息
  const [info, setInfo] = useState({})

  // 子表格展开的keys
  const [expandedRowKeys, setExpandedRowKeys] = useState([])

  useEffect(() => {
    // 查询活动信息
    getDetail();
    // 查询优惠券列表
    queryCouponList();
  }, [])

  // 请求活动奖励优惠券列表
  const queryCouponList = () => {
    request({
      url: `/mall-admin/api/couponActivity/markingActivityList`,
      method: 'POST',
      data: {
        userScope: userScopeEnum[marketingToolId]
      },
      success(res) {
        if (!res) return

        if (!res) return
        let obj = {}
        res.map(item => {
          obj[item.id] = item.name
        })
        setCouponEnum(obj)
        console.log('couponEnum', obj, res)
      }
    })
  }
  // 活动奖励优惠券列表
  const [couponEnum, setCouponEnum] = useState({})

  // 处理接口数据
  const dealData = async (info) => {
    let baseInfo = info.gbActivityInfoDTO || {};
    // 对时间进行处理
    if (baseInfo.startTime || baseInfo.endTime) {
      baseInfo.time = `${moment(baseInfo.startTime).format("YYYY-MM-DD HH:mm")} ~ ${moment(baseInfo.endTime).format("YYYY-MM-DD HH:mm")}`
      delete baseInfo.startTime
      delete baseInfo.endTime
    }
    // 对图片进行处理
    let activityPics = [];
    try {
      activityPics = JSON.parse(baseInfo.activityPics || '').map(m => { return { url: m } })
    } catch (e) {
      activityPics = []
    }

    // 对是否自动成团进行处理
    baseInfo.autoSuccess = baseInfo.autoSuccess == 1;

    let activityRules = baseInfo.activityRules ? [{ url: baseInfo.activityRules }] : []
    baseInfo.activityPics = activityPics;
    baseInfo.activityRules = activityRules;

    // 对奖励信息进行处理
    let award = { ...baseAwardInfo }
    info.gbActivityConfigRewardDTOS.forEach(item => {
      let role = ''
      switch (item.rewardRange) {
        case 1:
          role = roleEnum.GROUP
          break;
        case 2:
          role = roleEnum.MEMBER
          break;
      }
      if (role) {
        if (item.fudouFlag || item.fukaFlag || item.couponFlag) {
          award[role] = 1;
          // 奖励福豆
          if (item.fudouFlag) {
            award[role + 'type'] = 0;
            award[role + 'num'] = item.fudouNum;
            award[role + 'expireDay'] = item.fudouExpireDay || '';
            award[role + 'time'] = item.fudouDelayHour;
          }
          // 奖励福卡
          if (item.fukaFlag) {
            award[role + 'type'] = 1;
            award[role + 'num'] = item.fukaNum;
            award[role + 'time'] = item.fukaDelayHour;
          }
          // 奖励优惠券
          if (item.couponFlag) {
            award[role + 'type'] = 2;
            award[role + 'couponName'] = (item.couponIds || []).map(m => parseInt(m))
            award[role + 'time'] = item.couponDelayHour;
          }
        }
      }
    })


    // 对活动列表信息进行处理
    let goods = (info.gbItemSkusResps || []).map(cur => {
      const { item, skus } = cur;
      const parsedSpecs = parseSpecData(cur.itemsSpecificationList);
      const rawData = generateCombinations(parsedSpecs, {});
      let specList = calculateRowSpans(rawData, parsedSpecs);
      console.log('specList', specList)
      return {
        itemId: item.id,
        name: item.name,
        mainImage: item.mainImage,
        stockQuantity: item.stockQuantity,
        lowPrice: item.lowPrice / 100,
        highPrice: item.highPrice / 100,
        specDetails: cur.itemsSpecificationList || [],
        disabled: true, // 正在进行中的团原先的商品不能编辑
        skuList: skus.map(n => {
          let specInfo = specList.find(spec => spec.specDetails.sort().join('') == n.specDetails.sort().join(''))
          return {
            ...specInfo,
            ...(n.gbActivityConfigSkuDTO || {}),
            gbPrice: (n.gbActivityConfigSkuDTO || {}).gbPrice ? (n.gbActivityConfigSkuDTO || {}).gbPrice / 100 : '',
            ...n,
            stockQuantity: n.skuStockQuantity, // 可售库存
            skuStock: n.stockQuantity,// 活动库存
            price: n.price / 100,
            uunitQuantity: (n.extraMap || {}).unitQuantity,
          }
        })
      }
    })

    return {
      ...baseInfo,
      award: {
        ...award
      },
      goods,
    };
  }

  // 查询活动信息
  const getDetail = () => {
    request({
      url: `/mall-admin/api/gb_activity/detail`,
      method: 'POST',
      data: {
        id
      },
      needMask: true,
      async success(res) {
        let data = await dealData(res);
        setInfo(data)
        form.setFieldsValue({
          award: data.award,
          goodsList: data.goods
        })
        setExpandedRowKeys([...(data.goods.filter(m => m.skuList && m.skuList.length > 1).map(n => n.itemId))])
      }
    })
  }

  // 表格列配置
  const columns = [
    {
      title: '商品信息',
      width: 360,
      render: (row, _, index) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img src={row.mainImage} style={{ width: '50px', heigth: '50px' }} />
          <div style={{ marginLeft: '10px' }}>
            <div>{row.name}</div>
            <div>商品ID：{row.itemId}</div>
          </div>
        </div>)
    },
    {
      title: '原销售价',
      width: 150,
      render: (row, _, index) => (
        <span>{`¥${row.lowPrice}${row.lowPrice == row.highPrice ? '' : ' ~ ¥' + row.highPrice}`}</span>)
    },
    {
      title: '拼团价',
      require: true,
      width: 150,
      render: (row, _, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          return <span>{row['skuList'][0]['gbPrice']}</span>
        }
      }
    },
    {
      title: '可售库存',
      dataIndex: 'stockQuantity',
      key: 'stockQuantity',
      width: 120,
    },
    {
      title: '活动库存',
      width: 150,
      render: (row, _, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          return <span>{row['skuList'][0]['skuStock']}</span>
        }
      }
    },
    {
      title: '限购数量',
      width: 150,
      render: (row, _, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          return <span>{row['skuList'][0]['skuLimit']}</span>
        }
      }
    },
    {
      title: '是否参与拼团',
      width: 120,
      render: (_, row, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          return <Form.Item name={['goodsList', index, 'skuList', 0, 'joinFlag']} valuePropName="checked" >
            <Switch
              disabled={row.disabled}
              checked={row['joinFlag']}
              onChange={(val) => { changeSubByField(row.itemId, index, 'joinFlag', val) }}
            />
          </Form.Item>

        }
      },
    },
    {
      title: '可用福豆',
      width: 120,
      render: (row, _, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          if (row.joinFlag) {
            return <Form.Item name={['goodsList', index, 'skuList', 0, 'isFudou']} valuePropName="checked">
              <Switch disabled={row.disabled} />
            </Form.Item>
          } else {
            return '-'
          }
        }
      }
    },
    {
      title: '可用福卡',
      width: 120,
      render: (row, _, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          if (row.joinFlag) {
            return <Form.Item name={['goodsList', index, 'skuList', 0, 'isLijing']} valuePropName="checked" >
              <Switch disabled={row.disabled} />
            </Form.Item>
          } else {
            return '-'
          }
        }
      }
    },
    {
      title: '可用优惠券',
      width: 120,
      render: (row, _, index) => {
        if (row.skuList && row.skuList.length > 1) {
          return moreDom
        } else {
          if (row.joinFlag) {
            return <Form.Item name={['goodsList', index, 'skuList', 0, 'isYouhui']} valuePropName="checked" >
              <Switch disabled={row.disabled} />
            </Form.Item>
          } else {
            return '-'
          }
        }
      }
    },
  ]

  const getAwardContent = (role) => {
    let award = info['award'] || {}
    let hasFlag = award[role] == 1;
    let type = award[role + 'type'];
    return <Space direction="vertical">
      <Row >
        <Radio.Group
          value={award[role]}
          disabled
          options={[
            { value: 0, label: '无奖励' },
            { value: 1, label: '有奖励' },
          ]}
        ></Radio.Group>
      </Row>

      {
        hasFlag && <Fragment>
          <Row >
            <Col className="label">奖励类型：</Col>
            <Col >{awardTypeEnum[type] || ''}</Col>
          </Row>
          {
            (type == 0 || type == 1) && <Row >
              <Col className="label">奖励额度{type == 0 ? '（个）' : '（元）'}：</Col>
              <Col >{award[role + 'num'] || ''}</Col>
            </Row>
          }
          {
            type == 0 && <Row >
              <Col className="label">福豆有效期（天）：</Col>
              <Col >{award[role + 'expireDay'] || ''}</Col>
            </Row>
          }
          {
            type == 2 && <Row >
              <Col className="label">优惠券名称：</Col>
              <Col >{(award[role + 'couponName'] || []).map(item => couponEnum[item]).join('、')}</Col>
            </Row>
          }
          <Row >
            <Col className="label">发放时间：</Col>
            <Col >{`确认收货  ${award[role + 'time']}  h后`}</Col>
          </Row>
        </Fragment>
      }
    </Space>;
  }


  // 活动奖励tabs配置
  const awardConfig = [
    {
      key: '1',
      label: '团长',
      children: getAwardContent(roleEnum.GROUP),
    },
    {
      key: '2',
      label: '团员',
      children: getAwardContent(roleEnum.MEMBER),
    }
  ];

  // 扩展列
  const expandRow = (bool, row) => {
    if (bool) {
      setExpandedRowKeys([...expandedRowKeys, row.itemId])
    } else {
      const idx = expandedRowKeys.findIndex(m => m === row.itemId);
      const copy = [...expandedRowKeys];
      copy.splice(idx, 1);
      setExpandedRowKeys(copy)
    }
  }

  // 嵌套表格渲染函数
  const expandedRowRender = (record, idx) => {
    if (record.skuList && record.skuList.length > 1) {
      // 当前行key
      let key = record.itemId;


      const specDetail = record.specDetails || [];

      const addCols = specDetail.map((item, index) => {
        return {
          title: item.name,
          dataIndex: item.name,
          fixed: 'left',
          width: 360 / (specDetail.length || 1),
          render: (value, _, index) => {
            const span = record['skuList'][index][`${item.name}Span`] || 0;
            return { children: value, props: { rowSpan: span } };
          }
        }
      })

      console.log('addCols', addCols)

      // 是否可编辑
      const disabledFlag = true

      // 嵌套表格列配置
      const cols = [
        ...addCols,
        {
          title: '原销售价（元）',
          dataIndex: 'price',
          key: 'price',
          width: 150,
        },
        {
          title: '拼团价（元）',
          key: 'gbPrice',
          dataIndex: 'gbPrice',
          width: 150,
          render: (_, row, index) => (
            row.gbPrice || '-'
          ),
        },
        {
          title: '可售库存',
          dataIndex: 'stockQuantity',
          key: 'stockQuantity',
          width: 120,
        },
        {
          title: '活动库存（件）',
          dataIndex: 'skuStock',
          key: 'skuStock',
          width: 150,
          render: (_, row, index) => (
            row.skuStock || '-'
          ),
        },
        {
          title: '限购数量',
          dataIndex: 'skuLimit',
          key: 'skuLimit',
          width: 150,
        },
        {
          title: '是否参与拼团',
          dataIndex: 'field',
          width: 120,
          key: 'field',
          render: (_, row, index) => (
            <Form.Item name={['goodsList', idx, 'skuList', index, 'joinFlag']} valuePropName="checked">
              <Switch
                disabled={disabledFlag}
                checked={((row.skuList || {})[index] || {})['joinFlag']}
                onChange={(val) => { changeSubByField(key, index, 'joinFlag', val) }}
              />
            </Form.Item>
          ),
        },
        {
          title: '可用福豆',
          dataIndex: 'isFudou',
          key: 'isFudou',
          width: 120,
          render: (_, row, index) => {
            if (row.joinFlag) {
              return <Form.Item name={['goodsList', idx, 'skuList', index, 'isFudou']} valuePropName="checked">
                <Switch disabled={disabledFlag} />
              </Form.Item>

            } else {
              return '-'
            }
          }
        },
        {
          title: '可用福卡',
          dataIndex: 'isLijing',
          key: 'isLijing',
          width: 120,
          render: (_, row, index) => {
            if (row.joinFlag) {
              return <Form.Item name={['goodsList', idx, 'skuList', index, 'isLijing']} valuePropName="checked">
                <Switch disabled={disabledFlag} />
              </Form.Item>
            } else {
              return '-'
            }
          }
        },
        {
          title: '可用优惠券',
          dataIndex: 'isYouhui',
          key: 'isYouhui',
          width: 120,
          render: (_, row, index) => {
            if (row.joinFlag) {
              return <Form.Item name={['goodsList', idx, 'skuList', index, 'isYouhui']} valuePropName="checked">
                <Switch disabled={disabledFlag} />
              </Form.Item>
            } else {
              return '-'
            }
          }
        },

      ];

      let totalWidth = cols.reduce((total, current) => total + current.width, 0)
      return <Table
        columns={cols}
        dataSource={record.skuList}
        scroll={{
          x: totalWidth + 'px',
        }}
        sticky
        pagination={false} />;
    } else {
      return null;
    }

  }

  return <div className="wrap">
    <Space direction="vertical">
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">活动名称：</Col>
        <Col span={contentSpan} className="content">{info.activityName}</Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">活动说明：</Col>
        <Col span={contentSpan} className="content">{info.activityDes}</Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">活动图片：</Col>
        <Col span={contentSpan} className="content img-containter">
          {
            (info.activityPics || []).map((item, index) => {
              return <Image key={index} width={102} height={102} src={item.url} />
            })
          }
        </Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">组团规则：</Col>
        <Col span={contentSpan} className="content">
          {
            (info.activityRules || []).map((item, index) => {
              return <Image key={index} width={102} height={102} src={item.url} />
            })
          }
        </Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">成团人数：</Col>
        <Col span={contentSpan} className="content">{info.activityNum}</Col>
      </Row>
      {
        marketingToolId == '2' && (
          <Row span={totalSpan}>
            <Col span={labelSpan} className="label">支付时限：</Col>
            <Col span={contentSpan} className="content">{`${info.paymentDeadline} 分钟`}</Col>
          </Row>
        )
      }
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">活动时间：</Col>
        <Col span={contentSpan} className="content">{info.time}</Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">是否自动成团：</Col>
        <Col span={contentSpan} className="content"><Switch disabled checked={info.autoSuccess} /></Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">活动奖励：</Col>
        <Col span={contentSpan} className="content" style={{ marginBottom: '10px' }}>
          <Tabs
            type="card"
            destroyInactiveTabPane={false}
            defaultActiveKey="1"
            items={awardConfig}
          />
        </Col>
      </Row>
      <Row span={totalSpan}>
        <Col span={labelSpan} className="label">活动商品：</Col>
        <Col span={contentSpan} className="content">
          <Form form={form}>
            <Form.Item name='goodsList'>
              <Table
                dataSource={info.goods}
                columns={columns}
                className='goods-table'
                rowKey='itemId'
                scroll={{ y: 500, x: columns.reduce((total, current) => total + current.width, 0) }}
                pagination={false}
                sticky
                expandable={{
                  expandedRowRender,
                  expandRowByClick: true,
                  rowExpandable: (record) => record.skuList && record.skuList.length > 1,
                  rowKey: 'itemId',
                  expandedRowKeys,
                  onExpand: expandRow,
                }}
              />
            </Form.Item>

          </Form>

        </Col>
      </Row>
    </Space>
  </div>
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
  _openPage: () => {
  }, request: {
    request: (params) => {
      params.url = params.url + "?shopId=" + sessionStorage.shopId
      const obj = Object.assign(params, {})
      request(obj);
    }
  }
})}>
  <GroupActivityLook />
</SpaConfigProvider>, document.getElementById("marketing-tools-group-activity-look")
);

const depFn = (arr, fn) => {
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    fn(item);
    if (Array.isArray(item.children)) {
      depFn(item.children, fn);
    }
  }
};

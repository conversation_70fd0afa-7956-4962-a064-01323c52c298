import request from "../../../utils/plugins/axios/request";

const { useRef, useState, Fragment, useEffect } = React;
const { Space, Switch, message, Modal, Button, Image, Tag, Row, Col, Card } = antd;
const { SearchList, Spa, SpaConfigProvider } = dtComponents;
const GroupList = () => {

  // 当前营销活动ID
  let marketingToolId = __lib__.getParam("marketingToolId");

  const searchListRef = useRef()

  const [statusEnum , setStatusEnum] = useState([]);

  const [toolInfo, setToolInfo] = useState({});
  const getConfig = async function () {
    let url = "https://maria.yang800.com/api/data/v2/952/784";
    const data = await axios.get(url)
    return data.data.data;
  };

  const pageEnum = {
    // 拼团购
    1: {
      addUrl: `/seller/marketing/marketing-tools-group-activity`,
      editUrl: `/seller/marketing/marketing-tools-group-activity`,
      detailUrl: `/seller/marketing/marketing-tools-group-activity-look`,
      dataUrl: `/seller/marketing/active-data`
    },
    // 分享购
    2: {
      addUrl: `/seller/marketing/marketing-tools-share-activity`,
      editUrl: `/seller/marketing/marketing-tools-share-activity`,
      detailUrl: `/seller/marketing/marketing-tools-share-activity-look`,
      dataUrl: `/seller/marketing/active-data-share`
    },
  }

  // 删除活动
  const delActivity = (id) => {
    Modal.confirm({
      title: '确认',
      content: '是否确认删除',
      onOk: () => {
        request({
          url: "/mall-admin/api/gb_activity/deleteActivityBeforeStart",
          type: "POST",
          data: {
            id
          },
          success: (data) => {
            message.success('删除成功')
            searchListRef.current.load();
          }
        })
      }
    })
  }

  const getMarketingToolList = () => {
          request({
              url: '/mall-admin/api/marketing_tools/list',
              method: 'POST',
              data: {},
              success: (data) => {
                  let info = (data || []).filter(item => item.id == marketingToolId);
                  console.log('info', info, data)
                  setToolInfo(info[0] || {})
              }
          })
      }

  // 作废活动
  const repealActivity = (id) => {
    request({
      url: '/mall-admin/api/gb_activity/check_activity_pause',
      method: 'post',
      data: {
        id,
      }
    }).then((res = {}) => {
      Modal.confirm({
        // title: '确认',
        content: res || '',
        onOk: () => {
          request({
            url: "/mall-admin/api/gb_activity/updateStatus",
            type: "POST",
            data: {
              id,
              activityStatus: 4, // 使其失效
            },
            success: (data) => {
              message.success('作废成功')
              searchListRef.current.load();
            }
          })
        }
      })
    })
  }

  // 获取活动状态枚举
  const getEnum = (enumType, fn) => {
    request({
      url: '/mall-admin/api/gb_common/enum',
      method: 'POST',
      data: { enumType },
      success: (data) => {
        if (typeof fn == 'function') {
          fn(data)
        }
      }
    })
  }

  useEffect(() => {
    getEnum('ActivityStatus', setStatusEnum)
    getMarketingToolList()
  }, [])


  return (
    <SearchList
      ref={searchListRef}
      scrollMode={"tableScroll"}
      paginationConfig={{ size: "default", showPosition: 'bottom' }}
      searchConditionConfig={{
        size: "middle",
      }}
      renderSearchTopView={() => {
        return (
          <div className="top-content">
            <h3 className='top-content-title'>{toolInfo.mtName}</h3>
            <div className='top-content-desc'>
              <span>{toolInfo.mtName}是一款商家自运营工具，对商品进行拼团促销，增强促销氛围，以提高下单转化率，提升店铺GMV</span>
              <Button type='primary' onClick={() => {
                window.open(`${pageEnum[marketingToolId].addUrl}?type=add&marketingToolId=${marketingToolId}`)
              }}>立即新建</Button>
            </div>
            <div className="card-containter">
              <Card className='top-content-card'>
                <Image height="30px" width="30px" src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/93129195263.png" preview={false}></Image>
                <span className='top-content-card-text'>下单转化率平均提升可达5%</span>
              </Card>
              <Card className='top-content-card'>
                <Image height="30px" width="30px" src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/93129184837.png" preview={false}></Image>
                <span className='top-content-card-text'>爆款商品必备工具</span>
              </Card>
            </div>

          </div>
        )
      }

      }
      getConfig={getConfig}
      tableCustomFun={{
        time: (row) => {
          return <span>{`${moment(row.startTime).format('YYYY-MM-DD HH:mm')} ~ ${moment(row.endTime).format('YYYY-MM-DD HH:mm')}`}</span>
        },
        activityStatus: (row) => {
          return (statusEnum.find(item => item.id == row.activityStatus) || {}).name || '';
        },
        operateFn: (row) => {
          return <Space wrap>
            {/* 进行中 */}
            {
              row.activityStatus == 2 &&
              <a  onClick={() => {repealActivity((row.id))}}>作废</a>
            }
            {/* 未开始 */}
            {
              (row.activityStatus == 1 || row.activityStatus == 2) &&
              <a  onClick={() => {
                window.open(`${pageEnum[marketingToolId].editUrl}?type=edit&activityId=${row.id}&marketingToolId=${marketingToolId}`)
              }}>编辑</a>
            }
            {/* 已失效和已结束 */}
            {
              [1].includes(row.activityStatus) &&
              <a type='link' onClick={() => {
                delActivity(row.id)

              }}>删除</a>
            }
            <a type='link' onClick={() => {
              window.open(`${pageEnum[marketingToolId].detailUrl}?activityId=${row.id}&marketingToolId=${marketingToolId}`)
            }}>活动详情</a>
            <a type='link' onClick={() => {
              window.open(`${pageEnum[marketingToolId].dataUrl}?activityId=${row.id}`)
            }}>活动数据</a>

          </Space>
        },
      }}
    />

  )
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
  _openPage: () => {
  }, request: {
    request: (params) => {
      params.url = params.url + "?shopId=" + sessionStorage.shopId
      const obj = Object.assign(params, {})
      request(obj);
    }
  }
})}>
  <GroupList />
</SpaConfigProvider>, document.getElementById("marketing-tools-group-list")
);

const depFn = (arr, fn) => {
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    fn(item);
    if (Array.isArray(item.children)) {
      depFn(item.children, fn);
    }
  }
};

<style type="text/css">
    .magic-radio {
      position: absolute;
      display: none; }
    .magic-radio + label {
      position: relative;
      padding-left: 30px;
      cursor: pointer;
      vertical-align: middle;
      font-size: 14px;
      color: black; }
    .magic-radio + label.radio:before {
        position: absolute;
        top: 0;
        left: 0;
        display: inline-block;
        width: 16px;
        height: 16px;
        content: '';
        border: 1px solid #614544; }
    .magic-radio + label:after {
        position: absolute;
        display: none;
        content: ''; }
     .magic-radio:checked + label:before {
      animation-name: none; }
    .magic-radio:checked + label:after {
      display: block; }
    .magic-radio + label:before {
      border-radius: 50%; }
    .magic-radio + label.radio:after {
      top: 4px;
      left: 4px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #614544; }
</style>
{{#component "component-standard-container coupon-create js-comp"}}
  <div class="page-react">
  <div class="component-header">
    <a href="/seller/marketing/activity-management?type={{#equals type 2}}2{{else}}1{{/equals}}&promotionDefId={{promotionDefId}}">< 返回{{#equals type 2}}店铺{{else}}商品{{/equals}}优惠券列表</a>
  </div>
  <div class="component-body">
    <form class="form form-aligned coupon-form" id="coupon-form" data-type="{{type}}" data-promotiondefid="{{promotionDefId}}" data-promotionid="{{promotionId}}">
      <fieldset>
        <div class="coupon-common">
          <div class="control-group">
            <label class="control-label"><span class="required">*</span>优惠券名称：</label>
            <input type="text" name="name" class="input-medium" value="{{_DATA_.name}}" pattern="^[\da-zA-Z\u4E00-\u9FA5]{1,15}$" required {{#equals method "detail"}}disabled{{/equals}}>
            <span class="note-error note-icon hint--top" aria-label="名称支持英文，数字和汉字，长度不超过15个字符">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
          </div>
          <div class="control-group">
            <label class="control-label"><span class="required">*</span>发放总数：</label>
            <input type="text" name="sendLimit"  class="" value="{{_DATA_.extra.sendLimit}}" maxLength="10" autocomplete="off" pattern="^(-)?\d{1,10}$" required {{#equals method "detail"}}disabled{{/equals}}>
            <span class="note-error note-icon hint--top" aria-label="请输入数字">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
            张
            <span class="cgray">如不限制张数，填写-1即可</span>
          </div>
          <div class="control-group">
            <label class="control-label"><span class="required">*</span>优惠方式：</label>
            <input type="radio" value="0" class="js-radio-conditionParams-isPerFee magic-radio" id="isPerFeeShopDiscount1" name="isPerFeeShopDiscount" {{#if _DATA_.behaviorParams.discountType}}{{else}}checked{{/if}} {{#equals method "detail"}}disabled{{/equals}}><label for="isPerFeeShopDiscount1" class="sp vl-bl radio">优惠券</label>
            <input type="radio" value="1" class="js-radio-conditionParams-isPerFee magic-radio" id="isPerFeeShopDiscount2" name="isPerFeeShopDiscount" {{#equals _DATA_.behaviorParams.discountType "per"}}checked{{/equals}} {{#equals method "detail"}}disabled{{/equals}}><label for="isPerFeeShopDiscount2" class="sp vl-bl radio">每满减券</label>
          </div>
          <div class="control-group choose-item-condition hide">
            <label class="control-label"><span class="required">*</span>优惠对象：</label>
            <input type="hidden" value="{{_DATA_.conditionParams.firstBought}}">
            <input type="radio" class="magic-radio" id="firstBought1" name="firstBought" value="false" {{#equals _DATA_.conditionParams.firstBought false}}checked{{/equals}}><label for="firstBought1" class="radio">无门槛</label>
            <input type="radio" class="magic-radio" id="firstBought2" name="firstBought" value="true" {{#equals _DATA_.conditionParams.firstBought true}}checked{{/equals}}><label for="firstBought2" class="radio">首次购买</label>
          </div>
          <div class="control-group choose-item-reduceFee">
            <label class="control-label"><span class="required">*</span>面值：</label>
            <input type="text" name="reduceFee" class="" value="{{formatPriceExt _DATA_.behaviorParams.reduceFee}}" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" maxlength="10"  {{#equals method "detail"}}disabled{{/equals}}>
            <span class="note-error note-icon hint--top" aria-label="请输入最长10位数字">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
            元
          </div>
          <div class="control-group" style="margin-top:10px">
            <label class="control-label"><span class="required">*</span>每人限领：</label>
            {{!--<input type="text" name="receiveLimit" class="" value="{{_DATA_.extra.receiveLimit}}" maxLength="10" autocomplete="off" pattern="^\d{1,10}$" required {{#equals method "detail"}}disabled{{/equals}}>
            <span class="note-error note-icon hint--top" aria-label="请输入数字">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>--}}
            <input type="text" name="receiveLimit" value="1" autocomplete="off" disabled>
            张
          </div>
          <div class="control-group">
            <label class="control-label"><span class="required">*</span>有效期：</label>
            <input class="order-input input-date js-common-start-at datepickerIpt" style="background-color:white;" type="text" name="startAt" value="{{formatDate _DATA_.startAt}}" placeholder="{{i18n "Please select a date" bundle="shop"}}" pattern="^\d{4}-\d{2}-\d{2}$" required {{#equals method "detail"}}disabled{{/equals}}>
            &nbsp;-&nbsp;
            <input class="order-input input-date js-common-end-at datepickerIpt" type="text" style="background-color:white;" name="endAt" value="{{formatDate _DATA_.endAt}}" placeholder="{{i18n "Please select a date" bundle="shop"}}" pattern="^\d{4}-\d{2}-\d{2}$" required {{#equals method "detail"}}disabled{{/equals}}>
          </div>
          <div>

          </div>
          <div class="control-group choose-item-reduceFee">
            <label class="control-label"><span class="required">*</span>使用条件：</label>
            <span class="w40">{{#equals type 2}}订单{{else}}商品{{/equals}}满</span>
            &nbsp;&nbsp;
            <input type="text" name="conditionFee" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" maxlength="10" value="{{formatPriceExt _DATA_.conditionParams.conditionFee}}"  {{#equals method "detail"}}disabled{{/equals}}>
            <span class="note-error note-icon hint--top" aria-label="请输入最长10位数字">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
            元 可用&nbsp;&nbsp;&nbsp;&nbsp;<span class="cgray">每单限用 1 张</span>
          </div>
          <div class="control-group choose-item-condition hide">
            <label class="control-label"><span class="required">*</span>使用条件：</label>
            <span class="w40">{{#equals type 2}}订单{{else}}商品{{/equals}}每满</span>
            &nbsp;&nbsp;
            <input type="text" style="width:160px;" name="conditionFees" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" maxlength="10" value="{{formatPriceExt _DATA_.conditionParams.conditionFee}}" {{#equals method "detail"}}disabled{{/equals}}>
            <span class="note-error note-icon hint--top" aria-label="请输入最长10位数字">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
            &nbsp;元减&nbsp;
            <input type="text" style="width:160px;" name="reduceFees" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" maxlength="10" value="{{formatPriceExt _DATA_.behaviorParams.reduceFee}}" {{#equals method "detail"}}disabled{{/equals}}>&nbsp;元
            <span class="cgray">每单限用 1 张</span>
            <div style="margin-top:10px;">
              <label class="control-label"><span class="required">*</span>最大满减值：</label>
              <input type="text" style="width:160px;" name="maxDiscount" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" maxlength="10" value="{{formatPriceExt _DATA_.behaviorParams.maxDiscount}}" {{#equals method "detail"}}disabled{{/equals}}>&nbsp;元
            </div>
          </div>
          <div class="control-group" style="margin-top:10px;">
            <label class="control-label"><span class="required">*</span>适用商品：</label>
            <span class="sp">
            {{#equals type 2}}
              全店通用
            {{else}}
              <input type="radio" value="1" class="js-radio-conditionParams-adjustItems magic-radio" id="isAdjustItems1" name="isAdjustItems" {{#equals _DATA_.skuScopeParams.type "include"}}checked{{/equals}} {{#equals method "detail"}}disabled{{/equals}}><label for="isAdjustItems1" class="sp vl-bl radio"> 特定商品</label>
              <input type="radio" value="0" class="js-radio-conditionParams-adjustItems magic-radio" id="isAdjustItems2" name="isAdjustItems" {{#equals _DATA_.skuScopeParams.type "exclude"}}checked{{/equals}} {{#equals method "detail"}}disabled{{/equals}}><label for="isAdjustItems2" class="sp vl-bl radio" style="margin-left:20px">全部商品</label>
            <span class="choose-item-wrapper hide" style="margin-top:10px;">
                {{inject "common/choose_item" type=type skuIds=_DATA_.skuScopeParams.skuIds itemIds=_DATA_.skuScopeParams.itemIds method=method}}
            </span>
            {{/equals}}
            </span>
          </div>
          <div class="control-group">
            <label class="control-label">使用限制：</label>
            <input type="text" name="restrictions" class="input-medium" value="{{_DATA_.extra.restrictions}}" maxlength="20" {{#equals method "detail"}}disabled{{/equals}}>
            <span class="cgray">填写使用限制，可告知消费者此券的商品使用范围（可选填）</span>
          </div>
          <div class="control-group">
            <label class="control-label">备注：</label>
            <input type="text" name="description" class="input-medium" value="{{_DATA_.description}}" maxlength="150" {{#equals method "detail"}}disabled{{/equals}}>
          </div>
        </div>
        {{#unless method}}<button class="btn btn-success btn-primary btn-medium" type="submit">确认</button>{{/unless}}

    </form>
  </div>
  </div>
{{/component}}

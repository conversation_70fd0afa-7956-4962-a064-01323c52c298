const {useState,useEffect,useRef,Fragment} = React;

const UserTypeSelector = ({
    value,
    configs,
    onChange,
    disabled,
}) => {
    const [selectedType, setSelectedType] = useState(null); // 改为单个值
  
    const userTypes = configs;
  
    const toggleSelection = (id) => {
        if(disabled) return
        onChange &&  onChange(id);
        setSelectedType((prevSelected) => (prevSelected === id ? null : id)); // 单选逻辑
    };

    useEffect(()=>{
        if(value){
            setSelectedType(value);
        }
    },[value])
  
    return (
      <div className="user-type-selector" style={{opacity:disabled?0.5:1}}>
        {userTypes.map((type) => (
          <div
            key={type.id}
            className={`user-type-card ${selectedType === type.id ? 'selected' : ''}`}
            onClick={() => toggleSelection(type.id)}
          >
            <h3>{type.label}</h3>
            <p>{type.description}</p>
          </div>
        ))}
      </div>
    );
  };
  
  export default UserTypeSelector;
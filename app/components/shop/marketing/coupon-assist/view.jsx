
const { Spa, SpaConfigProvider,SearchList } = dtComponents
import request from "../../../utils/plugins/axios/request";
const {useState,useEffect,useRef,Fragment} = React;
const {Button, Table,Switch,Modal,message,
    Tabs,Form,Input,Radio,Descriptions,DatePicker,Space,Select,InputNumber,
    Tooltip
} = antd;

const { RangePicker } = DatePicker;
const {QuestionCircleOutlined} = icons;
const formTailLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 14, offset: 3 },
  };
  import {valitorItems,createAcitivityName,createDefaultTime} from "../coupon_main/common"
export const Index = () => {
    let type = __lib__.getParam("type");
    let id = __lib__.getParam("id")
    const addBol = type === 'add';
    const lookBol = type === 'look';
    const editBol = type === 'edit';
    const [form] = Form.useForm();
    // const [useType,setUseType] = useState();

    const shareMethod = Form.useWatch(['share','method'],form)
    const shareEdMethod = Form.useWatch(['shareEd','method'],form)
    const useType = Form.useWatch("usageTimeType",form)
    // 编辑态缓存数据
    const shareId = useRef();
    const sharedId = useRef();
    const onFinish = (values)=>{
        console.log("values:",values);
        values.startTime = values.time[0].valueOf();
        values.endTime = values.time[1].valueOf();
        delete values.time;
        if(values.customUseTime){
            values.customUseStartTime = values.customUseTime[0].valueOf();
            values.customUseEndTime = values.customUseTime[1].valueOf();
            delete values.customUseTime
        }
        const scopeList = [];
        console.log("values.share:",values.share)
        if(values.share){
            scopeList.push({
                ...values.share,
                scopeValue:'sharer',
                type:'SHOP',
                id:shareId.current 
            })
            delete values.share
        }
        console.log("values.shareEd:",values.shareEd)
        if(values.shareEd){
            scopeList.push({
                ...values.shareEd,
                scopeValue:'shared',
                type:'SHOP',
                id:sharedId.current 
            })
            delete values.shareEd
        }
        values.scopeList =scopeList;
        values.userScope = "ALL_USER"
        values.type = "ASSISTANCE" // 助力裂变券
        if(editBol){
            values.id = id;
        }
        request({
            url:addBol?'/mall-admin/api/couponActivity/create':'/mall-admin/api/couponActivity/update',
            method: 'POST',
            data:{...values},

            success:(res)=>{
                message.success(addBol?"创建成功":'保存成功')
                setTimeout(()=>{
                    // history.back();
                   // window.close();
                   location.href = "/seller/marketing/coupon-main?tabIndex=item-2"
                },1000)
            }
        })
    }

    const getDetail = ()=>{
       request({
            url: `/mall-admin/api/couponActivity/detail?id=${id}`,
            method:'GET',
            success(res){
                if(res.customUseStartTime || res.customUseEndTime){
                    res.customUseTime = [moment(res.customUseStartTime),moment(res.customUseEndTime)]
                    delete res.customUseStartTime
                    delete res.customUseEndTime
                }
                if(res.endTime || res.startTime){
                    res.time = [moment(res.startTime),moment(res.endTime)]
                    delete res.endTime
                    delete res.startTime
                }
                    // values.share = 
                if(res.scopeList[0].scopeValue === 'sharer'){
                    shareId.current = res.scopeList[0].id
                    sharedId.current = res.scopeList[1].id
                    res.share = {
                        ...res.scopeList[0]
                    }
                    res.shareEd = {
                        ...res.scopeList[1]
                    }
                }
                if(res.scopeList[0].scopeValue === 'shared'){
                    shareId.current = res.scopeList[1].id
                    sharedId.current = res.scopeList[0].id
                    res.share = {
                        ...res.scopeList[1]
                    }
                    res.shareEd = {
                        ...res.scopeList[0]
                    }
                }
                form.setFieldsValue({...res})
            }
        })
    }
console.log("editBol || lookBol:",editBol || lookBol)
    useEffect(()=>{
        if(editBol || lookBol){
            getDetail()
        }
    },[])
    
    

    return <Fragment>
        <Form 
            form={form} 
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 14 }}
            onFinish={onFinish}
            initialValues={addBol?{
                fissionNum:3,
                share:{
                    method:'FULL_REDUCTION',
                    limitNum:1,
                },
                shareEd:{
                    method:'FULL_REDUCTION',
                    limitNum:1,
                },
                usageTimeType:'SAME',
                name: createAcitivityName("ASSISTANCE"),
                time: createDefaultTime(),
                customUseTime: createDefaultTime(),
            }:null}
        >
            <Descriptions title="基础规则" items={[]} />
            <Form.Item label={'活动名称'} name='name' extra="该名称仅用于管理，不对买家展示" 
                rules={[{required:true,message:'活动名称是必填的'}]}
            >
                <Input  maxLength={50} showCount disabled={editBol || lookBol}/>
            </Form.Item>         
            <Form.Item label={'人群规模'} name='userScope'>
                <div style={{backgroundColor:'#f5f5f5',padding:'8px 20px',height:'44px'}}>
                所有用户
                </div>
            </Form.Item>
            <Form.Item label={'分享人数'} name='fissionNum'
               rules={[{required:true,message:'分享人数是必选的'}]}
            >
                <Radio.Group
                    options={[
                        { value: 3, label: '3人' },
                        { value: 4, label: '4人' },
                        { value: 5, label: '5人' },
                    ]}
                    disabled={editBol || lookBol}
                    onChange={(e)=>{
                        const num = parseInt(e.target.value);
                        const fissionNum = parseInt(form.getFieldValue(['share','quantity']));
                        if(typeof fissionNum === 'number' && fissionNum.toString()!=="NaN"){
                           form.setFieldValue(['shareEd','quantity'],num * fissionNum) 
                        } else {
                            form.setFieldValue(['shareEd','quantity'],null) 
                        }
                    }}
                    ></Radio.Group>
            </Form.Item>
            {/* <Form.Item label={'领取时间'} name={["startTime","endTime"]} */}
            <Form.Item label={'领取时间'} name={"time"}
                rules={[{required:true,message:'领取时间是必选的'},{
                    validator(rule,value){
                        if(!value){
                            return Promise.resolve("")
                        }
                        if(value.length!==2){
                           return Promise.reject("领取时间是必选的")
                        }
                        const start =value[0].valueOf();
                        const end = value[1].valueOf();
                        const tamp = end - start;
                        if((tamp / 1000) > 999 * 24 * 60 * 60){
                            return Promise.reject("时间最大跨度999天");
                        }
                        return Promise.resolve("")
                    }
                }]}
            >
                <RangePicker showTime   disabled={editBol || lookBol}/>
            </Form.Item>
            <Form.Item label={'使用时间'} name='usageTimeType'
            rules={[{required:true,message:'使用时间是必选的'}]}
            >
                <Radio.Group
                    options={[
                        { value: "SAME", label: '与领取时间相同' },
                        { value: "LIME", label: '限制有效天数' },
                        { value: "CUSTOM", label: '自定义时间' },
                    ]}
                    disabled={editBol || lookBol}
                    ></Radio.Group>
            </Form.Item>
            {
                useType === "LIME" &&  
                <div style={{display:'flex',flexDirection:'row',alignItems:'center',alignItems:'center',
                    marginLeft:'12.4%'
                }}>
                    <Form.Item  name='validDays' {...formTailLayout}
                        rules={[{required:true,message:'有效天数是必填的'},
                            {
                                pattern:/^[1-9]\d{0,2}$/,
                                message:'有效天数只能输入不大于999的正整数'
                            },
                        ]}
                    >
                        <Input style={{width:'100px',marginRight:'10px'}} addonAfter="天"  disabled={editBol || lookBol} />
                    </Form.Item>
                    <div style={{marginBottom:'24px',marginLeft:'10px'}}>可使用</div> 
                </div>
            }
            {
                useType === "CUSTOM" &&  <Form.Item  name={"customUseTime"}
                {...formTailLayout}
                    rules={[{required:true,message:'使用时间是必选的'},{
                        validator(rule,value){
                            if(!value){
                                return Promise.resolve("")
                            }
                            if(value.length!==2){
                                return Promise.reject("使用时间是必选的")
                            }
                            const start =value[0].valueOf();
                            const end = value[1].valueOf();
                            const tamp = end - start;
                            if((tamp / 1000) > 999 * 24 * 60 * 60){
                                return Promise.reject("时间最大跨度999天");
                            }
                            return Promise.resolve("")
                        }
                    }]}
                >
                    <RangePicker showTime  disabled={editBol || lookBol}/>
                </Form.Item>
            }
            <Descriptions title="设置优惠信息" items={[]} />
            <div style={{
                display:'flex',
                flexDirection:'row',
                alignItems:'flex-start',
                width:'100%'
            }}>
                <div style={{width:'160px'}}>分享者优惠</div>
                <div style={{width:'100%'}}>
                    <Form.Item label={'优惠方式'} name={['share','method']} wrapperCol={{span:10}}
                    rules={[{required:true,message:'优惠方式是必选的'}]}
                    >
                        <Radio.Group
                            options={[
                                { value: "FULL_REDUCTION", label: '店铺满减券' },
                                { value: "DIRECT_REDUCTION", label: '店铺直减券' },
                            ]}
                            disabled={editBol || lookBol}
                            ></Radio.Group>
                    </Form.Item>
                    {
                        shareMethod === "DIRECT_REDUCTION" && <Fragment>
                            <Form.Item label="立减面额" name={['share',"deductionAmount"]} 
                                rules={[{required:true,message:'立减面额是必填的'},{
                                    validator(_,value){
                                        if(!value) return Promise.resolve('');
                                        if(!/^(?!0$)[1-9]\d{0,4}(\.\d)?$/.test(value)) 
                                            return Promise.reject("请输入1至99999之间的数字，只允许保留小数点后一位");
                                        if(value > 99999) 
                                            return Promise.reject("请输入1至99999之间的数字，只允许保留小数点后一位");
                                        return Promise.resolve("")
                                    }
                                }]}
                            >
                                <Input addonBefore="¥"  disabled={editBol || lookBol}/>
                            </Form.Item>
                            <Form.Item label="发放量" name={['share',"quantity"]} extra='优惠券创建后，发放量只能增加不能减少' 
                                rules={[{required:true,message:'发放量是必填的'},
                                    {
                                        pattern:/^(10000|[1-9]\d{0,3})$/,
                                        message:'只能输入最大10000正整数'
                                    },
                                ]}
                            >
                                <Input addonAfter="张"  disabled={lookBol} onChange={(e)=>{
                                    const num = parseInt(e.currentTarget.value);
                                    const fissionNum = form.getFieldValue('fissionNum');
                                    if(typeof num === 'number' && num.toString()!="NaN"){
                                        form.setFieldValue(['shareEd','quantity'],num * fissionNum) 
                                    } else {
                                        form.setFieldValue(['shareEd','quantity'],null) 
                                    }
                                }}/>
                            </Form.Item>
                            <Form.Item label="每人限领" name={['share',"limitNum"]} 
                                rules={[{required:true,message:'每人限领是必填的'},{
                                    pattern:/^[1-9]\d*$/,
                                    message:'只能输入正整数'
                                }]}
                            >
                                <Input addonAfter="张" disabled={true}/>
                            </Form.Item>
                        </Fragment>
                    }
                    {/* {满减 } */}
                    {
                        shareMethod === "FULL_REDUCTION"  &&
                        <Fragment> 
                            <Form.Item label="满减面额" required={true}  >
                                <Space aligns='center'>
                                    满
                                    <Form.Item
                                        name={["share","thresholdAmount"]}
                                        noStyle
                                        rules={[
                                            {
                                                pattern:/^([1-9]\d{0,4}|[1-9])$/,
                                                message:'满额只能输入不大于99999的正整数'
                                            },
                                            {
                                                validator(_,value){
                                                    const thresholdAmount = form.getFieldValue(["share","thresholdAmount"])
                                                    const thresholdDeductionAmount = form.getFieldValue(["share","thresholdDeductionAmount"])
                                                    if(!thresholdAmount){
                                                        return Promise.reject('满额是必填的') 
                                                    }
                                                    if(Number(thresholdAmount)<=Number(thresholdDeductionAmount)){
                                                        if(form.getFieldError(["share",'thresholdDeductionAmount']).length){
                                                            return Promise.resolve("")
                                                        }
                                                        return Promise.reject('满额必须大于减额') 
                                                    } else {
                                                        if(form.getFieldError('thresholdDeductionAmount').length){
                                                            form.validateFields(["share",'thresholdDeductionAmount'])
                                                        }
                                                    }
                                                    return Promise.resolve("")
                                                }
                                            }
                                        ]}
                                        
                                        style={{ display: 'inline-block', width: '70px' }}
                                    >
                                        <Input addonBefore="¥" placeholder="请输入"  disabled={editBol || lookBol}  />
                                    </Form.Item>
                                    减
                                    <Form.Item
                                        // name="thresholdDeductionAmount"
                                        name={["share","thresholdDeductionAmount"]}
                                        noStyle
                                        rules={[
                                            {
                                                pattern:/^([1-9]\d{0,4}|[1-9])$/,
                                                message:'减额只能输入不大于99999的正整数'
                                            },{
                                                validator(_,value){
                                                    const thresholdAmount = form.getFieldValue(["share","thresholdAmount"])
                                                    const thresholdDeductionAmount = form.getFieldValue(["share","thresholdDeductionAmount"])
                                                    
                                                    if(!thresholdDeductionAmount){
                                                        return Promise.reject('减额是必填的') 
                                                    }
                                                    if(Number(thresholdAmount)<=Number(thresholdDeductionAmount)){
                                                        if(form.getFieldError(["share","thresholdAmount"]).length){
                                                            return Promise.resolve("")
                                                        }
                                                        return Promise.reject('满额必须大于减额') 
                                                    } else {
                                                        if(form.getFieldError(["share","thresholdAmount"]).length){
                                                            form.validateFields([["share","thresholdAmount"]])
                                                        }
                                                    }
                                                    return Promise.resolve("")
                                                }
                                            }
                                        ]}
                                        style={{ display: 'inline-block', width: '70px' , margin: '0 8px' }}
                                    >
                                        <Input  addonBefore="¥" placeholder="请输入"  disabled={editBol || lookBol}  />
                                    </Form.Item>   
                                </Space>
                            </Form.Item>
                            <Form.Item label="发放量" name={['share',"quantity"]} extra='优惠券创建后，发放量只能增加不能减少'
                                rules={[{required:true,message:'发放量是必填的'},
                                    {
                                        pattern:/^(10000|[1-9]\d{0,3})$/,
                                        message:'只能输入最大10000正整数'
                                    },
                                ]}
                            >
                                <Input addonAfter="张"  disabled={lookBol} onChange={(e)=>{
                                    console.log("e:",e.currentTarget.value);
                                    const num = parseInt(e.currentTarget.value);
                                    const fissionNum = form.getFieldValue('fissionNum');
                                    // console.log(num,fissionNum)
                                    // form.setFieldValue(['shareEd','quantity'],num * fissionNum)
                                    if(typeof num === 'number' && num.toString()!="NaN"){
                                        form.setFieldValue(['shareEd','quantity'],num * fissionNum) 
                                    } else {
                                        form.setFieldValue(['shareEd','quantity'],null) 
                                    }
                                }}/>
                            </Form.Item>
                            <Form.Item label="每人限领" name={['share',"limitNum"]}
                                rules={[{required:true,message:'每人限领是必填的'},{
                                    pattern:/^[1-9]\d*$/,
                                    message:'只能输入正整数'
                                }]}
                                disabled={editBol || lookBol} 
                            >
                                <Input addonAfter="张" disabled={true}/>
                            </Form.Item>
                        </Fragment>
                    }
                </div>
            </div>
            <div style={{
                display:'flex',
                flexDirection:'row',
                alignItems:'flex-start',
                width:'100%'
            }}>
                <div style={{width:'160px'}}>
                    <Tooltip title="被分享者助力成功即可获得此券">
                        <span style={{verticalAlign:"middle", marginRight:"4px"}}>被分享者优惠</span>
                        <QuestionCircleOutlined style={{ verticalAlign: "middle" }} />
                    </Tooltip>
                </div>
                <div style={{width:'100%'}}>
                    <Form.Item label={'优惠方式'} name={['shareEd','method']} wrapperCol={{span:10}}
                      rules={[{required:true,message:'优惠方式是必选的'}]}
                    >
                        <Radio.Group
                            options={[
                                { value: "FULL_REDUCTION", label: '店铺满减券' },
                                { value: "DIRECT_REDUCTION", label: '店铺直减券' },
                            ]}
                            disabled={editBol || lookBol} 
                            ></Radio.Group>
                    </Form.Item>
                    {
                        shareEdMethod === "DIRECT_REDUCTION" && <Fragment>
                            <Form.Item label="立减面额" name={['shareEd',"deductionAmount"]}
                                 rules={[{required:true,message:'立减面额是必填的'},{
                                    pattern:/^(?!0$)[1-9]\d{0,4}(\.\d)?$/,
                                    message:'请输入1至99999之间的数字，只允许保留小数点后一位'
                                }]}
                            >
                                <Input addonBefore="¥"  disabled={editBol || lookBol} />
                            </Form.Item>
                            <Form.Item label="发放量" name={['shareEd',"quantity"]} extra='优惠券创建后，发放量只能增加不能减少'
                             rules={[{required:true,message:'发放量是必填的'}]}
                            >
                                <Input addonAfter="张" disabled={true}/>
                            </Form.Item>
                            <Form.Item label="每人限领" name={['shareEd',"limitNum"]}
                                rules={[{required:true,message:'每人限领是必填的'},{
                                    pattern:/^[1-9]\d*$/,
                                    message:'只能输入正整数'
                                }]}
                            >
                                <Input addonAfter="张" disabled={true}/>
                            </Form.Item>
                        </Fragment>
                    }
                    {/* {满减 } */}
                    {
                        shareEdMethod === "FULL_REDUCTION"  &&
                        <Fragment> 
                            <Form.Item label="满减面额" required={true}>
                                <Space aligns='center'>
                                    满
                                    <Form.Item
                                        name={['shareEd',"thresholdAmount"]}
                                        noStyle
                                        rules={[
                                            {required:true,message:'满额是必填的'},
                                            {
                                                pattern:/^([1-9]\d{0,4}|[1-9])$/,
                                                message:'只能输入不大于99999的正整数'
                                            },{
                                                validator(_,value){
                                                    const thresholdAmount = form.getFieldValue(["shareEd","thresholdAmount"])
                                                    const thresholdDeductionAmount = form.getFieldValue(["shareEd","thresholdDeductionAmount"])
                                                    if(!thresholdAmount){
                                                        return Promise.reject('满额是必填的') 
                                                    }
                                                    if(Number(thresholdAmount)<=Number(thresholdDeductionAmount)){
                                                        if(form.getFieldError(["shareEd",'thresholdDeductionAmount']).length){
                                                            return Promise.resolve("")
                                                        }
                                                        return Promise.reject('满额必须大于减额') 
                                                    } else {
                                                        if(form.getFieldError('thresholdDeductionAmount').length){
                                                            form.validateFields(["shareEd",'thresholdDeductionAmount'])
                                                        }
                                                    }
                                                    return Promise.resolve("")
                                                }
                                            }
                                        ]}
                                        style={{ display: 'inline-block', width: '70px' }}
                                    >
                                        <Input addonBefore="¥" placeholder="请输入" disabled={editBol || lookBol}  />
                                    </Form.Item>
                                    减
                                    <Form.Item
                                        name={['shareEd',"thresholdDeductionAmount"]}
                                        noStyle
                                        
                                        rules={[
                                            {required:true,message:'减额是必填的'},
                                            {
                                                pattern:/^([1-9]\d{0,4}|[1-9])$/,
                                                message:'只能输入不大于99999的正整数'
                                            },{
                                                validator(_,value){
                                                    const thresholdAmount = form.getFieldValue(["shareEd","thresholdAmount"])
                                                    const thresholdDeductionAmount = form.getFieldValue(["shareEd","thresholdDeductionAmount"])
                                                    
                                                    if(!thresholdDeductionAmount){
                                                        return Promise.reject('减额是必填的') 
                                                    }
                                                    if(Number(thresholdAmount)<=Number(thresholdDeductionAmount)){
                                                        if(form.getFieldError(["shareEd","thresholdAmount"]).length){
                                                            return Promise.resolve("")
                                                        }
                                                        return Promise.reject('满额必须大于减额') 
                                                    } else {
                                                        if(form.getFieldError(["shareEd","thresholdAmount"]).length){
                                                            form.validateFields([["shareEd","thresholdAmount"]])
                                                        }
                                                    }
                                                    return Promise.resolve("")
                                                }
                                            }
                                        ]}
                                        style={{ display: 'inline-block', width: '70px' , margin: '0 8px' }}
                                    >
                                        <Input  addonBefore="¥" placeholder="请输入" disabled={editBol || lookBol}  />
                                    </Form.Item>   
                                </Space>
                                
                            </Form.Item>
                            <Form.Item label="发放量" name={['shareEd',"quantity"]} extra='分享者优惠券发放量*分享人数'
                              rules={[{required:true,message:'发放量是必填的'}]}
                            >
                                <Input addonAfter="张" disabled={true} />
                            </Form.Item>
                            <Form.Item label="每人限领" name={['shareEd',"limitNum"]}
                             rules={[{required:true,message:'每人限领是必填的'},{
                                pattern:/^[1-9]\d*$/,
                                message:'只能输入正整数'
                            }]}
                            >
                                <Input addonAfter="张" disabled={true}/>
                            </Form.Item>
                        </Fragment>
                    }
                </div>
            </div>
            {
                (editBol || addBol) && <Form.Item>
                    <Button type="primary" htmlType="submit">{type==='add'?'创建':'保存'}</Button>
                </Form.Item>
            }
        </Form>
    </Fragment>

}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => { },
    request: {
        request: (params) => {
            const obj = Object.assign(params, {
                type: 'POST',
                contentType: 'application/json',
            })
            request(obj)
        }
    }
})}>
    <Index />
</SpaConfigProvider>, document.getElementById("coupon-assist"));
/**
 * @description 成团数据
 * <AUTHOR>
 */

import { lib } from "../../../common/react/utils/lib";
import Info from "./components/info";
import GroupCurve from "./components/group-curve";
import Table from "./components/group-table";
const { useState } = React;
const GroupData = () => {
    let activityId = lib.getParam("activityId") || "";
     const [type, setType] = useState(1);
    return (
        <div className="group-data-wrap">
            <Info activityId={activityId} onType={(type) => { setType(type) }}></Info>
            <GroupCurve activityId={activityId} type={type}></GroupCurve>
            <Table activityId={activityId}></Table>
        </div>
    );
}
export default GroupData;
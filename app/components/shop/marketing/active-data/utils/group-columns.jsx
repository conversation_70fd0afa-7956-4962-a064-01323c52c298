/**
 * @description: 成团表格数据
 * @author: wangchunting
 */
export const columns = [{
  title: "团长",
  width: 180,
  dataIndex: "memberHeadName",
  key: "memberHeadName",
},
{
  title: "团员",
  width: 180,
  dataIndex: "memberName",
  key: "memberName",
},
{
  title: "成团状态",
  width: 180,
  dataIndex: "groupStatusDesc",
  key: "groupStatusDesc",
},
{
  title: "有效GMV",
  // width: 180,
  dataIndex: "gmvEffectiveCount",
  key: "gmvEffectiveCount",
},
{
  title: "退款GMV",
  // width: 180,
  dataIndex: "gmvRefundCount",
  key: "gmvRefundCount",
},
{
  title: "开团时间",
  // width: 180,
  dataIndex: "groupTimeDesc",
  key: "groupTimeDesc",
},
{
  title: "成团时间",
  // width: 180,
  dataIndex: "successTimeDesc",
  key: "successTimeDesc",
},
]

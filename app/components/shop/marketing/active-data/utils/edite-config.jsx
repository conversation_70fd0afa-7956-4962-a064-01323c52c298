/**
 * @description: 修改补足成团数
 * @author: wangchunting
 */
/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line no-undef
const { FormInput } = dtComponents
export const configs = [{
  type: FormInput,
  fProps: {
    label: "补足成团数（个）",
    name: "baseCount",
    rules: [{ required: true, message: "请输入补足成团数" }],
    extra: "添加后前台展示成团数将在实际累计成团数上增加补足成团数",
  },
  url: "/mall-admin/api/gb_activity_index/groupCountModify",
  cProps: {
    width: 200,
  }
}]

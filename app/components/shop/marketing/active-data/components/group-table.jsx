/**
 * @description: 成团表格数据
 * @author: wangchunting
 */
import request from "../../../../utils/plugins/axios/request";
import PaginationComponent from "../fn/paginationComponent"
import { columns } from "../utils/group-columns"
const { useEffect, useState } = React;
const { Form, Table, Button } = antd;
const { FormSelect, FormRangePicker } = dtComponents

function TablePage({ activityId }) {
    const [form] = Form.useForm()
    const [dataSource, setDataSource] = useState([]);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 20,
        totalPage: 1,
    });

    useEffect(() => {
        getTableData(pagination.currentPage, pagination.pageSize);
    }, []);

    function getTableData(currentPage, pageSize, params) {
        request({
            url: '/mall-admin/api/gb_activity_index/groupStatusInfo',
            method: 'POST',
            data: {
                ...params,
                activityId,
                currentPage: currentPage || 1,
                pageSize: pageSize || 20
            },
            needMask: true,
            success: (res) => {
                setDataSource(res.dataList);
                setPagination(res.page);
            }
        })
    }

    // pagination分页改变触发
    const changePagination = (page, pageSize) => {
        pagination.currentPage = page;
        pagination.pageSize = pageSize;
        setPagination({ ...pagination });
        getTableData(page, pageSize);
    };

    // 搜索
    const handleSearch = () => {
        form.validateFields().then(values => {
            console.log(50, values)
            const [groupTimeStart, groupTimeEnd] = values.groupTimeStart || [];
            const [successTimeStart, successTimeEnd] = values.successTimeStart || []
            let params = {
                ...values,
                groupTimeStart: groupTimeStart ? moment(groupTimeStart).format('x') : null,
                groupTimeEnd: groupTimeEnd ? moment(groupTimeEnd).format('x') : null,
                successTimeStart: successTimeStart ? moment(successTimeStart).format('x') : null,
                successTimeEnd: successTimeEnd ? moment(successTimeEnd).format('x') : null
            }
            getTableData(pagination.currentPage, pagination.pageSize, params);
        });
    }

    // 重置
    const handleReset = () => {
        form.resetFields();
        getTableData(pagination.currentPage, pagination.pageSize);
    }

    return (
        <div>
            <Form layout="inline" form={form} >
                <FormSelect
                    fProps={{ label: '成团状态', name: 'groupStatus' }}
                    url="/mall-admin/api/gb_activity_index/select"
                />
                <FormRangePicker
                    fProps={{
                        label: '开团时间',
                        name: 'groupTimeStart',
                    }}
                    cProps={{
                        width: 260
                    }}
                />
                <FormRangePicker
                    fProps={{
                        label: '成团时间',
                        name: 'successTimeStart',
                    }}
                    cProps={{
                        width: 260
                    }}
                />
                <Button type="primary" onClick={handleSearch} style={{ marginRight: 16 }}>
                    搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
                <Table pagination={false} columns={columns} dataSource={dataSource} style={{ marginTop: 24, width: '100%' }}></Table>
                {
                    pagination.totalCount > 0 &&
                    <PaginationComponent pagination={pagination} changePagination={changePagination} style={{ width: '100%', textAlign: 'center', margin: 24 }} />
                }
            </Form>
        </div>
    )
}
export default TablePage
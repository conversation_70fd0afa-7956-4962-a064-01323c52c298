/**
 * @description 成团数据
 * <AUTHOR>
 */

import request from "../../../../utils/plugins/axios/request";
const { useState, useRef, useEffect } = React;
const { Card, Col, Row, Statistic, Tooltip, Button } = antd;
const { QuestionCircleOutlined, LineChartOutlined, FundOutlined } = icons;
import EditeModal from "./edite-modal"

const Info = ({ activityId, onType }) => {
    const [editeOpen, setEditeOpen] = useState(false);
    const [gmvCountObj, setGmvCountObj] = useState({});
    const [groupCountObj, setGroupCountObj] = useState({});
    const [memberCountObj, setMemberCountObj] = useState({});
    const [participateCountObj, setParticipateCountObj] = useState({});
    const [groupSuccessRateObj, setGroupSuccessRateObj] = useState({});
    const [type, setType] = useState(1);  // 1 gmv,2成团数量,3成团人数，4参与人数，5 成团率	


    useEffect(() => {
        fetchData(`/mall-admin/api/gb_activity_index/gmvCount`, setGmvCountObj); //活动统计-GMV
        fetchData("/mall-admin/api/gb_activity_index/groupCount", setGroupCountObj); //活动统计-成团数
        fetchData("/mall-admin/api/gb_activity_index/memberCount", setMemberCountObj); //活动统计-成团成功人数
        fetchData("/mall-admin/api/gb_activity_index/participateCount", setParticipateCountObj); //活动统计-参与活动人数
        fetchData("/mall-admin/api/gb_activity_index/groupSuccessRate", setGroupSuccessRateObj); //活动统计-成团率
    }, []);
    const fetchData = (url, setData) => {
        request({
            url,
            method: "POST",
            data: {
                activityId,
            },
            needMask: true,
            success: (res) => {
                setData(res);
            }
        });
    }
    const chartRef = useRef(null);

    useEffect(() => {
        const chartInstance = echarts.init(chartRef.current);
        const option = {
            series: [
                {
                    name: '成功率',
                    type: 'pie',
                    radius: ['80%', '90%'],
                    avoidLabelOverlap: false,
                    hoverAnimation: false,
                    label: {
                        show: false,
                        position: 'center',
                    },
                    emphasis: {
                        label: {
                            show: false,
                            fontSize: '20',
                            fontWeight: 'bold',
                        },
                    },
                    labelLine: {
                        show: false,
                    },
                    data: [
                        { value: 87, itemStyle: { color: '#1890ff' } },
                        { value: 13, itemStyle: { color: '#f0f2f5' } },
                    ],
                    startAngle: 90,
                },
            ],
        };

        chartInstance.setOption(option);

        return () => {
            chartInstance.dispose();
        };
    }, []);

    function handleType(type) {
        setType(type);
        onType(type);
    }
    const statisticsData = [
        { key: 1, title: "累计成团数量", value: groupCountObj.groupCount || 0 },
        { key: 2, title: "补足成团数", value: groupCountObj.baseCount || 0 },
        { key: 3, title: "正在成团数", value: groupCountObj.groupCountIng || 0 },
        { key: 4, title: "成团成功数", value: groupCountObj.groupSuccessCount || 0 },
    ];
    return (
        <div className="dashboard">
            <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={5} onClick={() => handleType(1)}>
                    <Card className={`custom-card ${type === 1 ? "active" : ""}`}>
                        <div className="card-header">
                            <FundOutlined className="card-icon" />
                            <div className="card-title">
                                <span>有效GMV</span>
                                <Tooltip title="有效GMV=实际成交GMV-退款GMV">
                                    <QuestionCircleOutlined />
                                </Tooltip>
                            </div>
                        </div>
                        <div className="card-content">
                            <Statistic
                                value={gmvCountObj.gmvEffectiveCount || 0}
                                prefix="¥"
                                className="main-value"
                            />
                            <Statistic
                                title="退款GMV"
                                value={gmvCountObj.gmvRefundCount || 0}
                                prefix="¥"
                                className="sub-value"
                            />
                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={12} md={5} onClick={() => handleType(2)}>
                    <Card className={`custom-card ${type === 2 ? "active" : ""}`} >
                        <div className="card-header">
                            <div className="card-title">
                                <span className="card-title-text">成团数量</span>
                                <Button type="primary" size="small" onClick={() => setEditeOpen(true)}>修改</Button>
                            </div>
                        </div>
                        <div className="card-content-group">
                            {statisticsData.map(statistic => (
                                <Statistic
                                    key={statistic.key}
                                    title={statistic.title}
                                    value={statistic.value}
                                    suffix="(个)"
                                />
                            ))}
                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={12} md={5} onClick={() => handleType(3)}>
                    <Card className={`custom-card ${type === 3 ? "active" : ""}`}>
                        <div className="card-title-text">成团成功人数</div>
                        <div className="card-header" style={{ marginTop: 24 }}>
                            <span className="card-title" >累计成团人数</span>
                            <Statistic value={memberCountObj.memberCount || 0} suffix="人" className="main-value" />
                        </div>
                        {/* <div className="trend" style={{ marginTop: 24 }}>
                            <span>平台新用户</span>
                            <span className="down">+{memberCountObj.successNewUser || 0}人</span>
                        </div> */}
                    </Card>
                </Col>

                <Col xs={24} sm={12} md={5} onClick={() => handleType(4)}>
                    <Card className={`custom-card ${type === 4 ? "active" : ""}`}>
                        <div className="card-title-text">参与活动人数</div>
                        <div className="card-header" style={{ marginTop: 24 }}>
                            <span className="card-title">累计参与活动人数</span>
                            <Statistic value={participateCountObj.participateCount || 0} suffix="人" className="main-value" />
                        </div>
                        <div className="trend" style={{ marginTop: 24 }}>
                            <span>平台新用户</span>
                            <span className="down">+{participateCountObj.platformNewUser || 0}人</span>
                        </div>
                    </Card>
                </Col>

                <Col xs={24} sm={12} md={4} onClick={() => handleType(5)}>
                    <Card className={`custom-card ${type === 5 ? "active" : ""}`}>
                        <div className="card-header">
                            <LineChartOutlined className="card-icon" />
                            <span className="card-title">累计成团成功率</span>
                        </div>
                        <div className="card-content-group">
                            <div className="success-rate">{groupSuccessRateObj.groupSuccessRate || 0}%</div>
                            <div ref={chartRef} style={{ width: '50%', height: '100px' }}></div>
                        </div>
                    </Card>
                </Col>
            </Row>
            {
                editeOpen &&
                <EditeModal
                    activityId={activityId}
                    open={editeOpen}
                    onClose={() => setEditeOpen(false)}
                    row={groupCountObj}
                    onOk={() => {
                        setEditeOpen(false);
                        fetchData("/mall-admin/api/gb_activity_index/groupCount", setGroupCountObj);
                    }}
                />
            }
        </div >
    );
};

export default Info;
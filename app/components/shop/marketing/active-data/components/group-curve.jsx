/**
 * @description: 曲线图
 * @author: wangchunting
 */
import request from "../../../../utils/plugins/axios/request";
const { useState, useEffect } = React;
const { Radio } = antd;

function GroupCurve({ activityId, type }) {
    const [dateType, setDateType] = useState(1);
    const [detail, setDetail] = useState({});
    useEffect(() => {
        groupCurve();
    }, [type])

    function groupCurve(day) {
        request({
            url: "/mall-admin/api/gb_activity_index/groupCurve",
            method: "POST",
            data: {
                activityId: activityId,
                dayTime: day ? day : dateType, // 1 ：7天 ， 2： 30天	
                type: type //1 gmv,2成团数量,3成团人数，4参与人数，5 成团率	
            },
            needMask: true,
            success: (res) => {
                setDetail(res);
                getOptionEcharts(res);
            }
        });
    }

    function getOptionEcharts(data) {
        var chartDom = document.getElementById("main");
        var myChart = echarts.init(chartDom);
        const colors = [" #2A78FF", "#3BA272"];

        var option = {
            color: colors,
            tooltip: {
                trigger: "axis",
            },
            legend: {
                right: "20%",
            },
            title: {
                left: "32px",
            },
            xAxis: {
                type: "category",
                data: data.xdateList,
            },
            yAxis: {
                type: "value",
            },
            // series: [],
        };

        switch (type) {
            case 1:
                option.title = {
                    text: "GMV",
                    subtext: "单位（元）",
                };
                option.legend.data = ["有效GMV", "退款GMV"];
                option.series = [
                    {
                        name: "有效GMV",
                        type: "line",
                        data: data.ydateList[0],
                    },
                    {
                        name: "退款GMV",
                        type: "line",
                        data: data.ydateList[1],
                    },
                ];
                break;
            case 2:
                option.title = {
                    text: "成团数量",
                    subtext: "单位（个）",
                };
                option.legend.data = ["成团数量"];
                option.series = [
                    {
                        name: "成团数量",
                        type: "line",
                        data: data.ydateList[0],
                    },
                ];
                break;
            case 3:
                option.title = {
                    text: "成团成功人数",
                    subtext: "单位（人）",
                };
                option.legend.data = ["成团成功人数"];
                option.series = [
                    {
                        name: "成团成功人数",
                        type: "line",
                        data: data.ydateList[0],
                    },
                ];
                break;
            case 4:
                option.title = {
                    text: "参与活动人数",
                    subtext: "单位（人）",
                };
                option.legend.data = ["参与活动人数"];
                option.series = [
                    {
                        name: "参与活动人数",
                        type: "line",
                        data: data.ydateList[0],
                    },
                ];
                break;
            case 5:
                option.title = {
                    text: "成团成功率",
                    subtext: "单位（%）",
                };
                option.legend.data = ["成团成功率"];
                option.series = [
                    {
                        name: "成团成功率",
                        type: "line",
                        data: data.ydateList[0],
                    },
                ];
                break;
            default:
                break;
        }
        myChart.clear(); // 清除图表

        option && myChart.setOption(option);
    }

    const handleDateChange = (e) => {
        setDateType(e.target.value)
        groupCurve(e.target.value);
    }

    return <div className="group-curve-content" style={{ paddingTop: 50 }}>
        <div className="group-curve-title">
            {/* <div className="group-curve-title-text">成团数量</div> */}
            <div className="group-curve-title-date">
                <div className="text">
                    {detail.rangeTimeDesc || ""}
                </div>
                <Radio.Group onChange={handleDateChange} defaultValue={1}>
                    <Radio.Button value={1}>近7天</Radio.Button>
                    <Radio.Button value={2}>近30天</Radio.Button>
                </Radio.Group>
            </div>
        </div>
        <div id="main" style={{ width: "100%", height: "330px", padding: "24px 0" }}></div>
    </div>
}

export default GroupCurve;

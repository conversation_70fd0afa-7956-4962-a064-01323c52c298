/**
 * @description: 修改补足成团数
 * @author: wangchunting
 */
import { configs } from '../utils/edite-config'
import request from "../../../../utils/plugins/axios/request";
const { useEffect } = React;
const { Form, Modal, message } = antd;
const { SourceInfoComponent } = dtComponents

const EditeModal = ({ row, open, onClose, onOk, activityId }) => {
    console.log(row)
    const [form] = Form.useForm()

    useEffect(() => {
        form.setFieldsValue({ baseCount: row.baseCount })
    }, [row])

    // 提交
    const handleOk = () => {
        form.validateFields().then(values => {
            let data = { ...values, activityId }
            request({
                url: `/mall-admin/api/gb_activity_index/baseCountModify`,
                method: "POST",
                data: data,
                success: (res) => {
                    message.success("修改成功");
                    onClose();
                    onOk();
                },
            })
        });
    };

    // 取消
    const handleCancel = () => {
        onClose();
        form.resetFields();
    };
    const layout = {
        labelCol: {
            style: { width: '138px', paddingBottom: '24px' },
        },
    }
    return (
        <Modal
            title="修改补足成团数"
            width={580}
            open={open}
            keyboard={false}
            maskClosable={false}
            onCancel={handleCancel}
            onOk={handleOk}>
            <Form {...layout} layout="inline" form={form}>
                <SourceInfoComponent data={configs} infoEdit={false} />
            </Form>
        </Modal>

    )
}
export default EditeModal

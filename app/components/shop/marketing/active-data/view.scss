.title-text {
	font-size: 16px;
	font-weight: 600;
}

.text-center {
	text-align: center;
}

.mr-24 {
	margin-right: 24px;
}

.operate {
	display: flex;
	color: #1890ff !important;

	>div {
		margin-right: 8px;

		&:last-child {
			margin-right: 0;
		}
	}
}

.on-active-data {
	height: calc(100% - 50px);
	background-color: rgb(255, 255, 255);

	.product-data-wrap {
		height: 100%;
	}

	.active-data-product-tab {
		height: 100%;
	}

	.active-data-tab {
		padding: 24px;
		background-color: #fff;


		.selected {
			background: #007bff;
			color: white;
		}

		.new-search-list,
		.new-search-list .height-100-percent {
			height: calc(100% - 18px);
		}

		.ant-tabs-content-holder {
			height: 100%;
		}

		.ant-tabs-content {

			height: 100%;
		}

		.ant-tabs-tabpane {
			height: 100%;
		}

		.activity-tab {
			height: 100%;
		}

		.ant-tabs-tab {
			.ant-tabs-tab.ant-tabs-tab-active {
				.ant-tabs-tab-btn {
					color: #1677FF;
				}
			}
		}
	}

	.group-data-wrap {
		.group-curve-content {
			padding-top: 24px;
		}

		.group-curve-title {
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-text {
				font-size: 16px;
				font-weight: 600;
			}

			&-date {
				display: flex;
				align-items: center;

				.text {
					font-size: 12px;
					color: #999;
					margin-right: 32px;
				}
			}
		}

		.dashboard {
			.custom-card {
				border-radius: 8px;
				background: #F9FAFC;
				/* 添加背景色 */
				border: none;
				height: 176px;
				border-radius: 10px;

				&.active {
					// border: #1890ff 1px solid;
					background: rgba(42, 120, 255, 0.09);
					border-radius: 10px;
					border: 2px solid #2A78FF;
				}

				.ant-card-body {
					// display: flex ;
					// flex-direction: row;
					// justify-content: space-between;
				}

				.card-icon {
					border-radius: 50%;
					background: #175BD3;
					color: #fff;
					padding: 4px;
					font-size: 14px;
					margin-right: 8px;
				}

				.card-header {
					// margin-bottom: 12px;
					display: flex;
					align-items: center;
				}

				.card-title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 14px;
					color: #6E6A6A;
					font-weight: 500;
					width: 100%;
				}

				.card-title-text {
					font-weight: 500;
					font-size: 16px;
					color: #000000;
				}

				.card-content {
					margin-left: 30px;
				}

				.card-content-group {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;
					font-size: 14px;
					color: #6E6A6A;

					.ant-card-body {
						padding: 20px;

					}

					.ant-statistic {
						width: 50%;

						.ant-statistic-content {
							font-size: 20px;

							.ant-statistic-content-value {
								font-size: 20px !important;
								font-weight: 500;
								line-height: 0;
							}

							.ant-statistic-content-suffix {
								font-size: 12px !important;
							}
						}
					}

					.success-rate {
						font-weight: 500;
						font-size: 26px;
					}
				}

				.main-value {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 26px;
					color: #000000;
					line-height: 1;

					// margin-top: 8px;
					.ant-statistic-content {
						display: flex;
						line-height: 1;
					}
				}

				.sub-value {
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 14px;
					color: #999999;
					margin-top: 16px;
					line-height: 1;

					.ant-statistic-content {
						font-size: 14px !important;
						margin-top: 8px;
					}
				}

				.trend {
					font-size: 12px;
					color: rgba(0, 0, 0, 0.45);
					display: flex;
					align-items: center;
					// justify-content: space-between;


					.up {
						color: #52c41a;
					}

					.down {
						color: #f5222d;
						font-size: 14px;
						font-weight: bold;
						margin-left: 24px;
					}
				}

				.chart-label {
					text-align: center;
					font-size: 12px;
					color: rgba(0, 0, 0, 0.45);
					margin-top: -10px;
				}
			}
		}
	}
}

/**
 * @description 商品数据
 * <AUTHOR>
 */
const { useRef } = React;
const { SearchList } = dtComponents
const ProductData = () => {

    const searchListRef = useRef()
    const getConfig = async function () {
        const data = await axios.get("https://maria.yang800.com/api/data/v2/967/800")
        return data.data.data;
    };
    return (
        <div className="product-data-wrap">
            <SearchList
                ref={searchListRef}
                scrollMode={"tableScroll"}
                paginationConfig={{ size: "default", showPosition: 'bottom' }}
                searchConditionConfig={{
                    size: "middle",
                }}
                getConfig={getConfig}
                tableCustomFun={{
                    showQty: (row) => {
                        return `${row.availableQty}/${row.initQty}`
                    }
                }}
            />
        </div>
    )
};

export default ProductData;

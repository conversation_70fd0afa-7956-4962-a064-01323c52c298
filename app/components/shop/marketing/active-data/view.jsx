import request from "../../../utils/plugins/axios/request";
import GroupData from "./group-data";
import ProductData from "./product-data";
const { Tabs, ConfigProvider, locales } = antd;
const { Spa, SpaConfigProvider } = dtComponents;
const { zh_CN } = locales
const { useState } = React
function Page() {
	const [key, setKey] = useState('1'); 
	const onChange = (key) => {
		console.log(key);
		setKey(key);
	};
	const items = [ 
		{
			key: '1',
			label: '成团数据',
			children: <GroupData />,
		},
		{ 
			key: '2',
			label: '商品数据',

			children: <ProductData className="active-data-product" />,
		},
	];
	return (
		<Tabs defaultActiveKey="1" items={items} onChange={onChange} className={`active-data-tab ${key === '2' ? 'active-data-product-tab' : ''}`} />
	)
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
	_openPage: () => { },
	request: {
		request: (params) => {
			const obj = Object.assign(params, {
				type: 'POST',
				contentType: 'application/json',
			})
			request(obj)
		}
	}
})}>
	<ConfigProvider locale={zh_CN}><Page /></ConfigProvider>
</SpaConfigProvider>, document.getElementById("active-data"));
/**
 * @description: 分页组件
 */
const { Pagination } = antd

function PaginationComponent({ pagination, changePagination, ...props }) {
    return (
        <Pagination
            {...props}
            align="center"
            position
            showSizeChanger
            pageSize={pagination.pageSize || 1}
            current={pagination.currentPage}
            total={pagination.totalCount}
            pageSizeOptions={["10", "20", "30", "40", "50", "100", "200"]}
            onChange={changePagination}
            showTotal={total => `总共 ${total} 条`}
            size="small"
            className="text-right padding-small-top padding-base-bottom margin-base-right"
        />
    );
}

export default PaginationComponent;

import request from "../../utils/plugins/axios/request";
import OffSitePromotionAuthDrawer from "./modal/auth-drawer";
import RejectModal from "./modal/reject-modal";
import {ActivityUserStatusEnum} from "./activity-userstatus-enum";
import {lib} from "../../common/react/utils/lib";
import ActivityStatusEnum from "../offsite_promotion/activity-status-enum";

const {Fragment, useRef, useState} = React;
const {Space, Button, Tabs, message,Modal} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents


export const OffsitePromotionViewRegistration = () => {
  const searchListRef = useRef();
  const [tabValue, setTabValue] = useState(()=>lib.getParam("_status")||"");
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [showType, setShowType] = useState("");
  const [editRow, setEditRow] = useState(null);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [tabNum, setTabNum] = useState({});
  const selectIds = useRef();
  const getConfig = async function () {
    const data = await axios.get("https://maria.yang800.com/api/data/v2/939/764")
    return data.data.data;
  };
  const getTabNum = (obj) => {
    let data={...obj}
    delete data.status;
    request({
      url: `/mall-admin/api/activity/user/total`,
      method: "POST",
      data:data,
      success: (res) => {
        setTabNum(res)
      },
    })
  }
  const doAuth = (row) => {
    setDrawerVisible(true)
    setShowType("auth")
    setEditRow(row)
  }

  function batchPass() {
    if (!selectIds.current.length) {
      return message.error("请选择操作的数据")
    }
    Modal.confirm({
      title: '批量通过',
      content: '确定批量通过吗？',
      onOk: () => {
        request({
          url: "/mall-admin/api/activity/user/auditBatch",
          data: {
            idList: selectIds.current,
            auditStatus: "ACTIVITY_USER_PASS"
          },
          success: (data) => {
            message.success("批量通过成功")
            searchListRef.current.resetSelected()
            searchListRef.current.load();
          }
        })
      }
    })
  }
  function batchCancel() {
    if (!selectIds.current.length) {
      return message.error("请选择操作的数据")
    }
    Modal.confirm({
      title: '批量取消',
      content: '确定批量取消吗？',
      onOk: () => {
        request({
          url: "/mall-admin/api/activity/user/cancelBatch",
          data: {
            idList: selectIds.current,
          },
          success: (data) => {
            message.success("批量取消成功")
            searchListRef.current.resetSelected()
            searchListRef.current.load();
          }
        })
      }
    })
  }
  function batchReject() {
    if (!selectIds.current.length) {
      return message.error("请选择操作的数据")
    }
    setRejectModalVisible(true)

  }

  function toDetail(row) {
    setEditRow(row)
    setDrawerVisible(true)
    setShowType("detail")
  }

  return (
    <SearchList
      ref={searchListRef}
      scrollMode={"tableScroll"}
      paginationConfig={{size: "default", showPosition: 'bottom'}}

      searchConditionConfig={{
        size: "middle",
      }}
      getConfig={getConfig}
      onSearch={(obj,type)=>{

      }}
      onSearchReset={()=>{
        setTabValue("");
      }}
      tableCustomFun={{

        myOperation: (row) => {
          return <Space wrap>
            {(row.auditStatus  === ActivityUserStatusEnum.ACTIVITY_USER_WAIT)?
              <a className={"link"} onClick={() => doAuth(row)}>审核资料</a>:null}
            {
              (row.auditStatus  === ActivityUserStatusEnum.ACTIVITY_USER_PASS||
              row.auditStatus  === ActivityUserStatusEnum.ACTIVITY_USER_REJECT)?
              <a className={"link"} onClick={() => toDetail(row)}>查看</a>:null
            }

          </Space>
        },
      }}
      renderLeftOperation={() => {
        const auditStartTime = Number(lib.getParam("_auditStartTime"));
        const auditEndTime = Number(lib.getParam("_auditEndTime"));
        return <Space>
          <Button type={"primary"} onClick={() => batchPass()}>批量通过</Button>
          <Button onClick={() => batchReject()}>批量驳回</Button>
          <Button onClick={() => batchCancel()}>批量取消</Button>
          <div>审核有效时间：{lib.joinTimeStr(auditStartTime, auditEndTime)}</div>
        </Space>
      }}
      onTableSelected={(ids, rows) => {
        selectIds.current = ids;
      }}
      onLoadBeforeHandleParams={(obj,type)=>{
        if(type==="init"){
          obj.auditStatus= lib.getParam("_status")
        }
        getTabNum(obj)
        return obj;
      }
      }
      renderOperationTopView={() => {
        return <div>
          <Tabs
            activeKey={tabValue}
            items={[
              {
                key: '',
                label: `全部（${tabNum.allNum||"0"}）`,
              },
              {
                key: ActivityUserStatusEnum.ACTIVITY_USER_WAIT,
                label: `待审核（${tabNum.waitNum||"0"}）`,
              },
              {
                key: ActivityUserStatusEnum.ACTIVITY_USER_PASS,
                label: `审核通过（${tabNum.passNum||"0"}）`,
              },
              {
                key: ActivityUserStatusEnum.ACTIVITY_USER_REJECT,
                label: `驳回（${tabNum.rejectNum||"0"}）`,
              },
              {
                key: ActivityUserStatusEnum.ACTIVITY_USER_CREATE,
                label: `未提交（${tabNum.createNum||"0"}）`,
              },
              {
                key: ActivityUserStatusEnum.ACTIVITY_USER_CANCEL,
                label: `已取消（${tabNum.cancelNum||"0"}）`,
              },
            ]} onChange={(e) => {
            setTabValue(e);
            const obj = {
              auditStatus: e,
            }
            searchListRef.current.changeImmutable(obj)
          }}/>
        </div>
      }}
      renderModal={() => {
        return <Fragment>
          <OffSitePromotionAuthDrawer
            showType={showType}
            id={editRow && editRow.id}
            visible={drawerVisible}
            onClose={(success) => {
              if (success){
                searchListRef.current.load();
              }
              setDrawerVisible(false)
            }}/>
          <RejectModal
            isBatch={true}
            visible={rejectModalVisible}
            id={selectIds.current}
            onClose={(success) => {
              if (success){
                searchListRef.current.load();
              }
              setRejectModalVisible(false)
            }}/>
        </Fragment>
      }}
    />

  )
}
ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          method: 'post',
        })
        request(obj);
      }
    }
  })}><OffsitePromotionViewRegistration/>
  </SpaConfigProvider>, document.getElementById("offsite-promotion-view-registration")
);

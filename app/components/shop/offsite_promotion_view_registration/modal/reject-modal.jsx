import request from "../../../utils/plugins/axios/request";
const {useEffect} = React
const {Modal, Form, Input,message} = antd

export default function RejectModal({isBatch = false, id, visible, onClose}) {
  const [form] = Form.useForm()
  useEffect(() => {
    if (visible) {
      form.resetFields()
    }
  }, [visible])
  const onFinish = (values) => {
    reject(values)
  }
  const handleOk = () => {
    form.submit()
  }
  const handleCancel = () => {
    onClose()
  }
  const reject = (values) => {
    let data, url
    if (isBatch) {
      data = {
        idList: id,
        auditStatus: "ACTIVITY_USER_REJECT",
        rejectReasons: values.rejectReasons
      }
      url = '/mall-admin/api/activity/user/auditBatch'
    } else {
      data = {
        id: id,
        auditStatus: "ACTIVITY_USER_REJECT",
        rejectReasons: values.rejectReasons
      }
      url = '/mall-admin/api/activity/user/audit'
    }

    request({
      url: url,
      method: "POST",
      data: data,
      needMask: true,
      success: (data) => {
        message.success("驳回成功")
        onClose(true)
      }
    })
  }
  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      onOk={handleOk}
      title="驳回"
      width={600}
      destroyOnClose={true}>

      <Form form={form} onFinish={onFinish}>
        <Form.Item label={"驳回原因"} name={"rejectReasons"} rules={[{required: true}]}>
          <Input placeholder={"请输入驳回原因"}/>
        </Form.Item>
      </Form>
    </Modal>
  )
}

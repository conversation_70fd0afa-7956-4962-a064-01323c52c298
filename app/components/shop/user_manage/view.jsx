import UserSetTagLabel from "./modal/user-set-tag-label";
import request from "../../utils/plugins/axios/request";

const {useRef, Fragment, useState} = React;
const {Modal, Space, Row, Switch,message} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents

export const SubStoreList = () => {
  const [operationRow, setOperationRow] = useState(null)
  const searchListRef = useRef()
  const [showTagSet, setShowTagSet] = useState(false)
  const getConfig = async () => {
    const data = await new Promise((resolve, reject) => {
      $.ajax({
        url: "https://maria.yang800.com/api/data/v2/874/698",
        success: (res) => {
          resolve(res)
        }
      })
    })
    return data.data;
  };

  /**
   * 设置标签
   * @param row
   */
  const tagSet = (row) => {
    setOperationRow(row)
    setShowTagSet(true)
  }

 const enableFunc=(row)=> {
    let content = row.whetherOrder ? "关闭后，该用户将可禁止下单，是否确认操作?" : "开启后，该用户将可以下单，是否确认操作";
    Modal.confirm({
      cancelText: "取消",
      okText: "确定",
      title: "提示",
      content: content,
      onOk: () => {
        let enable= !row.whetherOrder;
        request({
          url: "/api/user/update/whetherOrder",
          type: "POST",
          data: {
            id: row.userId,
            whetherOrder: enable
          },
          success: (data) => {
            message.success("设置成功")
            searchListRef.current.load()
          }
        })


      },
    });
  }


  return (
    <SearchList
      ref={searchListRef}
      scrollMode={"tableScroll"}
      paginationConfig={{size: "default",showPosition:'bottom'}}
      searchConditionConfig={{
        size: "middle",
      }}
      getConfig={getConfig}
      pageLoadTarget={{
        convertPaginationParam: function (pagination) {
          return {pageNo: pagination.currentPage, pageSize: pagination.pageSize};
        },
        convertResponseData: function (data, prePage) {
          return {
            dataList: data.data,
            page: {
              currentPage: prePage.currentPage,
              pageSize: prePage.pageSize,
              totalCount: data.total
            }
          };
        }
      }}
      renderModal={() => {
        return <Fragment>
          <UserSetTagLabel
            operationRow={operationRow}
            open={showTagSet}
            onClose={(success) => {
              if(success){
                searchListRef.current.load()
              }
              setShowTagSet(false)
              setOperationRow(null)
            }
            }
          />

        </Fragment>
      }}
      renderRightOperation={() => {
        return <Space>

        </Space>
      }}
      tableCustomFun={{
        myOperation: (row) => {
          return <Space wrap>
            <a className={"link"} onClick={() => {
              tagSet(row)
            }}>设置标签</a>
          </Space>
        },

        enableStatus(row) {
          return (
            <Fragment>
              <Switch checked={row.whetherOrder} onChange={() =>enableFunc(row)} />
            </Fragment>
          );
        }
      }}
    />

  )
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          type: 'POST',
          contentType: 'application/json',
        })
        request(obj)
      }
    }
  })}><SubStoreList/>
  </SpaConfigProvider>, document.getElementById("user-manage")
);

  # 请务必保留1个缩进
  ## 商品模块 - SELLER 可配置资源
  view_product_list:
    name: 查看商品列表
    requests:
      # 商品上架列表
      - get: /seller/on-shelf
      # 商品下架列表
      - get: /seller/off-shelf
  list_product:
    name: 上架商品
    requests:
      - post: /api/seller/items/status
  delist_product:
    name: 下架商品
    requests:
      - post: /api/seller/items/status
  add_product:
    name: 创建商品
    requests:
      # 选类目 / SPU 页
      - get: /seller/release-items
      # 查类目 API
      - get: /api/xxx
      # 查 SPU API
      - get: /api/xxx
      # 发商品表单页
      - get: /seller/item-publish
      # 查询品牌
      - get: /api/brand/suggest
      # 发商品 API
      - post: /api/seller/items
  edit_product:
    name: 更新商品
    requests:
      # 更新商品表单页
      - get: /seller/item-publish
      # 更新商品 API
      - put: /api/seller/items
  delete_product:
    name: 删除商品
    requests:
      - delete:
  manage_shop_category:
    name: 维护店铺类目
    requests:
      # 店铺管理页
      - get: /seller/shop-category
      - post: /api/seller/shopCategories
      - put: /api/seller/shopCategories/\d+/name
      - delete: /api/seller/shopCategories/\d+
      # TODO
  classify_product:
    name: 归类商品 (关联店铺类目)
    requests:
      # 商品分类页
      - get: /seller/item-sort

"items/seller/shelf_item": # component path
  category: "ADMIN" # category: OFFICIAL, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "店铺商品管理"
  service: "findItemsAsSeller"

"items/seller/shelf_item/frontend_templates/label-checkbox": # component path
  category: "ADMIN" # category: OFFICIAL, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "标签设置"
  service: "findLabelCheckbox"

"items/seller/release_items_category": # component path
  category: "ADMIN" # category: OFFICIAL, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "后台类目管理"
  service: "findChildrenByBackCategoryId" # app:serviceKey

"items/seller/item_publish/with_category": # component path
  category: "ADMIN" # category: ADMIN, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "卖家创建商品信息 无spu"
  service: "findGroupedAttributeByCategoryId"

"items/seller/item_publish/with_spu": # component path
  category: "ADMIN" # category: ADMIN, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "卖家创建商品信息 有spu"
  service: "findSpuByIdForSeller"

"items/seller/item_publish/with_item": # component path
  category: "ADMIN" # category: ADMIN, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "卖家编辑商品信息"
  service: "findForEdit"

"items/seller/category_tree":
  category: "ADMIN"
  name: "店铺内类目树展示"
  service: findEntireTreeByShopId
"items/seller/shop_category":
  category: ADMIN
  name: "店铺内类目管理"
  service: findEntireTreeByShopId
"items/seller/classify_goods":
  category: ADMIN
  name: "店铺类目与商品关联"
  service: findByShopIdAndCategoryId
  services:
    _CATEGORY_: findEntireTreeByShopId

"items/buyer/favourite_items":
  category: ADMIN
  name: "商品收藏"
  service: findFavouritedItems

"items/seller/member-price-list":
  category: "ADMIN"
  name: "会员价管理"
  service: findMembershipPriceByShopId
"items/seller/third-party-stock":
  category: "ADMIN"
  name: "商品库存中心"
  service: findThirdPartyStockList

"items/seller/distribution-price-list":
  category: "ADMIN"
  name: "微分销价管理"
  service: findDistributionPriceList

"items/seller/select-item-on-shelf-list": # component path
  category: "ADMIN" # category: OFFICIAL, TEMPLATE, SHOP, ITEM, ITEM_TEMPLATE
  name: "售卖第三方商品"
  service: "findItemsByThirdParty"

"items/seller/goods_tags":
  category: "ADMIN"
  name: "商品标签"
  service: "findGoodsTags"

"items/items_search":
  category: "OFFICIAL"
  name: "商品主搜"
  service: "searchWithAggs"

"items/nav_category":
  category: "OFFICIAL"
  name: "前台类目完全展示"
  service: "findChildrenOf"

"items/tab_floor":
  category: "OFFICIAL"
  name: "首页多TAB楼层"
  service: ""

"items/seller/store-sku-commission-list":
  category: "ADMIN"
  name: "分销商品服务费管理"
  service: findStoreDistributionCommissionList

"items/seller/goods_display":
  category: "ADMIN"
  name: "商品展示"
  service: findStoreGoodsDisplay

"items/seller/address-module":
  category: "ADMIN"
  name: "区域可售模版"
  service: ""

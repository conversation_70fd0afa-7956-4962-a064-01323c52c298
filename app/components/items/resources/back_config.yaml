findChildrenByBackCategoryId:
  method: GET
  type: http
  url: /api/backCategories/{pid}/children

findItemsAsSeller:
  method: GET
  type: http
  url: /api/seller/items/paging
  query:
    - key: shopId:_MY_SHOP_ID_
    - key: itemCode
    - key: itemId
    - key: itemName
    - key: status
    - key: statuses::1
    - key: sellOutStatus
    - key: shopCategoryId
    - key: type
    - key: typees
    - key: restrictedSalesAreaTemplateId
    - key: pageNo
    - key: pageSize

    
findLabelCheckbox:
  method: GET
  type: http
  url: /api/seller/tag
  query:
    - key: _id
    - key: shopId
    - key: name
    - key: createdAt
    - key: updateAt
    - key: lastOP
    - key: empty

findItemByIdWithCache:
  method: GET
  type: http
  url: /api/items/{itemId}/cache

findItemsByIds:
  method: GET
  type: http
  url: /api/items/find-by-ids
  query:
    - key: ids
      desc: 商品 ID 列表 (逗号分隔)
findThirdPartyStockList:
  method: GET
  type: http
  url: /api/item/thirdParty/stock/paging
  query:
    - key: thirdPartyId
    - key: matchStatus
    - key: depotName
    - key: outerSkuId
    - key: outerSkuName
    - key: type
    - key: pageNo
    - key: pageSize

findDistributionPriceList:
  method: GET
  type: http
  url: /api/distributionPrice/paging
  query:
    - key: itemId
    - key: name
    - key: pageNo
    - key: pageSize

findSpuByIdForSeller:
  method: GET
  type: http
  url: /api/seller/spu/{spuId}

findForEdit:
  method: GET
  type: http
  url: /api/item/{itemId}/for-edit

findGroupedAttributeByCategoryId:
  method: GET
  type: http
  url: /api/backCategories/{categoryId}/grouped-attribute

findEntireTreeByShopId:
  method: GET
  type: http
  url: /api/seller/shopCategories/fullTree
  query:
    - key: shopId

findByShopIdAndCategoryId:
  method: GET
  type: http
  url: /api/seller/shopCategories/items
  query:
    - key: pageNo
    - key: pageSize
    - key: shopCategoryId

findFavouritedItems:
  method: GET
  type: http
  url: /api/buyer/favorite-item
  query:
    - key: shopId
    - key: pageNo
    - key: pageSize

findMembershipPriceByShopId:
  method: GET
  type: http
  url: /api/membershipPrice/shop
  query:
    - key: userName
    - key: skuName
    - key: pageNo
    - key: pageSize

findItemsByThirdParty:
  method: GET
  type: http
  url: /api/seller/items/paging
  query:
    - key: shopId
    - key: itemCode
    - key: itemId
    - key: itemName
    - key: status
    - key: statuses
    - key: restrictedSalesAreaTemplateId
    - key: pageNo
    - key: pageSize


findGoodsTags:
  method: GET
  type: http
  url: /api/seller/tag/item
  query:
    - key: itemName
    - key: tagName
    - key: shopId
    - key: name
    - key: tagId
    - key: itemId
    - key: pageNo
    - key: pageSize

searchWithAggs:
  method: GET
  type: http
  url: /api/search
  query:
    - key: q
      desc: 关键词
    - key: bid
      desc: 品牌id
    - key: shopId
      desc: 店铺内搜索
    - key: shopCatId
      desc: 店铺类目搜索
    - key: attrs
      desc: 属性
    - key: fcid
      desc: 分类ID
    - key: bids
      desc: 品牌
    - key: regionIds
      desc: 区域id
    - key: ids
      desc: id列表
    - key: bcids
      desc: 后台类目id列表
    - key: p_f
      desc: 构建范围查询
    - key: p_t
      desc: 构建范围查询
    - key: aggs
      desc: 构建聚合查询
    - key: sort
      desc: 构建排序
    - key: highlight
      desc: 需要高亮的字段列表
    - key: pageNo
    - key: pageSize

findChildrenOf:
  method: GET
  type: http
  url: /api/frontCategories/childrenTree
  query:
    - key: ids
findStoreDistributionCommissionList:
  method: GET
  type: http
  url: /api/supplier/profit-set-sku-list
  query:
    - key: itemId
    - key: name
    - key: pageNo
    - key: pageSize

findStoreGoodsDisplay:
  method: GET
  type: http
  url: /api/sub-store/areaByName
  query:
    - key: pageNo
    - key: pageSize
    - key: serviceProviderName
      desc: 服务商名称

findRestrictedSalesAreaTemplate:
  method: POST
  type: http
  url: /api/restrictedSalesAreaTemplate/find
  query:
    - key: status
    - key: name
    - key: currentPage
    - key: pageSize
    - key: shopId:_MY_SHOP_ID_

{{#component "on-off-shelf component-standard-container js-comp"}}
<div class="component-body">
  <div class="search-item">
    <div id="block" class="block filter-block">
      <form class="form js-standard-filter-form">
        <fieldset>
          <span class="span3">
            <label for="">&nbsp;&nbsp;{{i18n "Product" bundle="items"}}ID&nbsp;&nbsp;
              <input type="text" class="item-filter-item" name="itemId" value="{{itemId}}" pattern="^\d+$" maxlength="9"
                placeholder="{{i18n "Product ID (number)" bundle="items"}}">
            </label>
          </span>
          <span class="span3">
            <label for="">&nbsp;&nbsp;{{i18n "Product Name" bundle="items"}}&nbsp;&nbsp;
              <input type="text" class="item-filter-item" name="name" value="{{name}}"
                placeholder="{{i18n "Product Name" bundle="items"}}">
            </label>
          </span>
          <span class="pull-right">
            <label for="filter">
              <button type="submit" class="btn btn-secondary js-item-filter">搜索</button>
              <button type="button" class="btn btn-info js-filter-clear">重置</button>
              <!-- <a class="btn btn-secondary" data-toggle="confirm" data-title="筛选条件已选择？" data-content="" data-event="confirm:export">导出</a>-->
            </label>
          </span>
        </fieldset>
      </form>
    </div>
  </div>
  <div class="item-list">
    <table class="table item-manage-table">
      <thead>
        <tr>
          <th width="24"><input type="checkbox" class="js-batch-select input-checkbox" autocomplete="off"></th>
          <th width="286" class="left-text">
            {{i18n "Product Name" bundle="items"}}
          </th>
          <th width="160">{{i18n "Product" bundle="items"}}ID</th>
          <th width="160">规格</th>
          <th width="100">建议零售价</th>
          <th width="60">供货价</th>
          <th width="110" class="td-operation">{{i18n "Operation" bundle="items"}}</th>
        </tr>
      </thead>
      <tbody>
        {{#each _DATA_.data}}
        <tr data-id="{{id}}">
          <td>{{#equals status "-2"}}-{{else}}<input type="checkbox" class="js-item-select input-checkbox"
              autocomplete="off">{{/equals}}</td>
          <td class="left-text">
            <a href="/items/{{itemId}}" target="_blank">{{name}}</a><br>
          </td>
          <td>{{itemId}}</td>
          <td class="center-text">
            {{#each attrs}}

            {{this.attrKey}}:{{this.attrVal}} <br>

            {{/each}}
          </td>
          <td> {{#if extraPrice}}{{formatPrice extraPrice.distributionPrice}}{{/if}}</td>
          <td> {{#if extraPrice}}{{formatPrice extraPrice.supplyPrice}}{{/if}}</td>
          <td class="td-operation">
            <a href="javascript:void(0)" class="js-set-distribution-price" data-id="{{id}}">设置微分销价</a>
          </td>
        </tr>
        {{/each}}
      </tbody>
      <tfoot>
        <tr>
          <td colspan="2">
          </td>
          <td colspan="6" class="td-operation">
            <div class="item-pagination" data-total="{{_DATA_.total}}"
              data-size="{{#if pageSize ~}}{{pageSize}}{{else}}20{{~/if}}"></div>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
</div>
{{/component}}
const Pagination = require("pokeball").Pagination,
  Modal = require("pokeball").Modal,
  CommonDatepicker = require("utils/module").plugins.commonDatepicker

const setDistributionPriceTemplate = Handlebars.templates["items/seller/distribution-price-list/frontend_templates/set-distribution-price"]
let setMemberPriceSelectedIds
// jquery = require("jquery@3.3.1")
// let autocomplete = require("jquery-autocomplete-js")

class DistributionPriceList {
  // submitSetMemberPrice;
  constructor($) {
    this.target = this.$el
    this.itemBatch = $(".js-batch-select")
    this.itemSelect = $(".js-item-select")
    this.itemFilterClear = $(".js-filter-clear")
    this.itemOffShelf = $(".js-item-off")
    this.itemOnShelf = $(".js-item-on")
    this.itemBatchOn = $(".js-item-batch-on")
    this.itemBatchExport = $(".js-item-batch-export")
    this.itemBatchOff = $(".js-item-batch-off")
    this.itemDelete = $(".js-item-delete")
    this.itemBatchDelete = $(".js-item-batch-delete")
    this.jsImport = $(".js-import")
    this.pagination = $(".item-pagination")
    this.skuAttr = $(".js-sku-attr")
    this.$setDistributionPriceForm = $("#set-distribution-price-form")
    this.bindEvent()
  }

  bindEvent() {
    this.$el.find(".js-edit-detail").on("click", evt => this.getItemDetail(evt))
    this.$el.find(".js-set-member-price").on("click", evt => this.setMemberPrice(evt))
    this.$el.find(".js-set-distribution-price").on("click", evt => this.setDistributionPrice(evt))
    $(document).on("click", ".js-submit-item-detail", (evt) => this.submitItemDetail(evt))
    new Pagination(this.pagination).total(this.pagination.data("total")).show(this.pagination.data("size"), {
      num_display_entries: 3,
      jump_switch: true,
      page_size_switch: true,
      maxPage: -1
    })
    $(".datepicker").datepicker({
      yearRange: [2000, 2100]
    })
    new CommonDatepicker(this.$el)
    this.itemOffShelf.on("click", evt => this.offTheItem(evt))
    this.itemOnShelf.on("click", evt => this.onTheItem(evt))
    this.itemBatchOff.on("click", evt => this.batchOffItems(evt))
    this.itemBatchOn.on("click", evt => this.batchOnItems(evt))
    this.itemBatchExport.on("click", evt => this.batchExportItems(evt))
    this.itemBatch.on("click", evt => this.selectBatch(evt))
    this.itemSelect.on("change", evt => this.selectItem(evt))
    this.itemDelete.on("click", evt => this.deleteTheItem(evt))
    this.itemBatchDelete.on("click", evt => this.batchDeleteItems(evt))
    this.itemFilterClear.on("click", evt => this.clearFilter(evt))
    $(document).on("confirm:export", evt => this.export(evt))



  }
  elementScroll(el) {
    const jumpTop = $(el).offset().top - this.top - 20
    $('html, body').animate({
      scrollTop: jumpTop
    })
  }

  submitItem(evt) {
    evt.preventDefault()
    let $form = $("#set-distribution-price-form").serializeArray(),
      type = "POST"
    let data = {};
    $.each($form, function() {
      data[this.name] = this.value;
    });
    data.distributionPrice=centFormat(data.distributionPrice)
    data.supplyPrice = centFormat(data.supplyPrice)
    $("body").spin("medium")
    $.ajax({
      url: "/api/distributionPrice/setSkuPrice",
      type: type,
      contentType: "application/json",
      data: JSON.stringify(data),
      success: (data) => {
        window.setDistributionPriceModel.close()
        new Modal({
          icon: "success",
          title: "设置成功",
          content: "设置微分销价成功"
        }).show()
        $("body").overlay(false)
        window.location.reload()
      },
      error: function (data) {

        new Modal({
          icon: "error",
          content: "设置失败," + data.responseJSON.message,
          title: "设置失败"
        }).show()
      }
    })
  }


  validateSkuUnitQuantity(skus) {
    let result = true
    $.each(skus.skuPrices, function (key, val) {
      if (!val.distributionPrice) {
        result = false
      }
      if (val.profit > val.distributionPrice) {
        new Modal({
          icon: "error",
          content: "设置失败,利润不可大于售价",
          title: "设置失败"
        }).show()
        result = false
        return false

      }
    })
    return result
  }


  // 商品选择去除全选框
  selectItem(evt) {
    if (!$(evt.currentTarget).prop("checked")) {
      $(".js-batch-select").prop("checked", false)
    }

    this.checkItem()
  }

  // 批量选择
  selectBatch(evt) {
    $("input.js-item-select").prop("checked", !!$(evt.currentTarget).prop("checked"))
    this.checkItem()
  }

  // 清除筛选
  clearFilter() {
    $("input.item-filter-item").val("")
    window.location.href = window.location.pathname
  }



  // 设置微分销价
  setDistributionPrice(evt) {
    const skuId = $(evt.currentTarget).data("id")
    evt.stopPropagation()
    const data = {
      skuId: skuId
    }
    console.log("会员价设置获取sku信息如下")
    console.log(data)
    const setDistributionPrice = $(setDistributionPriceTemplate({
      data
    }))

    window.setDistributionPriceModel = new Modal(setDistributionPrice)
    // trade = new Modal setMemberPriceTemplate({data: itemId})
    // trade.show()
    window.setDistributionPriceModel.show()
    $("#set-distribution-price-form").validator()
    $("#set-distribution-price-form").on("submit", evt => this.submitItem(evt))
    $(".js-batch-distribution-set").on("click", evt => this.batchSetPrice(evt))
  }

  isAllselected() {
    let status
    status = true
    if (!($(".sku-choose", this.$el).length === $(".js-sku-attr").filter(".selected").length)) {
      status = false
    }
    return status
  }
}

module.exports = DistributionPriceList

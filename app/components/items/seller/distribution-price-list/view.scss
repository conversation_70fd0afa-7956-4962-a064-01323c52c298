@import "compass/css3/inline-block";
@import "pokeball/theme";

.on-off-shelf {

  .block {
    margin-bottom: 20px;
  }

  table {
    .top-text {
      vertical-align: top;
    }
    .item-logo {
      float: left;
      img {
        width: 58px;
        height: 58px;
        border: 1px solid $color-border;
      }
    }
    .item-description {
      float: left;
      width: 210px;
      margin-left: 10px;

      a {
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        @include inline-block(none);
      }
    }
  }

  .no-padding {
    padding: 0;
  }
}

.set-member-price-form {
  margin-top: 20px;
  padding: 0 15px;
  position: relative;

  .close-warning {
    color: $color-danger;
    font-weight: bold;
    font-size: 20px;
    position: absolute;
    right: 10px;
    top: 10px;
  }

  #choose {
    list-style: none;

    .control-group {

      .sku-attr {
        border: 1px solid $color-border;
        @include inline-block(middle);
        margin-right: 0;
        margin-bottom: 5px;
        padding: 5px 15px;
        color: $color-text;
        line-height: 14px;
        font-size: 12px;
        position: relative;

        &.hasImage {
          padding: 0;
        }

        &.disabled,
        &[disabled] {
          cursor: not-allowed;
          background-color: $color-background;
        }
      }

      .sku-items-label {
        float: left;
        width: 90px;
        height: 25px;
        overflow: hidden;
        text-align: left;
        font-size: 12px;
        color: $color-text-assist;
        @include inline-block(middle);
      }

      .sku-items {
        width: 400px;
        @include inline-block(middle);

        img {
          width: 25px;
          height: 25px;
        }
      }

      .selected {
        border: 1px solid $color-primary;
        position: relative;

        .attr-checked {
          display: block;
        }
      }

      .attr-checked {
        position: absolute;
        display: none;
        right: -1px;
        bottom: -1px;
        // display: inline-block;
        width: 13px;
        height: 13px;

        .bg-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }
    }
    .total-stock {
      color: $color-text-assist;
    }
  }

  #choose-btns {
    margin-top: 26px;
    padding-left: 92px;

    .report-item {
      padding: 14px;
    }

    .btn {
      font-size: 16px;
      width: 180px;
      height: 40px;
      text-align: center;
      padding: 0;
    }

    .btn-secondary {
      background-color: #ffffff;
      border: 1px solid #197aff;
      color: #197aff;
      &:hover {
        border: 1px solid #24344e;
        color: #24344e;
      }
    }

    .btn-primary {
      border: 1px solid #197aff;
      background-color: #197aff;
      &:hover {
        border: 1px solid #24344e;
        background-color: #24344e;
      }
    }
  }
  .intelligent-search .select-span {
    position: absolute;
    border: 1pt solid #c1c1c1;
    overflow: hidden;
    width: 232px;
    height: 34px;
    clip: rect(-1px 234px 207px 218px);
  }
  .intelligent-search .select-span select {
    width: 234px;
    height: 34px;
    margin: -2px;

  }
  .intelligent-search .input-span {
    position: absolute;
    border: 1pt solid #c1c1c1;
    border-left: 1pt solid #c1c1c1;
    border-bottom: 1pt solid #c1c1c1;
    width: 218px;
    height: 34px;
  }
  .intelligent-search .input-span input {
    width: 216px;
    height: 32px;
    border: 0pt;
  }
}
.batch-set {
  .group-title {
    position: static;
    width: auto;
    margin-right: 0 !important;
  }
  .group-content {
    padding-left: 10px;
    display: inline-block;
  }
}

<div class="modal">
  <div class="modal-header">
    <h2>设置会员价</h2>
  </div>
  <form class="form form-aligned set-member-price-form" style="width:500px" id="set-member-price-form"
        data-id="{{data.item.id}}">
    <fieldset>
      <div class="invoice-edit-form modal-body" data-item-skus="{{json this}}">

        <div class="invoice-common ">
          <div class="control-group">
            <label class="control-label"><span class="required">*</span>会员名：</label>
            <input type="text" name="memberUserName" class="input-medium memberUserName"
                   placeholder="请填写选择会员名" maxLength="30" required autocomplete="off" >




            <span class="note-error note-icon hint--top" aria-label="请填写选择会员名">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
          </div>
          <div class="control-group" style="margin-top: 25px;">
            <!--  <label class="control-label"><span class="required">*</span>规格：</label>-->
            <ul id="choose" data-skus="{{json data.skus}}"
                data-attrs="{{json data.groupedSkuAttrs}}" style="margin-left: 38px;">
              {{#each data.groupedSkuAttrs}}
                <li class="sku-choose js-attr-sort" data-index="{{@index}}">
                  <div class="control-group">
                    <label class="dt sku-items-label" for="input-name">{{attrKey}}</label>
                    <div class="smdd sku-items">
                      <div class="wrap-input">
                        {{#each skuAttributes}}
                          <a class="{{#if image}}hasImage{{/if}} js-sku-attr sku-attr" data-show="{{showImage}}"
                             {{#if image}}data-src="{{image}}"{{/if}} data-id="{{id}}" href="javascript:;"
                             title="{{attrVal}}" data-attr="{{attrKey}}:{{attrVal}}" data-key="{{attrKey}}">
                            {{#if image}}<img src="{{cdnPath image "60"}}" alt="{{attrVal}}"
                                              data-src="{{image}}">{{else}}{{attrVal}}{{/if}}
                            <span class="attr-checked">
                        <img src="/assets/images/other-images/sku-c-a.png" class="bg-image">
                      </span>
                          </a>
                        {{/each}}
                      </div>
                    </div>
                  </div>
                </li>
              {{/each}}


            </ul>

          </div>
          <div class="control-group" style="margin-top: 25px;">
            <label class="control-label"><span class="required">*</span>会员价：</label>
            <input type="text" name="membershipPrice" class="input-medium" placeholder="请填写会员价"
                   maxLength="18" required pattern="^[0-9]+(.[0-9]{2})?$">

            <span class="note-error note-icon hint--top" aria-label="请填写会员价">
              <i class="icon-pokeball icon-pokeball-error "></i>
            </span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <input type="hidden" name="userId" class="enter-input">
        <input type="hidden" name="skuId" class="enter-input">
        {{#if data.trade.paperNo}}
          <button class="btn btn-success btn-primary btn-medium close">{{i18n "Confirm" bundle="user"}}</button>
        {{else}}
          <button class="btn btn-success btn-primary btn-medium" type="submit">{{i18n "Confirm" bundle="user"}}</button>
          <button class="btn btn-info btn-medium close">{{i18n "Cancel" bundle="user"}}</button>
        {{/if}}
      </div>
    </fieldset>
  </form>
</div>

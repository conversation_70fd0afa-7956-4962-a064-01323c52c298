/**
 * @description 余额使用规则
 * <AUTHOR>
 */
import request from "../../../utils/plugins/axios/request";
const { useEffect, useRef } = React
const { Form, Input, Button, message } = antd
const { FormInput } = dtComponents;
const GiftRules = () => {
    const editorRef = useRef(null);
    const [form] = Form.useForm();
    const { TextArea } = Input;

    const id = sessionStorage.shopId
    useEffect(() => {
        getDetail()
    }, [])
    function getDetail() {
        request({
            url: `/mall-admin/api/rule/balance/getInfo`,
            method: 'post',
            needMask: true,
            data: {
                shopId: id,
            },
            success: (data) => {
                form.setFieldsValue({ defaultValidityDay: data.defaultValidityDay })
                initEditor(data.remark || "")
            }
        })
    }
    function initEditor(fwb) {
        tinymce.init({
            selector: '#mytextarea',
            language: 'zh_CN',
            plugins: 'image',
            toolbar: 'image',
            setup: (editor) => {
                editorRef.current = editor;
            },
            init_instance_callback: (editor) => {
                editor.setContent(fwb);
            },
            images_upload_handler: function (blobInfo) {
                return new Promise((resolve, reject) => {
                    var formData = new FormData();
                    formData.append('file', blobInfo.blob(), blobInfo.filename());
                    request({
                        url: '/api/file/upload',
                        method: 'post',
                        needMask: true,
                        data: formData,
                        success: function (data) {
                            resolve(data);
                        },
                        fail: function (error, msg) {
                            reject(new Error(msg || '上传图片失败'));
                        }
                    });
                });
            }
        });
        return () => {
            // 卸载编辑器
            tinymce.get(editorRef.current.id).remove();
        };
    }

    const onFinish = () => {
        form.validateFields().then(values => {
            console.log('Received values of form2:', values);
            const content = editorRef.current.getContent();
            request({
                url: "/mall-admin/api/rule/balance/addRuleBalance",
                method: "POST",
                data: {
                    ...values,
                    shopId: sessionStorage.shopId,
                    remark: content,
                },
                success: () => {
                    message.success("操作成功");
                },
            });
        });
    };

    const layout = {
        labelCol: {
            style: { width: "180px" },
        },
    };
    return (
        <div>
            <div className="title-text" style={{ marginBottom: '8px' }}>福卡使用规则</div>
            <p>福卡订单下单时可直接抵扣，无使用限制</p>
            <div className="title-text" style={{ marginBottom: '8px' }}>福豆使用规则</div>
            <p>1.福豆使用规则按照商品支付金额的一定比例计算。</p>
            <p>2.平台优惠设置可能实时发生变动，请用户以下单时页面展示为准。</p>
            <p>3.用户发起取消订单，退款成功后订单使用的福豆会自动退还至账户；。</p>
            <p>4.福豆有效期：从获得开始，指定时间内有效。特殊活动获得的福豆，有效期以具体活动规则为准。</p>


            <div className="title-text" style={{ marginBottom: '8px' }}>默认规则</div>
            <Form
                {...layout}
                form={form}
                name="control-ref"
                onFinish={onFinish}>
                <FormInput
                    fProps={{
                        label: "福豆默认使用有效期（天）",
                        name: "defaultValidityDay",
                        rules: [{ required: true, message: '请输入福豆默认使用有效期' }, { pattern: /^[1-9]\d*$/, message: '请输入正整数' }],
                    }}
                />
                <Form.Item label="小程序侧使用规则展示" name="remark">
                    <TextArea id="mytextarea"></TextArea>
                </Form.Item>
                <div className="text-center" style={{ width: "100%" }}>
                    <Button type="primary" htmlType="submit">确认</Button>
                </div>
            </Form>
        </div>
    );
};

export default GiftRules;
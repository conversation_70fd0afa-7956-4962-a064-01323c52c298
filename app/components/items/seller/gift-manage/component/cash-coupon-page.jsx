/**
 * @description 余额获取活动-商家补贴
 * <AUTHOR>
 */
import AddModal from "./add-modal"
const { useRef, useState } = React;
const { Space, Button } = antd;
const { SearchList } = dtComponents

const Activity = ({ selectedId }) => {
    const searchListRef = useRef()
    const [addModalVisible, setAddModalVisible] = useState(false);
    const getConfig = async function () {
        const data = await axios.get("https://maria.yang800.com/api/data/v2/945/770")
        return data.data.data;
    };
    
    return (
        <SearchList
            ref={searchListRef}
            scrollMode={"tableScroll"}
            paginationConfig={{ size: "default", showPosition: 'bottom' }}
            searchConditionConfig={{
                size: "middle",
            }}
            getConfig={getConfig}
            renderModal={() => {
                return <AddModal
                    selectedId={selectedId}
                    open={addModalVisible}
                    onClose={() => {
                        setAddModalVisible(false)
                        searchListRef.current.load();
                    }}
                />
            }}
            tableCustomFun={{
                transferMoney: (row) => {
                    return `${row.transferMoney}${row.type === "10" ? "个" : "元"}`
                },
            }}
            renderLeftOperation={() => {
                return <Space>
                    <Button type="primary" onClick={() => setAddModalVisible(true)}>新增活动</Button>
                </Space>
            }}
        />

    )
}

export default Activity


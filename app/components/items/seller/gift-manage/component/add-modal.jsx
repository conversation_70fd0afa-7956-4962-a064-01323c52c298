/**
 * @description 新增弹窗
 * <AUTHOR>
 */
import request from "../../../../utils/plugins/axios/request";
import { ACTIVITY_TYPE_ENUM } from "../enum"
import FirstJoinForm from "./first-join-from";
import InviteFriendForm from "./invite-friend-from";
import CashCouponForm from "./cash-coupon-from";
const { Form, message, Modal } = antd;
const { useEffect, useState } = React
function AddModal({ id, selectedId, open, onClose }) {
    const [form] = Form.useForm();
    const [title, setTitle] = useState("")
    const [submitUrl, setSubmitUrl] = useState("")
    const [detailUrl, setDetailUrl] = useState("")
    const [useInfo, setUseInfo] = useState({})
    const [defaultValidityDay, setDefaultValidityDay] = useState(null); //默认天数
    let isEdite = !!id
    function getDesc(selectedId) {
        let title = ""
        let submitUrl = ""
        let detailUrl = ""
        switch (selectedId) {
            case ACTIVITY_TYPE_ENUM.FIRST_JOIN_GROUP:
                title = `${isEdite ? "编辑" : "新增"}首次入群福豆活动`;
                submitUrl = "/mall-admin/api/rule/activity/first/add"
                detailUrl = "/mall-admin/api/rule/activity/first/getInfo"
                break;
            case ACTIVITY_TYPE_ENUM.INVITE_FRIEND:
                title = `${isEdite ? "编辑" : "新增"}邀请好友活动`;
                submitUrl = "/mall-admin/api/rule/invite/friend/add"
                detailUrl = "/mall-admin/api/rule/invite/friend/getInfo"
                break;
            case ACTIVITY_TYPE_ENUM.CASH_COUPON_SUBSIDY:
                title = `${isEdite ? "编辑" : "新增"}商家补贴活动`;
                submitUrl = "/mall-admin/api/rule/cash/voucher/add"
                break;
            default:
                break
        }
        setTitle(title)
        setSubmitUrl(submitUrl)
        setDetailUrl(detailUrl)
    }


    // 获取福豆有效天数
    function getDefaultValidityDay() {
        request({
            url: '/mall-admin/api/rule/balance/getDefaultValidityDay',
            method: "POST",
            data: {
                shopId: sessionStorage.shopId
            },
            needMask: true,
            success: (data) => {
                setDefaultValidityDay(data)
                let type = data < 0 ? 2 : 1;
                form.setFieldsValue({ validDayType: isEdite ? 2 : type });
            },
        })
    }
    const getDetail = () => {
        request({
            url: detailUrl,
            method: "POST",
            data: {
                id,
                shopId: sessionStorage.shopId
            },
            needMask: true,
            success: (data) => {
                data.activityStartTime = [moment(moment(data.activityStartTime).format("yyyy-MM-DD")), moment(moment(data.activityEndTime).format("yyyy-MM-DD"))];
                form.setFieldsValue(data)
            }
        })
    }

    useEffect(() => {
        getDesc(selectedId)
        if (isEdite) {
            getDetail()
        }
        // if (selectedId === ACTIVITY_TYPE_ENUM.FIRST_JOIN_GROUP || selectedId === ACTIVITY_TYPE_ENUM.INVITE_FRIEND) {
        getDefaultValidityDay()
        // }
    }, [isEdite])

    const handleOk = () => {
        form.validateFields().then(values => {
            let data = {
                ...values,
                shopId: sessionStorage.shopId,
                moneyValidityDay: values.validDayType === 1 ? defaultValidityDay : values.moneyValidityDay
            };

            if (ACTIVITY_TYPE_ENUM.FIRST_JOIN_GROUP === selectedId || ACTIVITY_TYPE_ENUM.INVITE_FRIEND === selectedId) {
                const [start, end] = values.activityStartTime
                data = {
                    ...data,
                    activityStartTime: start ? moment(start).format('x') : null,
                    activityEndTime: end ? moment(end).format('x') : null,
                    // moneyValidityDay: values.moneyValidityDay ? values.moneyValidityDay : defaultValidityDay
                    // moneyValidityDay: values.validDayType === 1 ? defaultValidityDay : values.moneyValidityDay
                }
            }

            if (ACTIVITY_TYPE_ENUM.CASH_COUPON_SUBSIDY === selectedId) {
                data = {
                    ...data,
                    transferUserId: useInfo.transferUserId,
                    fileUrlList: values.fileUrlList.map(obj => obj.url)
                }
            }

            if (isEdite) {
                data = {
                    ...data,
                    id
                }
            }

            if (ACTIVITY_TYPE_ENUM.CASH_COUPON_SUBSIDY === selectedId) {
                Modal.confirm({
                    title: '提示',
                    content: '确认后将充值至用户福卡余额，请确认是否充值？',
                    okText: '确认',
                    cancelText: '取消',
                    onOk: () => {
                        getSubmit(data)
                    },
                })
            } else {
                getSubmit(data)
            }

        });
    };

    function getSubmit(data) {
        request({
            url: submitUrl,
            method: "POST",
            data: data,
            success: () => {
                message.success("操作成功");
                onClose();
                form.resetFields()
            },
        });
    }

    const layout = {
        labelCol: {
            style: { width: "138px" },
        },
    };

    return (
        <Modal
            title={title}
            open={open}
            onOk={handleOk}
            onCancel={() => {
                onClose();
                form.resetFields()
            }}
            keyboard={false}
            maskClosable={false}
            destroyOnClose
            width={500}>
            {
                <Form {...layout} form={form}>
                    {selectedId === ACTIVITY_TYPE_ENUM.FIRST_JOIN_GROUP &&
                        <FirstJoinForm
                            isEdite={isEdite}
                            defaultValidityDay={defaultValidityDay}
                            onChangeValidDayType={(e) => {
                                form.resetFields(["moneyValidityDay"]);
                            }}
                            form={form}
                        />}
                    {selectedId === ACTIVITY_TYPE_ENUM.INVITE_FRIEND &&
                        <InviteFriendForm
                            isEdite={isEdite}
                            defaultValidityDay={defaultValidityDay}
                            onChangeValidDayType={(e) => {
                                form.resetFields(["moneyValidityDay"]);
                            }}
                            form={form}
                        />}
                    {selectedId === ACTIVITY_TYPE_ENUM.CASH_COUPON_SUBSIDY &&
                        <CashCouponForm
                            isEdite={isEdite}
                            defaultValidityDay={defaultValidityDay}
                            onChangeValidDayType={(e) => {
                                form.resetFields(["moneyValidityDay"]);
                            }}
                            onUserInfo={(res) => {
                                form.setFieldsValue({ nickName: res.nickName });
                                setUseInfo(res);
                            }}
                            onClearNickName={() => {
                                form.setFieldsValue({ nickName: '' });
                                setUseInfo({});
                            }}
                            form={form}
                        />}
                </Form>
            }

        </Modal>
    );
}
export default AddModal

/**
 * @description 新增活动-福卡补贴
 * <AUTHOR>
 */

import request from "../../../../utils/plugins/axios/request";
import { TYPE_ENUM, VALID_DAY_ENUM } from "../enum"
const { FormInput, FormSelect, DTUploaderFile, FormTextDefault, FormRadioGroup } = dtComponents
const { useState, Fragment } = React
const { Form, message, Input } = antd;
const { PlusOutlined } = icons
export const picUploadButton = (text) => (
    <div className={"image-empty"}>
        <div className={"placeholder"}>
            <PlusOutlined />
            <div className={"text"}>{text || '上传'}</div>
        </div>
    </div>
)
function CashCouponComponent({ isEdite, onUserInfo, defaultValidityDay, onClearNickName, onChangeValidDayType, form }) {
    const [type, setType] = useState(TYPE_ENUM.GIFT_MONEY);
    const [validDayType, setValidDayType] = useState();
    const [subImages, setImages] = useState(Array(5).fill(null))

    function getQueryUser(phone) {
        request({
            url: '/mall-admin/api/rule/cash/voucher/queryUser',
            method: "POST",
            data: {
                shopId: sessionStorage.shopId,
                phone: phone
            },
            needMask: true,
            success: (data) => {
                onUserInfo(data)
            },
        })
    }

    const innerOnChange = (index, { file, fileList }) => {
        // const { onChange } = props
        if (file.status === "uploading") {
            subImages[index] = file
            setImages([...subImages])
        } else if (file.status === "done") {
            subImages[index] = file
            setImages([...subImages])
            // onChange(subImages)
        } else if (file.status === "removed") {
            subImages[index] = null
            setImages([...subImages])
            // onChange(subImages)
        } else if (file.status === "error") {
            message.error("上传失败")
        }
    }
    return (
        <Fragment>
            <Form.Item
                label="用户信息"
                name="phone"
                rules={[{ required: true, message: '请输入用户信息' }]}
            >
                <Input
                    onBlur={(e) => {
                        getQueryUser(e.target.value)
                    }}
                    onChange={(e) => {
                        // 如果输入框更改了 清空用户ID
                        if (e.target.value) {
                            onClearNickName()
                        }
                    }}
                />
            </Form.Item>
            <FormTextDefault
                fProps={{
                    name: 'nickName',
                    label: '用户昵称',
                }} />
            <FormRadioGroup
                fProps={{
                    label: '余额类型',
                    name: 'type',
                    rules: { required: true },
                    // initialValue: TYPE_ENUM.ACCOUNT_MONEY,
                }}
                url="/mall-admin/api/rule/cash/voucher/getTypeList"
                onChange={(e) => {
                    setType(e);
                    form.setFieldsValue({
                        transferMoney: undefined
                    });
                }}
            />
            {
                type === TYPE_ENUM.GIFT_MONEY &&
                <div div style={{ display: "flex", alignItems: "center" }}>
                    <FormRadioGroup
                        fProps={{
                            label: '福豆有效期（天）',
                            name: "validDayType",
                            rules: { required: true },
                            initialValue: 1
                        }}
                        list={[
                            { id: 1, name: `默认（${defaultValidityDay}天）`, disabled: defaultValidityDay <= 0 },
                            { id: 2, name: '自定义' },
                        ]}
                        cProps={{
                            width: 260,
                        }}
                        onChange={(e) => {
                            setValidDayType(e)
                            onChangeValidDayType(e)
                        }}
                    />
                    {
                        (isEdite || validDayType === VALID_DAY_ENUM.CUSTOMIZE) &&
                        <FormInput
                            fProps={{
                                name: "moneyValidityDay",
                                rules: [{ required: true, message: '请输入天数' }, { pattern: /^[1-9]\d*$/, message: '请输入正整数' }],
                            }}
                            cProps={{
                                width: 100,
                            }}
                        />
                    }
                </div>
            }
            <FormInput
                fProps={{
                    // label: '补贴金额',
                    label: `补贴金额${type === "10" ? "(个)" : "(元)"}`,
                    name: "transferMoney",
                    // rules: { required: true },
                    rules: [
                        { required: true, message: '请输入补贴金额' },
                        {
                            pattern: type === "10" ? /^[1-9]\d*$/ : /^\d+(\.\d{1,2})?$/,
                            message: type === "10" ? '请输入正整数' : '请输入数字，最多2位小数'
                        }
                    ],
                    extra: '1福卡=1元,10福豆=1元'
                }} />
            <FormSelect
                fProps={{
                    label: '补贴原因',
                    name: 'transferReason',
                    rules: { required: true },
                }}
                url="/mall-admin/api/rule/cash/voucher/getTransferReasonList"
            />
            <Form.Item
                label="上传附件"
                name="fileUrlList"
                extra="支持扩展名：.png .jpg"
                valuePropName="fileList"
                getValueFromEvent={e => {
                    if (Array.isArray(e)) {
                        return e;
                    }
                    return e && e.fileList;
                }}
                rules={[{ required: true, message: "请上传附件" }]}>
                <DTUploaderFile
                    maxCount={5}
                    size={5}
                    accept={[".jpg", ".jpeg", ".png"]}
                    listType={"picture-card"}
                    fileList={subImages[0] ? [subImages[0]] : []}
                    uploadButton={picUploadButton("上传")}
                    // customRequest={customRequest}
                    onChange={(file) => {
                        innerOnChange(0, file)
                    }}
                />
            </Form.Item>
        </Fragment>
    )
}
export default CashCouponComponent;

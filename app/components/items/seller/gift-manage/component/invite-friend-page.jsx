/**
 * @description 余额获取活动-邀请好友
 * <AUTHOR>
 */
import AddModal from "./add-modal"
import { STATUS_ENUM } from "../enum"
const { useRef, useState } = React;
const { Space, Button } = antd;
const { SearchList, Confirm } = dtComponents

const Activity = ({ selectedId }) => {
    const searchListRef = useRef()
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [id, setId] = useState(null);
    const getConfig = async function () {
        const data = await axios.get("https://maria.yang800.com/api/data/v2/944/769")
        return data.data.data;
    };

    return (
        <SearchList
            ref={searchListRef}
            scrollMode={"tableScroll"}
            paginationConfig={{ size: "default", showPosition: 'bottom' }}
            searchConditionConfig={{
                size: "middle",
            }}
            getConfig={getConfig}
            renderModal={() => {
                return <AddModal
                    id={id}
                    selectedId={selectedId}
                    open={addModalVisible}
                    onClose={() => {
                        setAddModalVisible(false)
                        setId(null)
                        searchListRef.current.load();
                    }}
                />
            }}
            renderLeftOperation={() => {
                return <Space>
                    <Button type="primary" onClick={() => setAddModalVisible(true)}>新增活动</Button>
                </Space>
            }}
            tableCustomFun={{
                inviterMoney: (row) => {
                    return `${row.inviterMoney}${row.type === "10" ? "个" : "元"}`
                },
                inviteeMoney: (row) => {
                    return `${row.inviteeMoney}${row.type === "10" ? "个" : "元"}`
                },
                operateFn: (row) => {
                    return (
                        <div className="operate">
                            {
                                row.status === STATUS_ENUM.IN_PROGRESS &&
                                <Confirm
                                    data={{
                                        title: '使失效',
                                        name: '使失效',
                                        content: '失效后活动时间内将不再派发福豆，是否确认失效？',
                                        url: '/mall-admin/api/rule/invite/friend/invalid',
                                        params: { id: row.id, shopId: sessionStorage.shopId },
                                    }}
                                    onReload={() => {
                                        searchListRef.current.load();
                                    }}></Confirm>
                            }
                            
                            {
                                row.status === STATUS_ENUM.NOT_START &&
                                <div onClick={() => {
                                    setId(row.id);
                                    setAddModalVisible(true);
                                }
                                }>编辑</div>
                            }

                            {
                                [STATUS_ENUM.NOT_START, STATUS_ENUM.EXPIRED, STATUS_ENUM.INVALID].includes(row.status) &&
                                <Confirm
                                    data={{
                                        title: '删除',
                                        name: '删除',
                                        content: '是否确认删除？',
                                        url: '/mall-admin/api/rule/invite/friend/remove',
                                        params: { id: row.id, shopId: sessionStorage.shopId },
                                    }}
                                    onReload={() => {
                                        searchListRef.current.load();
                                    }}></Confirm>
                            }
                        </div>
                    )
                }
            }}
        />

    )
}

export default Activity


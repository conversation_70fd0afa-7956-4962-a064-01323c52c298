/**
 * @description 新增活动-首次入群
 * <AUTHOR>
 */
import { TYPE_ENUM, VALID_DAY_ENUM } from "../enum";
const { FormInput, FormRangePicker, FormRadioGroup } = dtComponents;
const { useState, Fragment } = React
function FirstJoinComponent({ isEdite, defaultValidityDay, onChangeValidDayType, form }) {
    const [type, setType] = useState(TYPE_ENUM.GIFT_MONEY);
    const [validDayType, setValidDayType] = useState();

    const disabledDate = (current) => {
        return current && current < moment().startOf('day');
    };
    return (
        <Fragment>
            <FormInput
                fProps={{
                    label: '活动名称',
                    name: 'activityName',
                    rules: [{ required: true, message: '请输入活动名称' }, { max: 20, message: '请输入最长20位字符' }],
                }}
                cProps={{
                    width: 260
                }} />
            <FormRadioGroup
                fProps={{
                    label: '余额类型',
                    name: 'type',
                    rules: { required: true },
                    initialValue: TYPE_ENUM.GIFT_MONEY,
                }}
                url="/mall-admin/api/rule/activity/first/getTypeList"
                onChange={(e) => {
                    setType(e)
                    form.setFieldsValue({
                        activityMoney: undefined,
                    });
                }}
            />
            <FormInput
                fProps={{
                    // label: '(元)',
                    label: `入群金额${type === "10" ? "(个)" : "(元)"}`,
                    name: "activityMoney",
                    rules: [
                        { required: true, message: '请输入入群金额' },
                        {
                            pattern: type === "10" ? /^[1-9]\d*$/ : /^\d+(\.\d{1,2})?$/,
                            message: type === "10" ? '请输入正整数' : '请输入数字，最多2位小数'
                        }
                    ],
                }}
                cProps={{
                    width: 260
                }} />
            <FormRangePicker
                fProps={{
                    label: '生效时间',
                    name: 'activityStartTime',
                    rules: { required: true },
                }}
                cProps={{
                    RangePicker: disabledDate,
                    width: 260
                }}
            />
            {
                type === TYPE_ENUM.GIFT_MONEY &&
                <div div style={{ display: "flex", alignItems: "center" }}>
                    <FormRadioGroup
                        fProps={{
                            label: '福豆有效期（天）',
                            name: "validDayType",
                            rules: { required: true },
                            initialValue: 1
                        }}
                        list={[
                            { id: 1, name: `默认（${defaultValidityDay}天）`, disabled: defaultValidityDay <= 0 },
                            { id: 2, name: '自定义' },
                        ]}
                        cProps={{
                            width: 260,
                        }}
                        onChange={(e) => {
                            setValidDayType(e)
                            onChangeValidDayType(e)
                        }}
                    />
                    {
                        (isEdite || validDayType === VALID_DAY_ENUM.CUSTOMIZE) &&
                        <FormInput
                            fProps={{
                                name: "moneyValidityDay",
                                rules: [{ required: true, message: '请输入天数' }, { pattern: /^[1-9]\d*$/, message: '请输入正整数' }],
                            }}
                            cProps={{
                                width: 100,
                            }}
                        />
                    }
                </div>
            }
        </Fragment >
    )
}
export default FirstJoinComponent;

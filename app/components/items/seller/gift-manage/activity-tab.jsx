/**
 * @description 余额获取活动
 * <AUTHOR>
 */
// import request from "../../../../utils/plugins/axios/request";
import FirstJoinPage from "./component/first-join-page"
import InviteFriendPage from "./component/invite-friend-page"
import CashCouponPage from "./component/cash-coupon-page"
import { ACTIVITY_TYPE_ENUM } from "./enum"
const { useState } = React;

const Activity = () => {
    const [selectedId, setSelectedId] = useState(ACTIVITY_TYPE_ENUM.INVITE_FRIEND);

    const categories = [
        { id: ACTIVITY_TYPE_ENUM.INVITE_FRIEND, name: "邀请好友" },
        { id: ACTIVITY_TYPE_ENUM.FIRST_JOIN_GROUP, name: "首次入群" },
        { id: ACTIVITY_TYPE_ENUM.CASH_COUPON_SUBSIDY, name: "商家补贴" }
    ];

    return (
        <div className="activity-tab">
            <div className="category-container">
                <span className="category-label">所属类别：</span>
                {categories.map((item) => (
                    <span
                        key={item.id}
                        onClick={() => setSelectedId(item.id)}
                        className={`category-item ${selectedId === item.id ? "selected" : ""}`}
                    >
                        {item.name}
                    </span>
                ))}
            </div>
            {selectedId === ACTIVITY_TYPE_ENUM.FIRST_JOIN_GROUP && <FirstJoinPage selectedId={selectedId} />}
            {selectedId === ACTIVITY_TYPE_ENUM.INVITE_FRIEND && <InviteFriendPage selectedId={selectedId} />}
            {selectedId === ACTIVITY_TYPE_ENUM.CASH_COUPON_SUBSIDY && <CashCouponPage selectedId={selectedId} />}
        </div>
    )
}

export default Activity


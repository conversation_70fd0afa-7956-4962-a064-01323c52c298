.title-text {
    font-size: 16px;
    font-weight: 600;
}

.text-center {
    text-align: center;
}

.mr-24 {
    margin-right: 24px;
}

.category-container {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
}

.category-label {
    background: #e5e7eb;
    padding: 5px 10px;
    border-radius: 5px;
    margin-right: 10px;
}

.category-item {
    padding: 5px 10px;
    margin-right: 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s, color 0.3s;
}

.category-item:hover {
    background: #f0f0f0;
}



.operate {
    display: flex;
    color: #1890ff !important;

    >div {
        margin-right: 8px;

        &:last-child {
            margin-right: 0;
        }
    }
}

.gift-manage-tab,
.coupon-manage-tab {
    height: 100%;

    .selected {
        background: #007bff;
        color: white;
    }

    .new-search-list,
    .new-search-list .height-100-percent {
        height: calc(100% - 18px);
    }

    .ant-tabs-content-holder {
        height: 100%;
    }

    .ant-tabs-content {

        height: 100%;
    }

    .ant-tabs-tabpane {
        height: 100%;
    }

    .activity-tab {
        height: 100%;
    }

    .ant-tabs-tab {
        .ant-tabs-tab.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                color: #1677FF;
            }
        }
    }
}
import request from "../../../utils/plugins/axios/request";
import GiftRules from "./gift-rule-tab";
import Activity from "./activity-tab";
const { Tabs } = antd;
const { Spa, SpaConfigProvider } = dtComponents;
function Page() {
	const onChange = (key) => {
		console.log(key);
	};
	const items = [
		{
			key: '1',
			label: '余额获取活动',
			children: <Activity />,
		},
		{
			key: '2',
			label: '余额使用规则',
			children: <GiftRules />,
		},
	];
	return (
		<Tabs defaultActiveKey="1" items={items} onChange={onChange} className="gift-manage-tab"/>
	)
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
	_openPage: () => { },
	request: {
		request: (params) => {
			const obj = Object.assign(params, {
				type: 'POST',
				contentType: 'application/json',
			})
			request(obj)
		}
	}
})}>
	<Page />
</SpaConfigProvider>, document.getElementById("gift-manage"));
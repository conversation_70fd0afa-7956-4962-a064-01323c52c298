import request from "../../../utils/plugins/axios/request";
import AddModal from "./component/add-modal"
const { useRef, useState } = React;
const { Button } = antd;
const { SearchList, Spa, SpaConfigProvider, Confirm } = dtComponents

export const ServiceProviderStore = () => {
	const searchListRef = useRef();
	const [id, setId] = useState(null);
	const [addModalVisible, setAddModalVisible] = useState(false);
	const getConfig = async function () {
		const data = await axios.get("https://maria.yang800.com/api/data/v2/947/772")
		return data.data.data;
	};
	return (
		<SearchList
			ref={searchListRef}
			scrollMode={"tableScroll"}
			paginationConfig={{ size: "default", showPosition: 'bottom' }}
			searchConditionConfig={{
				size: "middle",
			}}
			getConfig={getConfig}
			pageLoadTarget={{
				convertPaginationParam: function (pagination) {
					return { current: pagination.currentPage, size: pagination.pageSize };
				},
				convertResponseData: function (data, prePage) {
					return {
						dataList: data.data,
						page: {
							currentPage: prePage.currentPage,
							pageSize: prePage.pageSize,
							totalCount: data.total
						}
					};
				}
			}}
			renderModal={() => {
                return <AddModal
                    id={id}
                    open={addModalVisible}
                    onClose={() => {
                        setAddModalVisible(false)
                        setId(null)
                        searchListRef.current.load();
                    }}
                />
            }}
			renderLeftOperation={() => {
				return <Button type="primary" onClick={() => setAddModalVisible(true)}>新增品牌</Button>
			}}
			tableCustomFun={{
				operateFn: (row) => {
					return (
						<div className="operate">
							<div onClick={() => {
								setId(row.id);
								setAddModalVisible(true);
							}
							}>编辑</div>

							<Confirm
								data={{
									title: '删除',
									name: '删除',
									content: '是否确认删除？',
									url: '/api/itemBrand/delete',
									params: { id: row.id },
								}}
								onReload={() => {
									searchListRef.current.load();
								}}></Confirm>
						</div>
					)
				}
			}}
		/>

	)
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
	_openPage: () => {
	}, request: {
		request: (params) => {
			const obj = Object.assign(params, {
				method: 'POST',
			})
			request(obj);
		}
	}
})}><ServiceProviderStore />
</SpaConfigProvider>, document.getElementById("brand-manage")
);

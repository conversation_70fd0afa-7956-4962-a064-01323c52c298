/**
 * @description 新增弹窗
 * <AUTHOR>
 */
import request from "../../../../utils/plugins/axios/request";

const { FormInput } = dtComponents;
const { Form, message, Modal } = antd;
const { useEffect } = React
function AddModal({ id, open, onClose }) {
    const [form] = Form.useForm();
    let isEdite = !!id

    const getDetail = () => {
        request({
            url: "/api/itemBrand/detail",
            method: "POST",
            data: { id },
            needMask: true,
            success: (data) => {
                form.setFieldsValue(data)
            }
        })
    }

    useEffect(() => {
        if (isEdite) {
            getDetail()
        }
    }, [id])

    const handleOk = () => {
        form.validateFields().then(values => {
            if (isEdite) {
                values.id = id
            }
            request({
                url: isEdite ? "/api/itemBrand/update" : "/api/itemBrand/save",
                method: "POST",
                data: {
                    ...values,
                },
                success: () => {
                    message.success("操作成功");
                    onClose();
                    form.resetFields()
                },
            });
        });
    };

    const layout = {
        labelCol: {
            style: { width: "138px" },
        },
    };
    return (
        <Modal
            title={isEdite ? "编辑品牌" : "新增品牌"}
            open={open}
            onOk={handleOk}
            onCancel={() => {
                onClose();
                form.resetFields()
            }}
            keyboard={false}
            maskClosable={false}
            destroyOnClose
            width={500}>
            <Form {...layout} form={form}>
                <FormInput
                    fProps={{
                        label: '品牌名称',
                        name: 'name',
                        rules: [{ required: true, message: '请输入品牌名称' }, { max: 10, message: '请输入最长10位字符' }],
                    }}
                    cProps={{
                        width: 260
                    }} />
            </Form>
        </Modal>
    );
}
export default AddModal

{{#component "component-category-tree"}}
  <div class="menu shop-category-menu">
    <ul id="shop-category-menu">
      <a href="/seller/item-sort?shopCategoryId=0"><li class="menu-li sup {{#equals shopCategoryId 0}}active{{/equals}}" >{{i18n "All Products" bundle="items"}}</li></a>
      <a href="/seller/item-sort?shopCategoryId=-1"><li class="menu-li sup {{#unless shopCategoryId}}active{{/unless}} {{#equals shopCategoryId "-1"}}active{{/equals}}">{{i18n "Unsorted Products" bundle="items"}}</a></li>
      <a href="javascript:;"><li class="no-action-li">{{i18n "Sorted Products" bundle="items"}}</li></a>
      {{#each _DATA_}}
        <a href="/seller/item-sort?shopCategoryId={{id}}"> <li class="category-li menu-li {{#equals @root.shopCategoryId id}}active{{/equals}}" data-name="{{name}}">
            {{#if hasChildren}}<span class="caret"></span>{{/if}}
            {{name}}
        </li> </a>
        {{#each children}}
          <a href="/seller/item-sort?shopCategoryId={{id}}"> <li class="sub category-li menu-li {{#equals @root.shopCategoryId id}}active{{/equals}}" data-id="{{id}}">
              <span class="angle">└</span>
              {{name}}
          </li> </a>
        {{/each}}
      {{/each}}
    </ul>
</div>
{{/component}}

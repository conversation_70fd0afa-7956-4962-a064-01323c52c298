@import "compass/css3/inline-block";
@import "pokeball/theme";

.item-publish {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .lh32 {
    line-height: 32px;
    &.cgray {
      color: #999999;
    }
  }

  .selectric {
    p {
      color: #999999;
    }
    .label {
      margin: 0;
      padding: 0 5px;
    }
  }

  p {
    line-height: 32px;
  }

  .form-section {
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 12px;
    margin-right: 20px;
  }

  .section-content {
    background-color: $whitesmoke;
    // padding: 0 0 20px;
    width: 830px;

    &.no-bg {
      background-color: #fff;
    }
  }

  .form-left,
  .form-right {
    width: 50%;
    float: left;
  }

  .form-inline {
    .control-group {
      float: left;
      margin-right: 20px;
    }
  }

  .control-group {
    position: relative;
    margin-top: 20px;

    &.last-line {
      margin-bottom: 20px;
    }

    .note-icon {
      padding-right: 7px;

      i {
        vertical-align: baseline;
      }
    }
  }

  label.group-title {
    position: absolute;
    left: 10px;
    top: 0;
    width: 95px;
    text-align: right;
    line-height: 32px;

    .group-line-title {
      @include inline-block(none);
      width: 100%;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      b.required {
        margin-right: 0;
      }
    }

    // for ie '...' color issue
    .group-line-title:before {
      content: '';
      color: $gray;
    }
  }

  .group-content {
    padding-left: 110px;

    .datepicker[readonly] {
      background-color: #fff;
    }
  }

  .line-title {
    line-height: 32px;
  }

  .selected-category {
    border-width: 1px 0;
    width: 100%;
    background-color: $color-background;
    text-align: center;
    padding: 10px 0;
    margin: 20px 0;
  }

  li {
    list-style: none;
  }

  .spu-title {
    margin: 30px 0 0 0;

    label {
      font-size: 14px;

      &.operate-tips {
        float: right;
        color: $color-border;
      }
    }
  }

  .js-item-image {
    background-color: transparent;
    padding: 0;
  }

  .image-area {
    margin: 0;

    .image-tip {
      line-height: 32px;
    }

    .main-image {
      @include inline-block(top);
    }


    .image-container {
      width: 100px;
      height: 100px;
      margin: 7px 18px 7px 0px;
      border: 1px solid $color-border;
      text-align: center;
      @include inline-block(top);
      position: relative;

      img {
        width: 100%;
        height: 100%;
      }

      .image-tip-add {
        display: none;
      }

      &.empty {
        border: 1px dashed $color-border;

        .image-tip-add {
          text-align: center;
          font-size: 30px;
          @include inline-block(middle);
          line-height: 100px;
          color: $color-border;
        }

        &:hover {
          border: 1px dashed $color-text;

          .image-tip-add {
            color: $color-text;
          }
        }
      }

      &.error {
        border-color: $color-danger;

        .image-tip-add {
          color: $color-danger;
        }
      }

      .image-operate {
        display: none;

        i {
          color: $color-text-white;
          line-height: 25px;
          cursor: pointer;
          margin: 0 5px;
        }
      }

      &:hover {
        .image-operate {
          @include inline-block(middle);
          position: absolute;
          width: 100%;
          height: 25px;
          bottom: 0;
          left: 0;
          background-color: rgba(0,0,0,0.4);
        }
      }
    }
  }

  .images-list {
    @include inline-block(top);
    border-left: 1px solid $color-border;
    padding-left: 20px;
    width: 565px;

    .item-image:first-child {
      i.icon-feebas-left-arrow {
        display: none;
      }
    }

    .item-image:nth-last-child(2) {
      i.icon-feebas-right-arrow {
        display: none;
      }
    }
  }

  .js-sku-attr-delete {
    i {
      cursor: pointer;
    }
  }

  .attr-unit-show,
  .sku-attr-status {
    @include inline-block(middle);

    i,
    .sku-attr-show {
      @include inline-block(middle);
      color: $color-text-assist;
    }

    .active {
      color: $color-success;
    }
  }

  label.attr-label {
    width: 105px;
    line-height: 30px;
    margin: 0 5px 0 0;
    text-align: right;
  }

  .attribute-list {
    .attr-show-li {
      width: 50%;
    }

    .select {
      width: 150px;
    }

    .control-group {
      margin: 0 0 20px 0;
      padding: 0;
    }
  }

  .label-align-btn {
    width: 84px;
    @include inline-block(middle);
    text-align: right;
  }

  .single-line {
    padding: 20px 0;
  }

  .category-attribute {
    padding: 20px 0 0;

    .attribute-value-error {
      color: $color-danger;
      padding: 5px 10px;
    }
  }

  .new-attribute-area {
    border-top: 1px solid $color-border;
    padding: 20px 0;

    &.category-has-not-attribute {
      border-top: 0;
      padding: 0 0 20px 0;
    }
  }

  .selectric-input-small {
    .selectric {
      width: 72px;
    }
  }

  .category-attr-input {
    @include inline-block(middle);
  }

  .attr-unit-input {
    vertical-align: top;
  }

  .attribute-delete-icon {
    color: $color-text;
  }

  .js-upload-attr-image {
    padding: 0;
  }

  .attribute-image {
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
    color: $color-text-assist;
    margin: 0;
    @include inline-block(middle);
    cursor: pointer;
  }

  .error-tip {
    color: $color-danger;

    i {
      font-size: 14px;
      line-height: 14px;
      vertical-align: text-bottom;
    }
  }

  .sku-info-area,
  .sku-list-area {
    .error-tip {
      display: none;
    }

    &.error {
      .error-tip {
        @include inline-block(baseline);
      }
    }
  }

  .sku-list-area {
    padding: 5px 0 10px;
    border-bottom: 1px solid $color-border;

    &.last {
      border-bottom: 0 none;
    }

    .sku-image-radio {
      vertical-align: middle;
    }

    .sku-group-content {
      @include inline-block(bottom);
      width: auto;
      padding-left: 110px;
    }

    .sku-show-image {
      @include inline-block(middle);
    }

    .attr-label {
      width: 85px;
      margin-right: 12px;
    }

    .sku-attr-key {
      margin: 0;
      padding-right: 20px;
    }

    .sku-attr-vals {
      padding: 0 0 0 110px;
      margin: 0;
      @include inline-block(middle);
    }

    .sku-value-item {
      @include inline-block(middle);
      margin: 5px 10px 5px 0;
    }

    .attribute-operation {
      margin: 0 30px;
    }

    .sku-attribute-label {
      margin-right: 5px;
      vertical-align: middle;
      color: #333;
    }
  }

  .new-sku-attribute-area {
    padding: 20px 0;
  }

  .sku-table {
    max-width: 100%;
    width: auto !important;

    .td-inner-line {
      margin: 5px 0;
      white-space: nowrap;

      label {
        @include inline-block(middle);
        text-align: right;
        white-space: nowrap;
        width: 100%;
      }

      .sku-price-input {
        width: 100px;
        padding: 3px 5px;
      }
    }

    .control-group {
      margin: 0;
    }
  }

  .item-submit-area {
    padding: 30px 75px;

    .error-tip {
      color: $color-danger;
    }
  }

  .hide {
    display: none !important;
  }

  .mt0 {
    margin-top: 0 !important;
  }

  .w100 {
    width: 100px !important;
  }

  .lh-32 {
    line-height: 32px;
  }

  .input-575 {
    width: 575px !important;
  }

  .js-show-advertise-tip {
    color: $color-text-assist;
    font-size: 20px;
    cursor: pointer;
  }

  .batch-set {
    .group-title {
      position: static;
      width: auto;
      margin-right: 0 !important;
    }
    .group-content {
      padding-left: 10px;
      display: inline-block;
    }
  }
  .logo{
    width: 80px;
    height: 80px;
    border: 1px solid #dfdfdf;
    vertical-align: bottom;
    margin-left:110px;
  }
  .disappear{
    display: none;
  }

  .miniAppImg{
    transition:all 2s;/*图片放大过程的时间*/
    position: relative;
  }
  .miniAppImg:hover{
    cursor: crosshair;
    transform: scale(4); /*以y轴为中心旋转*/
    height: 100%;
    z-index: 1000;
  }
}
.form-aligned .pull-left .control-group  .label-list{
  text-align: left;
}
.form-aligned .pull-left .control-group  .label-list input{
  margin-right: 4px;
}
.form-aligned .control-group .note-error, .form-aligned .control-group .note-error-empty {
  vertical-align: middle;
}


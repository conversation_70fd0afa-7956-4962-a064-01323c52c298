<td class="center-text">
  <div class="td-inner-line" id="marketPriceId" >
    <label class="control-group">市场价&nbsp;&nbsp;<input type="text" class="sku-price-input js-sku-origin-price js-need-validated" pattern="^\d{1,7}(\.\d{0,2})?$" value="{{formatPrice sku.extraPrice.originPrice}}" placeholder="请输入数字">
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </label>
  </div>
  <div class="td-inner-line">
    <label class="control-group"><small id="integralExchangeId">销售价&nbsp;</small>&nbsp;<input type="text" class="sku-price-input js-sku-price js-need-validated" pattern="^\d{1,7}(\.\d{0,2})?$" value="{{#if sku.price}}{{formatPrice sku.price}}{{/if}}" placeholder="请输入数字">
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </label>
  </div>
  <div class="td-inner-line">
    <label class="control-group"><small id="crossedPrice">划线价&nbsp;</small>&nbsp;<input type="text" class="sku-price-input js-sku-original-price js-need-validated" pattern="^\d{1,7}(\.\d{0,2})?$" value="{{#if sku.extraPrice.crossedPrice}}{{formatPrice sku.extraPrice.crossedPrice}}{{/if}}" placeholder="请输入数字">
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </label>
  </div>
  <!--<div class="td-inner-line" id="integralExchangeId" style="{{#if sku.extraPrice.integralPrice}}{{else}}display:none;{{/if}}">
    <label class="control-group">兑换积分&nbsp;&nbsp;<input type="text" class="sku-price-input js-sku-integral-price" pattern="^\d{1,7}$" value="{{sku.extraPrice.integralPrice}}" placeholder="请输入数字">
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </label>
  </div>-->
</td>
<td>
  <div class="td-inner-line control-group">
    <input type="text" class="input-small js-sku-quantity js-need-validated" pattern="^\d+$" value="{{sku.stockQuantity}}" placeholder="输入数字">
   {{#if sku.extra.thirdPartyStockQuantity}} <span>仓库配额：{{sku.extra.thirdPartyStockQuantity}}</span>{{/if}}
    <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
      <i class="icon-pokeball icon-pokeball-error "></i>
    </span>
  </div>
</td>
<td>
  <div class="td-inner-line control-group">
    <input type="text" class="input-small js-unit-quantity js-need-validated js-sku-required" name="unitQuantity" data-key="unitQuantity" pattern="^\d{1,4}(\.\d{1})?$" value="{{#if sku.extra.unitQuantity}}{{sku.extra.unitQuantity}}{{else}}1{{/if}}" placeholder="计量单位数量" required />
    <span class="note-error note-icon hint--top" aria-label="请输入计量单位数量，不超过10000">
      <i class="icon-pokeball icon-pokeball-error "></i>
    </span>
  </div>
</td>
<td class="center-text">
  <input type="text" class="input-small js-sku-out-id w100" value="{{sku.outerSkuId}}" placeholder="sku外部编码">
  <input type="hidden" class="input-small js-sku-pushSystem w100" value="{{sku.tags.pushSystem}}" placeholder="哪个第三方平台">
</td>
<td class="center-text">
  <input type="text" class="input-small js-sku-order-split-line w100" name="skuOrderSplitLine" value="{{sku.extra.skuOrderSplitLine}}" placeholder="拆单数量">
</td>
<td class="center-text">
  <input type="text" class="input-small js-order-quantity-limit w100" name="orderQuantityLimit" value="{{sku.extra.orderQuantityLimit}}" placeholder="限购数量">
</td>
{{!--<td class="center-text">
 <select id="skipAoXinPush" name="skipAoXinPush"
  value="{{sku.extra.skipAoXinPush}}">
  <option value="false" {{#equals sku.extra.skipAoXinPush 'false'}} selected{{/equals}}>否</option>
  <option value="true" {{#equals sku.extra.skipAoXinPush 'true'}} selected{{/equals}}>是</option>
</select>
</td>--}}

<div class="batch-set">
  <div class="control-group mt0">
    <label class="group-title line-title pull-left">批量设置：</label>
    <div class="group-content pull-left">
      <input type="text" class="input-text input-small js-batch-validator" name="js-sku-origin-price" data-type="PRICE" placeholder="市场价" value="" />
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </div>
    <div class="group-content pull-left">
      <input type="text" class="input-text input-small js-batch-validator" name="js-sku-price" data-type="PRICE" placeholder="{{#equals type '3'}}兑换积分{{else}}{{#equals item.type 1}}销售价{{else}}{{#if item.type}}兑换积分{{else}}销售价{{/if}}{{/equals}}{{/equals}}" value="" />
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </div>
    <div class="group-content pull-left">
      <input type="text" class="input-text input-small js-batch-validator" name="js-sku-quantity" data-type="QUANTITY" placeholder="库存数量" value="" />
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </div>
    <div class="group-content lh-32">
      <a class="js-batch-set" href="javascript:;">确定</a>
    </div>
  </div>
</div>
<table class="table table-grid sku-table">
  <thead>
  <tr>
    {{#each data.attrs}}
      <th width="70" class="center-text">
        {{key}}
      </th>
    {{/each}}
    <th width="100" class="center-text">
      价格
    </th>
    <th width="70" class="center-text">
      库存数量
    </th>
    <th width="100" class="center-text">
      <b class="required">*</b>计量单位数量
    </th>
    <th width="120" class="center-text">外部SKU编码</th>
    <th width="80" class="center-text">拆单数量</th>
    <th width="80" class="center-text">限购数量</th>
  </tr>
  </thead>
  <tbody>
  {{#if data}}
    {{#each data.values}}
      <tr class="js-sku-tr" data-attr="{{json list}}"
          data-key="{{#each list}}{{attr}}:{{value}}{{#unless @last}};{{/unless}}{{/each}}"
          {{#if id}}data-id="{{id}}"{{/if}}>
        {{#each list}}
          {{#if rowspan}}
            <td rowspan="{{rowspan}}" class="center-text">
              {{value}}
            </td>
          {{/if}}
        {{/each}}
        {{> items/seller/item_publish/common/all_templates/_sku_info}}
      </tr>
    {{/each}}
  {{else}}
    {{#if skuWithCustoms}}
    {{!--{{#unless skuWithCustoms.[0].sku.attrs}}
        {{#each skuWithCustoms}}
          <tr class="js-sku-tr" {{#if sku.id}}data-id="{{sku.id}}"{{/if}}>
            {{> component:items/seller/item_publish/common/all_templates/_sku_info}}
          </tr>
        {{/each}}
      {{else}}
        <tr class="js-sku-tr">
          {{> component:items/seller/item_publish/common/all_templates/_sku_info}}
        </tr>
      {{/unless}}--}}
      {{#each skuWithCustoms}}
        <tr class="js-sku-tr" {{#if sku.id}}data-id="{{sku.id}}"{{/if}}>
          {{!--> component:items/seller/item_publish/common/all_templates/_sku_info--}}
          <td class="center-text">
            <div class="td-inner-line" id="marketPriceId" >
              <label class="control-group">市场价&nbsp;&nbsp;<input type="text" class="sku-price-input js-sku-origin-price js-need-validated" pattern="^\d{1,7}(\.\d{0,2})?$" value="{{formatPrice sku.extraPrice.originPrice 1}}" placeholder="请输入数字">
                <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
              </label>
            </div>
            <div class="td-inner-line">
              <label class="control-group"><i id="integralExchangeId" style="font-style: normal;">{{#equals ../type '3'}} 兑换积分 {{else}}  {{#equals ../item.type 1}}销售价{{else}}  {{#if ../item.type}} 兑换积分 {{else}}销售价{{/if}} {{/equals}} {{/equals}}</i>&nbsp;
                <input type="text" class="sku-price-input js-sku-price js-need-validated" pattern="^\d{1,7}(\.\d{0,2})?$" value="{{#if sku.price}}{{#equals type '3'}}{{formatPrice sku.price 1}}{{else}}{{#equals ../item.type 1}}{{formatPrice sku.price}}{{else}}{{#if ../item.type}}{{formatPrice sku.price 1}}{{else}}{{formatPrice sku.price}}{{/if}}{{/equals}}{{/equals}}{{/if}}" placeholder="请输入数字">
                <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
              </label>
            </div>
            <div class="td-inner-line" id="marketPriceId" >
              <label class="control-group">划线价&nbsp;&nbsp;<input type="text" class="sku-price-input js-sku-original-price js-need-validated" pattern="^\d{1,7}(\.\d{0,2})?$" value="{{formatPrice sku.extraPrice.crossedPrice 1}}" placeholder="请输入数字">
                <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
              </label>
            </div>
            <!--<div class="td-inner-line" id="integralExchangeId" style="{{#if sku.extraPrice.integralPrice}}{{else}}display:none;{{/if}}">
    <label class="control-group">兑换积分&nbsp;&nbsp;<input type="text" class="sku-price-input js-sku-integral-price" pattern="^\d{1,7}$" value="{{sku.extraPrice.integralPrice}}" placeholder="请输入数字">
      <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </label>
  </div>-->
          </td>
          <td>
            <div class="td-inner-line control-group">
              <input type="text" class="input-small js-sku-quantity js-need-validated" pattern="^\d+$" value="{{sku.stockQuantity}}" placeholder="输入数字">
              {{#if sku.extra.thirdPartyStockQuantity}} <span>仓库配额：{{sku.extra.thirdPartyStockQuantity}}</span>{{/if}}
              <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字">
      <i class="icon-pokeball icon-pokeball-error "></i>
    </span>
            </div>
          </td>
          <td>
            <div class="td-inner-line control-group">
              <input type="text" class="input-small js-unit-quantity js-need-validated js-sku-required" name="unitQuantity" data-key="unitQuantity" pattern="^\d{1,4}(\.\d{1})?$" value="{{#if sku.extra.unitQuantity}}{{sku.extra.unitQuantity}}{{else}}1{{/if}}" placeholder="计量单位数量" required />
              <span class="note-error note-icon hint--top" aria-label="请输入计量单位数量，不超过10000">
      <i class="icon-pokeball icon-pokeball-error "></i>
    </span>
            </div>
          </td>
          <td class="center-text">
            <input type="text" class="input-small js-sku-out-id w100" value="{{sku.outerSkuId}}" placeholder="sku外部编码">
            <input type="hidden" class="input-small js-sku-pushSystem w100" value="{{sku.tags.pushSystem}}" placeholder="哪个第三方平台">
          </td>
          <td class="center-text">
            <input type="text" class="input-small js-sku-order-split-line w100" name="skuOrderSplitLine" value="{{sku.extra.skuOrderSplitLine}}" placeholder="拆单数量">
          </td>
          <td class="center-text">
            <input type="text" class="input-small js-order-quantity-limit w100" name="orderQuantityLimit" value="{{sku.extra.orderQuantityLimit}}" placeholder="限购数量">
          </td>



        </tr>
      {{/each}}
    {{else}}
      <tr class="js-sku-tr">
        {{> component:items/seller/item_publish/common/all_templates/_sku_info}}
      </tr>
    {{/if}}
  {{/if}}
  </tbody>
</table>

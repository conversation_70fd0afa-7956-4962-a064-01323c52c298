const Modal = require("pokeball").Modal
const attributeTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/attribute_template"],
    skuAttibuteTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/sku_attribute"],
    skuTableTemplate = Handlebars.partials["items/seller/item_publish/common/all_templates/_sku_table"],
    itemImageTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/image"],
    skuValueTemplate = Handlebars.partials["items/seller/item_publish/common/frontend_templates/_sku_value"],
    attributeImageTemplate = Handlebars.partials["items/seller/item_publish/common/all_templates/_attribute_image"],
    skuInfoTemplate = Handlebars.partials["items/seller/item_publish/common/all_templates/_sku_info"],
    labelCheckboxTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/label-checkbox"],
    attributeDropDownTemplate = Handlebars.partials["items/seller/item_publish/common/all_templates/_attribte_dropdown"],
    attributeInputTemplate = Handlebars.partials["items/seller/item_publish/common/all_templates/_attribute_input"],
    attrErrorTipTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/attribute_error_tip"],
    feeOptionTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/fee_option_template"],
    comfirmModalTemplate = Handlebars.templates["items/seller/item_publish/common/frontend_templates/confirm-modal"]
const richEditorTemplate = Handlebars.templates["items/seller/shelf_item/frontend_templates/rich_editor"]

const Properties = require("items/resources/properties")
const { skuMostDimension } = Properties.resource.item
const optionsTemplate = Handlebars.templates["common/address_select/frontend_templates/options"]

class ItemPublish {
    constructor($) {
        this.$jsExtendAttribute = $("#js-extend-attribute")
        this.$jsExtendSkuAttribute = $("#js-extend-sku-attribute")
        this.$jsCategoryAttibuteList = $("#js-category-attribute-list")
        this.$jsSkuAttributeArea = $("#js-sku-attribute-area")
        this.$jsExtendSkuAttributeArea = $("#js-extend-sku-attribute-area")
        this.$jsItemMainImage = $("#js-item-main-image")
        this.$jsSkuInfoArea = $("#js-sku-info-area")
        this.jsitemSetLabel = $(".js-item-set-label")
        this.$itemForm = $("#js-item-form")
        this.$jsSkuTableArea = $("#js-sku-table-area")
        this.$jsNewAttributeList = $("#js-new-attribute-list")
        this.$brandSearch = $("#js-item-brand")
        this.$deleteAttr = $(".js-sku-attr-delete")
        this.canSkuAttrs = this.getOriginCanSkuKey(this.$jsCategoryAttibuteList.data("attr"))
        this.top = this.$el.offset().top
        this.bindEvent()
        this.initAddress()
        this.initCustomOrigin()// 原产地下拉框数据绑定
        this.getModalduleData()
    }

    // 事件绑定和委托
    bindEvent() {
        this.$el.on("click", ".js-select-sku-attr", evt => this.selectSkuAttribute(evt))
        $(".datepicker").datepicker()
        // $(".activityDatepicker").datepicker({minDate: new Date(),yearRange: [new Date(),2100 ]})
        $(".js-input-datepicker").datepicker()
        $(() => $(".dropdown").dropdown({
            onInitialize: ($el) => {
                this.initCategoryAttrValueType($el)
                return true
            },
            onAppend: (text, $el) => this.validateAttributeValue($.trim(text), $el),
            onRemove: (text, $el) => {
                $el.find(".js-group-item").val("").focus()
                return true
            }
        }))

        if (this.$itemForm.data("id")) {
            this.registerSkuInfo()
            this.renderSkuSelectedAttribute()
        }
        if ($("#item-type-select").val() == "3" || $("#item-type-select").val() == "4" || $("#item-type-select").val() == "5") {
            //$(".js-sku-integral-price").addClass("js-need-validated")
            $("#integralExchangeId").text("兑换积分")
            //$("#marketPriceId").hide()
        } else {
            $("#integralExchangeId").text("销售价")
            $("#marketPriceId").show()

            // 去掉必填项
            // $(".js-sku-integral-price").removeClass("js-need-validated")
        }

        this.$jsExtendAttribute.on("click", evt => this.extendNewAttributes(evt))
        this.$jsExtendSkuAttribute.on("click", evt => this.extendSkuAttributes(evt))
        this.fileUpload()
        this.bindFormEvent()
        this.$el.on("click", ".js-edit-sku-value", evt => this.editSkuAttributeValue(evt))
        this.$el.on("focus", ".js-sku-price", evt => this.removeSkuInfoError(evt))
        this.$el.on("click", ".js-delete-sku", evt => this.deleteSku(evt))
        this.$el.on("click", ".js-delete-user-attr", evt => this.deleteAttibuteItem(evt))
        this.$el.on("click", ".js-delete-image", evt => this.deleteItemImage(evt))
        this.$el.on("click", ".js-move-image", evt => this.moveImagePosition(evt))
        this.$el.on("click", ".refreshFeeTemplate", evt => this.getFeeTemplate(evt))
        this.$el.on("click", ".js-sku-attr-delete", evt => this.deleteAttr(evt))
        this.jsitemSetLabel.on("click", evt => this.itemSetLabel(evt))
        $(window).on("beforeunload", evt => this.windowBeforeLoad(evt))
        this.$el.on("confirm:leaveWindow", evt => this.confirmLeave(evt))
        this.$el.on("click", ".js-batch-set", evt => this.batchSetPeiceAndStock(evt))
        this.$el.on("keyup", "[name=js-sku-origin-price]", evt => this.checkBatchBtn(evt))
        this.$el.on("keyup", "[name=js-sku-price]", evt => this.checkBatchBtn(evt))
        this.$el.on("keyup", "[name=js-sku-quantity]", evt => this.checkBatchBtn(evt))
        this.$el.on("change", "#id-item-type", evt => this.itemTypeChange(evt))
        this.$el.on("change", ".activityDatepicker", evt => this.timeChange(evt))
        this.$el.on("change", "#item-type-select", evt => this.itemTypeSelectChange(evt))
        this.$el.on("change", "input[type=radio][name=isCommission]", evt => this.isCommissionChange(evt))
        this.$el.find(".control-price").on("blur", evt => this.controlPriceValidate(evt))
        $(document).on("click", ".js-submit-item-detail", (evt) => this.submitItemDetail(evt))
        this.$el.on("change", "input[type=radio][name=setStoreProfitWay]", evt => this.setStoreProfitWayFn(evt))
        const shopInfo = JSON.parse(sessionStorage.shopInfo)
        // 只是新增且是第三方平台商品时提取，修改不用
        if (!this.$itemForm.data("id")) {
            // 非第三方商品时，暂时去掉保税 开启自建保税商品时，可以自建跨境保税商品
            if (sessionStorage.isThirdPartyItem != "1") {
                if (shopInfo.extra && (!shopInfo.extra.manualBonded || shopInfo.extra.manualBonded == '0')) {
                    $("#id-item-type option[value='1']").remove()
                } else {
                    $("#id-item-type").append("<option value='1'>跨境保税</option>")
                    $("#id-item-type").val("1").selectric("refresh") // 商品类型1
                    $(".customs-declaration-info").css("display", "block")
                    $("#id-item-type").data("value", "1")
                }
            } else {
                this.bindItemInfoReady()
                // 先清空下拉框
                $("#id-item-type").empty()
                if (sessionStorage.pushSystem == '1') {
                    $("#id-item-type").append("<option value='1'>跨境保税</option>")
                    $("#id-item-type").val("1").selectric("refresh") // 商品类型1
                    $("#id-item-type").data("value", "1")
                } else if (sessionStorage.pushSystem == '3') {
                    if (sessionStorage.goodsCreateType == '1') {
                        $("#id-item-type").append("<option value='1'>跨境保税</option>")
                        $("#id-item-type").val("1").selectric("refresh") // 商品类型1
                        $("#id-item-type").data("value", "1")
                        $(".item-publish-with-category #input-hsCode").attr("required", "required")
                        $(".customs-declaration-info").css("display", "block")
                    } else {
                        // pushSystem==3表示大贸，大贸 thirdPartyId=3
                        $("#id-item-type").append("<option value='0'>一般贸易</option>")
                        $("#id-item-type").val("0").selectric("refresh") // 商品类型1
                        $("#id-item-type").data("value", "0")
                        $(".customs-declaration-info").css("display", "none")
                        $(".item-publish-with-category #input-hsCode").removeAttr("required")
                    }
                }
            }
        }


        this.searchBrand()
        this.getFeeTemplate()
        // 若小二端商品中心开启仅修改商品价格且是编辑时，则商家端编辑商品只能修改价格和库存
        if (sessionStorage.OnlyModifyPrice == 1 && this.$itemForm.data("id")) {
            $("input,select,textarea", $("#js-item-form")).attr("readonly", true)
            // $('select').selectric('destroy'); // 摧毁，不可用
            $(".selectric-items", $("#js-item-form")).hide()
            $(".dropdown-menu", $("#js-item-form")).hide()
            $(".js-images-list").hide()
            $(".js-sku-quantity").attr("readonly", false)
            $(".js-sku-origin-price").attr("readonly", false)
            $(".js-sku-price").attr("readonly", false)
            // $('select').attr("disabled",true)
        }
        // 小二端销售模式配为普通商城、微分销时不显示服务费设置
        if (sessionStorage.salesPattern == "commonShop" || sessionStorage.salesPattern == "we-distribution") {
            $(".commission-info").hide()
        }
        // 只微分销模式才显示控价设置
        if (sessionStorage.salesPattern == "we-distribution") {
            $(".control-price-set").show()
        }
        // 是门店分销时显示门店服务费导购服务费
        if (sessionStorage.salesPattern == "subStore") {
            $(".subStoreMode").show()
            $("#firstLevelName").text("门店服务费：");
            $("#secondLevelName").text("导购服务费：");
            $("#activityFirstRate").text("门店服务费：");
            $("#activitySecondRate").text("导购服务费：");
        }
        // 是阶梯分销时才显示生产日期her

        if (sessionStorage.salesPattern == "ladderDistribution") {
            $(".ladderDistributionMode").show()

        }
        // 开启粉丝才显示拉新涌进

        if (shopInfo.extra && shopInfo.extra.openFans == '1') {
            // 显示拉新服务费
            $(".showPullNewCommission").show()
        }
    }

    // 获取模版列表数据
    getModalduleData() {
        $.ajax({
            url: "/api/restrictedSalesAreaTemplate/find",
            data: JSON.stringify({
                "pageSize": 1000,
                "pageNo": 1,
                "status": 1
            }),
            contentType: "application/json",
            type: 'POST',
            success: (data) => {
                // window.location.reload();
                console.log(data);
                // let param = this.getSearchParam(location.href)
                const val = $("#item-module-select").data("value");
                const arr = data.data.data || [];
                let html = '';
                arr.map((item) => {
                    html += `<option value=${item.id} ${item.id === val ? 'selected' : ''}>${item.name}</option>`
                })
                $("#item-module-select").append(html)
                $("#item-module-select").selectric("refresh")
            }
        })
    }
    getSearchParam(url) {
        if (url.indexOf("?") == -1) {
            return null;
        }
        let search = url.split("?")[1];
        let result = {},
            ps = search.split("&");
        ps.map(item => {
            let [key, value] = item.split("=");
            result[key] = decodeURIComponent(value);
        });
        return result;
    }

    bindFormEvent() {
        this.$itemForm.off()
        this.$itemForm.validator({
            identifier: "input.js-need-validated,[required]:not(.js-attr-sku-val)",
            isErrorOnParent: true,
            errorCallback: (errorFields) => {
                this.elementScroll(errorFields[0].$el)
            }
        })
        this.$itemForm.on("submit", evt => this.submitItem(evt))
        FormValidator.registerPattern("mainImage", (val, $el) => !!val)
        FormValidator.registerPattern("deliverTemplate", (feeTemplateId, $el) => {
            if (feeTemplateId > -1) {
                $(".selectric-js-fee-template .selectric").css("border-color", "#2dc12d")
                return true
            }
            $(".selectric-js-fee-template .selectric").css("border-color", "#ff2200")
            return false
        })

    }

    setStoreProfitWayFn(evt) {
        if ($("input[type=radio][name=setStoreProfitWay]").prop("checked")) {
            $(".profitRate").text("%")
        } else {
            $(".profitRate").text("元")
        }
    }

    elementScroll(el) {
        const jumpTop = $(el).offset().top - this.top - 20
        $("html, body").animate({ scrollTop: jumpTop })
    }

    validateDispath($form, data) {
        return this.validateSkuAttr($form) && this.validateSkuInfo(data.skus) && this.validateSkuUnitQuantity(data.skus)
    }

    validateSkuAttr($form) {
        const $skuAttr = $(".js-sku-area", $form)
        return _.every($skuAttr, (i) => {
            const $checkedAttr = $(".js-select-sku-attr:checked", i)

            if ($checkedAttr.length > 0) {
                return true
            }
            $(i).addClass("error")
            this.elementScroll(i)
            return false
        })
    }

    validateSkuInfo(skus) {
        if (skus.length) {
            return true
        }
        this.$jsSkuInfoArea.addClass("error")
        this.elementScroll(this.$jsSkuInfoArea)
        return false
    }

    validateAttributeValue(text, $el) {
        this.tipValueErrorRemove($el)
        const type = $el.data("type")
        switch (type) {
            case "STRING":
                return /^((?![:_]).){1,15}$/.test(text) ? true : this.tipValueError("不包含:和_的1-15个字符", $el)
            case "DATE":
                return /^\d{4}-\d{2}-\d{2}$/.test(text) ? true : this.tipValueError("请输入日期", $el)
            default:
                return /^[+-]?\d{1,8}(\.\d{1,2})?$/.test(text) ? true : this.tipValueError("请输入最大8位数字", $el)
        }
    }

    validateSkuUnitQuantity(skus) {
        let result = true
        $.each(skus, (key, val) => {
            if (!val.extra.unitQuantity) {
                result = false
            }

        })
        return result
    }

    tipValueErrorRemove($el) {
        $(".js-attribute-error", $el).remove()
    }

    tipValueError(message, $el) {
        $(".dropdown-menu", $el).append(attrErrorTipTemplate({ message }))
    }

    removeSkuInfoError(evt) {
        this.$jsSkuInfoArea.removeClass("error")
    }

    // 未保存离开提示
    windowBeforeLoad(evt) {
        return `${i18n.ct("leavePage", "items")}`
    }

    confirmLeave() {
        $(window).off("beforeunload")
    }

    initCategoryAttrValueType($target) {
        const valueType = $target.data("type")
        if (valueType === "DATE") {
            $(".js-attr-val-input", $target).attr("readonly", true).datepicker()
        } else if (/_/.test(valueType)) {
            $(".js-attr-val-input", $target).data("type", "NUMBER")
        }
    }

    // 获取一开始可作为销售属性的key
    getOriginCanSkuKey(attrs) {
        return _.without(_.flatten(_.map(attrs, (i) => {
            if (i.attributeRule && i.attributeRule.attrMetasForK) {
                return i.attributeRule.attrMetasForK.SKU_CANDIDATE === "true" ? i.attrKey : 0
            } else if (i.attrMetasForK) {
                return i.attrMetasForK.SKU_CANDIDATE === "true" ? i.attrKey : 0
            }
            return 0
        })), 0)
    }

    // 获取当前可以作为销售属性的属性值
    getRealCanSkuKey() {
        let $skuArea = $(".js-sku-area", this.$target),
            skuKeys = _.map($skuArea, (i) => $(i).data("key").toString())

        return _.difference(this.canSkuAttrs, skuKeys)
    }

    // 品牌筛选
    searchBrand() {
        this.$brandSearch.suggest({
            url: "/api/brands?name=",
            dataFormat: (data) => _.map(data, (i) => i.name),
            callback: (text) => {
                $.each(this.$brandSearch.data("source"), (i, d) => {
                    if (d.name === text) this.$brandSearch.data("id", d.id).data("name", d.name)
                })
            }
        })
    }

    // 获取运费模板
    getFeeTemplate(evt) {
        evt && evt.preventDefault()
        $.ajax({
            url: "/api/seller/paging-delivery-fee-template?pageSize=200",
            type: "GET",
            contentType: "application/json",
            data: {},
            success: (data) => {
                this.renderFeeTemplate(data)
            }
        })
    }

    // 渲染运费模板
    renderFeeTemplate(data) {
        $(".freight-box").html(feeOptionTemplate({
            data: data.data,
            originFeeTemplateId: $(".freight-box").data("id")
        })).find("select").selectric("refresh")
        this.bindFormEvent()
    }

    // 只新增且是第三方平台商品时赋值，修改时不用，根据搜索的skuCode获取商品信息并绑定商品信息
    bindItemInfoReady() {
        const skuJson = {}
        // 调用获取第三方SKU接口取SKU商品数据
        $.ajax({
            url: `/api/item/thirdParty/sku?thirdPartyId=${sessionStorage.pushSystem}&skuCode=${sessionStorage.skuCode}`,
            type: "get",
            success: (data) => {
                if (data) {
                    // 赋值商品信息
                    $("#input-name").val(data.name)
                    $("input[name='brand']").val(data.brand)
                    $("input[name='mainImage']").val(data.mainImage)
                    $("input[name='mainImage']").prev()[0].outerHTML = `<img src="${data.mainImage}"/>`
                    $("input[name='specification']").val(data.specification)
                    $("input[name='unit']").val(data.unit)
                    $("input[name='origin']").val(data.origin)
                    $("input[name='weight']").val(data.weight)
                    $("input[name='js-sku-origin-price']").val(data.originPrice)
                    $("input[name='js-sku-price']").val(data.price)
                    $(".form-section .js-sku-out-id").val(data.outerSkuId || sessionStorage.skuCode)
                    $("input[name='unitQuantity']").val(data.unitQuantity)
                    $("#id-item-type").val((data.type == "1" ? "1" : "0")).selectric("refresh") // 商品类型
                    if (data.type == "1" || sessionStorage.pushSystem == '1') {
                        $("#id-item-type").val(data.type == "1" ? "1" : "0").selectric("close")
                        $(".customs-declaration-info").css("display", "block")
                    }
                    // 赋值海关信息
                    if (data.skuCustom) {
                        $("#input-hsCode").val(data.skuCustom.hsCode)
                        $("#customOriginId").val(data.skuCustom.customOriginId)
                        $("#customTaxHolder").val(data.skuCustom.customTaxHolder)
                        $("#provinceId").val(data.skuCustom.provinceId)
                        $("#cityId").val(data.skuCustom.cityId)
                        sessionStorage.customOriginId = data.skuCustom.customOriginId
                        sessionStorage.provinceId = data.skuCustom.provinceId
                        sessionStorage.cityId = data.skuCustom.cityId
                    }
                }
            },
            error: (data) => {
                $(".form-section .js-sku-out-id").val(sessionStorage.skuCode)
                new Modal({ icon: "error", content: "提示信息," + data.responseJSON.message, title: "获取第三方平台商品信息失败" }).show()
            }


        })
    }
    // 海淘类型切换时，若是保税的则显示海关报关信息录入项
    itemTypeChange(evt) {
        if ($("#id-item-type").val() == "1" || $("#id-item-type").val() == "2") {
            $(".item-publish-with-category #input-hsCode").attr("required", "required")
            $(".customs-declaration-info").css("display", "block")
        } else {
            $(".customs-declaration-info").css("display", "none")
            // 清空录入的海关数据
            $("#input-hsCode").val("")
            $("#customOriginId").val("")
            $("#customTaxHolder").val("")
            $("#provinceId").val("")
            $("#cityId").val("")
            // 去掉必填项
            $("#input-hsCode").removeAttr("required")
        }
    }
    timeChange(evt) {
        if ($("input[name='matchingStartTimeString']").val() || $("input[name='matchingEndTimeString']").val()) {
            $(".item-commission-rate").attr("required", "required")
            this.$itemForm.validator({
                isErrorOnParent: true,
            })
        } else {
            $(".item-commission-rate").removeAttr("required")
        }
    }

    // 商品类型切换时，若是积分商品则价格改为积分
    itemTypeSelectChange(evt) {
        if ($("#item-type-select").val() == "3" || $("#item-type-select").val() == "4" || $("#item-type-select").val() == "5") {
            //$(".js-sku-integral-price").addClass("js-need-validated")
            $("#integralExchangeId").text("兑换积分")
            $(".js-sku-price").val("")
            $(".js-sku-origin-price").val("")
            //$("#marketPriceId").hide()
        } else {
            $("#integralExchangeId").text("销售价")
            $("#marketPriceId").show()
            // 清空录入的积分兑换
            $(".js-sku-price").val("")
            $(".js-sku-origin-price").val("")
            // 去掉必填项
            // $(".js-sku-integral-price").removeClass("js-need-validated")
        }
    }
    // 开启单品服务费改变
    isCommissionChange(evt) {
        if (evt.currentTarget.value == '0') {
            // 隐藏服务费设置
            $(".comissionSetId").hide()
            $(".item-commission-single").removeAttr("required")
        } else {
            $(".item-commission-single").attr("required", "required")
            this.$itemForm.validator({
                isErrorOnParent: true,
            })
            $(".comissionSetId").show()
            $("input[name='setStoreProfitWay'][value='true']").prop("checked", true)
            if ($("input[type=radio][name=setStoreProfitWay]").prop("checked")) {
                $(".profitRate").text("%")
            } else {
                $(".profitRate").text("元")
            }
        }
    }


    /**
     * 初始化地址
     * @type {Function}
     */
    initAddress(container, callback) {
        this.addressSelect = ".address-select"
        container = container || $(document)
        $(container).on("change", this.addressSelect, event => this.selectAddress(event))
        let provinceId = parseInt($(`${this.addressSelect}[data-level=1]`, container).val()),
            cityId = parseInt($(`${this.addressSelect}[data-level=2]`, container).val())

        this.selectAddress({}, { ids: [provinceId, cityId], level: 1 }, container, callback)
    }

    /**
     * 获取市列表
     * @param  {[type]} event  [description]
     * @param  {[type]} option [description]
     * @param  {function} callback Callback function to execute after address init done
     * @return {[type]}        [description]
     */
    selectAddress(event, option, container, callback) {
        const levels = 2// 表示2级联动，3级写3
        let level = option ? option.level : +$(event.currentTarget).data("level") + 1,
            thisContainer = container || $(event.currentTarget).closest(".control-group"),
            parent = $(`${this.addressSelect}[data-level=${level - 1}]`, thisContainer),
            parentId = parent.length ? parent.val() : 0
        option = option || { level }
        const $currentLevel = $(`${this.addressSelect}[data-level=${level}]`, thisContainer)
        $currentLevel.is(":visible") && $currentLevel.spin("small")
        $.get(`/api/address/${parentId}/children`, (data) => {
            $currentLevel.html(optionsTemplate({ data }))
            if (option.ids && _.isNaN(option.ids[level - 1])) {
                option.ids[0] = sessionStorage.provinceId
                option.ids[1] = sessionStorage.cityId
            }
            option.ids && $currentLevel.find(`option[value=${option.ids[level - 1]}]`).prop("selected", true)
            if (option.ids && !option.ids[0]) {
                // 赋海关所在地默认值安徽
                $("#provinceId").val('340000')
                $("#cityId").val('340800')
            }
            $currentLevel.selectric("refresh")
            $currentLevel.spin(false);
            (++option.level <= levels) && this.selectAddress({}, option, thisContainer, callback)
            option.level > this.levels && _.isFunction(callback) && callback()
        })
    }

    // 原产地下拉框数据绑定
    initCustomOrigin(container, callback) {
        this.CustomOriginSelect = "#customOriginId"
        container = container || $(document)
        //$(container).on("change", this.CustomOriginSelect, event => this.selectCustomOrigin(event))
        const provinceId = parseInt($(`${this.CustomOriginSelect}`, container).val())

        this.CustomOriginAddress({}, { ids: [provinceId], level: 1 }, container, callback)
        if ($($(".item-publish span")[0]).css("color") == "rgba(0, 0, 0, 0)") {
            $($(".item-publish span")[0]).css("color", "red")
        }
        // 若小二端商品中心开启仅修改商品价格，则商家端编辑商品只能修改价格和库存
        if (sessionStorage.OnlyModifyPrice == 1 && this.$itemForm.data("id")) {
            $("select", $("#js-item-form")).attr("readonly", true);
            $('select').selectric('destroy'); // 摧毁，不可用
            $(".selectric-items", $("#js-item-form")).hide();
        }
    }

    /**
     * 获取绑定原产地下拉列表
     * @param  {[type]} event  [description]
     * @param  {[type]} option [description]
     * @param  {function} callback Callback function to execute after address init done
     * @return {[type]}        [description]
     */
    CustomOriginAddress(event, option, container, callback) {
        const thisContainer = container || $(event.currentTarget).closest(".control-group")
        $.get(`/api/address/listCountry`, (data) => {
            $("#customOriginId").html(optionsTemplate({ data }))
            $("#customOriginId").find(`option[value=${option.ids[0]}]`).prop("selected", true)
            if (_.isNaN(option.ids[0])) {
                $("#customOriginId").find(`option[value=${sessionStorage.customOriginId}]`).prop("selected", true)
            }
            $("#customOriginId").selectric("refresh")
            $("#customOriginId").spin(false)
        })
    }


    renderSkuSelectedAttribute() {
        let skus = this.$jsSkuTableArea.data("sku"),
            hasSpu = this.$jsSkuAttributeArea.data("spu")

        _.each(skus, i => {
            _.each(i.sku.attrs, (v) => {
                const input = $(`.js-select-sku-attr[name='${v.attrKey}'][value='${v.attrVal}']:not(:checked)`, this.$itemForm)
                // input.trigger("click")
                if (input) {
                    input.attr("checked", true)
                }
            })
        })

        if (hasSpu) {
            $(".js-select-sku-attr", this.$itemForm).attr("disabled", "disabled")
        }
    }

    registerSkuInfo() {
        let skus = this.$jsSkuTableArea.data("sku"),
            skuObject = {}
        _.each(skus, i => {
            i.skuAttributeKeyAndValue = _.map(i.attrs, (v) => v.attrKey && v.attrVal ? `${v.attrKey}:${v.attrVal}` : 0).join(";")
            skuObject[`${i.skuAttributeKeyAndValue}`] = i
        })

        window.skuObject = skuObject
    }

    // 上传
    fileUpload() {
        const _this = this
        $("input[type=file]").fileupload({
            url: "/api/user/files/upload?folderId=0",
            dataType: "html",
            done: (evt, data) => {
                const type = $(evt.target).closest(".btn").data("type")
                const imageUrl = _.values(JSON.parse(data.result))[0]
                if (type == 1) {
                    _this.itemImagesUpload(evt, imageUrl)
                } else if (type == 2) {
                    _this.attributeImagesUpload(evt, imageUrl)
                } else if (type == 3) {
                    let thisButton = $(evt.target).closest(".btn")
                    thisButton.siblings("img").attr("src", imageUrl).show()
                    thisButton.siblings("input").val(imageUrl)
                    thisButton.css("margin-left", "0px")
                }
            },
            error(data) {
                new Modal({ icon: "error", content: "上传失败，请确认上传文件正确后重试", title: "上传失败" }).show()
            }
        })
    }

    itemImagesUpload(evt, imageUrl) {
        const $self = $(evt.target).closest(".js-item-image")
        if ($self.hasClass("js-main-image")) {
            if ($("img", $self).length) {
                $("img", $self).remove()
            }
            $self.prepend(itemImageTemplate({ imageUrl, type: "main" }))
            this.fileUpload()
            $self.removeClass("empty error")
            $self.find(".image-input").val(imageUrl)
        } else if ($self.hasClass("js-item-image-container")) {
            $self.find("img").attr("src", imageUrl).data("src", imageUrl)
        } else {
            const newImage = $(itemImageTemplate({ imageUrl }))
            this.fileUpload()
            $self.before(newImage)
            this.checkImagesLength()
        }
    }

    // 删除图片
    deleteItemImage(evt) {
        evt.stopPropagation()
        $(evt.currentTarget).closest(".js-item-image").remove()
        this.checkImagesLength()
    }

    checkImagesLength() {
        const isEnough = $(".js-images-list").find(".item-image").length > 4
        $(".js-images-list").find(".btn-upload.image-end")[isEnough ? "addClass" : "removeClass"]("hide")
    }

    // 移动辅图位置
    moveImagePosition(evt) {
        evt.stopPropagation()
        let direct = $(evt.currentTarget).data("direct"),
            image = $(evt.currentTarget).closest(".js-item-image"),
            prev = $(image).prev(),
            next = $(image).next()
        if (direct === "left") {
            prev.before(image)
        } else {
            next.after(image)
        }
    }

    // 展开属性添加面板
    extendNewAttributes(evt) {
        evt.preventDefault()
        if (this.$jsExtendAttribute.next().length === 0) {
            $(evt.currentTarget).addClass("hide")
            const attributeItem = $(attributeTemplate())
            this.$jsExtendAttribute.after(attributeItem)
            this.bindAttributeEvent(attributeItem)
        }
    }

    bindAttributeEvent(attributeItem) {
        $("select", attributeItem).selectric()
        $(".js-select-attr-val-type", attributeItem).on("change", evt => this.changeAttrValType(evt, attributeItem))
        $(".close", attributeItem).on("click", evt => this.closeAttribute(evt))
        $("form", attributeItem).validator({ isErrorOnParent: true })
        $("form", attributeItem).on("submit", evt => this.submitAttribute(evt))
    }

    changeAttrValType(evt, attributeItem) {
        let val = $(evt.currentTarget).val(),
            unitInput = $(".js-attr-unit-input", attributeItem)
        switch (val) {
            case "STRING", "DATE":
                unitInput.addClass("hide")
                break
            case "NUMBER":
                unitInput.removeClass("hide")
                break
            default:
                unitInput.addClass("hide")
        }
    }

    // 获取所有属性的key
    getAttrKeys() {
        return _.map($(".js-category-attr"), (attr) => $(attr).data("attr").attrKey.toString())
    }

    // 提交属性数据
    submitAttribute(evt) {
        evt.preventDefault()
        // 阻止内嵌表单的submit事件冒泡
        evt.stopPropagation()
        let $from = $(evt.currentTarget),
            data = $from.serializeObject()

        data.attrKey = $.trim(data.attrKey)
        data.attrVal = $.trim(data.attrVal)

        if (_.includes(this.getAttrKeys(), data.attrKey)) {
            $(".js-error-tip").removeClass("hide")
        } else {
            $(".js-error-tip").addClass("hide")
            data.attrVals = _.flatten([data.attrVal])

            const $attributeItem = $(attributeTemplate(data))
            this.$jsCategoryAttibuteList.append($attributeItem)
            $attributeItem.dropdown()
            this.$jsExtendAttribute.removeClass("hide")
            this.closeAttribute(evt)
        }
    }

    // 单项删除属性
    deleteAttibuteItem(evt) {
        evt.preventDefault()
        $(evt.currentTarget).closest(".js-category-attr").remove()
    }

    // 关闭属性面板
    closeAttribute(evt, skuArea) {
        $(evt.currentTarget).closest(".js-attribute-area").find(".js-attribute-new").removeClass("hide")
        $(evt.currentTarget).closest(".js-extend-template").remove()
        if (skuArea) skuArea.removeClass("hide")
    }

    // 展开添加sku属性面板
    extendSkuAttributes(evt) {
        evt.preventDefault()
        if (this.$jsExtendSkuAttribute.next().length === 0) {
            let attrs = this.getRealCanSkuKey(),
                skuLimit = skuMostDimension > $(".js-sku-area", this.$jsSkuAttributeArea).length,
                skuAttribute = $(skuAttibuteTemplate({ editable: true, attrs, skuLimit }))

            this.$jsExtendSkuAttribute.after(skuAttribute)
            $("select", skuAttribute).selectric()
            this.bindSkuAttributeEvent(skuAttribute)
        }
    }

    // 绑定sku属性的操作事件
    bindSkuAttributeEvent(skuAttribute, skuArea) {
        this.$jsExtendSkuAttribute.addClass("hide")
        $(".close", skuAttribute).on("click", evt => this.closeAttribute(evt, skuArea))
        $("form .js-select-new-sku-key", skuAttribute).on("click", evt => this.addNewSku(evt))
    }

    editSkuAttributeValue(evt) {
        evt.preventDefault()
        let $skuArea = $(evt.currentTarget).closest(".js-sku-area"),
            attrKey = $skuArea.data("key"),
            sku = _.map($skuArea.find(".js-select-sku-attr"), (i) => ({
                attrKey,
                attrVal: $(i).val(),
                checked: $(i).prop("checked")
            })),
            type = $skuArea.data("type").split("_")[0],
            skuAttribute = $(skuAttibuteTemplate({ value: "true", data: sku, type, attrKey }))

        if (!$(".js-sku-value", $skuArea).length) {
            $skuArea.append(skuAttribute)
            this.bindSkuAttrValEvent(skuAttribute, $skuArea, type)
        }
    }

    bindSkuAttrValEvent(skuAttribute, $skuArea, type) {
        $(".js-sku-value", $skuArea).validator({ isErrorOnParent: true })
        if (type == "DATE") {
            $(".datepicker", $skuArea).attr("readonly", true).datepicker()
        }
        $(".js-close", skuAttribute).on("click", evt => this.deleteSkuValue(evt))
        $("form", $skuArea).on("submit", evt => this.addNewSkuValue(evt, $skuArea))
    }

    addNewSkuValue(evt, skuArea) {
        evt.preventDefault()
        // 阻止内嵌表单的submit事件冒泡
        evt.stopPropagation()
        let $form = $(evt.currentTarget),
            data = $form.serializeObject(),
            skuValue = skuValueTemplate(data)

        $(skuArea).find(".js-sku-attr-vals").append(skuValue)
        $(".js-attr-val-container", $form).removeClass("success")
        $("input:text", $form).val("")
        this.fileUpload()
    }

    getCanSkuAttributesAndDisabled(attrKey) {
        let $attr = $(`.js-category-attr-input[data-key='${attrKey}']`),
            $groupItem = $(".js-group-item", $attr),
            $attrIcon = $attr.closest(".js-category-attr").find("i"),
            attrData = $attr.closest(".js-category-attr").data("attr"),
            $options = $(".js-dropdown-item", $attr),
            attributeRules = _.without(_.map($options, i => {
                const value = $(i).hasClass("js-attr-val") ? $(i).data("val") : $(i).find("span").text()
                return value != "" ? { attrVal: value, attrKey: attrData.attrKey } : 0
            }), 0),
            dropdown = $attr.data("dropdown")

        $groupItem.closest($groupItem.data("parent")).removeClass("error empty")
        $groupItem.removeAttr("required")
        $attrIcon.toggleClass("hide")
        dropdown.disable()

        return _.extend(attrData, { attributeRules })
    }

    addNewSku(evt) {
        let $form = $(evt.currentTarget).closest("form"),
            attrKey = $("select[name=attrKey]", $form).val(),
            data = this.getCanSkuAttributesAndDisabled(attrKey),
            newSku = $(skuAttibuteTemplate(data))

        this.$jsExtendSkuAttributeArea.before(newSku)
        this.closeAttribute(evt)
        this.fileUpload()
    }

    addSkuAttributeValue(evt, skuArea) {
        evt.preventDefault()
        let $valueInput = $(evt.currentTarget).find("input[name=value]"),
            value = $valueInput.val(),
            skuValue = $(skuValueTemplate({ attrVal: value }))

        if (value) {
            $valueInput.val(null)
            if (skuArea) skuArea.remove()
            $(evt.currentTarget).find(".value-area").append(skuValue)
            $(".js-close", skuValue).on("click", evt => this.deleteSkuValue(evt))
        }
    }

    deleteSku(evt) {
        evt.preventDefault()
        const $sku = $(evt.currentTarget)
        new Modal({
            icon: "info",
            title: "确认删除",
            isConfirm: true,
            overlay: true,
            content: "是否确认删除整个规格分组？"
        }).show(() => {
            this.revertAttributeStatus($sku)
        })
    }

    deleteAttr(evt) {
        const $attr = $(evt.currentTarget)
        $attr.closest("li").remove()
    }

    revertAttributeStatus($sku) {
        let $skuArea = $sku.closest(".js-sku-area"),
            attrKey = $skuArea.data("key"),
            $groupItem = $(".js-group-item", $attr),
            $attr = $(`.js-category-attr-input[data-key='${attrKey}']`),
            $attrIcon = $attr.closest(".js-category-attr").find("i")

        $skuArea.remove()
        this.selectSkuAttribute()
        $attrIcon.toggleClass("hide")
        $groupItem.attr("required", $attr.data("required"))
        $attr.data("dropdown").enable()
    }

    attributeImagesUpload(evt, imageUrl) {
        let $self = $(evt.target),
            attrKey = $self.data("key"),
            attrVal = $self.data("value")
        $self.closest(".btn").replaceWith(attributeImageTemplate({ imageUrl, attrKey, attrVal }))
        this.fileUpload()
    }

    deleteSkuValue(evt) {
        $(evt.currentTarget).closest(".js-sku-value").remove()
    }

    selectSkuAttribute(evt) {
        // $(evt.currentTarget).attr("checked", $(evt.currentTarget).prop("checked"));
        let attrs = _.map($(".js-sku-area:has(:checked)"), i => {
            $(i).removeClass("error")
            return { key: $(i).data("key") }
        }),
            values = _.map($(".js-sku-area:has(:checked)"), j => (_.map($(".js-select-sku-attr:checked", j), i => ({
                attr: $(i).attr("name"),
                value: $(i).val()
            }))
            )),
            data = { attrs, values: this.combine(values) }
        const skus = this.$jsSkuTableArea.data("sku")
        if (attrs.length) {
            this.renderSkuTable(data)
            $("#js-price-and-stock .js-sku-required").removeAttr("required")
        } else {
            $("#js-price-and-stock").removeClass("hide")
            $(".js-sku-required").attr("required", true)
            this.$jsSkuTableArea.empty()
        }

        this.bindFormEvent()
    }

    // 组合所有的sku属性，并序列化成可以渲染的结果
    combine(arr) {
        arr.reverse()

        const r = [];
        (function fn(t, a, n) {
            if (n == 0) return r.push(t)
            for (let i = 0; i < a[n - 1].length; i++) {
                fn(t.concat(a[n - 1][i]), a, n - 1)
            }
        }([], arr, arr.length))

        let row = [],
            rowspan = r.length
        for (let n = arr.length - 1; n > -1; n--) {
            row[n] = parseInt(rowspan / arr[n].length)
            rowspan = row[n]
        }
        row.reverse()

        let temp = $.extend(true, [], r),
            attrs = $.extend(true, [], r)
        _.each(r, (d, j) => {
            for (let index = 0; index < row.length; index++) {
                const list = temp[j]

                if (j % row[index] == 0) {
                    list[index].rowspan = row[index]
                }

                const sku = window.skuObject && window.skuObject[`${(_.map(list, i => `${i.attr}:${i.value}`)).join(";")}`]

                if (sku) {
                    attrs[j] = _.extend(sku, { list })
                } else {
                    attrs[j] = { list }
                }
            }
        })
        return attrs
    }

    renderSkuTable(data) {
        $("#js-price-and-stock").addClass("hide")
        const skuTable = $(skuTableTemplate({ data }))
        this.$jsSkuTableArea.html(skuTable)
        $("input", skuTable).on("focusout", evt => this.addSkuInfo(evt))
    }

    addSkuInfo(evt) {
        let skuObject = window.skuObject || {},
            skus = _.flatten(_.values(skuObject)) || [],
            keys = _.keys(skuObject) || [],
            $jsSkuTr = $(evt.currentTarget).closest("tr"),
            originPrice = centFormat($jsSkuTr.find(".js-sku-origin-price").val()),
            // platformPrice = centFormat($jsSkuTr.find(".js-sku-platform-price").val()),
            // integralPrice = ($jsSkuTr.find(".js-sku-integral-price").val()),
            sku = {
                skuAttributeKeyAndValue: $jsSkuTr.data("key"),
                stockQuantity: $jsSkuTr.find("input.js-sku-quantity").val(),
                price: centFormat($jsSkuTr.find("input.js-sku-price").val()),
                outerSkuId: $jsSkuTr.find("input.js-sku-out-id").val(),
                extraPrice: { originPrice },
                type: $("#item-type-select").val()
            }

        if (_.contains(keys, sku.skuAttributeKeyAndValue)) {
            _.each(skuObject, (i, j) => {
                if (i.skuAttributeKeyAndValue === sku.skuAttributeKeyAndValue) {
                    _.extend(i, sku)
                }
            })
        } else {
            skuObject[`${sku.skuAttributeKeyAndValue}`] = sku
        }

        window.skuObject = skuObject
    }

    validateBatchInput(text, $el) {
        const type = $el.data("type")
        switch (type) {
            case "QUANTITY":
                return !!/^\d{1,8}$/.test(text)
            default:
                return !!/^[+-]?\d{1,8}(\.\d{1,2})?$/.test(text)
        }
    }

    batchSetPeiceAndStock(evt) {
        const self = $(evt.currentTarget)
        const result = this.checkBatchBtn()
        if (!result) {
            return false
        }
        _.map($(".batch-set input"), (i) => {
            if ($(i).val() != "") {
                $(".sku-table").find(`.${$(i).prop("name")}`).val($(i).val())
            }
        })
        $(".batch-set input").val("")
    }

    controlPriceValidate(evt) {
        const self = $(evt.currentTarget)
        let lowPrice = parseFloat(($("input[name='lowPrice']").val() || 0))
        let highPrice = parseFloat($("input[name='highPrice']").val() || 0)
        if (highPrice != 0 && lowPrice > highPrice) {
            new Modal({ icon: "error", title: "提示信息", content: "控价设置最低价不能大于最高价！" }).show()
            return false
        }
    }

    checkBatchBtn(evt) {
        let result = true,
            isValid = false,
            hasVal = false,
            resultTemp
        resultTemp = _.map($(".batch-set input"), (i) => {
            let resultThis
            isValid = this.validateBatchInput($(i).val(), $(i)),
                hasVal = ($(i).val() != "")
            if (hasVal) {
                $(i).closest(".group-content")[isValid ? "removeClass" : "addClass"]("error unvalid")
                resultThis = isValid ? 1 : 2
            } else {
                $(i).closest(".group-content").removeClass("error unvalid")
                resultThis = 0
            }
            return resultThis
        })
        if (_.indexOf(resultTemp, 2) > -1) {
            result = false
        } else if (_.indexOf(resultTemp, 1) > -1) {
            result = true
        } else {
            result = false
        }
        return result
    }

    organizeItemBaseInfo(form) {
        const item = $(form).serializeObject()
        item.originPrice = centFormat(item.originPrice)
        if (this.$brandSearch.data("id")) {
            item.brandId = this.$brandSearch.val() ? this.$brandSearch.data("id") : ""
            item.brandName = this.$brandSearch.val() ? this.$brandSearch.data("name") : ""
        }
        console.log($("#item-module-select").val(), $("#item-module-select").data("value"))
        item.restrictedSalesAreaTemplateId = $("#item-module-select").val() || $("#item-module-select").data("value")
        item.extra = this.organizeItemExtra(form)
        if (item.productionDateStart) {
            if (item.productionDateEnd) {
                item.extra.productionDateStart = item.productionDateStart
                item.extra.productionDateEnd = item.productionDateEnd
                delete item.productionDateStart
                delete item.productionDateEnd
            }
        }
        if (item.activityStartTime) {
            if (item.activityEndTime) {
                item.extra.activityStartTime = item.activityStartTime
                item.extra.activityEndTime = item.activityEndTime
                delete item.activityStartTime
                delete item.activityEndTime
            }
        }
        return item
    }

    organizeItemExtra(form) {
        let unit = $.trim(form.find("input[name=unit]").val()) || "件",
            selfPlatformLink = $.trim(form.find("input[name=selfPlatformLink]").val()),
            unitQuantity = form.find("input[name=unitQuantity]").val(),
            activityMainImage = form.find("input[name='activityMainImage']").val(),
            activityDetailImages = form.find("input[name='activityDetailImages']").val(),
            // activityStartTime = this.DateToUnix(form.find("input[name='activityStartTime']").val()),
            // activityEndTime = this.DateToUnix(form.find("input[name='activityEndTime']").val()),
            skuOrderSplitLine = form.find("input[name=skuOrderSplitLine]").val(),
            orderQuantityLimit = form.find("input[name=orderQuantityLimit]").val(),
            countryOfDeparture = $.trim(form.find("input[name=countryOfDeparture]").val())
        return { unit, selfPlatformLink, unitQuantity, activityMainImage, activityDetailImages, skuOrderSplitLine, orderQuantityLimit, countryOfDeparture }
    }

    organizeItemDetailImages(form) {
        let $imagesContainers = $(".js-item-image.item-image:not(#js-item-main-image)", form),
            images = _.map($imagesContainers, (d) => {
                let $image = $(d).find("img"),
                    name = $image.data("name"),
                    url = $image.data("src")
                return { url, name }
            })
        return { images }
    }
    DateToUnix(string) {
        return new Date(string).getTime() / 1000
        // var f = string.split(' ', 2);
        // var d = (f[0] ? f[0] : '').split('-', 3);
        // var t = (f[1] ? f[1] : '').split(':', 3);
        // return (new Date(
        //   parseInt(d[0], 10) || null,
        //   (parseInt(d[1], 10) || 1) - 1,
        //   parseInt(d[2], 10) || null,
        //   parseInt(t[0], 10) || null,
        //   parseInt(t[1], 10) || null,
        //   parseInt(t[2], 10) || null
        // )).getTime() / 1000;
    }
    organizeSkuInfo(form) {
        const skuInfo = _.without(_.map($(".js-sku-table-area:not(.hide) .js-sku-tr", form), i => {
            let data = $(i).data("attr"),
                id = $(i).data("id"),
                priceValue = $.trim($(i).find("input.js-sku-price").val()),
                price = priceValue == "" ? 0 : centFormat(priceValue),
                stockQuantityValue = $.trim($(i).find("input.js-sku-quantity").val()),
                stockQuantity = stockQuantityValue == "" ? 0 : stockQuantityValue,
                originPriceValue = $.trim($(i).find("input.js-sku-origin-price").val()),
                originPrice = originPriceValue == "" ? 0 : centFormat(originPriceValue),
                crossedPriceValue = $.trim($(i).find("input.js-sku-original-price").val()),
                crossedPrice = originPriceValue == "" ? 0 : centFormat(crossedPriceValue),
                unitQuantity = $.trim($(i).find("input.js-unit-quantity").val()),
                skuOrderSplitLine = $.trim($(i).find("input[name=skuOrderSplitLine]").val()),
                activitySalesPrice = centFormat(form.find("input[name=activitySalesPrice]").val()),
                activityStartTime = form.find("input[name=moneyActivityStartTime]").val(),
                activityEndTime = form.find("input[name=moneyActivityEndTime]").val(),
                orderQuantityLimit = $.trim($(i).find("input[name=orderQuantityLimit]").val()),
                // platformPriceValue = $.trim($(i).find("input.js-sku-platform-price").val()),
                //integralPriceValue = $.trim($(i).find("input.js-sku-integral-price").val()),
                // integralPrice = integralPriceValue == "" ? 0 :integralPriceValue,
                outerSkuId = $(i).find("input.js-sku-out-id").val(),
                attrs = data ? _.map(data, d => ({ attrKey: d.attr, attrVal: d.value })) : null,
                extraPrice = { originPrice, crossedPrice },
                extra = {
                    unitQuantity,
                    skuOrderSplitLine,
                    orderQuantityLimit,
                    activitySalesPrice,
                    activityStartTime,
                    activityEndTime
                },
                sku = { price, stockQuantity, outerSkuId, id, attrs, extraPrice, extra }
            // debugger
            /* if(integralPrice!=0)
             {
               sku = { price, stockQuantity, outerSkuId, id, attrs,extraPrice:{integralPrice, originPrice}, extra }
             }else*/
            if (!price && price != 0) {
                if ($("#item-type-select").val() == "3" || $("#item-type-select").val() == "4" || $("#item-type-select").val() == "5") {
                    new Modal({ icon: "error", title: "提示信息", content: "请输入兑换积分！" }).show()
                    return false
                }
            }


            return sku.price ? sku : 0
        }), 0)
        return skuInfo
    }

    organizeOtherAttributeInfo(form) {
        const attrs = _.without(_.map($(".js-group-item:not(:disabled)", form), i => {
            let $input = $(i),
                attrKey = $input.data("key"),
                attrVal = $.trim($input.val()),
                group = $input.data("group"),
                unit = $input.data("unit")

            if (attrVal) {
                return { attrKey, attrVal, group, unit }
            }
            return 0
        }), 0)

        const groupAttrs = _.groupBy(attrs, (i) => i.group)
        return _.map(groupAttrs, (otherAttributes, group) => ({ group, otherAttributes }))
    }

    organizeSkuAttributeInfo(form) {
        const skus = _.flatten(_.map($(".js-sku-area:has(:checked)", form), i => {
            const showImage = $(".js-sku-show-image", i).prop("checked")

            return _.map($(".js-select-sku-attr", i), j => {
                let $input = $(j),
                    attrKey = $input.attr("name"),
                    attrVal = $input.val(),
                    unit = $input.data("unit"),
                    image = $input.closest(".js-sku-item").find(".js-attribute-image").data("src")
                return { attrKey, attrVal, image, showImage, unit }
            })
        }))

        const groupSku = _.groupBy(skus, (i) => i.attrKey)
        return skus.length ? _.map(groupSku, (skuAttributes, attrKey) => ({ attrKey, skuAttributes })) : null
    }

    organizeItemDto(form) {
        let item = this.organizeItemBaseInfo(form),
            itemDetail = this.organizeItemDetailImages(form),
            groupedSkuAttributes = this.organizeSkuAttributeInfo(form),
            skus = this.organizeSkuInfo(form),
            groupedOtherAttributes = this.organizeOtherAttributeInfo(form),
            itemDeliveryFee = { deliveryFeeTemplateId: $(".js-fee-template").val() }
        return { item, itemDetail, groupedSkuAttributes, skus, groupedOtherAttributes, itemDeliveryFee }
    }

    submitTimer
    submitItem(evt) {
        evt.preventDefault()
        let $form = this.$itemForm,
            FullItem = this.organizeItemDto($form),
            type = FullItem.item.id ? "PUT" : "POST",
            activityCommissionConfig = {}
        // return { images }
        // 新增时增加是否第三方平台字段,
        // 创建了保税商品后再创建积分自建商品，在编辑保税商品，保存以后，在返回保税商品编辑中查看，保税商品就变成了一般贸易商品，是因为保存完后没有清除sessionStorage.isThirdPartyItem
        // bug if('0')进来
        if (sessionStorage.isThirdPartyItem == "1") {
            FullItem.item.isThirdPartyItem = 1 //1表示是第三方平台商品
            FullItem.item.pushSystem = sessionStorage.pushSystem // pushSystem表示第三方平台id
        } else {
            // 修改时不修改是否第三方平台，会出bug跨境保税保存后变成一般贸易
            if (!FullItem.item.id) {
                FullItem.item.isThirdPartyItem = 0 //1表示是第三方平台商品
            }
        }
        if (!FullItem.itemDeliveryFee.deliveryFeeTemplateId) {
            new Modal({
                icon: "error",
                title: "创建失败",
                content: "请选择运费模板"
            }).show()
            return false
        }
        var searchURL = window.location.search;
        searchURL = searchURL.substring(1, searchURL.length);
        var targetPageId = searchURL.split("&")[0].split("=")[1];
        if ($("#item-type-select").val() == "" && targetPageId == "3") {
            new Modal({
                icon: "error",
                title: "提示信息",
                content: "积分商品请选择商品类型！"
            }).show()
            return false
        }
        if (this.validateDispath($form, FullItem)) {
            const typeVal = $("#id-item-type").val() || $("#id-item-type").data("value")
            // 商品类型必填校验
            if (!typeVal) {
                new Modal({
                    icon: "error",
                    title: "提示信息",
                    content: "请选择海淘类型！"
                }).show()
                return false
            }
            if (!FullItem.item.isBonded) {
                FullItem.item.isBonded = typeVal;
                console.log('isBonded值丢失')
            }

            // 选保税时，判断海关信息必填项
            if (typeVal != "0") {
                if (!FullItem.item.hsCode) {
                    new Modal({ icon: "error", title: "提示信息", content: "请录入HS海关代码！" }).show()
                    return false
                }
                if (!FullItem.item.customOriginId) {
                    new Modal({ icon: "error", title: "提示信息", content: "请选择商品原产地！" }).show()
                    return false
                }
                if (!FullItem.item.customTaxHolder) {
                    new Modal({ icon: "error", title: "提示信息", content: "请选择税金的承担方！" }).show()
                    return false
                }
                if (!FullItem.item.provinceId) {
                    new Modal({ icon: "error", title: "提示信息", content: "请选择所在地省！" }).show()
                    return false
                }
                if (!FullItem.item.restrictedSalesAreaTemplateId) {
                    new Modal({ icon: "error", title: "提示信息", content: "请选择可售模版！" }).show()
                    return false
                }
                if ((!$(".customs-declaration-info #cityId").val()) && !FullItem.item.cityId) {
                    new Modal({ icon: "error", title: "提示信息", content: "请选择所在地市！" }).show()
                    return false
                }

                FullItem.item.cityId = $(".customs-declaration-info #cityId").val() || FullItem.item.cityId
            }
            // 增加海关报关信息
            if (FullItem.item.hsCode) {
                const skuCustom =
                {
                    hsCode: FullItem.item.hsCode,
                    customOriginId: FullItem.item.customOriginId || $("#customOriginId").val(),
                    customOrigin: $("#customOriginId").find("option:selected").text(),
                    customTaxHolder: FullItem.item.customTaxHolder,
                    provinceId: FullItem.item.provinceId,
                    province: $("#provinceId").find("option:selected").text(),
                    cityId: FullItem.item.cityId,
                    city: $("#cityId").find("option:selected").text()
                }
                // 修改时要加上id,要不会插入两条重复数据
                if (FullItem.item.id) {
                    skuCustom.id = FullItem.item.customId
                }
                FullItem.skuWithCustoms = []
                if (FullItem.skus.length > 0) {
                    _.forEach(FullItem.skus, (value) => {
                        if (sessionStorage.pushSystem) {
                            value.tags = {
                                pushSystem: sessionStorage.pushSystem // 报关推送平台(1：洋800,2其他平台)英文逗号隔开，自建商品不传。
                            }
                        }
                        // 修改时要加上pushSystem
                        if (FullItem.item.id && $(".js-sku-pushSystem").val()) {
                            value.tags = {
                                pushSystem: $(".js-sku-pushSystem").val()// 报关推送平台(1：洋800)英文逗号隔开，自建商品不传。
                            }
                        }
                        FullItem.skuWithCustoms.push({ sku: value, skuCustom })
                    })
                }
                delete FullItem.skus
            } else {
                FullItem.skuWithCustoms = []
                if (FullItem.skus.length > 0) {
                    _.forEach(FullItem.skus, (value) => {
                        if (sessionStorage.pushSystem) {
                            value.tags = {
                                pushSystem: sessionStorage.pushSystem // 报关推送平台(1：洋800，2：其他平台)英文逗号隔开，自建商品不传。
                            }
                        }
                        // 修改时要加上pushSystem
                        if (FullItem.item.id) {
                            value.tags = {
                                pushSystem: $(".js-sku-pushSystem").val()// 报关推送平台(1：洋800)英文逗号隔开，自建商品不传。
                            }
                        }
                        FullItem.skuWithCustoms.push({ sku: value })
                    })
                }
                delete FullItem.skus
            }
            // debugger
            // 赋值商品服务费设置
            if (FullItem.item.isCommission == '1') {
                if (!FullItem.item.serviceProviderRate || !FullItem.item.firstRate || !FullItem.item.secondRate) {
                    new Modal({ icon: "error", title: "提示信息", content: "请输入服务费" }).show()
                    return false
                }
                if (FullItem.item.matchingStartTimeString || FullItem.item.matchingEndTimeString) {
                    if (!FullItem.item.matchingEndTimeString || !FullItem.item.matchingStartTimeString || !FullItem.item.activityServiceProviderRate || !FullItem.item.activityFirstRate || !FullItem.item.activitySecondRate) {
                        new Modal({ icon: "error", title: "提示信息", content: "请输入活动服务费或生效日期" }).show()
                        return false
                    }
                }
                FullItem.isCommission = FullItem.item.isCommission;
                // 设置方式是按比例还是固定金额
                if ($("input[name='setStoreProfitWay']:checked").val() == "false") {
                    FullItem.serviceProviderFee = FullItem.item.serviceProviderRate ? centFormat(FullItem.item.serviceProviderRate) : 0;
                    FullItem.firstFee = FullItem.item.firstRate ? centFormat(FullItem.item.firstRate) : 0;
                    FullItem.secondFee = FullItem.item.secondRate ? centFormat(FullItem.item.secondRate) : 0;
                    //FullItem.firstCommission=(FullItem.item.firstCommission*100).toFixed(0);
                    FullItem.commission = (FullItem.item.commission * 100).toFixed(0);
                    FullItem.serviceProviderRate = 0;
                    FullItem.firstRate = 0;
                    FullItem.secondRate = 0;

                    if (FullItem.item.matchingEndTimeString || FullItem.item.matchingStartTimeString) {
                        activityCommissionConfig.matchingStartTimeString = FullItem.item.matchingStartTimeString;
                        activityCommissionConfig.matchingEndTimeString = FullItem.item.matchingEndTimeString;
                        activityCommissionConfig.serviceProviderFee = FullItem.item.activityServiceProviderRate ? centFormat(FullItem.item.activityServiceProviderRate) : 0;
                        activityCommissionConfig.firstFee = FullItem.item.activityFirstRate ? centFormat(FullItem.item.activityFirstRate) : 0;
                        activityCommissionConfig.secondFee = FullItem.item.activitySecondRate ? centFormat(FullItem.item.activitySecondRate) : 0;
                        activityCommissionConfig.commission = (FullItem.item.commission * 100).toFixed(0);
                        activityCommissionConfig.serviceProviderRate = 0;
                        activityCommissionConfig.firstRate = 0;
                        activityCommissionConfig.secondRate = 0;
                    }
                } else {
                    // 按比例
                    FullItem.serviceProviderRate = (FullItem.item.serviceProviderRate * 100).toFixed(0);
                    FullItem.firstRate = (FullItem.item.firstRate * 100).toFixed(0); //比例改成万分之
                    FullItem.secondRate = (FullItem.item.secondRate * 100).toFixed(0);
                    // FullItem.firstCommission=(FullItem.item.firstCommission*100).toFixed(0);
                    FullItem.commission = (FullItem.item.commission * 100).toFixed(0);
                    FullItem.serviceProviderFee = 0;
                    FullItem.firstFee = 0;
                    FullItem.secondFee = 0;
                    if (FullItem.item.matchingEndTimeString || FullItem.item.matchingStartTimeString) {
                        activityCommissionConfig.matchingStartTimeString = FullItem.item.matchingStartTimeString;
                        activityCommissionConfig.matchingEndTimeString = FullItem.item.matchingEndTimeString;
                        activityCommissionConfig.serviceProviderRate = (FullItem.item.activityServiceProviderRate * 100).toFixed(0);
                        activityCommissionConfig.firstRate = (FullItem.item.activityFirstRate * 100).toFixed(0); //比例改成万分之
                        activityCommissionConfig.secondRate = (FullItem.item.activitySecondRate * 100).toFixed(0);
                        activityCommissionConfig.commission = (FullItem.item.commission * 100).toFixed(0);
                        activityCommissionConfig.serviceProviderFee = 0;
                        activityCommissionConfig.firstFee = 0;
                        activityCommissionConfig.secondFee = 0;
                    }
                }

            } else {
                FullItem.isCommission = FullItem.item.isCommission;
            }
            if (FullItem.item.isCommission == '1' && (FullItem.item.matchingEndTimeString || FullItem.item.matchingStartTimeString)) {
                FullItem.activityCommissionConfig = activityCommissionConfig
            } else {
                delete FullItem.activityCommissionConfig
                FullItem.deleteActivityCommissionConfig = true
            }
            // 赋值控价设置
            if (FullItem.item.highPrice) {
                let lowPrice = parseFloat(($("input[name='lowPrice']").val() || 0))
                let highPrice = parseFloat($("input[name='highPrice']").val() || 0)
                if (highPrice != 0 && lowPrice > highPrice) {
                    new Modal({ icon: "error", title: "提示信息", content: "控价设置最低价不能大于最高价！" }).show()
                    return false
                }
                FullItem.item.lowPrice = centFormat(FullItem.item.lowPrice)
                FullItem.item.highPrice = centFormat(FullItem.item.highPrice)
            }
            $("body").spin("medium")
            console.log(FullItem, "FullItem");
            if (this.submitTimer) return
            this.submitTimer = setTimeout(() => {
                clearTimeout(this.submitTimer)
            }, 2000)
            $.ajax({
                url: "/api/seller/items",
                type,
                contentType: "application/json",
                data: JSON.stringify(FullItem),
                success: (data) => {
                    // debugger
                    // 清除sessionStorage
                    sessionStorage.removeItem("pushSystem")
                    sessionStorage.removeItem("skuCode")
                    sessionStorage.removeItem("customOriginId")
                    sessionStorage.removeItem("provinceId")
                    sessionStorage.removeItem("cityId")
                    // 创建了保税商品后再创建积分自建商品，在编辑保税商品，保存以后，在返回保税商品编辑中查看，保税商品就变成了一般贸易商品，是因为保存完后没有清除sessionStorage.isThirdPartyItem
                    sessionStorage.removeItem("isThirdPartyItem")
                    console.log(data, "datadata");

                    this.confirmLeave()
                    // 保存成功,若是创建则提示是否进入编辑商品详情页，点否则跳到上下架页，是编辑商品也跳回上下架页
                    if (!FullItem.item.id) {
                        window.comfirmItemDetailModal = new Modal(comfirmModalTemplate({
                            data: {
                                title: "保存成功",
                                content: "保存成功，是否进入编辑商品详情页"
                            }
                        }))
                        window.comfirmItemDetailModal.show()
                        $("#modalConfrimId").on("click", () => this.ConfirmFn(data))
                        $("#modalCancelId").on("click", () => this.CancelFn(FullItem))
                    } else {
                        if ($("#item-type-select").val() == "3" || $("#item-type-select").val() == "4" || $("#item-type-select").val() == "5") {
                            window.location.href = FullItem.item.status === "1" ? "/seller/ladder-distribution/on-shelf" : "/seller/ladder-distribution/off-shelf"
                        }
                        else {
                            window.location.href = "/seller/on-shelf"
                        }
                    }
                }
            })
        }
    }
    ConfirmFn(itemId) {
        $.ajax({
            url: `/api/seller/items/${itemId}/detail`,
            type: "GET",
            dataType: "html",
            success: (data) => {
                window.comfirmItemDetailModal.close()
                this.renderRichEditor(itemId, data)
            }
        })
    }
    CancelFn(FullItem) {
        if ($("#item-type-select").val() == "3" || $("#item-type-select").val() == "4" || $("#item-type-select").val() == "5") {
            window.location.href = FullItem.item.status === "1" ? "/seller/ladder-distribution/on-shelf" : "/seller/ladder-distribution/off-shelf"
        } else {
            window.location.href = "/seller/on-shelf"
        }

    }

    renderRichEditor(itemId, data) {
        const richEditor = $(richEditorTemplate({
            data,
            id: itemId
        }))

        window.editorModal = new Modal(richEditor)
        window.editorModal.show("", {
            beforeClose: () => {
                $("iframe.wysihtml5-sandbox, input[name='_wysihtml5_mode']").remove()
                $("body").removeClass("wysihtml5-supported")
            }
        })

        const editor = new wysihtml5.Editor("wysihtml5-editor", {
            toolbar: "wysihtml5-editor-toolbar",
            parserRules: wysihtml5ParserRules
        })

        editor.on("load", () => editor.composer)

        $(".wysihtml5-sandbox").addClass("text-tool-iframe").attr("id", "iframe-whsihtml5")
        this.fileUploadDetail()
    }

    fileUploadDetail(evt) {
        let $self = $(".js-item-detail-modal input[name=file]"),
            $imageInput = $(".js-item-detail-modal .image-input")
        $self.fileupload({
            url: "/api/user/files/upload",
            type: "POST",
            dataType: "html",
            done: (evt, data) => {
                const url = _.values(JSON.parse(data.result))[0]
                $imageInput.val(`${url}`)
            },
            error(data) {
                new Modal({
                    icon: "error",
                    content: "上传失败，请确认上传文件正确后重试",
                    title: "上传失败"
                }).show()
            }
        })
    }

    setLabel() {
        $.ajax({
            url: "/api/seller/tag",
            type: "GET",
            contentType: "application/json",
            data: {},
            success: (data) => {
                const labelCheckbox = $(labelCheckboxTemplate({ data: { items, tagList: data.data } }))
                window.setLabelModal = new Modal(labelCheckbox)
                window.setLabelModal.show()
                $("#set-items-tags-form").on("submit", this.settingLabel)
            }
        })
    }

    settingLabel(evt) {
        const tagIds = _.map($("input[name='tagName']:checked"), (i) => $(i).val())
        const itemIds = $("input[name='itemIds']").val()
        if (tagIds.length == 0) {
            new Modal({
                icon: "error",
                title: "提示信息",
                content: "请勾选标签"
            }).show()
            return
        }
        $("body").overlay()
        $.ajax({
            url: "/api/seller/tag/item/batch",
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: { itemIds, tagIds: tagIds.join(',') },
            success: (data) => {
                window.setLabelModal.close()
                new Modal({ icon: "success", title: "设置成功", content: "设置标签成功" }).show()
                setTimeout("window.location.reload()", 1000)
                return $("body").overlay(false)
            },
            error(data) {
                new Modal({ icon: "error", content: `设置失败,${data.responseJSON.message}`, title: "设置失败" }).show()
                setTimeout("window.location.reload()", 1000)
            }
        })
    }

    submitItemDetail(evt) {
        let detail = $("#iframe-whsihtml5").contents().find("body").html(),
            itemId = $(evt.currentTarget).data("id")
        $.ajax({
            url: `/api/seller/items/${itemId}/detail`,
            type: "POST",
            data: {
                detail
            },
            success: () => {
                window.editorModal.close()
                new Modal({
                    icon: "success",
                    title: "编辑成功",
                    content: "编辑商品详情成功"
                }).show()
                if ($("#item-type-select").val() == "3" || $("#item-type-select").val() == "4" || $("#item-type-select").val() == "5") {
                    window.location.href = "/seller/ladder-distribution/off-shelf"
                } else {
                    window.location.href = "/seller/on-shelf"
                }

            }
        })
    }
}

module.exports = ItemPublish

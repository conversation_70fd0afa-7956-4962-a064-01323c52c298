<div class="form-section section-top-bottom clearfix">
  <span class="pull-left section-title">基本信息</span>
  <div class="pull-left section-content clearfix">
    <div class="clearfix">
      <div class="">
        <div class="control-group">
          <label class="group-title line-title"><b class="required">*</b>{{i18n "Product Title" bundle="items"}}
            ：</label>
          <div class="group-content">
            <input type="text" id="input-name" class="input-text is-filled input-575" name="name" maxLength="200"
              placeholder="请输入商品名称" value="{{item.name}}" required />
          </div>
        </div>
      </div>
      <div class="form-right">
        <div class="control-group">
          <label class="group-title line-title">{{i18n "Brand" bundle="items"}}：</label>
          <div class="group-content">
            <input type="text" class="input" id="js-item-brand" name="brand" value="{{item.brandName}}" {{#if
              item}}data-id="{{item.brandId}}" data-name="{{item.brandName}}" {{/if}} placeholder="请搜索品牌名称"
              autocomplete="off" onkeydown="if(event.keyCode == 13) return false;" />
          </div>
        </div>
      </div>
    </div>
    <div class="clearfix">
      <div class="">
        <div class="control-group">
          <label class="group-title line-title">广告语：</label>
          <div class="group-content">
            <input type="text" id="input-advertise" class="input-text is-filled input-575" name="advertise"
              maxLength="100" placeholder="请输入商品广告语" value="{{item.advertise}}" />&nbsp;&nbsp;<span class="hint--bottom"
              aria-label="广告语会出现在前台商品标题下方"><i
                class="icon-feebas icon-feebas-warning-fill js-show-advertise-tip"></i></span>
          </div>
        </div>
      </div>
    </div>
    <div class="control-group image-area">
      <label class="group-title"><b class="required">*</b>{{i18n "Product Images" bundle="items"}}：</label>
      <div class="group-content">
        <div class="image-tip">请上传小于1MB的PNG或JPG的图片</div>
        <div class="btn btn-upload main-image {{#unless
          item.mainImage}}empty{{/unless}} image-container js-main-image js-item-image" id="js-item-main-image"
          data-type="1">
          <input type="file" name="file">
          {{#if item.mainImage}}
          <img src="{{item.mainImage}}" alt="">
          {{else}}
          <span class="image-tip-add">+</span>
          {{/if}}
          <input type="hidden" class="image-input" name="mainImage" value="{{item.mainImage}}" data-pattern="mainImage"
            required>
        </div>
        <ul class="images-list js-images-list clearfix">
          {{#if itemDetail.images}}
          {{#each itemDetail.images}}
          <li class="pull-left btn btn-upload image-container js-item-image-container item-image js-item-image"
            data-type="1">
            <input type="file" name="file">
            <img src="{{url}}" alt="" data-src="{{url}}" data-name="{{name}}" />
            <span class="image-operate">
              <i class="icon-feebas icon-feebas-left-arrow js-move-image" data-direct="left"></i>
              <i class="icon-feebas icon-feebas-trash js-delete-image"></i>
              <i class="icon-feebas icon-feebas-right-arrow js-move-image" data-direct="right"></i>
            </span>
          </li>
          {{#if @last}}
          {{#gt @index 3}}
          {{else}}
          <li class="pull-left btn btn-upload image-container image-end empty js-item-image" data-type="1">
            <input type="file" name="file">
            <span class="image-tip-add">+</span>
          </li>
          {{/gt}}
          {{/if}}
          {{/each}}
          {{else}}
          <li class="pull-left btn btn-upload image-container image-end empty js-item-image" data-type="1">
            <input type="file" name="file">
            <span class="image-tip-add">+</span>
          </li>
          {{/if}}
        </ul>
      </div>
    </div>
    <div class="control-group image-area">
      <label class="group-title">活动图片(目录图)：</label>
      <div class="group-content">
        <div class="image-tip">请上传小于1MB的PNG或JPG的图片</div>
        <div class="btn btn-upload main-image {{#unless
          item.extra.activityMainImage}}empty{{/unless}} image-container js-main-image js-item-image"
          id="js-activity-main-image" data-type="1">
          <input type="file" name="file">
          {{#if item.extra.activityMainImage}}
          <img src="{{item.extra.activityMainImage}}" alt="">
          {{else}}
          <span class="image-tip-add">+</span>
          {{/if}}
          <input type="hidden" class="image-input" name="activityMainImage"
            value="{{item.extra.activityMainImage}}" data-pattern="mainImage">
        </div>
      </div>
    </div>
    <div class="control-group image-area">
      <label class="group-title">活动图片(主图)：</label>
      <div class="group-content">
        <div class="image-tip">请上传小于1MB的PNG或JPG的图片</div>
        <div class="btn btn-upload main-image {{#unless
          item.extra.activityDetailImages}}empty{{/unless}} image-container js-main-image js-item-image"
          id="js-activity-main-image" data-type="1">
          <input type="file" name="file">
          {{#if item.extra.activityDetailImages}}
          <img src="{{item.extra.activityDetailImages}}" alt="">
          {{else}}
          <span class="image-tip-add">+</span>
          {{/if}}
          <input type="hidden" class="image-input" name="activityDetailImages" value="{{item.extra.activityDetailImages}}"
            data-pattern="mainImage">
        </div>
      </div>
    </div>
    <div class="control-group">
      <label class="group-title line-title">生效日期：</label>
      <div class="group-content ">
        <span class="input-pair">
          <input class="input-text activityDatepicker" type="text" name="activityStartTime"
            value="{{item.extra.activityStartTime}}" placeholder="开始日期" />
          <span class="spacing">-</span>
          <input class="input-text activityDatepicker" type="text" name="activityEndTime"
            value="{{item.extra.activityEndTime}}" placeholder="结束日期" />
        </span>
      </div>
      <div style="margin-top:6px;color:#afafaf; margin-left: 110px;">请输入时间格式为YYYY-MM-dd HH:mm:ss 例：2000-01-01 11:03:03
      </div>
    </div>
    <div class="form-left">
      <div class="control-group">
        <label class="group-title line-title">型号：</label>
        <div class="group-content">
          <input type="text" id="input-specification" class="input-text" name="specification" maxLength="200"
            placeholder="请输入型号" value="{{item.specification}}" />
        </div>
      </div>
      <div class="control-group">
        <label class="group-title line-title">上市时间：</label>
        <div class="group-content">
          <div class="input-group-datepicker">
            <input type="text" id="input-birthday" class="input-text datepicker js-input-datepicker js-group-item"
              data-group="BASIC" name="birthday" placeholder="请选择日期" data-key="birthday" readonly {{#each otherAttrs}}
              {{#equals group 'BASIC' }} {{#each otherAttributeWithRules}} {{#equals attributeRule.attrKey 'birthday'
              }}value="{{attrVal}}" {{/equals}} {{/each}} {{/equals}} {{/each}} />
          </div>
        </div>
      </div>
      <div class="control-group">
        <label class="group-title line-title">计量单位：</label>
        <div class="group-content">
          <input type="text" id="input-unit" class="input-text is-filled" name="unit" maxLength="200"
            placeholder="计量单位, 默认为 件" value="{{item.extra.unit}}" />
        </div>
      </div>
      <div class="control-group last-line">
        <label class="group-title line-title"><b class="required">*</b>运费模板：</label>
        <div class="group-content freight-box" data-id="{{itemDeliveryFee.deliveryFeeTemplateId}}">
          <span class="lh32 cgray">Loading...</span>
        </div>
      </div>
      <div class="control-group ">
        <label class="group-title line-title"><b class="required">*</b>海淘类型：</label>
        <div class="group-content ">
          <select id="id-item-type" name="isBonded" disabled data-value="{{item.isBonded}}">
            <option value="">请选择</option>
            {{#equals item.isThirdPartyItem '1'}}<option value="1" {{#equals item.isBonded '1' }} selected{{/equals}}>
              跨境保税</option> {{/equals}}
            {{#equals item.isThirdPartyItem '2'}}{{#equals item.isBonded '1'}}<option value="1" selected{{/equals}}>跨境保税
            </option> {{/equals}}
            <option value="0" {{#equals item.isBonded '0' }} selected{{/equals}}>一般贸易</option>
            <option value="2" {{#equals item.isBonded '2' }} selected{{/equals}}>跨境直邮</option>
          </select>
        </div>
      </div>

      <div class="control-group ">
        <label class="group-title line-title">商品类型：</label>
        <div class="group-content ">
          <select id="item-type-select" name="type" data-value="{{item.type}}">
            <option value="">请选择</option>
            <option value="3" {{#equals type '3' }} selected {{else}} {{#equals item.type '3' }}
              selected{{/equals}}{{/equals}}>积分商品</option>
            <option value="4" {{#equals item.type '4' }} selected{{/equals}}>新客礼</option>
            <option value="5" {{#equals item.type '5' }} selected{{/equals}}>累积礼</option>
          </select>
        </div>
      </div>
      <div class="control-group ">
        <label class="group-title line-title"><b class="required">*</b>可售地区模版：</label>
        <div class="group-content ">
          <select id="item-module-select" name="restrictedSalesAreaTemplateId" data-value="{{item.restrictedSalesAreaTemplateId}}">
            {{!-- <option value="">请选择</option>
            <option value="3" {{#equals type '3' }} selected {{else}} {{#equals item.restrictedSalesAreaTemplateTd '3' }}
              selected{{/equals}}{{/equals}}>积分商品</option>
            <option value="4" {{#equals item.restrictedSalesAreaTemplateTd '4' }} selected{{/equals}}>新客礼</option>
            <option value="5" {{#equals item.restrictedSalesAreaTemplateTd '5' }} selected{{/equals}}>累积礼</option> --}}
          </select>
        </div>
      </div>
    </div>
    <div class="form-right">
      <div class="control-group">
        <label class="group-title line-title">自营平台：</label>
        <div class="group-content">
          <input type="text" id="input-unit" class="input-text" name="selfPlatformLink" placeholder="请输入自营平台链接"
            value="{{item.extra.selfPlatformLink}}" />
        </div>
      </div>
      <div class="control-group">
        <label class="group-title line-title">商品代码(货号)：</label>
        <div class="group-content">
          <input type="text" id="input-itemCode" class="input-text" name="itemCode" maxLength="200"
            placeholder="请输入商品代码" value="{{item.itemCode}}" />
        </div>
      </div>
      <div class="control-group">
        <label class="group-title line-title">商品条形码：</label>
        <div class="group-content">
          <input type="text" id="input-barCode" class="input-text" name="barCode" maxLength="200" placeholder="请输入商品条形码"
            value="{{item.barCode}}" />
        </div>
      </div>
      <div class="control-group">
        <label class="group-title line-title">产地：</label>
        <div class="group-content">
          <input type="text" id="input-unit" class="input-text js-group-item" data-group="BASIC" name="origin"
            maxLength="200" placeholder="请输入产地" data-key="origin" {{#each otherAttrs}} {{#equals group 'BASIC' }}
            {{#each otherAttributeWithRules}} {{#equals attributeRule.attrKey 'origin' }}value="{{attrVal}}" {{/equals}}
            {{/each}} {{/equals}} {{/each}} />
        </div>
      </div>
      <div class="control-group last-line">
        <label class="group-title line-title">重量：</label>
        <div class="group-content">
          <input type="text" id="input-unit" class="input-text js-group-item" data-group="BASIC" name="weight"
            maxLength="200" placeholder="请输入重量" data-key="weight" {{#each otherAttrs}} {{#equals group 'BASIC' }}
            {{#each otherAttributeWithRules}} {{#equals attributeRule.attrKey 'weight' }}value="{{attrVal}}" {{/equals}}
            {{/each}} {{/equals}} {{/each}} />
        </div>
      </div>
      <div class="control-group">
        <label class="group-title line-title">发货地</label>
        <div class="group-content">
          <input type="text" class="input-text" name="countryOfDeparture" placeholder="请输入起运国"
            value="{{item.extra.countryOfDeparture}}" />
        </div>
      </div>
      <div class="control-group subStoreMode" style="display:none;">
        <label class="group-title line-title">参与积分活动：</label>
        <div class="group-content ">
          <select name="tips" data-value="{{item.tips}}">
            <option value="0" {{#equals item.tips '0' }} selected{{/equals}}>否</option>
            <option value="1" {{#equals item.tips '1' }} selected{{/equals}}>是</option>
          </select>
        </div>
      </div>
      <div class="control-group ladderDistributionMode">
        <label class="group-title line-title">产品有效期：</label>
        <div class="group-content ">
          <span class="input-pair">
            <input class="input-text datepicker" type="text" name="productionDateStart"
              value="{{item.extra.productionDateStart}}" placeholder="开始日期" style="width:90px" />
            <span class="spacing">-</span>
            <input class="input-text datepicker" type="text" name="productionDateEnd"
              value="{{item.extra.productionDateEnd}}" placeholder="结束日期" style="width:90px" />
          </span>&nbsp;&nbsp;<span class="hint--bottom" aria-label="区间设置规则：2018年11月04日 至 2019年02月03日；"><i
              class="icon-feebas icon-feebas-warning-fill js-show-advertise-tip"></i></span>
        </div>

      </div>
      <div class="control-group ladderDistributionMode" style="display:none;">
        <label class="group-title line-title" style="width: 100px;">商品分享背景图：</label>
        <img id="shop-logo" class="miniAppImg logo {{#if item.extra.itemBgImageUrl}}{{else}}disappear{{/if}}"
          src="{{#if item.extra.itemBgImageUrl}}{{item.extra.itemBgImageUrl}}{{/if}}">
        <input name="itemBgImageUrl" type="hidden" value="{{item.extra.itemBgImageUrl}}">
        <div class="btn btn-upload" data-type="3"
          style="{{#if item.extra.itemBgImageUrl}}{{else}}margin-left: 110px;{{/if}};">
          <input type="file" name="file">
          <!-- <span class="select-image-text">{{i18n "Select Image" bundle="shop"}}</span>-->
          <span class="">+</span>
        </div>
        <span class="hint--bottom" aria-label="请上传分辨率为750*1334的图片" style="vertical-align: top;">
          <i class="icon-feebas icon-feebas-warning-fill "></i>
        </span>
      </div>
      <!--<div class="control-group last-line" style="display:none;">
        <label class="group-title line-title">标签：</label>
        <div class="group-content">
          <button class="btn btn-medium btn-info js-item-set-label ladder-distribution" name="label">查看设置标签</button>
        </div>
      </div>-->
    </div>

  </div>
</div>
<div class="form-section clearfix customs-declaration-info" style="display:{{#if item}}{{#equals item.isBonded " 0"}}
  none{{else}} block{{/equals}}{{else}}none{{/if}}">
  <span class="pull-left section-title">海关报关<br>信息</span>
  <input type="hidden" name="customId" value="{{skuWithCustoms.[0].skuCustom.id}}">
  <div class="pull-left section-content">
    <div class="control-group">
      <label class="attr-label"><b class="required">*</b>HS海关代码：</label>
      <div class="selectric-wrapper">
        <div class="group-content">
          <input type="text" id="input-hsCode" class="input-text is-filled" name="hsCode" maxLength="200"
            placeholder="请输入HS海关代码" value="{{skuWithCustoms.[0].skuCustom.hsCode}}" />
          <span class="error-tip show"><b> 注意事项：海关代码填错会影响计税</b></span>
        </div>
      </div>
    </div>
    <div class="control-group">
      <label class="attr-label"><b class="required">*</b>商品原产地：</label>
      <div class="selectric-wrapper">
        <div class="group-content">
          <select id="customOriginId" name="customOriginId" tabindex="0"
            value="{{skuWithCustoms.[0].skuCustom.customOriginId}}">
            <option value="{{skuWithCustoms.[0].skuCustom.customOriginId}}">请选择</option>
          </select>
        </div>
      </div>
    </div>
    <div class="control-group">
      <label class="attr-label"><b class="required">*</b>税金的承担方：</label>
      <div class="selectric-wrapper">
        <div class="group-content">
          <select id="customTaxHolder" name="customTaxHolder" tabindex="0"
            value="{{skuWithCustoms.[0].skuCustom.customTaxHolder}}">
            <option value="">请选择</option>
            <option value="1" {{#equals skuWithCustoms.[0].skuCustom.customTaxHolder '1' }} selected{{/equals}}>买家承担
            </option>
            <option value="2" {{#equals skuWithCustoms.[0].skuCustom.customTaxHolder '2' }} selected{{/equals}}>商家承担
            </option>
          </select>
        </div>
      </div>
    </div>
    <div class="control-group">
      <label for="province"><span class="required">*</span>所在地:</label>
      <div class="selectric-wrapper">
        <div class="group-content">
          <select id="provinceId" name="provinceId" class="address-select" data-level="1">
            <option value="{{skuWithCustoms.[0].skuCustom.provinceId}}">请选择</option>
          </select>
          <select id="cityId" name="cityId" class="address-select" data-level="2">
            <option value="{{skuWithCustoms.[0].skuCustom.cityId}}">请选择</option>
          </select>

        </div>
      </div>
    </div>
  </div>
</div>
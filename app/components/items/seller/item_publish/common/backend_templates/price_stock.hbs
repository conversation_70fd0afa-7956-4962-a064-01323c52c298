<div class="form-section clearfix">
  <span class="pull-left section-title">价格库存</span>
  <div class="pull-left section-content no-bg sku-info-area" id="js-sku-info-area">
    <div id="js-price-and-stock" class="js-sku-table-area">
      {{> component:items/seller/item_publish/common/all_templates/_sku_table}}
    </div>
    <div id="js-sku-table-area" class="js-sku-table-area" {{#if skuWithCustoms}}data-sku="{{json skuWithCustoms}}"
      {{/if}}></div>
    <p class="error-tip">
      <i class="icon-feebas icon-feebas-alert-fill"></i> 请至少填写一个销售价
    </p>
  </div>
</div>
<div class="form-section clearfix">
  <span class="pull-left section-title">活动销售价</span>
  <div class="control-group" style="margin-top:0px;">
    <span class="input-pair">
      {{#if skuWithCustoms}}
        {{#each skuWithCustoms}}
          <input class="input-text js-need-validated control-price" name="activitySalesPrice" type="text"
                 value="{{formatPrice this.sku.extra.activitySalesPrice}}" oninput="if(value.length>11) value=value.slice(0,11)"
                 onkeyup="this.value=this.value.replace(/[^\d\.]/g,'')" pattern="^\d{1,8}(\.\d{0,2})?$" />
          <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字"/>
        {{/each}}
      {{else}}
        <input class="input-text js-need-validated control-price" name="activitySalesPrice" type="text"
               value="" oninput="if(value.length>11) value=value.slice(0,11)"
               onkeyup="this.value=this.value.replace(/[^\d\.]/g,'')" pattern="^\d{1,8}(\.\d{0,2})?$" />
          <span class="note-error note-icon hint--top" aria-label="请输入最大8位的数字"/>
      {{/if}}
    </span>
  </div>
</div>

<div class="form-section clearfix">
  <span class="pull-left section-title">生效日期：</span>
  <div class="control-group" style="margin-top:0px;">
    <span class="input-pair">
   {{#if skuWithCustoms}}
     {{#each skuWithCustoms}}
         <input class="input-text activityDatepicker" name="moneyActivityStartTime" type="text"
                value="{{this.sku.extra.activityStartTime}}" placeholder="开始日期" />
      <span class="spacing">-</span>
      <input class="input-text activityDatepicker" name="moneyActivityEndTime" type="text"
             value="{{this.sku.extra.activityEndTime}}" placeholder="结束日期" />
       </span>
     {{/each}}
   {{else}}
     <input class="input-text activityDatepicker" name="moneyActivityStartTime" type="text"
            value="" placeholder="开始日期" />
     <span class="spacing">-</span>
     <input class="input-text activityDatepicker" name="moneyActivityEndTime" type="text"
            value="" placeholder="结束日期" />
     </span>
   {{/if}}


    <div style="margin-top:6px;color:#afafaf; margin-left: 79px;">请输入时间格式为YYYY-MM-dd HH:mm:ss 例：2000-01-01 11:03:03
    </div>

  </div>

</div>
<div class="form-section clearfix commission-info">
  <span class="pull-left section-title">服务费设置</span>
  <div class="pull-left section-content">
    <div class="control-group">
      <label class="attr-label"><b class="required">*</b>开启单品服务费：</label>
      <label class="label-list"><input name="isCommission" type="radio" value="0" {{#equals isCommission
          0}}checked{{/equals}} {{#if isCommission}}{{else}}checked{{/if}}>关闭</label>
      <label class="label-list"><input name="isCommission" type="radio" value="1" data-flag="{{isCommission}}" {{#equals
          isCommission 1}}checked{{/equals}}>开启</label>
    </div>
    <div class="control-group comissionSetId" style="display:{{#if isCommission}} block {{else}}none{{/if}}">
      <label class="attr-label"><b class="required">*</b>服务费类型：</label>
      {{!-- <label class="label-list"><input name="setStoreProfitWay" type="radio" value="false" data-flag="{{firstRate}}"
          {{#equals flag 0}}checked{{/equals}} {{#if flag}}{{else}}checked{{/if}}>固定金额</label> --}}
      <label class="label-list"><input name="setStoreProfitWay" type="radio" value="true" {{#equals flag
          1}}checked{{/equals}}>按比例</label>

    </div>
    <div class="control-group comissionSetId" data-data="{{json activityCommissionConfig}}" style="display:{{#if isCommission}} block {{else}}none{{/if}}; margin-left: 20px;">
      <label class="attr-label">生效日期：</label>
        <span class="input-pair">
          <input class="input-text activityDatepicker" type="text" name="matchingStartTimeString"
            value="{{activityCommissionConfig.matchingStartTimeString}}" placeholder="开始日期" />
          <span class="spacing">-</span>
          <input class="input-text activityDatepicker" type="text" name="matchingEndTimeString"
            value="{{activityCommissionConfig.matchingEndTimeString}}" placeholder="结束日期" />
        </span>
      <div style="margin-top:6px;color:#afafaf; margin-left: 110px;">请输入时间格式为YYYY-MM-dd HH:mm:ss 例：2000-01-01 11:03:03
      </div>
    </div>
    <div style="display: flex;">
      <div class="control-group comissionSetId" style="display:{{#if isCommission}} block {{else}}none{{/if}}">
        <label class="attr-label"><b class="required">*</b>单独服务费设置：</label>
        <div class="selectric-wrapper">
          <div class="group-content" style="padding-left: 1px;">
            <div class="am-u-sm-9 am-u-md-6 am-u-lg-5 am-u-end">
              <div class="am-input-group am-margin-bottom">
                <span class="am-input-group-label am-input-group-label__left" id="serviceProviderName">服务商服务费：</span>
                {{#if flag}}
                <input type="text" name="serviceProviderRate"
                  value="{{#if serviceProviderRate}}{{formatPrice serviceProviderRate}}{{else}}0{{/if}}"
                  class="am-form-field item-commission-single" style="margin-top: 4px;">
                {{else}}
                <input type="text" name="serviceProviderRate"
                  value="{{#if serviceProviderFee}}{{formatPrice serviceProviderFee}}{{else}}0{{/if}}"
                  class="am-form-field item-commission-single" style="margin-top: 4px;">
                {{/if}}

                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right profitRate">{{#equals
                  flag 1}}%{{else}}元{{/equals}} </span>
              </div>
              <div class="am-input-group am-margin-bottom">
                <span class="am-input-group-label am-input-group-label__left" id="firstLevelName">一级服务费：</span>
                {{#if flag}}<input type="text" name="firstRate"
                  value="{{#if firstRate}}{{formatPrice firstRate}}{{else}}0{{/if}}"
                  class="am-form-field am-field-valid item-commission-single" style="margin-top: 4px;">{{else}}
                <input type="text" name="firstRate" value="{{#if firstFee}}{{formatPrice firstFee }}{{else}}0{{/if}}"
                  class="am-form-field am-field-valid item-commission-single" style="margin-top: 4px;">
                {{/if}}
                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right profitRate">{{#equals
                  flag 1}}%{{else}}元{{/equals}} </span>
              </div>
              <div class="am-input-group am-margin-bottom">
                <span class="am-input-group-label am-input-group-label__left" id="secondLevelName">二级服务费：</span>
                {{#if flag}}<input type="text" name="secondRate"
                  value="{{#if secondRate}}{{formatPrice secondRate}}{{else}}0{{/if}}" class="am-form-field item-commission-single"
                  style="margin-top: 4px;">{{else}}
                <input type="text" name="secondRate" value="{{#if secondFee}}{{formatPrice secondFee}}{{else}}0{{/if}}"
                  class="am-form-field item-commission-single" style="margin-top: 4px;">
                {{/if}}

                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right profitRate">{{#equals
                  flag 1}}%{{else}}元{{/equals}} </span>
              </div>

              <!-- <div class="am-input-group am-margin-bottom showPullNewCommission"  style="display:none;">
              <span class="am-input-group-label am-input-group-label__left">新客服务费：</span>
              <input type="text" name="firstCommission" value="{{#if firstCommission}}{{formatPrice firstCommission}}{{else}}0{{/if}}" class="am-form-field"  style="margin-top: 4px;">
              <span class="widget-dealer__unit am-input-group-label am-input-group-label__right">
                                               元                                           </span>
            </div>-->
              <div class="am-input-group am-margin-bottom showPullNewCommission" style="display:none;">
                <span class="am-input-group-label am-input-group-label__left">忠诚服务费：</span>
                <input type="text" name="commission"
                  value="{{#if commission}}{{formatPrice commission}}{{else}}0{{/if}}" class="am-form-field"
                  style="margin-top: 4px;">
                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right">
                  元 </span>
              </div>
              <div class="help-block">
                <p>
                  <small>注：如不开启单品服务费则默认使用全局服务费比例</small><br>
                  <small class="showPullNewCommission" style="display:none;">新客服务费全局设置需在【服务管理-服务费管理】中进行设置</small>
                </p>

              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="control-group comissionSetId" style="display:{{#if isCommission}} block {{else}}none{{/if}}">
        <label class="attr-label" style="width: 120px;">活动单独服务费设置：</label>
        <div class="selectric-wrapper">
          <div class="group-content" style="padding-left: 1px;">
            <div class="am-u-sm-9 am-u-md-6 am-u-lg-5 am-u-end">
              <div class="am-input-group am-margin-bottom">
                <span class="am-input-group-label am-input-group-label__left" id="serviceProviderName">服务商服务费：</span>
                {{#if flag}}
                <input type="text" name="activityServiceProviderRate"
                  value="{{#if activityCommissionConfig.serviceProviderRate}}{{formatPrice activityCommissionConfig.serviceProviderRate}}{{else}}0{{/if}}"
                  class="am-form-field item-commission-rate" style="margin-top: 4px;">
                {{else}}
                <input type="text" name="activityServiceProviderRate"
                  value="{{#if activityCommissionConfig.serviceProviderFee}}{{formatPrice activityCommissionConfig.serviceProviderFee}}{{else}}0{{/if}}"
                  class="am-form-field item-commission-rate" style="margin-top: 4px;">
                {{/if}}

                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right profitRate">{{#equals
                  flag 1}}%{{else}}元{{/equals}} </span>
              </div>
              <div class="am-input-group am-margin-bottom">
                <span class="am-input-group-label am-input-group-label__left" id="activityFirstRate">一级服务费：</span>
                {{#if flag}}<input type="text" name="activityFirstRate"
                  value="{{#if activityCommissionConfig.firstRate}}{{formatPrice activityCommissionConfig.firstRate}}{{else}}0{{/if}}"
                  class="am-form-field am-field-valid item-commission-rate" style="margin-top: 4px;">{{else}}
                <input type="text" name="activityFirstRate" value="{{#if activityCommissionConfig.firstFee}}{{formatPrice activityCommissionConfig.firstFee }}{{else}}0{{/if}}"
                  class="am-form-field am-field-valid item-commission-rate" style="margin-top: 4px;">
                {{/if}}
                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right profitRate">{{#equals
                  flag 1}}%{{else}}元{{/equals}} </span>
              </div>
              <div class="am-input-group am-margin-bottom">
                <span class="am-input-group-label am-input-group-label__left" id="activitySecondRate">二级服务费：</span>
                {{#if flag}}<input type="text" name="activitySecondRate"
                  value="{{#if activityCommissionConfig.secondRate}}{{formatPrice activityCommissionConfig.secondRate}}{{else}}0{{/if}}" class="am-form-field item-commission-rate"
                  style="margin-top: 4px;">{{else}}
                <input type="text" name="activitySecondRate" value="{{#if activityCommissionConfig.secondFee}}{{formatPrice activityCommissionConfig.secondFee}}{{else}}0{{/if}}"
                  class="am-form-field item-commission-rate" style="margin-top: 4px;">
                {{/if}}

                <span class="widget-dealer__unit am-input-group-label am-input-group-label__right profitRate">{{#equals
                  flag 1}}%{{else}}元{{/equals}} </span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>



<div class="form-section clearfix control-price-set" style="display: none;">
  <span class="pull-left section-title">控价设置</span>
  <div class="control-group" style="margin-top:0px;">
    <span class="input-pair">
      <input class="input-text js-need-validated control-price" type="text" name="lowPrice"
        value="{{formatPrice item.extra.minPrice}}" placeholder="输入最低价" style="width:90px"
        oninput="if(value.length>11) value=value.slice(0,11)" onkeyup="this.value=this.value.replace(/[^\d\.]/g,'')"
        pattern="^\d{1,8}(\.\d{0,2})?$" />
      <span class="spacing">-</span>
      <input class="input-text js-need-validated control-price" type="text" name="highPrice"
        value="{{formatPrice item.extra.maxPrice}}" placeholder="输入最高价" style="width:90px"
        oninput="if(value.length>11) value=value.slice(0,11)" onkeyup="this.value=this.value.replace(/[^\d\.]/g,'')"
        pattern="^\d{1,8}(\.\d{0,2})?$" />
      <span class="note-error note-icon hint--top" aria-label="请输入最多10位的数字,小数位最多2位" style="padding-left: 33px;">
        <i class="icon-pokeball icon-pokeball-error "></i>
      </span>
    </span>



    <div style="margin-top:6px;color:#afafaf; margin-left: 66px;">设置分销商加价的限价限制</div>
  </div>

</div>

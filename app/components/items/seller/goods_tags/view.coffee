###
  用户标签
###
Pagination = require "pokeball/components/pagination"
Modal = require "pokeball/components/modal"
Tip = require "common/tip_and_alert/view"
CommonDatepicker = require("utils/module").plugins.commonDatepicker

changeTypeTemplate = Handlebars.templates["shop/user_manage/frontend_templates/change_type"]
setStoreBindTemplate = Handlebars.templates["shop/user_manage/frontend_templates/set-store-bind"]

class UserTag
  constructor: ->
    @statusChange = ".js-status-change"
    @typeChange = ".js-type-change"
    @changeTypeForm = ".change-type-form"
    @statusBatchFrozen = ".js-batch-status-frozen"
    @guiderBatchSet = ".js-batch-set-guider"
    @guiderSet = ".js-set-guider"
    @statusBatchUnfreeze = ".js-batch-status-unfreeze"
    @statusBatchAll = ".batch-checkbox-all"
    @statusBatch = ".batch-checkbox"
    @typeChangeModal=""
    @bindEvent()

  bindEvent: ->
    $(".datepicker").datepicker({maxDate: new Date(), yearRange: [1984, new Date().getFullYear()]})
    new CommonDatepicker(@$el)
    pageSize = if _.isNumber($.query.get("pageSize")) then $.query.get("pageSize") else 20
    new Pagination(".users-pagination").total($(".users-pagination").data("total")).show(pageSize, {num_display_entries: 5, jump_switch: true, page_size_switch: true, maxPage: -1})
    $(document).on "click", @statusChange, @userStatusChange
    #$(document).on "click", @typeChange, @userTypeChange
    $(document).on "click", @statusBatchFrozen, @userStatusBatchFrozen
    $(document).on "click", @guiderBatchSet, @batchSetGuider
    $(document).on "click", @guiderSet, @userTypeChange
    $(document).on "click", @statusBatchUnfreeze, @userStatusBatchUnfreeze
    $(document).on "click", @statusBatchAll, @setAllChecked
    $(document).on "click", @statusBatch, @setChecked
    $(document).on "submit", @changeTypeForm, @saveTypeChange
    @fileUpload()
    @$el.find(".js-bind-store").on "click", (evt) => @setStoreBind(evt)
    if sessionStorage.shopId=='30' #坦图门店分销小程序要去掉导购
      @$el.find(".js-set-guider").css("display","none")
      @$el.find(".js-bind-store").css("display","none")
    if sessionStorage.salesPattern=="ladderDistribution"
      $(".td-operation").hide()
    if sessionStorage.salesPattern=="subStore"
      $(".td-operation").show()

  #调整用户状态
  userStatusChange: (evt)=>
    @batchStatus($(evt.currentTarget).data("change-status"), [$(evt.currentTarget).closest("tr").data("user-id")])

  #批量处理用户状态
  batchStatus: (newStatus, ids)->
    $("body").spin("big")
    ids = ids || ""
    if !ids
      ids = []
      $.each $(".batch-checkbox:checked"), (i, d)->
        ids[i] = $(@).closest("tr").data("user-id")
    $.ajax
      url: "/api/user/statuses"
      type: "POST"
      data: {ids: ids, status: newStatus}
      success: ->
        window.location.reload()

  #批量冻结用户
  userStatusBatchFrozen: =>
    if !$(".batch-checkbox:checked").length
      new Modal
        icon: "info"
        title: "提示"
        content: "您至少需要选择一个会员"
      .show()
    else
      @batchStatus(-2)

  #批量解冻用户
  userStatusBatchUnfreeze: =>
    if !$(".batch-checkbox:checked").length
      new Modal
        icon: "info"
        title: "提示"
        content: "您至少需要选择一个会员"
      .show()
    else
      @batchStatus(1)

#批量设置导购
  batchSetGuider: =>
    if !$(".batch-checkbox:checked").length
      new Modal
        icon: "info"
        title: "提示"
        content: "您至少需要选择一个用户"
      .show()
    else
      $("body").spin("big")
      ids = []
      $.each $(".batch-checkbox:checked"), (i, d)->
        ids[i] = $(@).closest("tr").data("user-id")
      $.ajax
        url: "/api/subStore/member/set-guider"
        type: "POST"
        data: {ids: ids, status: newStatus}
        success: ->
          window.location.reload()

  ###*
   * 更改用户类型对话框
   * @return {[type]} [description]
  ###
  userTypeChange: (event) =>
    @typeChangeModal = new Modal changeTypeTemplate({data: $(event.target).data()})
    @typeChangeModal.show()
    $(@changeTypeForm).validator()

  ###*
   * 更改用户类型
   * @return {[type]} [description]
  ###
  saveTypeChange: (event) =>
    event.preventDefault()
    id = $(event.target).data("id")
    name = $(event.target).find('[name=name]').val()
    $.ajax
      url: "/api/subStore/member/set-guider"
      type: "POST"
      data: {userId: id, name: name}
      success: (data) =>
        @typeChangeModal.close()
        new Modal
          icon: "success"
          title: "设置成功"
          content: "设置导购员成功！"
        .show()
        return $("body").overlay(false)

  #全选
  setAllChecked: (evt)=>
    $(@statusBatch).prop("checked", $(evt.currentTarget).prop("checked"))

  #选择
  setChecked: (evt)=>
    if !$(evt.currentTarget).prop("checked")
      $(@statusBatchAll).prop("checked", false)
    else
      if $(@statusBatch).length is $(".batch-checkbox:checked").length
        $(@statusBatchAll).prop("checked", true)

  # 绑定导购员所属门店
  setStoreBind:(evt)=>
    userId = $(evt.currentTarget).data("id")
    evt.stopPropagation()
    setItemBd = $(setStoreBindTemplate({
      data: { userId }
    }))
    window.setStoreBindModal = new Modal(setItemBd)
    window.setStoreBindModal.show()
    this.getSuggest(evt)
    $("#set-store-bind-form").validator()
    $("#set-store-bind-form").on("submit", (evt) => this.submitStoreBind(evt))

  submitStoreBind:(evt)=>
    evt.preventDefault()
    $form = $("#set-store-bind-form").serializeArray()
    data = {}
    $.each $form, (i,d)=>
      data[d.name] = d.value

    type = "post"
    if !data.shopId
      new Modal({ icon: "error", content: "请选择搜索的门店" , title: "设置失败" }).show()
      return false
    postData={subStoreId:data.shopId,userId:data.userId}
    $("body").spin("medium")
    $.ajax
      url: "/api/subStore/member/allocate-guider",
      type: type,
      data: postData,
      success: (data) =>
        if(data==true)
            window.setStoreBindModal.close()
            new Modal({
              icon: "failure",
              title: "绑定成功",
              content: "门店绑定成功"
            }).show()
            return $("body").overlay(false)
        else
          window.setStoreBindModal.close()
          new Modal({
            icon: "error",
            title: "绑定失败",
            content: "门店绑定失败"
          }).show()
          return $("body").overlay(false)
      error:  (data)=>
         new Modal({ icon: "error", content: "绑定失败," + data.responseJSON.message, title: "绑定失败" }).show()

  fileUpload: ()=>
    $("input[name=file]").fileupload
      url: "/api/seller/tag/user/import"
      dataType: "json"
      done: (evt, data) =>
        thisButton = $(evt.target).closest(".btn")
        if(data.result)
          new Modal({icon: "error", content: "上传成功", title: "上传成功"}).show()
        if(!data.result)
          new Modal({icon: "error", content: "上传文件内容错误", title: "上传失败"}).show()
      error: (data) =>
        new Modal({icon: "error", content: data.responseJSON.message, title: "上传失败"}).show()

  getSuggest:(event)=>
    sug=$("#set-store-bind-form").find("input[name='shopName']")
    $("#set-store-bind-form").find("input[name='shopName']").suggest({
      url: "/api/supplier/subStore-page?name=",
      dataFormat: ((data) =>
        result = []
        if data.data.length==0
          result.push('')
          sug.data("result", {})
        _.map(data.data, (i) =>
          return result.push(i.name)
        )
        sug.data("result", result)
        return result
      ),
      callback: (text) =>

        $.each(sug.data("source").data, (i, d) =>
          if d.name == text
            sug.val(d.name)
            $("#set-store-bind-form").find("input[name='shopId']").val(d.id)
            return false

        )

    })
module.exports = UserTag

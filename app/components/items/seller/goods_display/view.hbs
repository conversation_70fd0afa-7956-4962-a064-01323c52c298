{{#component "item-display component-standard-container js-comp"}}
  
  <div class="component-body">
    <div class="search-item">
      <div id="block" class="block filter-block">
        <form class="form js-standard-filter-form">
          <fieldset>
            <span class="span3">
              <label for="">服务商名称 <input type="text" class="item-filter-item" name="serviceProviderName" value="{{serviceProviderName}}"
                                       placeholder="服务商名称">
              </label>
            </span>
            <span class="pull-right">
              <label for="filter">
                <button type="submit" class="btn btn-secondary js-item-filter">搜索</button>
                <button type="button" class="btn btn-info js-filter-reset">重置</button>
              </label>
            </span>
          </fieldset>
        </form>
      </div>
    </div>
    <div class="component-header">
      <button class="btn btn-primary js-create-item-area">新增</button>
      <button class="btn btn-primary js-batchAuthItem-sub-store">批量授权商品</button>
    </div>
    <div class="item-list">
      <table class="table goods-display-table" data-id="{{json _DATA_}}">
        <thead>
        <tr>
          <th width="200" class="left-text">
           授权服务商
          </th>
          <th width="240">展示商品</th>
          <th width="110" class="td-operation">{{i18n "Operation" bundle="items"}}</th>
        </tr>
        </thead>
        <tbody data-data="{{json _DATA_.data.data}}">
        {{#each _DATA_.data.data}}
          <tr data-id="{{areaId}}">
            <td class="left-text">
              {{#if area}}{{area}}
              {{else}}未选择服务商
              {{/if}}
              <a href="javascript:void(0)" class="js-edit-item-area" data-id="{{areaId}}">编辑</a>
            </td>
            <td data-id="{{areaId}}">
              {{#if item}}{{item}}
              {{else}}未选择商品
              {{/if}}
              <a href="javascript:void(0)" class="js-edit-item-goods" data-id="{{areaId}}">编辑</a>
            </td>
            <td class="td-operation">
              <a class="js-delete-item-area" data-toggle="confirm"
                 id="{{areaId}}"
                 data-title="{{i18n "Are you sure to delete it?" bundle="items"}}"
                 data-content="{{i18n "Cannot be restored after deleting, confirm delete?" bundle="items"}}"
                 data-event="confirm:delete-one">
                {{i18n "Delete" bundle="items"}}
              </a>

            </td>
          </tr>
        {{/each}}
        </tbody>
        <tfoot>
        <tr>
          <td colspan="3" class="td-operation">
            <div class="area-item-pagination" data-total="{{_DATA_.data.total}}" data-size="{{#if pageSize ~}}{{pageSize}}{{else}}20{{~/if}}"></div>
          </td>
        </tr>
        </tfoot>
      </table>
    </div>
  </div>
{{/component}}

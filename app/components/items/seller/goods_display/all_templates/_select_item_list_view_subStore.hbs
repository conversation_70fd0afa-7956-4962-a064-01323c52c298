{{#each data.list.data}}
  <tr data-id="{{id}}" data-data={{select}}>
    <td>
      <input type="checkbox" class="js-subStore-item-select input-checkbox" autocomplete="off"
             {{#equals select true}}checked{{/equals}}>
    </td>
    <td class="left-text">
      <a href="/items/{{id}}" target="_blank">{{name}}</a><br>
    </td>
    <td>{{itemCode}}</td>
    <td>{{id}}</td>
    <td>
              <span class="currency">
                {{#equals lowPrice highPrice}}
                  {{formatPrice lowPrice}}
                {{else}}
                  {{formatPrice lowPrice}}-{{formatPrice highPrice}}
                {{/equals}}
              </span>
    </td>
    <td>{{stockQuantity}}</td>
    <td>{{#if saleQuantity}}{{saleQuantity}}{{else}}0{{/if}}</td>
    <td>
      {{#equals status "1"}}{{i18n "List" bundle="items"}}{{/equals}}
      {{#equals status "-1"}}{{i18n "Unlist" bundle="items"}}{{/equals}}
      {{#equals status "0"}}{{i18n "Unlist" bundle="items"}}{{/equals}}
      {{#equals status "-2"}}{{i18n "frozen" bundle="items"}}{{/equals}}
    </td>
    <td>  {{#equals isBonded "1"}}跨境保税{{/equals}}
      {{#equals isBonded "0"}}一般贸易{{/equals}}
    </td>
  </tr>
{{/each}}


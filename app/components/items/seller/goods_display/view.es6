const Pagination = require("pokeball").Pagination;
const Modal = require("pokeball").Modal;
const childGoodsDisplay =
  Handlebars.partials[
    "items/seller/goods_display/all_templates/_select_item_list_view"
  ];
const childSubStoreGoodsDisplay =
  Handlebars.partials[
    "items/seller/goods_display/all_templates/_select_item_list_view_subStore"
  ];
const childGoodsFooter =
  Handlebars.partials[
    "items/seller/goods_display/all_templates/_select_item_list_footer"
  ];
const childServiceView =
  Handlebars.partials[
    "items/seller/goods_display/all_templates/_select_service_view"
  ];
const childSubStoreView =
  Handlebars.partials[
    "items/seller/goods_display/all_templates/_select_subStore_view"
  ];
const labelCheckboxTemplate =
  Handlebars.templates[
    "items/seller/goods_display/frontend_templates/label_checkbox"
  ];
  
const subStoreCheckboxTemplate =
  Handlebars.templates[
    "items/seller/goods_display/frontend_templates/subStore_checkbox"
  ];
const selectItemViewTemplate =
  Handlebars.templates[
    "items/seller/goods_display/frontend_templates/select_item_view"
  ];
const selectSubStoreItemViewTemplate =
  Handlebars.templates[
    "items/seller/goods_display/frontend_templates/select_item_view_subStore"
  ];
// jquery = require("jquery@3.3.1")
// let autocomplete = require("jquery-autocomplete-js")
class GoodsDisplay {
  constructor($) {
    this.target = this.$el;
    this.createItemArea = $(".js-create-item-area");
    this.jsDeleteItemArea = $(".js-delete-item-area");
    this.jsEditItemGoods = $(".js-edit-item-goods");
    this.jsSubStoreEditItemGoods = $(".js-batchAuthItem-sub-store");
    this.jsEditItemArea = $(".js-edit-item-area");
    this.areaItemPagination = $(".area-item-pagination");
    this.subStoreSelectItemId = [];
    this.selectGoods = [];
    this.pageNo = 1;
    this.pageSize = 10;
    this.areaId = null;
    this.serachGoodsParam = {};
    this.cachePageInfo = [];
    this.bindEvent();
  }

  bindEvent() {
    new Pagination(this.areaItemPagination)
      .total(this.areaItemPagination.data("total"))
      .show(this.areaItemPagination.data("size"), {
        num_display_entries: 3,
        jump_switch: true,
        page_size_switch: true,
        maxPage: -1,
      });
    this.createItemArea.on("click", (evt) => this.selectArea(evt));
    this.jsEditItemArea.on("click", (evt) => this.selectArea(evt));
    this.jsDeleteItemArea.on("click", (evt) => this.deleteAreaItem(evt));
    this.jsEditItemGoods.on("click", (evt) => this.editItemGoods(evt));
    this.jsSubStoreEditItemGoods.on("click", (evt) =>
      this.editSubStoreItemGoods(evt)
    );
    $(document).on("change", ".js-batch-select", (evt) =>
      this.selectBatch(evt)
    );
    $(document).on("change", ".js-item-select", (evt) => this.selectItem(evt));
    $(document).on("submit", "#set-items-area-form", (evt) =>
      this.submitItemsAreaForm(evt)
    );
    $(document).on("submit", "#set-subStore-area-form", (evt) =>
      this.submitSubStoreAreaForm(evt)
    );
    $(document).on("submit", "#item-area-goods-form", (evt) =>
      this.submitSearchGoods(evt)
    );
    $(document).on("submit", "#item-area-goods-subStore-form", (evt) =>
      this.submitSubStoreSearchGoods(evt)
    );
    $(document).on("click", ".js-filter-clear", (evt) => this.clearFilter(evt));
    $(document).on("click", ".js-filter-reset", (evt) => this.resetFilter(evt));
    $(document).on("click", ".btn-submit-subStore", (evt) =>
      this.submitSubStore(evt)
    );
    $(document).on("click", "#js-province-search", (evt) =>
      this.searchProvince(evt)
    );
    $(document).on("click", "#js-province-subStore-search", (evt) =>
      this.subStoreProvince(evt)
    );
    $(document).on("click", ".js-subStore-item-select", (evt) =>
      this.clickSubStoreGoods(evt)
    );
  }

  //为地区选择商品
  submitItemGoods(selectItemId, checked) {
    let json = JSON.stringify({
      areaId: this.areaId,
      itemId: selectItemId,
      checked: checked,
    });
    $.ajax({
      url: "/api/sub-store/edit-area",
      type: "POST",
      contentType: "application/json",
      data: json,
      success: (data) => {
        return $("body").overlay(false);
      },
    });
  }

  submitSubStore(evt) {
    _.forEach($("input.js-subStore-item-select"), (item) => {
      if (!!$(item).prop("checked")) {
        const itemId = $(item).closest("tr").data("id");
        this.subStoreSelectItemId.push(itemId);
      }
    });
    $.ajax({
      url: "/api/sub-store/area-all",
      type: "GET",
      success: (data) => {
        const labelCheckbox = $(
          subStoreCheckboxTemplate({ data: { list: data.data } })
        );
        window.selectAreaModal = new Modal(labelCheckbox);
        window.selectAreaModal.show();
      },
    });
    //this.openModal(selectItemId)
  }
  // openModal(selectItemId){

  // }

  clickSubStoreGoods(evt) {
    let areaId = $(evt.currentTarget).closest("tr").data("id");
    let checked = $(evt.currentTarget).prop("checked");
    // debugger;
    if (checked) {
      this.selectGoods.push(areaId);
    } else {
      const nIndex = this.selectGoods.indexOf(areaId);
      this.selectGoods.splice(nIndex, 1);
    }
  }
  selectArea(evt) {
    let areaId = $(evt.currentTarget).closest("tr").data("id");
    if (areaId) {
      this.areaId = areaId;
    } else {
      this.areaId = null;
    }
    $.ajax({
      url: "/api/sub-store/area-all-name",
      type: "GET",
      success: (data) => {
        const labelCheckbox = $(
          labelCheckboxTemplate({ data: { list: data.data } })
        );
        window.selectAreaModal = new Modal(labelCheckbox);
        window.selectAreaModal.show();
      },
    });
  }
  searchProvince(evt) {
    let val = $("input[name='province']").val()
    $.ajax({
      url: `/api/sub-store/area-all-name?province=${val}`,
      type: "GET",
      success: (data) => {
        // debugger
        // const labelCheckbox = $(
        //   labelCheckboxTemplate({ data: { list: data.data } })
        // );
        $("#set-items-area-form .modal-body").html(
          childServiceView({ data: { list: data.data } })
        );
        // window.selectAreaModal = new Modal(labelCheckbox);
        // window.selectAreaModal.show();
      },
    });
  }
  subStoreProvince(evt) {
    let val = $("input[name='subProvince']").val()
    $.ajax({
      url: `/api/sub-store/area-all?province=${val}`,
      type: "GET",
      success: (data) => {
        $("#set-subStore-area-form .modal-body").html(
          childSubStoreView({ data: { list: data.data } })
        );
      },
    });
  }

  selectItem(evt) {
    const itemId = $(evt.currentTarget).closest("tr").data("id");
    let checked = $(evt.currentTarget).prop("checked");
    if (!checked) {
      $(".js-batch-select").prop("checked", false);
    } else {
      let checked =
        _.findIndex($("input.js-item-select"), (item) => {
          return !$(item).prop("checked");
        }) === -1;
      $(".js-batch-select").prop("checked", checked);
    }
    this.submitItemGoods([itemId], checked);
  }

  // 批量选择
  selectBatch(evt) {
    let checked = !!$(evt.currentTarget).prop("checked");
    let selectItemId = [];
    _.forEach($("input.js-item-select"), (item) => {
      const itemId = $(item).closest("tr").data("id");
      selectItemId.push(itemId);
    });
    $("input.js-item-select").prop("checked", checked);
    this.submitItemGoods(selectItemId, checked);
  }
  // 清除筛选
  clearFilter() {
    $("input.item-filter-item").val("");
  }
  // 清除筛选
  resetFilter() {
    $("input.item-filter-item").val("");
    window.location.href = window.location.pathname
  }
  /**
   * 删除地区配置
   * @param evt
   */
  deleteAreaItem(evt) {
    $(document)
      .off("confirm:delete-one")
      .on("confirm:delete-one", (event, data) => {
        $.ajax({
          url: "/api/sub-store/delete-area",
          type: "POST",
          data: { areaId: data },
          success: (data) => {
            new Modal({
              icon: "success",
              title: "提示信息",
              content: "删除成功",
            }).show();
            window.location.reload();
          },
        });
      });
  }

  //提交选择地区
  submitItemsAreaForm(evt) {
    evt.preventDefault();

    let data = $("#set-items-area-form").serializeObject();
    let val = $("input[name='province']").val()
    if (data.area.length > 1) {
      if (!val) {
        data.area.splice($.inArray("", data.area), 1);
      }
      if (this.areaId) {
        data.areaId = this.areaId;
      }
      if (!Array.isArray(data.area)) {
        data.area = [data.area];
      }
      $.ajax({
        url: "/api/sub-store/edit-area",
        type: "POST",
        contentType: "application/json",
        data: JSON.stringify(data),
        success: (data) => {
          window.location.reload();
          window.selectAreaModal.close();
          return $("body").overlay(false);
        },
        error(data) {
          new Modal({
            icon: "error",
            content: `添加地区失败,${data.responseJSON.message}`,
            title: "提示",
          }).show();
        },
      });
    } else {
      window.selectAreaModal.close();
    }
  }
  submitSubStoreAreaForm(evt) {
    evt.preventDefault();

    let data = $("#set-subStore-area-form").serializeObject();
    // debugger;
    if (data.area.length > 1) {
      let val = $("input[name='subProvince']").val()
      if (!val) {
        data.area.splice($.inArray("", data.area), 1);
      }
      let json = JSON.stringify({
        itemIdList: this.selectGoods,
        areaIdList: Array.isArray(data.area)?data.area:[data.area],
      });
      if (this.areaId) {
        data.areaId = this.areaId;
      }
      $.ajax({
        url: "/api/sub-store/batchAuthItem",
        type: "POST",
        contentType: "application/json",
        data: json,
        success: (data) => {
          window.location.reload();
          window.selectAreaModal.close();
          return $("body").overlay(false);
        },
        error(data) {
          new Modal({
            icon: "error",
            content: `添加商品失败,${data.responseJSON.message}`,
            title: "提示",
          }).show();
        },
      });
    } else {
      window.selectAreaModal.close();
    }
  }

  editSubStoreItemGoods(evt) {
    evt.preventDefault();
    this.serachGoodsItem = {};
    this.pageNo = 1;
    this.areaId = $(evt.currentTarget).data("id");
    this.selectAreaItem((data) => {
      const labelCheckbox = $(
        selectSubStoreItemViewTemplate({
          data: { list: data.data },
          pageSize: this.pageSize,
        })
      );
      window.selectAreaModal = new Modal(labelCheckbox);
      window.selectAreaModal.show(() => {}, {
        beforeClose: () => window.location.reload(),
      });
      let $item = $("#item-area-goods-subStore-form");
      $item.validator({
        isErrorOnParent: true,
      });
      this.initSelectPagination();
      this.initSubStoreSelectPagination();
      this.checkSelectAll(data);
    });
  }
  editItemGoods(evt) {
    evt.preventDefault();
    this.serachGoodsItem = {};
    this.pageNo = 1;
    this.areaId = $(evt.currentTarget).data("id");
    this.selectAreaItem((data) => {
      const labelCheckbox = $(
        selectItemViewTemplate({
          data: { list: data.data },
          pageSize: this.pageSize,
        })
      );
      window.selectAreaModal = new Modal(labelCheckbox);
      window.selectAreaModal.show(() => {}, {
        beforeClose: () => window.location.reload(),
      });
      let $item = $("#item-area-goods-form");
      $item.validator({
        isErrorOnParent: true,
      });
      this.initSelectPagination();
      this.initSubStoreSelectPagination();
      this.checkSelectAll(data);
    });
  }

  checkSelectAll(data) {
    // console.log("checkSelectAll", data.data.data)
    let findIndex = _.findIndex(data.data.data, (item) => {
      return !item.select;
    });
    let checked = data.data.data.length && findIndex === -1;
    $(".js-batch-select").prop("checked", checked);
  }

  /**
   * 搜索商品
   * @param evt
   */
  submitSearchGoods(evt) {
    evt.preventDefault();
    let data = $("#item-area-goods-form").serializeObject();
    this.pageNo = 1;
    this.serachGoodsParam = data;
    this.selectAreaItem((data) => {
      $(".item-area-table tbody").html(
        childGoodsDisplay({ data: { list: data.data } })
      );
      $(".item-area-table tfoot").html(
        childGoodsFooter({ data: { list: data.data }, pageSize: this.pageSize })
      );
      this.initSelectPagination();
      this.initSubStoreSelectPagination();
      this.checkSelectAll(data);
    });
  }
  submitSubStoreSearchGoods(evt) {
    evt.preventDefault();
    let data = $("#item-area-goods-subStore-form").serializeObject();
    this.pageNo = 1;
    this.serachGoodsParam = data;
    this.selectAreaItem((data) => {
      $(".item-area-table tbody").html(
        childSubStoreGoodsDisplay({ data: { list: data.data } })
      );
      $(".item-area-table tfoot").html(
        childGoodsFooter({ data: { list: data.data }, pageSize: this.pageSize })
      );
      this.initSelectPagination();
      this.initSubStoreSelectPagination();
      this.checkSelectAll(data);
    });
  }

  refreshSelectAreaItem(data) {
    this.checkSelectAll(data);
    $(".item-area-table tbody").html(
      childGoodsDisplay({ data: { list: data.data } })
    );
  }
  refreshSubStoreSelectAreaItem(data) {
    // debugger;
    this.checkSelectAll(data);
    $(".item-area-table tbody").html(
      childSubStoreGoodsDisplay({ data: { list: data.data } })
    );
    _.forEach($("input.js-subStore-item-select"), ($item) => {
      // debugger;
      const itemId = $($item).closest("tr").data("id");
      this.selectGoods.map((item) => {
        if (item == itemId) {
          console.log($(item),"1111111111111111111111111111111111");
          $($item).prop("checked", true);
        }
      });
    });
  }

  /**
   * 查询商品
   * @param fn
   */
  selectAreaItem(fn) {
    $.ajax({
      url: "/api/sub-store/item-search",
      type: "GET",
      data: _.extend(
        { areaId: this.areaId, pageNo: this.pageNo, pageSize: this.pageSize },
        this.serachGoodsParam
      ),
      success: (data) => {
        fn(data);
      },
    });
  }

  initSelectPagination() {
    let $area = $(".area-item-select-pagination");
    new Pagination($area).total($area.data("total")).show($area.data("size"), {
      num_display_entries: 3,
      jump_switch: false,
      page_size_switch: false,
      maxPage: -1,
      callback: (pageNo, pageSize) => {
        this.pageNo = pageNo + 1;
        return this.selectAreaItem((data) => {
          this.refreshSelectAreaItem(data);
        });
      },
    });
  }
  initSubStoreSelectPagination() {
    let $area = $(".area-item-subStore-select-pagination");
    new Pagination($area).total($area.data("total")).show($area.data("size"), {
      num_display_entries: 3,
      jump_switch: false,
      page_size_switch: false,
      maxPage: -1,
      callback: (pageNo, pageSize) => {
        this.pageNo = pageNo + 1;
        return this.selectAreaItem((data) => {
          this.refreshSubStoreSelectAreaItem(data);
        });
      },
    });
  }
}

module.exports = GoodsDisplay;

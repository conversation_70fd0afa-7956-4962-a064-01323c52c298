import area from "./area";
import CheckGroup from "./check-group";
const {Modal,Checkbox,Space,Button} = antd
const { useState,useEffect } = React;

const ALLNAME = [];
area.map((item)=>{
    if(Array.isArray(item.children)){
        item.children.map((obj)=>{
            ALLNAME.push(obj.name);
        })
    }
})

export default (props) => {
    const {visible,title,onCancel,value,onOk} = props;
    const [areaData,setAreaData] = useState([]);
    const [checkedAll,setCheckedAll] = useState(false);
    const [indeterminate,setIndeterminate] = useState(false)

    const changeAll = () => {
        if(!checkedAll){
            setAreaData(ALLNAME)
        } else {
            setAreaData([]);
        }
        setCheckedAll(!checkedAll);
    }

    const findAllCheckedAreaLength = (arr) => {
        return areaData.length;
    }

    const okHandle = () => {
        onOk([...areaData])
        onCancel()
    }

    useEffect(()=>{
        if(value && Array.isArray(value)){
            setAreaData(value)
        }
    },[value])

    return (
        <Modal
          visible={visible}
          title={title}
          width={1000}
          onCancel={onCancel}
          wrapClassName="area-config-modal"
          footer={
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <div>
              <Checkbox checked={checkedAll} indeterminate={indeterminate} onChange={changeAll}>
                全选
              </Checkbox>
              <span style={{color:'gray'}}>已选择{findAllCheckedAreaLength(areaData)}个区域</span>
              </div>
            
              <Space>
                <Button onClick={onCancel}>取消</Button>
                <Button type="primary" onClick={okHandle}>
                  确定
                </Button>
              </Space>
            </div>
          }
        >
          {
            area.map((item)=>{
                const allItemsName = item.children.map(item=>item.name);
                const result = allItemsName.filter((filterItem)=>{
                    for(let i=0;i<areaData.length;i++){
                        if(areaData[i] === filterItem){
                            return true;
                        }
                    }
                })
                return (<CheckGroup
                    title={item.name}
                    value={result}
                    plainOptions={item.children}
                    onChange={(res)=>{
                        console.log("res:",res);
                        let arr = [...areaData];
                        for(let i=0;i<res.length; i++){
                            if(!result.includes(res[i])){
                                arr.push(res[i]);
                            }
                        }
                        const filterArr = result.filter((obj)=>!res.includes(obj));
                        filterArr.map((obj)=>{
                            arr = arr.filter((name)=>name!==obj);
                        })
                        console.log("result:",arr);
                        setIndeterminate(arr.length !== ALLNAME.length)
                        setCheckedAll(arr.length === ALLNAME.length)
                        setAreaData(arr);
                    }}
                />)
            })
          }
        </Modal>
    )
}
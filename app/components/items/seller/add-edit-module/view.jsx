import EditArea from "./edit-area"
import AreaConfig from "./area-config";
import area from "./area";
import {getParam} from "./util"
const {Button, Typography, Form, Input, Space,Divider,message} = antd
const {EditOutlined}=icons
const {SearchList, Spa, SpaConfigProvider,lib} = dtComponents
const {useState,useEffect} = React;
const ALLNAME = [];
area.map((item)=>{
    if(Array.isArray(item.children)){
        item.children.map((obj)=>{
            ALLNAME.push(obj.name);
        })
    }
})


function Page() {
    const [open,setOpen] = useState(false);
    const [form] = Form.useForm();
    const [detail,setDetail] = useState();
    const [serviceAreaConcise, setServiceAreaConcise] = useState([]);
    const lookBol = !!getParam("look")
    function serviceAreaConciseView() {
        return (
            <div
                className="spaceBottom"
                style={{
                    minHeight: "200px"
                }}
            >
                {getServiceAreaConciseData()}
            </div>
        );
    }

    function getServiceAreaConciseData() {
        let keyArr = [];
        for (const key in serviceAreaConcise) {
            keyArr.push(key);
        }
        return area.map((ite, idx) => {
            // if(!ite.children || ite.children.length === 0){
            //     return null;
            // }
            let bol = true;
            const arr = [];
            ite.children.map((item, index) => {
                if(serviceAreaConcise.includes(item.name)){
                    arr.push(item.name);
                }
            })
            if(arr.length === 0){
                bol = false
            }
            return (
                <div key={ite} className="item">
                    { bol &&
                     <React.Fragment>
                       <div className={'item-title'}>{ite.name}</div>
                       <Space
                        className="spaceBoxBottom"
                        split={<Divider type="vertical" />}
                        size={"middle"}>
                        {ite.children.map((item, index) => {
                            if(serviceAreaConcise.includes(item.name)){
                                return (
                                    <div key={item.province}>
                                        <span>
                                            {item.name}
                                            {/* {!item.tag ? "(部分地区)" : ""} */}
                                        </span>
                                    </div>
                                );
                            }
                            return null
                        })}
                    </Space>
                     </React.Fragment>
                    }
                </div>
            );
        });
    }

    const getDetail = () => {
        const id = getParam("id");
        if(!id) return;
        $.ajax({
            url:`/api/restrictedSalesAreaTemplate/findDetail/${id}`,
            data: {
                id: id,
                shopId: sessionStorage.shopId
            },
            ContentType: "application/json",
            type: "POST",
            success: (data) =>{
                setDetail(data.data);
                form.setFieldsValue(data.data);
                setServiceAreaConcise(data.data.defaultOne === 2?data.data.allowProvince:ALLNAME)
            },
        })
    }

    const submit = () => {
        form.validateFields().then(res=>{
            if(serviceAreaConcise.length ===0){
                message.warning("至少需要配置一个区域");
                return
            }
            $.ajax({
                url: getParam("id")?'/api/restrictedSalesAreaTemplate/update':'/api/restrictedSalesAreaTemplate/create',
                data:JSON.stringify({
                    "id":getParam("id"),
                    "name": res.name,
                    "description": res.description,
                    "allowProvince": serviceAreaConcise,
                    shopId: sessionStorage.shopId
                }),
                contentType: "application/json",
                type: 'POST',
                success: (res) => {
                    if(res.code>= 0) {
                        message.success(getParam("id")?'编辑成功':"新增成功")
                      window.location.href="/seller/address-module"
                    } else {
                        message.warning(res.errorMsg)
                    }
                }
            })
        }).catch(err=>{
            console.log("err:",err)
        })
    }

    useEffect(()=>{
        getDetail()
    },[])

    const editBol = !!getParam('id')
    return <div>
      <Typography.Title level={5}>基础信息</Typography.Title>
        <Form form={form}
              style={{ maxWidth: 600 }}
              labelCol={{ span: 5 }}
              wrapperCol={{ span: 19 }}>
            <Form.Item label='模版名称' name='name' key={"name"} required rules={[{required:true,message:'模版名称是必填的'}]}>
                <Input maxLength={30} disabled={lookBol||!!detail && detail.defaultOne && detail.defaultOne === 1} style={{width:'100%'}}/>
            </Form.Item>
            <Form.Item label='模版说明' name='description' key={"description"} >
                <Input.TextArea maxLength={200} style={{width:'100%'}} disabled={lookBol} showCount/>
            </Form.Item>
        </Form>

        <EditArea
            title={"配送服务范围"}
            isHideRightOperation={false}
            rightOperationRender={
                lookBol?null:<EditOutlined
                    onClick={() => {
                        setOpen(true)
                    }}
               />
            }
            noBorder
        >
            {
                serviceAreaConcise.length>0 ?
                    serviceAreaConciseView():null
            }
        </EditArea>
        {
            lookBol? null:
            <Space>
                <Button type="primary" onClick={()=>{
                    submit()
                }}>确认</Button>
                <Button onClick={()=>{
                    window.history.back();
                }}>返回</Button>
            </Space>
        }

        <AreaConfig
            visible={open}
            onCancel={()=>setOpen(false)}
            title={'编辑可售区域'}
            value={serviceAreaConcise||[]}
            onOk={(res)=>{
                setServiceAreaConcise(res);
            }}
        />

    </div>
}

ReactDOM.render(
  <Page />, document.getElementById("add-edit-module")
);

/*
	详情页数据展示组件
	cols：非必填，详情页的列数，默认3列
	fromData: 必填，详情中各项数据的键名，长度等
	[
		{
			label: 必填，该数据的中文名称
			key：必填，该数据的键名
		}
	]
	param: 必填，详情的数据来源
*/

// import React, { Component } from "react";
const {useState,useEffect,Component} = React;
// import './edit-area.less'

class EditArea extends Component {
	constructor(props) {
		super(props);
		this.state = {
			param: {}
		};
		if (props.param) {
			Object.assign(this.state, { param: props.param });
		}
	}
	componentDidMount() {

	}
	render() {
		const { title, tip, className, rightOperationRender, noBorder, children, style, isHideRightOperation } = this.props;
		return (
			<div className={`div-editarea ${className} ${noBorder ? "div-editarea-noborder" : ''}`} style={style || {}}>
				{title !== null && <div className="editarea-top">
					<span className="editarea-title">{title}</span>
					{
						tip && <span className="editarea-tip">
							<span className="icon-font">&#xe796;</span>
							{tip}
						</span>
					}
					{
						rightOperationRender ?
							<span className="editarea-rightOperationRender">{rightOperationRender}</span> : ''
					}
				</div>}
				{children}
			</div>
		)
	}
}

export default EditArea;

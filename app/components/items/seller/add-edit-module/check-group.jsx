
const { useState,useEffect } = React;
const { Checkbox, Divider,Space } = antd;
const CheckboxGroup = Checkbox.Group;

const App = ({title,value,plainOptions,onChange}) => {
  const [checkedList, setCheckedList] = useState(value);
  const checkAll = plainOptions.length === checkedList.length;
  const indeterminate = checkedList.length > 0 && checkedList.length < plainOptions.length;
  const onGrouphange = (list) => {
    onChange(list)
    setCheckedList(list);
  };
  const onCheckAllChange = (e) => {
    const result = plainOptions.map(item=> item.name)
    onChange(e.target.checked ? result : [])
    setCheckedList(e.target.checked ? result : []);
  };

  useEffect(()=>{
    setCheckedList(value);
  },[value])
  return (
    <div>
      <Space>
        <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
            {title}
        </Checkbox>
        <Divider />
        <CheckboxGroup options={plainOptions.map(item=>item.name)} value={checkedList} onChange={onGrouphange} />
      </Space>
    </div>
  );
};
export default App;
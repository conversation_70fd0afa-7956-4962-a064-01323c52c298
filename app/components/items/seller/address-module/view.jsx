const {Button, Table,Switch,Modal,message} = antd
const {SearchList, Spa, SpaConfigProvider} = dtComponents
const {useState,useEffect,useRef} = React;
import area from "../add-edit-module/area";
const ALLNAME = [];
area.map((item)=>{
    if(Array.isArray(item.children)){
        item.children.map((obj)=>{
            ALLNAME.push(obj.name);
        })
    }
})


function Page() {
  const [open,setOpen] = useState(false)
  const searchListRef = useRef()
  const pagination = useRef({})

  const getConfig = async () => {
    const data = await new Promise((resolve, reject) => {
      $.ajax({
        url: "https://maria.yang800.com/api/data/v2/791/605",
        success: (res) => {
          resolve(res)
        }
      })
    })
    return data.data;
  };


  return <div style={{height:"100%"}}>
    <SearchList
      searchConditionConfig={{
        size: "middle",
      }}
      paginationConfig={{size: "small",showPosition:'bottom'}}
      scrollMode="tableScroll"
      ref={searchListRef}
      getConfig={getConfig}
      renderLeftOperation={()=>{
        return (<Button type="primary" onClick={()=>{
            // href="/items/{{id}}" target="_blank"
            location.href = "/seller/add-edit-module";
        }}>添加模版</Button>)
      }}
      
      tableCustomFun={{
        name:(row)=>{
            return <a href={`/seller/add-edit-module?id=${row.id}`}  target="_blank"></a>
        },
        statusFn: (row) => {
            return (<Switch
                checkedChildren="开启"
                unCheckedChildren="关闭"
                checked={row.status===1}
                onClick={()=>{

                }}
                onChange={(checked)=>{
                    if(row.defaultOne === 1){
                        Modal.info({
                            title:'提示',
                            content: '该模版为系统自动创建,不支持关闭启用状态'
                        })
                        return
                    }
                    $.ajax({
                        url: '/api/restrictedSalesAreaTemplate/switchStatus',
                        contentType: 'application/json',
                        type: 'POST',
                        data:JSON.stringify({
                            id: row.id,
                            status: checked?1:2,
                        }),
                        success: (res) => {
                            if(res.code >=0){
                                searchListRef.current.load();
                            } else {
                                message.error(res.errorMsg)
                            }

                        },
                        fail: (err)=>{
                            console.log(err);
                        }
                    })
                }}
            />)
        },
        operateFn: (row) => {
            if(row.defaultOne === 1) return <a onClick={()=>{
                Modal.info({
                    title:'提示',
                    content: '该模版为系统自动创建，不支持手动编辑'
                })
            }}>编辑</a>
            return (
                <a href={`/seller/add-edit-module?id=${row.id}`}>编辑</a>
            )
        },
        allowProvince: (row) => {
            return (<div style={{
                "white-space":"pre-wrap",
                width: "100%",
                "overflow-wrap": "break-word",
                overflow: "hidden",
            }}>
                { row.defaultOne !== 1? row.allowProvince : ALLNAME.join(',') }
            </div>)
        }
      }}
      pageLoadTarget={{
        convertPaginationParam: function (pagination) {
           return {currentPage: pagination.currentPage, pageSize: pagination.pageSize};
        },
        convertResponseData: (data,a) => {
            const pagination = searchListRef.current.getPanigation();
            return {
                dataList: data.data.data,
                page: {
                    currentPage: pagination.currentPage|| 1,
                    pageSize: pagination.pageSize||20,
                    totalPage: Math.floor(data.data.total/(pagination.pageSize||20)) || 1,
                    totalCount: data.data.total
                }
            };
        }
      }}
    />

  </div>
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {request: (params) => {
        const obj = Object.assign(params,{
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(params.data),
            shopId: sessionStorage.shopId
        })
        $.ajax(obj)
    }}
  })}>
    <Page/>
  </SpaConfigProvider>, document.getElementById("address-module-list")
);

import {useSpecColumn} from "../item-edit/hooks/useSpecColumn";
import {ItemPriceInput} from "../item-edit/component/base/item-price-input";
import request from "../../../utils/plugins/axios/request";

const {useState, useEffect} = React;
const {Modal, Table, Form, InputNumber, message, Input} = antd;

function BatchTable({inputColumn,form, specDetail, skuDetail}) {
  const [dataSource, setDataSource] =useState([])

  const {specs} = useSpecColumn({
    specDetail, skuDetail, afterReorderSkus: (newSkuDetail,dataSource) => {
      form.setFieldValue('skuWithCustoms', newSkuDetail)
      setDataSource([...dataSource])
    }
  })

  const columns = [
    ...specs.map(spec => ({
      title: spec.name,
      width: 120,
      fixed: "left",
      dataIndex: spec.name,
      render: (value, _, index) => {
        const span = dataSource[index][`${spec.name}Span`] || 0;
        return {children: value, props: {rowSpan: span}};
      }
    })),
    {
      title: '',
      width: 0,
      render: (_, record, index) => (
        <Form.Item
          name={['skuWithCustoms', index, 'sku', 'id']}
          hidden
          wrapperCol={{span: 24}}
        >
          <InputNumber/>
        </Form.Item>
      ),
    },
    inputColumn,

  ];
  return <Table columns={columns}
                scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0), y: 600}}
                dataSource={[...dataSource]}
                pagination={false}
                rowKey="id"
                bordered/>
}
function PackageBatchTable({inputColumn,form, skuDetail}) {
  const [dataSource, setDataSource] =useState(skuDetail)

  useEffect(() => {
    if (Array.isArray(skuDetail)) {
      form.setFieldValue('skuWithCustoms', skuDetail)
      setDataSource([...skuDetail])
    }
  }, [skuDetail])
  const columns = [
    {
      title: "组合名称",
      width: 120,
      render: (_, record) => {
        return record.sku.name;
      },
    },
    {
      title: '',
      width: 0,
      render: (_, record, index) => (
        <Form.Item
          name={['skuWithCustoms', index, 'sku', 'id']}
          hidden
          wrapperCol={{span: 24}}
        >
          <InputNumber/>
        </Form.Item>
      ),
    },
    inputColumn,

  ];
  return <Table columns={columns}
                scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0), y: 600}}
                dataSource={[...dataSource]}
                pagination={false}
                rowKey="id"
                bordered/>
}
function UpdateSkuModal({type, open, onClose, rowItem}) {
  const [itemDetail,setItemDetail] =  useState({specDetail:[],skuDetail:[]});
  const [form] = Form.useForm();
  const validatePriceNumber = (_, value) => {

    if (!value && value != 0) {
      return Promise.resolve();
    }
    if (value === 0 || value < 0) {
      return Promise.reject(new Error("请输入大于0的数字，整数位上限8位"));
    }
    const regex = /^(?:0|[1-9]\d{0,4})(?:\.\d{1,2})?$/ // 整数5 位 小数 2
    if (regex.test(`${value/100}`)) {
      return Promise.resolve()
    }
    return Promise.reject(new Error("请输入整数5位，小数2位的数字"))
  };
  useEffect(() => {
    if (open) {
      request({
        url: `/mall-admin/api/items/sku/info/${rowItem.id}`,
        method: 'POST',
        data: {},
        success: (res) => {
          setItemDetail({specDetail:[...res.itemSpecificationParams],
            skuDetail:(res.skus||[]).map(item=>{
              return {
                sku: item
              }
            })})
        }
      })
    }
    return ()=>{
      if(!open){
        setItemDetail({specDetail:[],skuDetail:[]})
        form.resetFields();
      }
    }
  }, [open])

  let title = '';
  let inputColumn = {}
  switch (type) {
    case 'price':
      title = '调整销售价';
      inputColumn = {
        title: "销售价（元）",
        editable: true,
        width: 150,
        render: (_, record, index) => (
          <Form.Item
            name={['skuWithCustoms', index, 'sku', 'price']}//目前没有多规格 name 下标全部为0
            rules={[{required: true, message: '请输入销售价'}, {validator: validatePriceNumber}]}
            wrapperCol={{span: 24}}>
            <ItemPriceInput addonAfter="元"/>
          </Form.Item>
        ),
      }
      break;
    case 'stock':
      title = '调整可售库存';
      inputColumn = {
        title: (<span><span className="styles_required">*</span> 可售库存（件）</span>),
        editable: true,
        width: 180,
        render: (_, record, index) => (
          <Form.Item
            name={['skuWithCustoms', index, 'sku', 'stockQuantity']}
            rules={[{
              pattern: /^[1-9]\d{0,6}$/,
              message: "请输入7位正整数",
            },]}
            wrapperCol={{span: 24}}>
            <InputNumber min={0} style={{width: '100%'}} placeholder="可售库存"/>
          </Form.Item>
        ),
      }
      break;
  }
  const onFinish = (value) => {
    request({
      url: `/mall-admin/api/skus/update`,
      method: 'POST',
      data: {
        itemId: rowItem.id,
        skus: value.skuWithCustoms.map(item=> { return  {...item.sku}})
      },
      success: (res) => {
        message.success('修改成功')
        onClose(true)
      }
    })
  }
  return <Modal open={open}
                width={800}
                onOk={() => {
                  form.submit()
                }}
                onCancel={() => onClose(false)}
                destroyOnClose
                title={title}>
    <Form form={form} onFinish={onFinish}>
      {
        itemDetail.specDetail.length > 0 ?
          <BatchTable inputColumn={inputColumn} form={form} skuDetail={itemDetail.skuDetail} specDetail={itemDetail.specDetail}/>
          : rowItem && rowItem.type==2 ? <PackageBatchTable  inputColumn={inputColumn} form={form} skuDetail={itemDetail.skuDetail}/>:null
      }

    </Form>
  </Modal>;
}

export default UpdateSkuModal;

import MoveShow from "./move-show";
import { SourceType } from "../item-edit/utils/enum";
import UpdateCategoryModal from "./update-category-modal";
import UpdateModule from "./update-module";
import UpdatePrice from "./update-price";
import UpdateQuantity from "./update-quantity";
import UpdateSkuModal from "./update-sku-modal";
import copy from "../../../common/react/utils/copy-to-clipboard";
import { isJDItem } from "../item-edit/utils/method";
import { lib } from "../../../common/react/utils/lib";
import request from "../../../utils/plugins/axios/request";

const {useRef, useState, Fragment} = React;
const {Space, Switch, message, Modal, Button, Image, Dropdown, Tag, Popover} = antd;
const {SearchList, Spa, SpaConfigProvider} = dtComponents;
const { EditOutlined, DeleteOutlined,CopyOutlined} = icons;
const GoodsManage = () => {
  const searchListRef = useRef()
  const [moduleOpen, setModuleOpen] = useState(false);
  const [categoryOpen, setCategoryOpen] = useState(false);
  const [mode, setMode] = useState('single') // single: 单个分类编辑;batch: 多个分类编辑
  const [rowItem, setRowItem] = useState({})
  const [selects, setSelects] = useState([])
  const [updateSkuModalOpen, setUpdateSkuModalOpen] = useState(false);
  const [updateSkuModalType, setUpdateSkuModalType] = useState("");
  const getConfig = async function () {
    let url = "https://maria.yang800.com/api/data/v2/890/715";
    if(lib.isCommunityOperation()){
      url = "https://maria.yang800.com/api/data/v2/890/776";
    }else if (lib.isSubStore()){
      url = "https://maria.yang800.com/api/data/v2/890/798"
    }
    const data = await axios.get(url)
    return data.data.data;
  };
  const orderIndex = (id, action) => {
    // const id = $(evt.currentTarget).closest("td").data("id")
    const data = new FormData();
    data.append("action", action)
    request({
      url: `/api/seller/item/${id}/sort`,
      type: "POST",
      contentType: "application/x-www-form-urlencoded; charset=UTF-8",
      data: data,
      success: (data) => {
        // window.location.reload()
        searchListRef.current.load();
      },
      error(data) {
        // new Modal({ icon: "error", content: `设置失败,${data.responseJSON.message}`, title: "设置失败" }).show()
      }
    })
  }

  const changeItemStatus = (ids, status) => {
    // $("body").spin("medium")
    const msgs = {
      "1": '上架成功',
      "-1": '下架成功',
      "-3": '删除成功',
    }
    const tips = {
      "1": '上架后将会正常展示在小程序端，是否确认上架',
      "-1": '下架后将会不再展示在小程序端，是否确认下架',
      "-3": '是否确认删除',
    }
    if (tips[status]) {
      Modal.confirm({
        title: '确认',
        content: tips[status],
        onOk: () => {
          request({
            url: "/api/items/update/status",
            type: "POST",
            data: {
              ids,
              status
            },
            success: (data) => {
              message.success(msgs[status] || '设置完成')
              searchListRef.current.load();
            }
          })
        }
      })
    }

  }
  const defaultSearch= {}
  if(lib.getParam('itemIdParam')){
    defaultSearch.itemIdList=[lib.getParam('itemIdParam')]
  }
  return (
    <SearchList
      ref={searchListRef}
      scrollMode={"tableScroll"}
      paginationConfig={{size: "default", showPosition: 'bottom'}}
      searchConditionConfig={{
        size: "middle",
      }}
      getConfig={getConfig}
      pageLoadTarget={{
        convertPaginationParam: function (pagination) {
          return {current: pagination.currentPage, size: pagination.pageSize};
        },
        convertResponseData: function (data, prePage) {
          return {
            dataList: data.data,
            page: {
              currentPage: prePage.currentPage,
              pageSize: prePage.pageSize,
              totalCount: data.total
            }
          };
        }
      }}
      onSearchReset={()=>{
        if(lib.getParam('itemIdParam')){
          location.search=""
        }
      }}
      searchDefaultValue={defaultSearch}
      renderModal={() => {
        return <Fragment>
          <UpdateCategoryModal
            selects={selects}
            mode={mode}
            open={categoryOpen}
            row={rowItem}
            closeFn={(load) => {
              setCategoryOpen(false)
              setRowItem(null)
              if (load) {
                searchListRef.current.load();
              }
            }}
          />
          <UpdateModule ids={selects} open={moduleOpen}
                        closeFn={(load) => {
                          if (load) {
                            searchListRef.current.load();
                          }
                          setModuleOpen(false);
                        }}/>
          <UpdateSkuModal type={updateSkuModalType}
                          open={updateSkuModalOpen}
                          onClose={(success)=>{
                            if (success) {
                              searchListRef.current.load();
                            }
                            setUpdateSkuModalType("")
                            setUpdateSkuModalOpen(false);
                            setRowItem(null)
                          }
                          }
                          rowItem={rowItem}/>
        </Fragment>
      }}
      renderRightOperation={() => {
        return <Space>
          {
            lib.isCommunityOperation() ?     <Dropdown menu={
              { items:[
                  {
                    key: '0',
                    icon: <img
                      src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNCAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjY3NSAzLjQ1MDM5TDcuOTI1IDEuMDAwMzlDNy4zNSAwLjcwMDM5MSA2LjY1IDAuNzAwMzkxIDYuMDc1IDEuMDAwMzlMMS4zMjUgMy40NzUzOUMwLjY3NSAzLjc3NTM5IDAuMjUgNC40NzUzOSAwLjI1IDUuMjI1MzlWMTAuODAwNEMwLjI1IDExLjU1MDQgMC42NzUgMTIuMjI1NCAxLjMyNSAxMi41NzU0TDYuMSAxNS4wNTA0QzYuMzc1IDE1LjIwMDQgNi43IDE1LjI3NTQgNy4wMjUgMTUuMjc1NEM3LjM1IDE1LjI3NTQgNy42NSAxNS4yMDA0IDcuOTUgMTUuMDUwNEwxMi42NzUgMTIuNjAwNEMxMy4zNSAxMi4yNTA0IDEzLjc1IDExLjU3NTQgMTMuNzUgMTAuODI1NFY1LjIyNTM5QzEzLjc1IDQuNDc1MzkgMTMuMzI1IDMuNzc1MzkgMTIuNjc1IDMuNDUwMzlaTTYuNTUgMS44NzUzOUM2LjcgMS44MDAzOSA2Ljg1IDEuNzUwMzkgNyAxLjc1MDM5QzcuMTUgMS43NTAzOSA3LjMyNSAxLjc3NTM5IDcuNDUgMS44NTAzOUwxMi4yIDQuMzAwMzlDMTIuMjc1IDQuMzI1MzkgMTIuMzI1IDQuMzc1MzkgMTIuNCA0LjQyNTM5TDcgNy4yMjUzOUwxLjYgNC40NzUzOUMxLjY1IDQuNDI1MzkgMS43MjUgNC4zNzUzOSAxLjggNC4zNTAzOUw2LjU1IDEuODc1MzlaTTEuOCAxMS42NzU0QzEuNDUgMTEuNTAwNCAxLjI1IDExLjE1MDQgMS4yNSAxMC43NzU0VjUuNDAwMzlMMy4yNSA2LjQyNTM5VjguNjc1MzlDMy4yNSA4Ljg3NTM5IDMuMzUgOS4wNTAzOSAzLjUyNSA5LjEyNTM5TDMuODc1IDkuMzAwMzlDMy45IDkuMzI1MzkgMy45NSA5LjMyNTM5IDMuOTc1IDkuMzI1MzlDNC4xMjUgOS4zMjUzOSA0LjIyNSA5LjIyNTM5IDQuMjI1IDkuMDc1MzlWNi45MjUzOUw2LjQ3NSA4LjA3NTM5VjE0LjEwMDRMMS44IDExLjY3NTRaTTEyLjIgMTEuNjc1NEw3LjUgMTQuMTI1NFY4LjA3NTM5TDEyLjc1IDUuNDAwMzlWMTAuNzc1NEMxMi43NSAxMS4xNTA0IDEyLjU1IDExLjUwMDQgMTIuMiAxMS42NzU0WiIgZmlsbD0iIzU1NTg1QyIvPgo8L3N2Zz4K"/>,
                    label: '单商品'
                  },
                  {
                    key: '1',
                    icon: <img src={"data:image/svg+xml;base64,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"}/>,
                    label: '组合商品'
                  }
                ] ,
                onClick: (item) => {
                  switch (item.key) {
                    case '0':
                      window.open(`/seller/item-publish?isThirdPartyItem=0&sourceType=${SourceType.GOODS_SOURCE_TYPE_SELF}`)
                      break;
                    case '1':
                      window.open(`/seller/item-publish?isThirdPartyItem=0&sourceType=${SourceType.GOODS_SOURCE_TYPE_SELF}&package=1`)
                      break;
                  }
                }
              }} placement="bottom" arrow={{ pointAtCenter: true }}>
              <Button type="primary">新建商品</Button>
            </Dropdown>: lib.isCommonShop() ? <Button type="primary" onClick={()=>{
              window.open(`/seller/item-publish?isThirdPartyItem=0&sourceType=${SourceType.GOODS_SOURCE_TYPE_SELF}`)}
            }>新建商品</Button>: null
          }
            <Button className="btn" onClick={() => {
                  const getSearchData= searchListRef.current.getSearchData()
                  const resulet = Object.assign({shopId:sessionStorage.shopId}, {sourceId: sessionStorage.getItem("shopId")},getSearchData,)
                  // itemCode: criteria.itemCode,
                  if(resulet.itemName){
                    resulet.name= encodeURIComponent(resulet.itemName);
                  }
                  // status: criteria.status,
                  // sellOutStatus: criteria.sellOutStatus

                  const paramsStr = Object.entries(resulet)
                  .filter(([_, value]) => value !== undefined && value !== null && value !== '')
                  .map(([key, value]) => `${key}=${value}`)
                  .join('&');

                // Replace specific parameters
                const finalParamsStr = paramsStr
                  .replace('statuses=-1,-2', 'status=-1')
                  .replace('sellOutStatus=-1,-2', 'sellOutStatus=-1');

                //   return  ""
                window.open(`/api/seller/items/item-export?${finalParamsStr}`)
            }}>导出 &#xe638;</Button>
        </Space>
      }}
      renderLeftOperation={() => {
        return <Space>
          <Button shape="round" onClick={() => {
            if (selects.length === 0) {
              return message.error("请选择修改的数据")
            }
            // setModuleOpen(true)
            changeItemStatus(selects.map(item => item.id), 1)
          }}>批量上架</Button>
          <Button shape="round" onClick={() => {
            if (selects.length === 0) {
              return message.error("请选择修改的数据")
            }
            changeItemStatus(selects.map(item => item.id), -1)
          }}>批量下架</Button>
          <Button shape="round" onClick={() => {
            if (selects.length === 0) {
              return message.error("请选择修改的数据")
            }
            setModuleOpen(true)
          }}>批量修改可售区域</Button>
          <Button shape="round" onClick={() => {
            if (selects.length === 0) {
              return message.error("请选择修改的数据")
            }
            // setModuleOpen(true)
            setCategoryOpen(true);
            setMode('batch');

          }}>批量修改小程序分类</Button>
        </Space>
      }}
      onTableSelected={(ids, rows) => {
        setSelects([...rows]);
      }}
      tableCustomFun={{
        infoFn: (row) => {
          return (<div className={"shelf-item-component-desc"}>
            <Image
              style={{marginRight: 15}}
              className={"product-img"}
              src={row.mainImage}/>
            <div className={"product-desc"}>

              <span className={"product-name-span"}>
                <a className={"title-link"} href={`/items/${row.id}`} target="_blank">{row.name}</a>
                  <CopyOutlined onClick={() => {
                  copy(row.name);
                  message.success("复制成功！")
                }
                } />
              </span>
              <span className="product-desc-span" style={{color: "rgb(153, 153, 153)"}}>ID:{row.id}
                <CopyOutlined onClick={() => {
                  copy(row.id);
                  message.success("复制成功！")
                }
                } />
              </span>
              <div className={"product-tag"}>
              {isJDItem(row.sourceType) ?
                <Tag color="blue">京东云交易</Tag> :null
              }
              {/*自建单品*/}
              {
                row.isThirdPartyItem ===0 && row.type === 1 ?
                  <Tag color="blue">自建单品</Tag> :null
              }
                {
                  row.isThirdPartyItem ===0 && row.type === 2 ?
                    <Tag color="blue">组合商品</Tag> :null
                }
              </div>
            </div>
          </div>)
        },
        bondedFn:(row)=>{
          return <div className={"shelf-item-component-desc"}>
            {row.isBonded == 1 ? <span >全球购（保税）</span> : null}
            {row.isBonded == 0 ? <span >大贸（完税）</span> : null}
          </div>
        },
        templateFn: (row) => {
          return <Space wrap>
            <a href={`/seller/add-edit-module?id=${row.restrictedSalesAreaTemplateId}&look=1`}
               target="_blank">
              {row.restrictedSalesAreaTemplateName}
            </a>
          </Space>
        },
        sellOutFn: (row) => {
          return <Space wrap>
            <Switch
              checked={row.sellOutStatus !== 2}
              checkedChildren="未售罄"
              unCheckedChildren="售罄"
              onChange={(checked) => {
                console.log(checked)
                request({
                  url: `/api/items/update/selloutStatus`,
                  type: "POST",
                  contentType: "application/json",
                  data: {
                    ids: [row.id],
                    sellOutStatus: !checked ? 2 : 1
                  },
                  success: (data) => {
                    console.log(data);
                    message.success("设置成功")
                    searchListRef.current.load()
                  }
                })
              }}
            />
          </Space>
        },
        feesFn: (row) => {
          return (<div style={{"display": "flex", "flexDirection": "column"}}>
            <span>服务商：{row.serviceProviderCommissionRate / 100}%</span>
            <span>门店：{row.subStoreCommissionRate / 100}%</span>
            <span>导购：{row.guiderCommissionRate / 100}%</span>
          </div>)
        },
        priceFn: (row) => {
          return (<Fragment>
            {/* {
                        row.lowPrice===row.highPrice?
                        row.lowPrice/100:
                        row.lowPrice/100
                    } */}
            <UpdatePrice
              row={row}
              rowkey="lowPrice"
              load={() => {
                searchListRef.current.load()
              }}
              onEditSpecSku={()=>{
                setRowItem(row)
                setUpdateSkuModalType("price")
                setUpdateSkuModalOpen(true);
              }}
            />
          </Fragment>)
        },
        stockQuantityFn: (row) => {
          return (<Fragment>
            {/* {row.stockQuantity} */}
            <UpdateQuantity
              row={row}
              rowkey="stockQuantity"
              load={() => {
                searchListRef.current.load()
              }}
              onEditSpecSku={()=>{
                setRowItem(row)
                setUpdateSkuModalType("stock")
                setUpdateSkuModalOpen(true);
              }}
            />
          </Fragment>)
        },
        thirdSkuStockQuantityFn: (row) => {
          return (<Fragment>
            { row.isThirdPartyItem ===0?"-":row.thirdSkuStockQuantity}
          </Fragment>)
        },
        outerSkuIdFn:(row)=>{
          let node =(row.outerSkuIds||[]).join('\n')
          return  (row.outerSkuIds || []).length>3 ?<Popover
            title={ <Space  style={{ width: '200px'}}>
              {(row.outerSkuIds||[]).join('、')}
            </Space>}>
            <div className={"style_text-overflow"} >
              {node}
            </div>
          </Popover>: <div>
            {node}
          </div>
        },
        categoryPathFn: (row) => {
          var str = '';
          if (row.shopCategory && row.shopCategory.children && row.shopCategory.children.length > 0) {
            const arr = row.shopCategory.children;
            depFn(arr, (item) => {
              if (str) {
                str += '/' + item.name;
              } else {
                str += item.name;
              }
            })
          }
          return (
            <MoveShow value={str}
                      showFn={({injectedFn, hideFn}) => {
                        return <Fragment>
                          <EditOutlined
                            style={{
                              padding: '2px 5px',
                              color: '#1890ff'
                            }}
                            onClick={() => {
                              setMode('single')
                              setRowItem(row)
                              setCategoryOpen(true);
                              injectedFn(true);
                            }}
                          />
                          {
                            !!str && <DeleteOutlined
                              style={{
                                padding: '2px 5px',
                                color: 'red'
                              }}
                              onClick={() => {
                                injectedFn(true);
                                Modal.confirm({
                                  title: '确定',
                                  content: '删除类目的商品可在小程序【全部】分类中查看',
                                  onOk: () => {
                                    request({
                                      url: '/api/items/delete/shopCategory',
                                      methods: 'post',
                                      data: {
                                        ids: [row.id]
                                      },
                                      success: () => {
                                        message.success("删除成功")
                                        searchListRef.current.load()
                                        injectedFn(false);
                                        hideFn()
                                      }
                                    })
                                  },
                                  onCancel: () => {
                                    injectedFn(false);
                                    // hideFn()
                                  }
                                })
                              }}
                            />
                          }
                        </Fragment>
                      }}
            ></MoveShow>)
        },
        statusFn: (row) => {
          return (<Fragment>
            {/* {row.statusDesc} */}
            <Switch checked={[1].includes(row.status)} checkedChildren="已上架" unCheckedChildren="已下架"
                    onChange={(checked) => {
                      // console.log(checked)
                      changeItemStatus([row.id], checked ? 1 : -1)
                    }}/>
          </Fragment>)
        },
        operateFn: (row) => {
          return <Space wrap>
            <Button type='link' onClick={() => {
              window.open(`/seller/item-publish?type=1&itemId=${row.id}&isBonded=${row.isBonded}&thirdPartyId=3&isThirdPartyItem=${row.isThirdPartyItem}&sourceType=${row.sourceType}&package=${row.type==2?1:0}`)
            }}>编辑</Button>
            {
              [1, -1, 0].includes(row.status) &&
              <Button type='link' onClick={() => {
                changeItemStatus([row.id], "-3")

              }}>删除</Button>
            }

          </Space>
        },
      }}
    />

  )
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        params.url = params.url + "?shopId=" + sessionStorage.shopId
        const obj = Object.assign(params, {})
        request(obj);
      }
    }
  })}>
    <GoodsManage/>
  </SpaConfigProvider>, document.getElementById("goods-manage")
);

const depFn = (arr, fn) => {
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    fn(item);
    if (Array.isArray(item.children)) {
      depFn(item.children, fn);
    }
  }
};

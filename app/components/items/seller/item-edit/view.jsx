import {cashGiftSwitchFormName, skuSalesPriceFormName} from "./utils/form-name-const";
import {
  handleApiV3Goods,
  isJDItem,
  isSelfItem,
  transformEditValues,
  transformSubmitValues,
  validateCombosOnSubmit, validatePriceNumber
} from "./utils/method";

import ActivityImages from "./component/activity-images";
import ActivityPrice from "./component/activity-price";
import ActivityServiceFee from "./component/activity-service-fee";
import BaseInfo from "./component/base-info";
import CashGift from "./component/cash-gift";
import HiddenFormItem from "./component/hidden-form-item";
import ItemBlock from "./component/base/item-block";
import MainImages from "./component/main-images";
import NormalAttrs from "./component/normal-attrs";
import PackageActivityPrice from "./component/package-activity-price";
import PackageGoods from "./component/package-goods";
import ServiceFee from "./component/service-fee";
import ServiceFulfillment from "./component/service-fulfillment";
import SkuInfo from "./component/sku-info";
import SkuSpec from "./component/sku-spec";
import getScroll from "../../../common/react/utils/getScroll";
import itemEditStore from "./ItemEditStore";
import {lib} from "../../../common/react/utils/lib";
import packageSelectGoodsStore from "./packageSelectGoodsStore";
import request from "../../../utils/plugins/axios/request";
import scrollTo from "../../../common/react/utils/scrollTo";
import Schema from "./utils/async-validator";
import {activityValidator, validator, validatorCash} from "./utils/sku-validator";

const {useEffect, useState,useMemo, useRef, Fragment} = React;
const {observer, observerBatching} = mobxReactLite;

const {Form, ConfigProvider, locales, Button, Layout, message, Tooltip} = antd;
const {Spa, SpaConfigProvider} = dtComponents;
const {Sider} = Layout;
const {zh_CN} = locales
const sharpMatcherRegx = /#(\S+)$/;


const App = observer(() => {
  const [form] = Form.useForm();
  let itemId = lib.getParam("itemId");
  let isThirdPartyItem = lib.getParam("isThirdPartyItem");
  let goodsType = lib.getParam("goodsType");//api v3 商品类型
  let outSkuCode = lib.getParam("skuCode")
  let sourceType = lib.getParam("sourceType")
  let isPackage = lib.getParam("package") === "1"
  const hasError = useRef(false)
  const rightID = `item-edit`;
  const animating = useRef(false);
  const normalNavArr = [
    {name: "基础信息", id: `${itemId}-edit-basic`},
    {name: "图文信息", id: `${itemId}-edit-image-desc`},
    {name: "销售信息", id: `${itemId}-edit-price`},
    {name: "服务履约", id: `${itemId}-edit-service-fulfillment`},
    {name: "活动设置", id: `${itemId}-edit-activity-setting`},
  ];
  const packageNavArr = [
    {name: "基础信息", id: `${itemId}-edit-basic`},
    {name: "图文信息", id: `${itemId}-edit-image-desc`},
    {name: "规格", id: `${itemId}-edit-package-price`},
    {name: "服务履约", id: `${itemId}-edit-service-fulfillment`},
    {name: "活动设置", id: `${itemId}-edit-activity-setting`},
  ];
  const [currentId, setCurrentId] = useState(`${itemId}-edit-basic`);
  // 社群运营
  const isCommunityOperation = lib.isCommunityOperation()
  const isCommonShop = lib.isCommonShop()

  useEffect(() => {
    document.getElementById(rightID).addEventListener("scroll", handleScroll);
    itemEditStore.requestSkuSelectList()
    if (isThirdPartyItem) {
      itemEditStore.setIsThirdPartyItem(Number(isThirdPartyItem))
      itemEditStore.setSourceType(Number(sourceType))
    }
    itemEditStore.setItemId(itemId)
    itemEditStore.setForm(form)
    fetchData()
    if (itemId) {
      itemEditStore.setItemId(itemId)
      itemEditStore.setIsEdit(true)
      getDetail()
      getRichDetail()
    } else {
      if (!isSelfItem(Number(sourceType))) {
        bindItemInfoReady()
      }
    }

    return () => {
      document.getElementById(rightID).removeEventListener("scroll", handleScroll);
    };
  }, [form])

  const getDetail = () => {
    request({
      url: `/mall-admin/api/items/detail/${itemId}`,
      method: "POST",
      data: {},
      needMask: true,
      success: (data) => {
        if (data.cashGiftUseTimeStart) {
          data.cashGiftUseTimeStart = [moment(data.cashGiftUseTimeStart), moment(data.cashGiftUseTimeEnd)]
        }
        if (data.item.type === 2) {
          data.skuWithCustoms.forEach((skuWithCustomsItem, index) => {
            packageSelectGoodsStore.newSkuSelectList[`${skuWithCustomsItem.sku.skuId}`] = lib.clone(skuWithCustomsItem.sku.skuComboList)
          })
        }
        form.setFieldsValue(transformEditValues(data))
        if (isPackage) {

        }
        itemEditStore.setItemsEditorPrivilege(data.itemsEditorPrivilege)
        itemEditStore.setItemData(data)
        if (data.otherAttrs) {
          itemEditStore.setOtherAttrs(data.otherAttrs)
        }
      }
    })
  }
  const bindItemInfoReady = () => {
    request({
        url: `/api/thirdPartySku/info`,
        method: "POST",
        data: {
          thirdPartyId: itemEditStore.thirdPartyId,
          outerSkuId: outSkuCode
        },
        success: (data) => {
          form.setFieldsValue(handleApiV3Goods(data))
        },
        error: (data) => {
        }
      }
    )
  }
  const getRichDetail = () => {
    request({
      url: `/api/seller/items/detail/v2`,
      method: "GET",
      params: {
        id: itemId
      },
      success: (data) => {
        form.setFieldValue("richDetail", data)
      }
    })
  }

  const handleScroll = () => {
    if (animating.current) {
      return;
    }
    const scrollTop = document.getElementById(rightID).scrollTop;
    let section = Array.from(document.getElementById(rightID).children);
    let activeChannel;
    section.map(item => {
      let itemTop = item.offsetTop;
      if (scrollTop > itemTop - 90) {
        activeChannel = item.id;
      }
    });
    setCurrentId(activeChannel);
  };

  const handleScrollClick = item => {
    let targetOffset = 12;
    setCurrentId(item);
    const container = document.getElementById(rightID);
    const scrollTop = getScroll(container, true);
    const sharpLinkMatch = sharpMatcherRegx.exec(`#${item}`);
    if (!sharpLinkMatch) {
      return;
    }
    const targetElement = document.getElementById(sharpLinkMatch[1]);
    if (!targetElement) {
      return;
    }
    const eleOffsetTop = getOffsetTop(targetElement, container);
    let y = scrollTop + eleOffsetTop;
    y -= targetOffset !== undefined ? targetOffset : 0;
    animating.current = true;
    scrollTo(y, {
      callback: () => {
        animating.current = false;
      },
      getContainer: getContainer,
    });
  };
  const getOffsetTop = (element, container) => {
    if (!element.getClientRects().length) {
      return 0;
    }
    const rect = element.getBoundingClientRect();
    if (rect.width || rect.height) {
      if (container === window) {
        container = element.ownerDocument.documentElement;
        return rect.top - container.clientTop;
      }
      return rect.top - container.getBoundingClientRect().top;
    }
    return rect.top;
  };
  const getContainer = () => {
    return document.getElementById(rightID);
  };
  /**
   * 初始化获取下拉
   */
  const fetchData = async () => {
    const listCountryArr = await request({url: "/api/address/listCountry/v2", method: "GET"})
    const customsTownArr = await request({url: "/api/items/customs/drop/down/box", method: "GET"})
    //可售模版
    const salesAreaTemplateArr = await request({
      url: "/api/restrictedSalesAreaTemplate/find",
      method: "POST",
      data: {
        "pageSize": 1000,
        "pageNo": 1,
        "status": 1
      }
    })
    //运费模版
    let deliveryFeeTemplateData = {
      "current": 1,
      "size": 200
    }
    if (isJDItem(sourceType)) {
      deliveryFeeTemplateData.isFree = true //京东云交易的品,运费模板数据源只提供【计价方式=商家承担运费】类型模板
    }
    const deliveryFeeTemplateArr = await request({
      url: "/api/seller/paging-delivery-fee-template/v2",
      method: "POST",
      data: deliveryFeeTemplateData
    })
    itemEditStore.setListCountryOption(listCountryArr)
    itemEditStore.setCustomsTownOption(customsTownArr)
    itemEditStore.setDeliveryFeeTemplateOption(deliveryFeeTemplateArr.data)
    itemEditStore.setSalesAreaTemplateOption(salesAreaTemplateArr.data)
  };

  function savaRichDetail(itemId, values, callback) {
    const imageList = values.itemDetail.imageList
    if (Array.isArray(imageList) && imageList.length > 0) {

      values.richDetail = imageList.map(detailImage => `<img src="${detailImage}">`).join("")
    }
    if (values.richDetail) {
      request({
        url: '/api/seller/items/edit/detail/v2',
        method: "POST",
        data: {
          itemId,
          detail: values.richDetail
        },
        success: (data) => {
          callback()
        }

      })
    } else {
      callback()
    }

  }

  function checkBeforeSubmit(values) {
    if (values.skuWithCustoms && isPackage) {
      const success = validateCombosOnSubmit(values.skuWithCustoms)
      if (!success) {
        message.error("组合商品的子品不允许重复")
        return false
      }
    }
    return true
  }

  async function checkSku(formValues) {
    const valitorItems = async (data) => {
      hasError.current = false
      return new Promise(async (resolve, reject) => {
        const values = data.skuWithCustoms
        await Promise.all(
          values.map(async (item, index) => {
            try {
              const price = item.sku.price;
              if (item.sku.extraPrice && item.sku.extraPrice.crossedPrice
                && price && item.sku.extraPrice.crossedPrice < price) {
                values[index].sku.errors = {"sku.extraPrice.crossedPrice": [new Error("划线价不能小于售价价格")]}
              } else {
                values[index].sku.errors = {}
              }
              await validator.validate(item)
            } catch ({errors, fields}) {
              if (errors.length > 0) {
                hasError.current = true
                values[index].sku.errors = fields
              } else {
                values[index].sku.errors = {}
              }
            }
            //校验礼金
            if(data.isCashGift===1) {
              try {
                await validatorCash.validate(item)
              } catch ({errors, fields}) {
                if (errors.length > 0) {
                  hasError.current = true
                  values[index].sku.errors = fields
                } else {
                  values[index].sku.errors = {}
                }
              }
            }
            //校验活动价
            if(data.activitySalesPriceSwitch===1){
              try {
                await activityValidator.validate(item)
              } catch ({errors, fields}) {
                if (errors.length > 0) {
                  hasError.current = true
                  values[index].sku.errors = fields
                } else {
                  values[index].sku.errors = {}
                }
              }
            }
          })
        )
        if (hasError.current) {
          itemEditStore.form.setFieldValue("skuWithCustoms", [...values])
          reject(["商品销售信息填写有误，请检查"])
        } else {
          resolve()
        }
      })

    }
    await valitorItems(formValues)
    return !hasError.current;
  }

  function submit() {
    form.validateFields()
      .then(async values => {
        if (!checkBeforeSubmit(values)) {
          return
        }
        const checkSkuPass= await checkSku(values)
        if (!checkSkuPass) {
          return
        }
        const isEdit = itemEditStore.isEdit
        if (values.cashGiftUseTimeStart) {
          const [start, end] = values.cashGiftUseTimeStart
          values.cashGiftUseTimeStart = start ? moment(start).format('x') : null
          values.cashGiftUseTimeEnd = end ? moment(end).format('x') : null
        }

        const data = transformSubmitValues({
          values,
          isEdit: itemEditStore.isEdit,
          isThirdPartyItem,
          pushSystem: itemEditStore.thirdPartyId,
          isPackage
        })
        if (isEdit) {
          savaRichDetail(itemId, values, () => {
          })
          request({
            url: '/mall-admin/api/items/update',
            method: "POST",
            data,
            needMask: true,
            success: (data) => {
              message.success("更新成功")
              window.location.href = "/seller/on-shelf"
            }

          })
        } else {
          request({
            url: '/mall-admin/api/items/create',
            method: "POST",
            data,
            needMask: true,
            success: (data) => {
              message.success(" 创建成功")
              savaRichDetail(data, values, () => {
                window.location.href = "/seller/on-shelf"
              })
            }
          })
        }
      })
      .catch(errorInfo => {
        console.log("errorInfo", errorInfo)

        message.error("提交出错,请检查页面填写是否有误！")
      });

  }
  function getScrollbarWidth() {
    const div = document.createElement('div');
    div.style.overflow = 'scroll';
    div.style.visibility = 'hidden';
    div.style.position = 'absolute';
    document.body.appendChild(div);
    const scrollbarWidth = div.offsetWidth - div.clientWidth;
    document.body.removeChild(div);
    return scrollbarWidth;
  }

  const scrollbarBottom = useMemo(() =>getScrollbarWidth(),[])
  const navArr = isPackage ? packageNavArr : normalNavArr
  return (
    <ConfigProvider locale={zh_CN}>
      <Layout className="Layout_box">
        <Sider className="Sider">
          <div className="publish_good_left_con">
            <div className="publish_good_left_con_data">
              {navArr &&
                navArr.map(item => {
                  return (
                    <a>
                      <div
                        className={`publish_good_left_test ${
                          currentId === item.id ? "actived" : ""
                        }`}
                        onClick={() => handleScrollClick(item.id)}>
                        {item.name}
                      </div>
                    </a>
                  );
                })}
            </div>
          </div>
        </Sider>
        <Layout className="Layout_content">
          <Form
            style={{height: "100%"}}
            form={form}
            onValuesChange={(changedValues, allValues) => {
              console.log("changedValues",changedValues,allValues)
            }}
            labelCol={{flex: '120px'}}
            labelAlign={"left"}
            wrapperCol={{span: 16}}
            colon={false}>
            {/*基础信息*/}
            <div id={"right_edit"}
                 style={{  paddingBottom: 55, marginTop: 12, marginLeft: 16}}>
              <ItemBlock title={"基础信息"} id={`${itemId}-edit-basic`}>
                <HiddenFormItem/>
                <BaseInfo/>
                <NormalAttrs/>
              </ItemBlock>

              {/*图文信息*/}

              <ItemBlock title={"图文信息"} id={`${itemId}-edit-image-desc`}>
                <MainImages/>
              </ItemBlock>
              {isPackage ? <ItemBlock id={`${itemId}-edit-package-price`} style={{paddingTop: 0}}>
                  {/*组合商品*/}
                  <Form.Item wrapperCol={{span: 24}} name={skuSalesPriceFormName} noStyle>
                    <PackageGoods/>
                  </Form.Item>
                </ItemBlock> :
                <ItemBlock title={"销售信息"} id={`${itemId}-edit-price`}>
                  {/*规格*/}
                  {isCommunityOperation ? <SkuSpec/>: null}
                  {/*价格设置*/}
                  <SkuInfo/>
                  {/*服务费设置*/}
                  {/* 社群模式选：是否可用福豆 */}
                  {
                    !isCommunityOperation ? lib.isSubStore() ? <ServiceFee/> : null :

                      <CashGift
                        cashGiftSwitchFormName={cashGiftSwitchFormName}
                        cashGiftUnitFormName={["skuWithCustoms", 0, "sku", "cashGiftUnit"]}
                        maxDeductionFormName={["skuWithCustoms", 0, "sku", "maxDeduction"]}
                        cashGiftUseTimeStartFormName={"cashGiftUseTimeStart"}
                      />
                  }
                </ItemBlock>
              }
              {/*服务履约*/}
              <ItemBlock title={"服务履约"} id={`${itemId}-edit-service-fulfillment`}>
                <ServiceFulfillment/>
              </ItemBlock>
              {/*活动设置*/}
              <ItemBlock title={"活动设置"} id={`${itemId}-edit-activity-setting`}>
                <ActivityImages/>
                {
                  isPackage ? <PackageActivityPrice/> :
                    <Fragment>
                      <ActivityPrice/>
                      <ActivityServiceFee/>
                    </Fragment>
                }
              </ItemBlock>
            </div>
          </Form>
          <div className="sell-float-bottom" style={{bottom: scrollbarBottom,}}>
            <div className="com-struct">
              <Button type="primary" onClick={() => submit()}>提交</Button>
            </div>
          </div>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
});

ReactDOM.render(<SpaConfigProvider spa={new Spa({
    _openPage: () => {
    }, request: {
      request: (params) => {
        const obj = Object.assign(params, {
          method: 'POST',
        })
        request(obj);
      }
    }
  })}>
    <App/>
  </SpaConfigProvider>, document.getElementById("item-edit")
);

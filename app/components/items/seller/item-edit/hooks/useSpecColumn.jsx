import {lib} from "../../../../common/react/utils/lib";
const {useState, useEffect} = React;

export const useSpecColumn = ({specDetail, skuDetail,afterReorderSkus}) => {
  const [specs, setSpecs] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  useEffect(() => {
      if(Array.isArray(specDetail) && specDetail.length>0){
        handleGenerateTable(specDetail)
      }
    },
    [specDetail])

  const parseSpecData = (valueSpec) => valueSpec
    .filter((spec) => spec)
    .map((spec) => ({
      id: spec.id,
      name: spec.name,
      itemSpecificationDetailParams: (spec.itemSpecificationDetailParams || []).filter((v) => v && v.name) // 过滤无 name 的值
    }))
    .filter((spec) => spec.itemSpecificationDetailParams.length > 0) // 过滤无有效值的规格

  // 合并计算逻辑
  const calculateRowSpans = (data, specs) => {
    return data.map((record, index) => {
      const spans = {};
      specs.forEach((spec,specIndex) => {
        const field = spec.name;
        const isLastSpec = specIndex === specs.length - 1;
        if (isLastSpec) {
          // 最后一列不合并，设置跨度为1
          spans[`${field}Span`] = 1;
        }else if (index === 0 || record[field] !== data[index - 1][field]) {
          let count = 1;
          for (let i = index + 1; i < data.length; i++) {
            if (data[i][field] === record[field]) count++;
            else break;
          }
          spans[`${field}Span`] = count;
        } else {
          spans[`${field}Span`] = 0;
        }
      });
      if(!record.id){
        record.id = lib.generateNumberString(false,18)
      }
      return { ...record, ...spans };
    });
  };
  // 计算规格组合（笛卡尔积）
  const generateCombinations = (parsedSpecs) => {
    if(!parsedSpecs|| parsedSpecs.length===0){
      return []
    }
    const valuesArray = parsedSpecs.map((s) => s.itemSpecificationDetailParams);
    const combinations = valuesArray.reduce(
      (acc, curr) => acc.flatMap((a) => curr.map((b) => [...a, b])),
      [[]]
    );
    return combinations.map((comb, index) => {
      return  {
        specDetails:parsedSpecs.map((s, i) => comb[i].frontId),
        ...Object.fromEntries(parsedSpecs.map((s, i) => [s.name, comb[i].name])),
      }
    });
  };
  function reorderSkus(firstArray, secondArray) {
    // 1. 创建映射：将第一个数组的 specDetails 转换为规范化键（排序后字符串）
    const skuMap = new Map();
    for (const skuObj of firstArray) {
      const specDetails = (skuObj.sku.specDetails||[]);
      const sortedKey = [...specDetails].sort().join(','); // 排序并生成唯一键
      skuMap.set(sortedKey, skuObj);
    }
    // 2. 按第二个数组的顺序提取匹配的 SKU
    const result = [];
    for (const item of secondArray) {

      const targetKey = [...item.specDetails].sort().join(','); // 同样生成规范化键
      const matchedSku = skuMap.get(targetKey);

      if (matchedSku) {
        result.push(matchedSku);
      }else{
        result.push({
          sku: {
            id:item.id,
            specDetails:item.specDetails
          },
        });
      }
    }
    return result;
  }
  // 生成表格数据
  const handleGenerateTable = (initialSpecData) => {

    const parsedSpecs = parseSpecData(initialSpecData);
    const rawData = generateCombinations(parsedSpecs);
    const processedData = calculateRowSpans(rawData, parsedSpecs);
    setSpecs(parsedSpecs);
    setDataSource(processedData);
    if(Array.isArray(skuDetail)){
      const newSkuDetail = reorderSkus(skuDetail, processedData);
      afterReorderSkus(newSkuDetail,processedData)
    }
  };
  return {specs,dataSource}

}

export const parseSpecData = (valueSpec) => valueSpec
  .filter((spec) => spec)
  .map((spec) => ({
    id: spec.id,
    name: spec.name,
    itemSpecificationDetailParams: (spec.itemSpecificationDetailParams || []).filter((v) => v && v.name) // 过滤无 name 的值
  }))
  .filter((spec) => spec.itemSpecificationDetailParams.length > 0) // 过滤无有效值的规格
// 合并计算逻辑
export const calculateRowSpans = (data, specs) => {
  return data.map((record, index) => {
    const spans = {};
    specs.forEach((spec,specIndex) => {
      const field = spec.name;
      const isLastSpec = specIndex === specs.length - 1;
      if (isLastSpec) {
        // 最后一列不合并，设置跨度为1
        spans[`${field}Span`] = 1;
      }else if (index === 0 || record[field] !== data[index - 1][field]) {
        let count = 1;
        for (let i = index + 1; i < data.length; i++) {
          if (data[i][field] === record[field]) count++;
          else break;
        }
        spans[`${field}Span`] = count;
      } else {
        spans[`${field}Span`] = 0;
      }
    });
    if(!record.id){
      record.id = lib.generateNumberString(false,18)
      record.sku ={
        status:-1,
        tags: {pushSystem:3},
        cashGiftUnit:1,
        extraPrice:{},
        extraMap:{unitQuantity:1}
      }
    }
    return { ...record, ...spans };
  });
};

// 计算规格组合（笛卡尔积）
export  const generateCombinations = (parsedSpecs) => {
  if(!parsedSpecs|| parsedSpecs.length===0){
    return []
  }
  const valuesArray = parsedSpecs.map((s) => s.itemSpecificationDetailParams);
  const combinations = valuesArray.reduce(
    (acc, curr) => acc.flatMap((a) => curr.map((b) => [...a, b])),
    [[]]
  );
  return combinations.map((comb, index) => {
    return  {
      specDetails:parsedSpecs.map((s, i) => comb[i].frontId),
      ...Object.fromEntries(parsedSpecs.map((s, i) => [s.name, comb[i].name])),
    }
  });
};


// 生成表格数据
export const handleGenerateTable = (initialSpecData) => {

  const parsedSpecs = parseSpecData(initialSpecData);
  const rawData = generateCombinations(parsedSpecs);
  const processedData = calculateRowSpans(rawData, parsedSpecs);
  return {parsedSpecs,processedData}
};

export function reorderSkus(firstArray, secondArray) {
  // 1. 创建映射：将第一个数组的 specDetails 转换为规范化键（排序后字符串）
  const skuMap = new Map();
  for (const skuObj of firstArray) {
    const specDetails = (skuObj.sku.specDetails||[]);
    const sortedKey = [...specDetails].sort().join(','); // 排序并生成唯一键
    skuMap.set(sortedKey, skuObj);
  }
  // 2. 按第二个数组的顺序提取匹配的 SKU
  const result = [];
  for (const item of secondArray) {

    const targetKey = [...item.specDetails].sort().join(','); // 同样生成规范化键
    const matchedSku = skuMap.get(targetKey);

    if (matchedSku) {
      item.sku.specDetails=item.specDetails
      matchedSku.sku.specDetails=item.specDetails
      result.push({
        ...item,
        ...matchedSku
      });
    }else{
      item.sku.specDetails=item.specDetails
      result.push(item);
    }
  }
  return result;
}

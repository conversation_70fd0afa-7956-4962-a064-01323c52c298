.item-edit {
  height: 100%;
  #item-edit{
    position: relative;
    overflow: scroll;
    scrollbar-gutter: stable;
  }
  .Layout_box {
    height: 100%;

    .Sider {
      background: transparent;
      position: fixed;
      z-index: 1000;

      .ant-layout-sider-children {
        height: auto;
      }

      .Anchor {
        height: 500px;

        .Anchor_link {
          display: block;
          height: 50px;
          line-height: 50px;
        }
      }
    }

    .Layout_content {
      background: transparent;
      //overflow: auto;
      margin-left: 200px;
      z-index: 300;
      .ant-descriptions-item-container {
        align-items: center;
      }

      .mandatory_title {
        color: #f00;
        margin-right: 5px;
      }
    }

    .ant-descriptions {
      // padding: 20px;
    }

    .ant-descriptions-view {
      // padding: 20px;
      // margin-top: 20px;
    }

    .ant-descriptions-title {
      margin-left: 24px;
    }

    .ant-table-wrapper {
      padding: 0 24px 24px;
    }

    .ant-descriptions-item-label {
      margin-left: 24px;
    }
  }
  #right_edit {
    z-index: 2;
    width: 1024px;
    margin-left: 258px;
    padding-bottom: 55px;
    padding-right: 18px
  }

  .ant-table-tbody>tr>td{
    padding: 14px 12px;
    .ant-form-item {
      margin-bottom: 0px;
    }
  }


  @media screen and (min-width: 1281px) and (max-width:1440px) {
    #ROOT {
      flex-direction:row;
      justify-content: center;
      position: relative
    }

    #right_edit {
      width:1240px;
    }
  }

  @media screen and (min-width: 1441px) and (max-width:1756px) {
    #right_edit {
      width:1240px;
      padding-right: 0
    }
  }

  @media screen and (min-width: 1757px) {
    #right_edit {
      width:1240px;
      margin: 0 auto;
      padding-right: 0
    }
  }
  .publish_good_left_con {

    min-width: 160px;
    height: 100%;
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    padding-top: 12px;
    margin-top: 12px;
    box-shadow: 0 2px 4px rgba(229, 229, 229, 0.5);

    .publish_good_left_con_data {
      border-radius: 2px;

      a {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;

        .publish_good_left_test {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          margin-bottom: 4px;
          padding-left: 20px;
          height: 40px;
          line-height: 40px;
          cursor: pointer;
          position: relative;
        }

        .actived {
          background: #f0f6ff;
          color: #0268ff;
        }

        .actived::before {
          content: "";
          position: absolute;
          left: 0;
          width: 4px;
          height: 40px;
          background: #0268ff;
          border-radius: 0px 100px 100px 0px;
        }
      }
    }
  }
  .styles_required{
    color: #f60;
    margin-left: 4px;
    font-size: 14px;
  }
  .sku-price-table{
      .hidden-column{
        display: none;
      }

  }
  .common-wrap {
    background: #f7f8fa;
    border-radius: 9px;
    padding: 18px;

    .attr-item-select,.attr-item-input,.trade-information-input {
      width: 270px !important;
      display: block;
    }
  }

  .wrapper-label {
    color: #111;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    overflow: hidden;
  }

  .component-category-line {
    align-items: center;
    display: flex;

    .empty-cat-warning {
      color: var(--color-error-4, #e72b00);
      margin-right: 17px;
      font-size: 14px
    }

    .info-cate {
      flex: 1;
      align-items: center;
      display: flex
    }

    .info-cate .path-name {
      font-size: 14px
    }

    .switch-cate-btn {
      font-size: var(--sell-font-size, 12px)
    }
  }

  .item-component-block {
    background: #fff;
    border-radius: 12px;
    margin: 12px auto;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(229, 229, 229, .5);

    .item-component-block-header {
      align-items: center;
      margin-bottom: 16px;
      display: flex;

      .item-component-header-title {
        color: #333;
        margin: 0 12px 0 0;
        padding: 0;
        font-size: 18px;
        font-weight: 600
      }
    }
  }

  .goods-images-group {
    width: 540px;
  }

  .image-list {
    justify-content: flex-start;
    padding: 0;
    display: flex;
    overflow: hidden;


    }
  .image-list,.main-video{
    .ant-upload-select-picture-card, .ant-upload-list-picture-card-container {
      width: 90px;
      height: 90px;
    }

    .ant-upload.ant-upload-select-picture-card:hover {
      .image-empty {
        .placeholder {
          .text {
            color: var(--ant-primary-color);
          }
        }
      }
  }

    .image-empty {
      .placeholder {
        color: #999;
        max-width: 70px;
        line-height: 100%;
        font-size: 12px;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        display: flex;

        .text {
          text-align: center;
          padding-top: 6px;

          &:hover {
            color: #3d7fff;
          }
        }
      }

    }
  }

  .sell-float-bottom {
    text-align: center;
    z-index: 100;
    background-color: #fff;
    border-top: 0;
    width: 100%;
    height: 66px;
    line-height: 66px;
    position: fixed;
    bottom: 0;
    left: 180px;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, .06)
  }
  .detail-img-list{
    display: flex;
    flex-direction: row;
    padding: 18px;
    background: #fafbfc;
    .image-list{
      margin-top: 16px;
    }
    &-preview{
      width: 378px;
      text-align: center;
      border: solid 1px #e8e8e8;
      &-title{
        padding-left: 24px;
        text-align: left;
        background:#f9f9fa;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #999999;
      }
      .viewGroup{
        width: 375px;
        height: 500px;
        overflow-y: auto;
      }
    }
    &-upload-img-list{
      margin-left: 16px;
      width: 530px;
      height: 542px;
      border: solid 1px #e8e8e8;
      background: #fff;
      padding: 12px;
      display: flex;
      flex-direction: column;
      &-header{
        display: flex;
        font-size: 14px;
        align-items: center;
        color: rgba(0, 0, 0, .85);
        .sub-title{
          color: rgba(0, 0, 0, .45);
        }
      }
      .image-item {
        position: relative;
        width: 104px;
        height: 104px;
        border: 1px solid #ccc;
        background: #F5F5F5;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;

      }
      .upload-mask{
        position: absolute;
        left:0;
        right:0;
        bottom:0;
        opacity: 0.8;
        background:#000;
      }
      .upload-img{
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 10;
        top: 0;
      }

      .image {
        width: 103px;
        height: 103px;
        object-fit: cover;
      }
      .styles_controls {
        align-items: center;
        background: rgba(0,0,0,.65);
        bottom: 0;
        display: flex;
        justify-content: center;
        left: 0;
        pointer-events: none;
        position: absolute;
        right: 0;
        top: 0;
        transition: all .3s;
        &.styles_bottom {
          border-radius: 0 0 4px 4px;
          bottom: 0;
          height: 32px;
          left: 0;
          position: absolute;
          right: 0;
          top: auto;
          width: 100%;

          .styles_iconSeparator {
            color: #85878a;
            display: block;
            font-size: 12px;
            font-style: normal;
            line-height: 14px
          }
        }
      }
       .styles_controls>span {
        align-items: center;
        display: flex;
        flex: 1 1;
        justify-content: space-around;
        margin: auto 3px;
      }
    }
  }
  .sku-spec{
    .style_helperWrapper {
      align-items: flex-start;
      display: flex;
    }
    .add-sku-name {
      align-items: center;
      cursor: pointer;
      display: flex;
      .new-sku {
        color: #898b8f;
        font-size: 14px;
        line-height: 20px
      }
    }
    .style_contentBox {
      background: #f8f9fa;
      border-radius: 4px;
      padding: 16px;
      width: 100%;
      .style_hasError {
        border-color: #ff4040
      }
    }


    .style_skuNameBox {
      display: flex;
      justify-content: space-between;
    .style_required {
        color: #ff4050;
        margin-right: 4px;
        position: relative;
        top: 2px
      }

      .style_specName {
        color: #252931;
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
        margin-right: 16px
      }
    }
    .style_skuValueInput{
      display: flex;
      margin-top: 12px;
      min-width: 290px;
      padding-right: 16px;
      align-items: center;

    }
    .style_specValueBox {
      margin-top: 16px;
    }
   .style_skuValueBox {
     align-items: flex-start;
     display: flex;
     flex-wrap: wrap;
     margin-right: -18px;
   }
    .style_skuNameBox .style_specPic {
      align-items: center;
      display: flex;
      margin-left: 12px
    }



    .style_disabledDeleteBtn {
      align-items: center;
      color: #aaabaf;
      cursor: not-allowed;
      display: inline-flex
    }

    .style_specOperations {
      display: flex;

     .style_item {
        align-items: center;
        color: #55585c;
        cursor: pointer;
        display: flex;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px
      }

      .style_item:hover {
        color: #1966ff
      }

       .style_disabled{
        color: #aaabaf;
        cursor: not-allowed
      }
    }

    .style_specOperations>:not(:first-child):before {
      border-left: 1px solid #dcdee1;
      content: "";
      display: inline-block;
      height: 12px;
      margin: 0 8px
    }
  }
  .style_delete{
    color: #55585c;
    cursor: pointer;
    margin-left: 8px;
    &:hover {
      color: #1966ff
    }
  }
  .table-upload-pic{
    text-align: center;
    &-select-picture-card{
      .ant-upload-select-picture-card,.ant-upload-list-picture-card-container{
        width: 32px;
        height: 32px;
      }
    }


    .image-sku-pic{
      align-items: center;
      background-color: #fff;
      display: inline-flex;
      font-size: 12px;
      color: #565960;
      margin-right: 0;
      border-radius: 4px;
      border: 1px dashed rgb(220, 222, 225);
    }

  }
  .package-select-goods-table{
    .ant-table {
      border: 1px solid #eeeff0;
    }
    .ant-table-summary > tr:last-of-type > td{
      border-top:none;
    }
    .ant-table-tbody > tr >td {
      font-size: 12px;
      line-height: 20px;
      word-wrap: break-word;
      color: #252931;
      padding: 14px 12px;
      word-break: break-all;
    }

  }
  .package-select-goods-list{
    &-item{

    }
    .item-title{
      color: #333;
      margin: 0 12px 0 0;
      padding: 0;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 24px;
    }

  }
}
.batch_wrapper {
  display: flex;
  flex-wrap: wrap;
  padding: 0 24px 0;
}
.styles_inputBox {
  flex-grow: 1;
  margin-bottom: 12px;
  margin-right: 12px;
  max-width: 162px;
  width: 126px;
  .styles_inputBox_form_item{
    width: 100%;
  }
  &.styles_inputBox_small{
    max-width: 120px;
    width: 90px;
    .ant-input{
      font-size: 14px;
    }
    .ant-input::placeholder {
      color: #bfbfbf;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none
    }
  }
  .ant-select-selection-overflow {
    flex-wrap: nowrap;
  }
  .ant-select-multiple {
    .ant-select-selection-item {
    background: rgba(0, 0, 0, .04);
    border: 1px solid #eeeff0;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: default;
    display: flex;
    flex: none;
    height: 24px;
    line-height: 22px;
    margin-bottom: 2px;
    margin-top: 2px;
    margin-inline-end: 5px;
    max-width: 100%;
    padding-inline-end: 4px;
    padding-inline-start: 8px;
    position: relative;
    transition: font-size .3s, line-height .3s, height .3s;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}
  .styles_hiddenRest {
    div[class*=-select-selection-overflow] div[class*=-select-selection-overflow-item] + div[class*=-select-selection-overflow-item-rest] {
      display: none;
    }
  }
  .ant-select-selection-item-content{
    display: inline-block;
    margin-right: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre;
  }
}
.style_shortName {
  align-items: center;
  display: flex;
  img {
    border-radius: 4px;
    height: 48px;
    margin-right: 10px;
    width: 48px;
  }
}

.style_shortGoodsInfo {
  display: flex;
  flex-direction: row;
  .style_text {
    color: #898b90cc;
    font-size: 12px;
    margin-right: 24px;
  }
}
.style_sortGround {
  display: flex;
  flex-wrap: wrap;
  justify-self: flex-start
}

.style_sortItem {
  background: #f3f4f6;
  border-radius: 4px;
  color: #12141a;
  cursor: pointer;
  font-size: 14px;
  line-height: 16px;
  padding: 6px 23px
}
.style_sortButton {
  color: #565960;
  cursor: pointer;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}.style_disabled {
   color: #aaabaf;
 }
.style_sortItemHasMargin{
  margin-bottom: 12px;
  margin-right: 12px
}
.popup-footer{
  align-items: baseline;
  display: flex;
  justify-content: space-between;
  padding: 0 12px;

}
.ant-input-group>:not(:last-child) {
  border-right-width: 1px;
  margin-right: -1px;
}
.component-category-select-modal {
  margin: 6px 0;

  .component-category {
    .category-path-wrap {
      color: #999;
      justify-content: space-between;
      align-items: center;
      margin: 0 0 6px 0;
      //padding: 8px 12px;
      font-size: 12px;
      line-height: 20px;
      display: flex;
      overflow: hidden;

      .path-label {
        vertical-align: -2px;
        height: 24px;
        font-size: 12px;
        line-height: 24px
      }

      .path-node {
        border-width: 0;
        border-radius: 0;
        height: 15px;
        padding: 0;
        font-size: 12px;
      }

      .connector-char {
        vertical-align: middle;
        height: 24px;
        padding: 0 4px;
        line-height: 24px;
        display: inline-block
      }
    }

    .category-lists-wrap {
      border: 1px solid #eee;
      width: 100%;
      position: relative;
      height: 330px;
      overflow: hidden
    }

    .category-list {
      float: left;
      border-right: 1px solid #eee;
      flex-direction: column;
      height: 330px;
      transition: all .5s;
      display: flex;
      position: relative;

      .search-wrap {
        width: 90%;
        margin: 0 5%;
        border-radius: 6px;
        position: relative;
      }

      .ant-list-items {
        height: 270px;
        overflow-y: auto;
      }

      .category-list-item {
        cursor: pointer;
        border-left: 2px solid transparent;
        width: 100%;

        &.selected {

          .category-name {
            color: #1890ff;
          }
        }
      }
    }
  }
}
.package-select-goods-search{
  margin: 24px 0;
  .ant-input{
    line-height: 1.5715;
    height: auto;
  }


}

.help-container{
  color: rgba(0, 0, 0, .45);
  margin-top: 8px;
}
.ant-drawer-footer-default{
  align-items: center;
  display: flex;
  justify-content: space-between;
  .ant-drawer-footer-help{
    margin-right: 24px;
    overflow: hidden;
    white-space: nowrap;
  }
}
.package-select-modal-goods-table{
  .style_goodsInfo {
    align-items: center;
    display: flex;
    width: 100%;
    .style_goodsImg {
      border-radius: 4px;
    }
    .style_goodsContent {
      color: #12141a;
      display: flex;
      flex: 1 1;
      flex-direction: column;
      font-size: 14px;
      font-weight: 400;
      height: 44px;
      justify-content: space-between;
      line-height: 20px;
      .style_goodsName {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 288px
      }
      .style_goodsId{
        color: #85878a;
        white-space: nowrap
      }
    }
  }
  .style_skuName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 288px;
  }
  tr.ant-table-expanded-row:hover>td, tr.ant-table-expanded-row>td {
    background: #f8f9fa;
  }
  .ant-table-tbody>tr>td .ant-table-wrapper .ant-table .ant-table-tbody>tr>td {
    background-color: #f8f9fa;
  }

}
.upload-popover-inner-content{
  align-items: center;
  display: flex;
  padding: 0 12px;
  .upload-action{
    margin: 0 12px;
    align-items: center;
    border-radius: 4px;
    color: #565960;
    cursor: pointer;
    display: inline-flex;
    flex: 0 0 24px;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    overflow: hidden;
    position: relative;
    text-align: center;
    width: 100%;
    &-icon{
      font-size: 16px;
      margin-bottom: 2px;
    }
    &-text{
      font-size: 12px;
      line-height: 16px;
    }
  }
}
.recent-select-categories{
  margin-bottom: 8px;
  &-title{
    color: #898b8f;
    font-size: 14px;
    line-height: 16px;
    margin-right: 8px;
  }
  .separator{
    margin: 0 16px;
    color: #c4c4c4;
  }
}

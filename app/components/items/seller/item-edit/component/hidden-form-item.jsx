const {Form, Input} = antd
const {observer} = mobxReactLite;
const HiddenFormItem = observer((props) => {
  return <div>
    <Form.Item name={["item", "id"]} hidden={true}>
      <Input/>
    </Form.Item>
    <Form.Item name={["item", "spuId"]} hidden={true}>
      <Input/>
    </Form.Item>
    <Form.Item name={["item", "categoryId"]} hidden={true}>
      <Input/>
    </Form.Item>
    {/* -1 下架*/}
    <Form.Item name={["item", "status"]} initialValue={-1} hidden={true}>
      <Input/>
    </Form.Item>
    <Form.Item name={["item", "sourceType"]} hidden={true}>
      <Input/>
    </Form.Item>
  </div>
})
export default HiddenFormItem

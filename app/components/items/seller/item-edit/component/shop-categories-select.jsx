import request from "../../../../utils/plugins/axios/request";

const {useState, useEffect, useMemo, Fragment,useRef} = React;
const {List, Input, Button, Modal,Cascader,Tooltip,Space} = antd
const {observer} = mobxReactLite;
const {RightOutlined} = icons


const ShopCategoriesSelect = observer((props) => {
  const [recentShopCategories, setRecentShopCategories] = useState([]);
  const [open, setOpen] = useState(false);
  const [categoriesTree, setCategoriesTree] = useState([])
  const [categoriesV1, setCategoriesV1] = useState({loading: true, data: []});
  const [categoriesV2, setCategoriesV2] = useState({loading: true, data: []});
  const [categoriesV3, setCategoriesV3] = useState({loading: true, data: []});
  const [selectedFirst, setSelectedFirst] = useState(null);
  const [selectedSecond, setSelectedSecond] = useState(null);
  const [selectedThird, setSelectedThird] = useState(null);
  const [filterFirst, setFilterFirst] = useState('');
  const [filterSecond, setFilterSecond] = useState('');
  const [filterThird, setFilterThird] = useState('');
  const [showSelectOnForm, setShowSelectOnForm] = useState('');
  const currentCategoriesId = useRef()
  useEffect(()=>{
    if(open){
      const saved = localStorage.getItem('recentShopCategories');
      setRecentShopCategories(saved ? JSON.parse(saved) : [])

      request( {
        url: '/api/shopCategory/default/tree/v2',
        method: 'POST',
        data:null,
        needMask:false,
        success: (res) => {
          setCategoriesTree(res)
        }
      })
    }
  },[open])
  useEffect(() => {
    if (Array.isArray(props.value)) {
      processValue(props.value)
    }
  }, [props.value])
  function processValue(value){
    let first =null, second =null, third = null
    if(value.length>0){
      first = value[0];
      setSelectedFirst(first)
    }else{
      setSelectedFirst(null)
    }
    if(value.length>1){
      second = value[1]
      setSelectedSecond(second)
    }else{
      setSelectedSecond(null)
    }
    if(value.length>2){
      third = value[2]
      setSelectedThird(third)
    }else{
      setSelectedThird(null)
    }
    let categoryId
    if(third!=null){
      categoryId =  third.id
    }else if(second!=null){
      categoryId =  second.id
    }else if (first!=null){
      categoryId =  first.id
    }
    currentCategoriesId.current =  categoryId
    let text = value.filter(item => item !== null).map(item => item.name).reduce((acc, curr) => (acc === null ? [curr] : [...acc, ' > ', curr]), null)
    setShowSelectOnForm(text)
  }
  const fetchData = async (selectedFirstId,selectedSecondId) => {
    const resultCategoriesV1 = await getCategoriesChildren(0)
    let resultCategoriesV2={loading: false, data: []}
    if (selectedFirstId) {
      resultCategoriesV2 = await getCategoriesChildren(selectedFirstId)
    }
    let resultCategoriesV3={loading: false, data: []}
    if(selectedSecondId){
      resultCategoriesV3 = await getCategoriesChildren(selectedSecondId)
    }
    setCategoriesV1(resultCategoriesV1)
    setCategoriesV2(resultCategoriesV2)
    setCategoriesV3(resultCategoriesV3)
  };
  useEffect( () => {
    if (open) {
      fetchData(selectedFirst?selectedFirst.id:null,selectedSecond?selectedSecond.id:null).catch(_ => {
        console.log("BackCategoriesSelect fetchData fail")})
    }
  }, [open])

  function getCategoriesChildren(pid) {

    return new Promise((resolve, reject)=>{
      request({
        url: `/mall-admin/api/shop/category/children/list/by/${pid}`,
        method: "POST",
        data: {},
        success: (res) => {
          resolve({loading: false, data: res})
        }
        , fail: (_) => {
          reject({loading: false, data: []})
        }
      })
    })

  }
  function getCategories(childrenId) {
    request({
      url:  `/mall-admin/api/shop/category/current/and/parent/by/${childrenId}`,
      method: "POST",
      needMask:true,
      data: {
      },
      success: (res) => {

        processValue(res.currentShopCategoryList)
        const allShopCategoryList = res.allShopCategoryList
        if (allShopCategoryList.length > 0) {
          setCategoriesV1({loading: false, data: allShopCategoryList[0]})
        }else{
          setCategoriesV1({loading: false, data: []})
        }
        if(allShopCategoryList.length > 1){
          setCategoriesV2({loading: false, data: allShopCategoryList[1]})
        }else{
          setCategoriesV2({loading: false, data: []})
        }
        if(allShopCategoryList.length > 2){
          setCategoriesV3({loading: false, data: allShopCategoryList[2]})
        }else{
          setCategoriesV3({loading: false, data: []})
        }
      }
    })

  }
  function resetSelectedSecond() {

    setSelectedSecond(null)
    setSelectedThird(null)
    setFilterSecond("")
  }

  function resetSelectedThird() {
    setSelectedThird(null)
  }

  const handleOnCancel = () => {
    const {onClose} = props
    setOpen(false)
    onClose(false)
  }



  const handleOnOk = () => {
    let categoryId
    let lastCategory
    if(selectedThird!=null){
      categoryId =  selectedThird.id
      lastCategory = selectedThird
    }else if(selectedSecond!=null){
      categoryId =  selectedSecond.id
      lastCategory = selectedSecond
    }else if (selectedFirst!=null){
      categoryId =  selectedFirst.id
      lastCategory = selectedFirst
    }
    setRecentShopCategories(prev => {
      const filtered = prev.filter(item => item.id !== lastCategory.id);
      let newRecentShopCategories = [lastCategory, ...filtered].slice(0, 5)
      localStorage.setItem('recentShopCategories', JSON.stringify(newRecentShopCategories));
      return newRecentShopCategories ;
    });
      confirmSelect(categoryId)
  }
  function confirmSelect(categoryId) {
    const {onClose} = props
    currentCategoriesId.current = categoryId
    setOpen(false)
    let text = [selectedFirst, selectedSecond, selectedThird].filter(item => item !== null).map(item => item.name).reduce((acc, curr) => (acc === null ? [curr] : [...acc, ' > ', curr]), null)
    setShowSelectOnForm(text)
    onClose(true, [selectedFirst, selectedSecond, selectedThird])
}
  const okButtonDisabled = useMemo(() => {
    if ((categoriesV3.data || []).length > 0) {
      return selectedThird === null
    }
    if ((categoriesV2.data || []).length > 0) {
      return selectedSecond === null
    }
    if ((categoriesV1.data || []).length > 0) {
      return selectedFirst === null
    }
  }, [categoriesV1,
    categoriesV2,
    categoriesV3,
    selectedFirst,
    selectedSecond,
    selectedThird])
  const showSelect = useMemo(() => {
    const filter = [selectedFirst, selectedSecond, selectedThird].filter(item => item);
    return filter.map(item =><Button
      className={"path-node"}
      type={'link'}>{item.name}</Button>).reduce((acc, curr) => (acc === null ? [curr] : [...acc, ' > ', curr]), null)
  }, [selectedFirst, selectedSecond, selectedThird])
  return (<Fragment>
      <div className="component-category-line">
        <div className="info-cate">
          <div className="path-name">{showSelectOnForm}</div>
          <Button type="link" onClick={() => setOpen(true)}>{showSelectOnForm ?"切换":"请选择"}</Button>
        </div>
      </div>
      <Modal
        title={"选择小程序类目"}
        width={900}
        open={open}
        onCancel={handleOnCancel}
        footer={
          <Space>
            <Button  onClick={handleOnCancel}>
              取消
            </Button>
            {
            okButtonDisabled? <Tooltip title={"请选择小程序类目至最后一级"}>
                <Button type="primary" disabled onClick={handleOnOk}>确认</Button>
              </Tooltip>:
              <Button type="primary"  onClick={handleOnOk}>确认</Button>
            }
          </Space>
        }
        wrapClassName={"component-category-select-modal"}>
        <div className={"component-category"}>
          <div className="category-path-wrap category-path">
            <div className="path-info-wrap">

              <div className="path-list"><span className="path-label">已选类目:</span>
                {showSelect}
              </div>
              <div className="path-tips"></div>
            </div>
            <div className="path-help"></div>
          </div>
          <Cascader style={{width: "100%" ,marginBottom: "12px"}}
                    showSearch={{
                      filter: (inputValue, path) =>{
                       return path.some((option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
                      }
                    }}
                    options={categoriesTree}
                    placeholder="请输入商品名称关键词搜索，如：帽子、面霜"
                    onChange={(value, selectedOptions) =>{
                      processValue(selectedOptions)
                      fetchData(value.length>1?value[0]:null,value.length>2?value[1]:null)
                    }}
                    fieldNames={{label: 'name', value: 'id',}}/>
          <div className={"recent-select-categories"}>
            <span className="recent-select-categories-title">最近选择：</span>
            {(recentShopCategories || []).map((item, index, array) => (
              <Fragment key={item.id}>
                <a onClick={() => {
                  getCategories(item.id)
                }}>{item.name}</a>
                {index < array.length - 1 && <span className="separator"> | </span>}
              </Fragment>
            ))}
          </div>
          <div className={"category-lists-wrap"}>
            {/* 一级类目 */}
            <div className={"category-list"}>
              <List
                itemLayout="horizontal"
                size="small"
                loading={categoriesV1.loading}
                header={<Input className={"search-wrap"} size="small" placeholder="输入类目名称" allowClear
                               onChange={(e) => setFilterFirst(e.target.value)}/>}
                style={{width: '250px'}}
                dataSource={categoriesV1.data.filter(item => item.name.includes(filterFirst))}
                renderItem={item => (
                  <List.Item
                    extra={item.hasChildren ? <RightOutlined/> : ""}
                    className={`category-list-item ${selectedFirst && selectedFirst.name === item.name ? 'selected' : ''}`}
                    onClick={async () => {
                      setSelectedFirst(item)
                      resetSelectedSecond()
                      if(item.hasChildren) {
                        setCategoriesV2({loading: true, data: []})
                        const result = await getCategoriesChildren(item.id)
                        setCategoriesV2(result)
                      }else{
                        setCategoriesV2({loading: false, data: []})
                      }
                    }
                    }>
                    <div className={"category-name"}>{item.name}</div>
                  </List.Item>
                )}
              />
            </div>
            {/* 二级类目 */}
            {selectedFirst && (
              <div className={"category-list"}>
                <List
                  size="small"
                  itemLayout="horizontal"
                  loading={categoriesV2.loading}
                  header={<Input className={"search-wrap"} size="small" placeholder="输入类目名称" allowClear
                                 onChange={(e) => setFilterSecond(e.target.value)}/>}
                  style={{width: '250px'}}
                  dataSource={categoriesV2.data.filter(item => item.name.includes(filterSecond))}
                  renderItem={item => (
                    <List.Item
                      extra={item.hasChildren ? <RightOutlined/> : ""}
                      className={`category-list-item ${selectedSecond && selectedSecond.name === item.name ? 'selected' : ''}`}
                      onClick={async () => {
                        setSelectedSecond(item)
                        resetSelectedThird()
                        if(item.hasChildren){
                          const result = await getCategoriesChildren(item.id)
                          setCategoriesV3(result)
                        }else{
                          setCategoriesV3({loading: false, data: []})
                        }
                      }}>
                      <div className={"category-name"}>{item.name}</div>
                    </List.Item>
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </Modal>
    </Fragment>
  );
})
export default ShopCategoriesSelect

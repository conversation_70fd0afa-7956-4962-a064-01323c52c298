import TradeInformation from "./trade-information";
import BackCategoriesSelect from "./back-categories-select";
import itemEditStore from "../ItemEditStore";
import request from "../../../../utils/plugins/axios/request";
import { lib } from "../../../../common/react/utils/lib";
import ShopCategoriesSelect from "./shop-categories-select";
const { useState, useCallback,useEffect } = React;
const { Form, AutoComplete,Cascader, Input, message } = antd;
const { DTInput, FormSelect } = dtComponents
const { observer } = mobxReactLite;

function BackCategoriesSelectFormItem({ value, onChange }) {
  return <BackCategoriesSelect value={value} onClose={(success, categories) => {
    if (success) {
      if ((categories || []).length > 0) {
        onChange(categories)
      } else {
        message.error("未选择商品类目")
      }
    }
  }
  } />
}
function ShopCategoriesSelectFormItem({ value, onChange }) {
  return <ShopCategoriesSelect value={value} onClose={(success, categories) => {
    if (success) {
      if ((categories || []).length > 0) {
        onChange(categories)
      } else {
        message.error("未选择小程序类目类目")
      }
    }
  }
  } />
}

/**
 * 商品基本信息
 */
const BaseInfo = observer((props) => {
  const [brandOptions, setBrandOptions] = useState([])
  const [shopCategoryOptions, setShopCategoryOptions] = useState([])
  // 社群运营
  const isCommunityOperation = lib.isCommunityOperation()
  useEffect(()=>{
    request({
      url: '/api/shopCategory/default/tree/v2',
      method: "POST",
      success: (data) => {
        setShopCategoryOptions(data)
      }
    })
  },[])

  const searchBrand = (value) => {
    request({
      url: '/api/brands/v2',
      method: "POST",
      data: {
        name: value
      },
      success: (data) => {
        setBrandOptions(data)
      }
    })
  }

  // 使用 lodash.debounce 对 fetchOptions 进行防抖处理
  const debouncedFetchOptions = useCallback(_.debounce(searchBrand, 300), []);

  const handleSearch = (value) => {
    debouncedFetchOptions(value); // 防抖处理
  };

  return <div>
    {/* 商品分类 */}
    <Form.Item
      label="当前类目"
      name={"backCategoryList"}
      rules={[{ required: true, message: '请选择商品类目' }]}>
      <BackCategoriesSelectFormItem />
    </Form.Item>
    <Form.Item
      label="小程序类目"
      name={"shopCategoryList"}>
      <ShopCategoriesSelectFormItem/>
    </Form.Item>
    {/* 商品ID */}
    {itemEditStore.itemId ? <Form.Item
      label="商品ID"
      name="itemId">
      {itemEditStore.itemId}
    </Form.Item> : null
    }

    {/* 商品名称 */}
    <Form.Item
      label="商品名称"
      name={["item", "name"]}
      rules={[
        { required: true, message: '请输入商品名称' },
        {
          validator: (_, value) => {
            if (value) {
              let length = value.trim().replace(/[^\x00-\xff]/g, "**").length;
              if (length < 15) {
                return Promise.reject(new Error("请输入至少8个汉字或15个字符"));
              }
              if (length > 120) {
                return Promise.reject(new Error("请输入不超过30个汉字或60个字符"));
              }
            }
            return Promise.resolve();
          },
        }
      ]}
    >
      <DTInput trimMode={"all"} placeholder="至少输入8个字（15个字符）以上30个字（60个字符）以下" />
    </Form.Item>
    {/* <Form.Item
      label="品牌"
      name={["item", "brandName"]}>
      <AutoComplete
        onSearch={(value) => {handleSearch(value)}} options={brandOptions} fieldNames={{label: "name", value: "name"}}
        onSelect={(value,option) => {
          itemEditStore.form.setFieldValue(["item", "brandId"], option.id)
        }}
      />
    </Form.Item> */}

    <FormSelect
      fProps={{
        label: '品牌',
        name: ["item", "brandId"],
      }}
      cProps={{
        placeholder: "请选择品牌",
        allowClear: true,
      }}
      url="/api/itemBrand/drop/down/box"
    />

    {/* 广告语 */}
    <Form.Item
      label="广告语"
      name={["item", "advertise"]}
      rules={[
        {
          validator: (_, value) => {
            if (value) {
              let length = value.trim().replace(/[^\x00-\xff]/g, "**").length;
              if (length < 15) {
                return Promise.reject(new Error("请输入至少8个汉字或15个字符"));
              }
              if (length > 120) {
                return Promise.reject(new Error("请输入不超过60个汉字或120个字符"));
              }
            }
            return Promise.resolve();
          },
        }
      ]}
      tooltip={"广告语会出现在前台商品标题下方"}
    >
      <DTInput trimMode={"all"} placeholder="请输入广告语" />
    </Form.Item>
    <TradeInformation />
    <div>
    </div>
  </div>
})

export default BaseInfo

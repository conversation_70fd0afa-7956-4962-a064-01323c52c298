import itemEditStore from "../ItemEditStore";
import {isJDItem} from "../utils/method";
import {lib} from "../../../../common/react/utils/lib";

const {useEffect} = React;

const {Form, Select, Input, Radio, Row, Col} = antd;
const {observer} = mobxReactLite;
/**
 * 贸易信息
 */
const TradeInformation = observer((props) => {
  const commonAttrsFormItmProps = {labelAlign: "right", labelCol: {flex: "120px"}}
  const isBondedFormName = ["item", "isBonded"]
  const isBonded = Form.useWatch(isBondedFormName, itemEditStore.form);
  let isPackage = lib.getParam("package") ==="1"
  useEffect(() => {

  }, [])
  const defaultIsBonded= lib.getParam("isThirdPartyItem")==1?{}:{initialValue:1}
  return <div>
    <Form.Item label="贸易信息" wrapperCol={{span: 22}}>
      {/* 贸易类型 */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="贸易类型"
            name={isBondedFormName}
            rules={[{required: true, message: '请选择贸易类型'}]}
            { ...defaultIsBonded}
          >
            <Radio.Group disabled={itemEditStore.isThirdPartyItem===1||(isPackage&& itemEditStore.isEdit)}>
              <Radio value={1}>全球购（保税）</Radio>
              <Radio value={0}>大贸（完税）</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
      </Row>
      {isBonded === 1 ?
        <div className={"common-wrap"}>
          <Row gutter={16}>
            {
              isPackage ? null : <Col span={12}>
                <Form.Item
                  {...commonAttrsFormItmProps}
                  label="HS海关代码"
                  name={["skuCustom", "hsCode"]}
                  rules={[{required: !isJDItem(itemEditStore.sourceType), message: '请输入HS海关代码'},
                    {
                      pattern: /^([0-9][0-9]*)$/,
                      message: "请输入数字",
                    },]}
                >
                  <Input placeholder="请输入" className={"attr-item-input"} maxLength={10}/>
                </Form.Item>
              </Col>
            }


            <Col span={12}>
              <Form.Item
                {...commonAttrsFormItmProps}
                label="商品原产地"
                name={["skuCustom", "customOriginId"]}
                rules={[{required: true, message: '请选择商品原产地'}]}>
                <Select showSearch={true}
                // optionFilterProp="children"
                filterOption={(input, option) => (option.name || '').toLowerCase().includes(input.toLowerCase())}

                className={"attr-item-select"} placeholder="请选择商品原产地" options={itemEditStore.listCountryOption} fieldNames={{label: "name", value: "id"}}/>

              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                {...commonAttrsFormItmProps}
                label="税金承担方"
                name={["skuCustom", "customTaxHolder"]}
                rules={[{required: true, message: '请选择税金承担方'}]}
              >
                <Select placeholder="请选择" className={"attr-item-select"} disabled={isJDItem(itemEditStore.sourceType)}>
                  <Option value={1}>买家承担税费</Option>
                  <Option value={2}>商家承担税费</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                {...commonAttrsFormItmProps}
                label="海关"
                name={[ "skuCustom", "town"]}
                rules={[{required: true, message: '请选择海关'}]}
              >
                <Select className={"attr-item-select"} placeholder="请选择海关" options={itemEditStore.customsTownOption} fieldNames={{label: "name", value: "name"}}/>
              </Form.Item>
            </Col>
          </Row>
        </div> : null
      }
    </Form.Item>
  </div>
})

export default TradeInformation

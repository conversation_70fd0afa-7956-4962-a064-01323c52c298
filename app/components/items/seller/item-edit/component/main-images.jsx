import {MainImagesFormItem, SubImagesFormItem} from "./base/item-image-form-item";
import DetailImgList from "./detail-img-list";
import {MainVideoFormItem} from "./base/item-video-form-item";
import {lib} from "../../../../common/react/utils/lib";

const {Form} = antd;
const {observer} = mobxReactLite;


const MainImagesGroup = observer((props) => {

  return <div className={"goods-images-group"}>
    <div className={"image-list"}>
      <Form.Item name={["item", "mainImageFileList"]} noStyle
                 rules={[{
                   validator: (_, value) => {
                     if (Array.isArray(value) && value[0]) {
                       return Promise.resolve();
                     }
                     return Promise.reject(new Error("请上传商品主图"));
                   },
                 }]}>
        <MainImagesFormItem/>
      </Form.Item>
      <Form.Item name={["itemDetail", "images"]} noStyle>
        <SubImagesFormItem/>
      </Form.Item>
    </div>
  </div>
})

const MainImages = observer((props) => {

  return <div>
    <Form.Item
      label="商品图片"
      extra={"仅支持png，jpg，jpeg格式，宽高比例为1:1（至少600*600px），大小5M内"}
      required={true}>
      <MainImagesGroup/>
    </Form.Item>
    {
      lib.isCommunityOperation()?<Form.Item
        label="主图视频"
        name={["itemDetail", "mainVideo"]}
        extra={"仅支持mp4格式上传，大小100M内，比例支持1:1、3:4 (分辨率不低于720P)"}>
        <MainVideoFormItem/>
      </Form.Item>:null
    }
    <Form.Item label="商品详情"
               name={["itemDetail", "imageList"]}
               wrapperCol={{width: "calc(100% - 160px)"}}
               rules={[{required: true, message: '请完善商品详情'}]}>
      <DetailImgList/>
    </Form.Item>

  </div>
})
export default MainImages

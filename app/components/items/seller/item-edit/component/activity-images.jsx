import {MainImagesFormItem, SubImagesFormItem} from "./base/item-image-form-item";
import itemEditStore from "../ItemEditStore";
import {
  activityMainImageFileListFormName, activitySubImageFileListFormName,
  activitySalesImageSwitchFormName, activityImageTimeFormName
} from "../utils/form-name-const";

const {useEffect} = React
const {Form, DatePicker, Checkbox} = antd;
const {RangePicker} = DatePicker;
const {observer} = mobxReactLite;

const MainImagesGroup = observer((props) => {
  return <div className={"goods-images-group"}>
    <div className={"image-list"}>
      <Form.Item name={activityMainImageFileListFormName} noStyle
                 rules={[{
                   validator: (_, value) => {
                     if (Array.isArray(value) && value[0]) {
                       return Promise.resolve();
                     }
                     return Promise.reject(new Error("请上传商品主图"));
                   },
                 }]}>
        <MainImagesFormItem/>
      </Form.Item>
      <Form.Item name={activitySubImageFileListFormName} noStyle>
        <SubImagesFormItem/>
      </Form.Item>
    </div>
  </div>
})
const ActivityImages = observer((props) => {
  const activityImageSwitch = Form.useWatch(activitySalesImageSwitchFormName, itemEditStore.form);
  useEffect(() => {
    if (activityImageSwitch === 0) {
      itemEditStore.form.setFieldValue(activityImageTimeFormName, null)
      itemEditStore.form.setFieldValue(activityMainImageFileListFormName, null)
      itemEditStore.form.setFieldValue(activitySubImageFileListFormName, null)
    }
  }, [activityImageSwitch])
  return <div>
    {/* 活动销售价 */}
    <Form.Item label="活动图文"
               name={activitySalesImageSwitchFormName}
               valuePropName="checked"
               initialValue={0}
               getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
      <Checkbox>是否开启活动</Checkbox>
    </Form.Item>
    {activityImageSwitch ?
      <div className={"common-wrap"} >
        <Form.Item
          label="活动时间"
          name={activityImageTimeFormName}
          rules={[{required: true, message: '请选择活动时间'}]}
        >
          <RangePicker showTime/>
        </Form.Item>
        <Form.Item
          label="商品图片"
          extra={"仅支持png，jpg，jpeg格式，宽高比例为1:1（至少600*600px），大小5M内"}
          rules={[{required: true}]}
          required={true}>
          <MainImagesGroup/>
        </Form.Item>
      </div>:null
    }

  </div>
})
export default ActivityImages

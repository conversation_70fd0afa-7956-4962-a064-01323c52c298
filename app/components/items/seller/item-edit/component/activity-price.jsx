import {ItemPriceInput} from "./base/item-price-input";
import itemEditStore from "../ItemEditStore";
import {
  activitySalesPriceSwitchFormName,
  activityPriceTimeFormName,
  activitySalesPriceFormName, specDetailFormName
} from "../utils/form-name-const";
import ActivityPriceSpec from "./activity-price-spec";
import {validatePriceNumber} from "../utils/method";

const {useEffect,Fragment} = React
const {Form, DatePicker, Checkbox} = antd;
const {RangePicker} = DatePicker;
const {observer} = mobxReactLite;

const ActivityPrice = observer((props) => {

  const activitySalesPriceSwitch = Form.useWatch(activitySalesPriceSwitchFormName, itemEditStore.form);
  useEffect(() => {
    if (activitySalesPriceSwitch === false) {
      itemEditStore.form.setFieldValue(activityPriceTimeFormName, null)
      itemEditStore.form.setFieldValue(activitySalesPriceFormName, null)
    }
  }, [activitySalesPriceSwitch])
  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };
  return <div>
    {/* 活动销售价 */}
    <Form.Item label="活动销售价" name={activitySalesPriceSwitchFormName}
               valuePropName="checked"
               initialValue={0}
               getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
      <Checkbox>是否开启活动</Checkbox>
    </Form.Item>
    {
      activitySalesPriceSwitch ?
        <Fragment>
          {
            itemEditStore.showSpecDetail ?<ActivityPriceSpec/> : <div className={"common-wrap"}>
              <Form.Item
                label="活动价格"
                name={activitySalesPriceFormName}
                rules={[{required: true, message: '请输入活动价格'}, { validator: validatePriceNumber }]}
              >
                <ItemPriceInput  addonAfter="元" placeholder={"活动价"}/>
              </Form.Item>
              <Form.Item
                label="活动时间"
                name={activityPriceTimeFormName}
                rules={[{required: true, message: '请选择活动时间'}]}>
                <RangePicker showTime disabledDate={disabledDate}/>
              </Form.Item>
            </div>
          }
        </Fragment> :null
    }

  </div>
})
export default ActivityPrice

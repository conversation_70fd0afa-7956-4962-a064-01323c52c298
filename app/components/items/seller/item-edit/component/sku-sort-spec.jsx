
const  { useState,useEffect } =React;
const { Modal, Button,Tooltip } = antd;

const {
  DndKitDndContext,
  DndKitMouseSensor,
  useDndKitSensor,
  useDndKitSortable,
  useDndKitSensors,
  dndKitClosestCenter,
  dndKitArrayMove,
  DndKitSortableContext,
  DndRectSortingStrategy,
  DndKitCSS,

} = dtDnd



const SortableItem = ({ value }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useDndKitSortable({ id: value.frontId });

  const style = {
    transform: DndKitCSS.Transform.toString(transform),
    transition,
    cursor: 'grab',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={"style_sortItem style_sortItemHasMargin"}
      {...attributes}
      {...listeners}
    >
      {value.name}
    </div>
  );
};

 function SkuSortSpecModal({ open, onClose,specGroupValue}) {
   const [items, setItems] = useState([]);
   useEffect(() => {
     if(open && Array.isArray(specGroupValue)){
       setItems(fitterSpecGroupValue(specGroupValue));
     }
     return () => {
       setItems([])
     }

  }, [open]);

  function fitterSpecGroupValue(specGroupValue){
    return [...specGroupValue.filter(item => item.name)].map(item => ({
      id: item.frontId,
      frontId: item.frontId,
      name: item.name
    }))
   }
  // 仅保留鼠标传感器
  const sensors = useDndKitSensors(
    useDndKitSensor(DndKitMouseSensor, {
      activationConstraint: {
        distance: 3 // 最小拖拽触发距离
      }
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((item) => {
        const oldIndex = item.findIndex(it=>it.id===active.id)
        const newIndex = item.findIndex(it=>it.id===over.id);
        return dndKitArrayMove(items, oldIndex, newIndex);
      });
    }
  };

   const handleReset = () => {setItems(fitterSpecGroupValue(specGroupValue))};
   const handleOk = () => onClose(true,items); // 传递排序结果给父组件

  return (
    <Modal
      title="颜色分类排序"
      open={open}
      onCancel={() => onClose(false)}
      footer={<div>
        <Button key="reset" type={'link'} style={{float:'left' }} onClick={handleReset}>重置</Button>
        <Button key="ok" type="primary" onClick={handleOk}>确定</Button>
        <Button key="cancel" onClick={() => onClose(false)}>取消</Button>
      </div>
      }
    >
      <DndKitDndContext
        sensors={sensors}
        collisionDetection={dndKitClosestCenter}
        onDragEnd={handleDragEnd}
      >
        <DndKitSortableContext
          items={items}
          strategy={DndRectSortingStrategy}
        >
          <div className="style_sortGround">
            {items.map((value) => (
              <SortableItem key={value.frontId} value={value} />
            ))}
          </div>
        </DndKitSortableContext>
      </DndKitDndContext>
    </Modal>
  );
}
const SkuSortSpec = ({specGroupValue,onSorted}) => {
  const [visible, setVisible] = useState(false);

  return (
    <div>
      {
        (specGroupValue.filter(item => item.name)||[]).length>1 ?
          <div className="style_sortButton" onClick={() => setVisible(true)}>自定义排序</div>
          :<Tooltip title={"至少添加2个规格值后，才能排序"}>
            <div className="style_sortButton style_disabled">自定义排序</div>
          </Tooltip>
      }

      <SkuSortSpecModal
        specGroupValue={specGroupValue}
        open={visible}
        onClose={(confirmed,result) => {
          if (confirmed) {
            // 处理确认逻辑
            onSorted && onSorted(result)
          }
          setVisible(false);
        }}
      />
    </div>
  );
};

export default SkuSortSpec;

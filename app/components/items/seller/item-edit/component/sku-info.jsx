import SkuPrice from "./sku-price";
import itemEditStore from "../ItemEditStore";
import SkuSpecTableForm from "./sku-spec-price";
import {skuSalesPriceFormName} from "../utils/form-name-const";
const {Form} = antd;
const {observer} = mobxReactLite;
const {Fragment,useMemo} = React;

const SkuInfo = observer((props) => {
  return <Fragment>
    {
      itemEditStore.showSpecDetail ?<SkuSpecTableForm/>: <SkuPrice/>
    }
  </Fragment>
})
export default SkuInfo

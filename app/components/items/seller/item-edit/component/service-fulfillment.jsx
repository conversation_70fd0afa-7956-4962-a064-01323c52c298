import itemEditStore from "../ItemEditStore";
import {isJDItem} from "../utils/method";
const {Form,Select} = antd;
const {observer} = mobxReactLite;
const ServiceFulfillment = observer((props) => {

  return <div>
    <Form.Item
      label="运费模板"
      name={["itemDeliveryFee","deliveryFeeTemplateId"]}
      rules={[{required: true, message: '请选择运费模板'}]}
      tooltip={ isJDItem(itemEditStore.sourceType)?"京东云交易商品需商家承担运费":null}
    >
      <Select placeholder="请选择运费模板" options={itemEditStore.deliveryFeeTemplateOption} fieldNames={{label: "name", value: "id"}}/>
    </Form.Item>

    {/* 可售地区设置 */}
    <Form.Item
      label="可售地区设置"
      name={["item","restrictedSalesAreaTemplateId"]}
      rules={[{required: true, message: '请选择可售地区模板'}]}
    >
      <Select placeholder="请选择可售地区模板" options={itemEditStore.salesAreaTemplateOption} fieldNames={{label: "name", value: "id"}}/>
    </Form.Item>
  </div>
})
export default ServiceFulfillment

import {ItemPriceInput} from "./base/item-price-input";

const {useState, useEffect, useMemo,useRef} = React;
import itemEditStore from "../ItemEditStore";
import {
  activitySalesPriceSwitchFormName,
  skuSalesPriceFormName,
} from "../utils/form-name-const";
import {TableCellErrorView} from "./sku-spec-price";
import Schema from "../utils/async-validator";
import {validatePriceNumber} from "../utils/method";
import {activityValidator, validator} from "../utils/sku-validator";

const {Form, Table, DatePicker, Tooltip, Button} = antd;
const {RangePicker} = DatePicker;
const {observer} = mobxReactLite;

const ActivityPriceSpec = observer((props) => {
  const [displayBatchSetting, setDisplayBatchSetting] = useState(false);
  const activitySalesPriceSwitch = Form.useWatch(activitySalesPriceSwitchFormName, itemEditStore.form);
  const [dataSource, setDataSource] = useState([])
  useEffect(() => {
    setDataSource([...itemEditStore.processedTableData])
  }, [itemEditStore.processedTableData])
  useEffect(() => {
    if (activitySalesPriceSwitch === false) {
      // if(Array.isArray(skuDetail)){
      //   itemEditStore.form.setFieldValue(skuSalesPriceFormName, skuDetail.map(item=>{
      //     if(item.sku.extraMap){
      //       item.sku.extraMap.activityPriceTime =null
      //       item.sku.extraMap.activitySalesPrice =null
      //     }
      //     return item
      //   }))
      // }
    }
  }, [activitySalesPriceSwitch])


  const [batchForm] = Form.useForm()
  const batchInputValue = () => {
    batchForm.validateFields().then((values) => {
      let oldSkuInfo = itemEditStore.form.getFieldValue(skuSalesPriceFormName)
      oldSkuInfo.map(item => {
        if (values.activitySalesPrice) {
          item.sku.extraMap.activitySalesPrice = values.activitySalesPrice
        }
        if (values.activityPriceTime) {
          item.sku.extraMap.activityPriceTime = values.activityPriceTime
        }
        return item
      })
      itemEditStore.form.setFieldValue(skuSalesPriceFormName, oldSkuInfo)
      setDataSource([...dataSource])
    })
  }
  const checkSetDisplayBatchSetting = (values) => {
    if (values.activityPriceTime || values.activitySalesPrice) {
      setDisplayBatchSetting(true)
    } else {
      setDisplayBatchSetting(false)
    }
  }


  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };
  return <div>
    <div className={"batch_wrapper"}>
      <Form form={batchForm}
            layout={"inline"}
            onValuesChange={(_, values) => {
              checkSetDisplayBatchSetting(values)
            }}>
        <div className={"styles_inputBox"} style={{width: 180, maxWidth: 200}}>
          <Form.Item
            label=""
            name={"activitySalesPrice"}
          >
            <ItemPriceInput addonAfter="元" placeholder={"活动价"}/>
          </Form.Item>
        </div>
        <div className={"styles_inputBox"} style={{width: 400, maxWidth: 450}}>
          <Form.Item
            label=""
            name={"activityPriceTime"}>
            <RangePicker showTime disabledDate={disabledDate}/>
          </Form.Item>
        </div>
        {
          displayBatchSetting ?
            <Button type="primary" ghost onClick={() => {
              batchInputValue()
            }}>批量设置</Button> :
            <Tooltip title={"请先输入值"}>
              <Button type="primary" ghost disabled>批量设置</Button>
            </Tooltip>
        }
      </Form>
    </div>
    <Form.Item name={skuSalesPriceFormName} noStyle>
      <ActivityPriceTable dataSource={dataSource}/>
    </Form.Item>
  </div>
})
const ActivityPriceTable = observer((props) => {

  const {dataSource, onChange, value} = props
  const innerChange = async (values,record,index) => {
    if (record.sku.errors) {
      try {
        await validator.validate(record)
        values[index].sku.errors = null
      } catch ({errors, fields}) {
        if (errors.length > 0) {
          record.sku.errors = fields
        } else {
          record.sku.errors = null
        }
      }
    }
    onChange(values)
  }
  const mergeDataSource = useMemo(() => {
    return value
  }, [value, dataSource])
  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };
  const columns = [
    ...itemEditStore.specs.map(spec => ({
      title: spec.name,
      width: 100,
      fixed: "left",
      dataIndex: spec.name,
      render: (value, record, index) => {
        const span = itemEditStore.processedTableData[index][`${spec.name}Span`] || 0;
        return {children: itemEditStore.processedTableData[index][`${spec.name}`], props: {rowSpan: span}};
      }
    })),
    {
      title: <span><span className="styles_required">*</span>活动价格</span>,
      width: 120,
      render: (_, record, index) => {
        return (
          // <Form.Item
          //   label=""
          //   wrapperCol={{span: 24}}
          //   name={["skuWithCustoms", index, "sku", "extraMap", "activitySalesPrice"]}
          //   rules={[{required: true, message: '请输入活动价格'}]}
          // >
          <TableCellErrorView record={record} name={"sku.extraMap.activitySalesPrice"}>
            <ItemPriceInput addonAfter="元" placeholder={"活动价"}
                            value={record.sku.extraMap ? Number(record.sku.extraMap.activitySalesPrice) : null}
                            onChange={(e) => {
                              value[index].sku.extraMap.activitySalesPrice = e
                              innerChange([...value],record,index)

                            }}/>
          </TableCellErrorView>
          // </Form.Item>
        )
      },
    },
    {
      title: <span><span className="styles_required">*</span>活动时间</span>,
      width: 400,
      render: (_, record, index) => {
        return (
          // <Form.Item
          //   label=""
          //   wrapperCol={{span: 24}}
          //   name={["skuWithCustoms", index, "sku", "extraMap", "activityPriceTime"]}
          //   rules={[{required: true, message: '请选择活动时间'}]}>
          <TableCellErrorView record={record} name={"sku.extraMap.activityPriceTime"}>
            <RangePicker showTime
                         disabledDate={disabledDate}
                         value={record.sku.extraMap.activityPriceTime ? record.sku.extraMap.activityPriceTime : undefined}
                         onChange={(e) => {
                           value[index].sku.extraMap.activityPriceTime = e
                           innerChange([...value],record,index)
                         }}/>
          </TableCellErrorView>
          // </Form.Item>
        )
      }
    },
  ];
  return <Table columns={columns}
                scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0), y: 500}}
                dataSource={mergeDataSource}
                pagination={false}
                rowKey="id"
                bordered/>
})
export default ActivityPriceSpec



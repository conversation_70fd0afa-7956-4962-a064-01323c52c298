import {ItemPriceInput} from "./base/item-price-input";
import itemEditStore from "../ItemEditStore";
import {
  activitySalesPriceSwitchFormName, skuSalesPriceFormName
} from "../utils/form-name-const";
import {validatePriceNumber} from "../utils/method";

const {useEffect,Fragment,useMemo,useState} = React
const {Form, DatePicker, Checkbox,Table,Tooltip,Button} = antd;
const {RangePicker} = DatePicker;
const {observer} = mobxReactLite;

const PackageActivityPrice = observer((props) => {
  const [displayBatchSetting, setDisplayBatchSetting] = useState(false);
  const activitySalesPriceSwitch = Form.useWatch(activitySalesPriceSwitchFormName, itemEditStore.form);
  useEffect(() => {
    if (activitySalesPriceSwitch === false) {
       const skuSalesPriceFormValue = itemEditStore.form.getFieldValue(skuSalesPriceFormName)
        if(Array.isArray(skuSalesPrice)){
          itemEditStore.form.setFieldValue(skuSalesPriceFormName,  skuSalesPriceFormValue.map(item=>{
            if(item.sku.extraMap){
              item.sku.extraMap.activitySalesPrice =null
              item.sku.extraMap.activityPriceTime =null
            }
            return item
          }))
      }
    }
  }, [activitySalesPriceSwitch])


  const columns = [
    {
      title: "组合名称",
      width: 120,
      dataIndex: "name",
      render: (_, record,index) => {
        return record.sku?record.sku.name:""
      }

    },
    {
      title: <span><span className="styles_required">*</span>活动销售价</span>,
      width: 120,
      render: (_, record,index) => (
        <Form.Item
          name={["skuWithCustoms", index, "sku", "extraMap", "activitySalesPrice"]}
          rules={[{required: true, message: '请输入活动销售价'}, { validator: validatePriceNumber }]}
        >
          <ItemPriceInput  addonAfter="元" placeholder={"活动价"} />
        </Form.Item>
      ),
    },
    {
      title:  <span><span className="styles_required">*</span>活动时间</span>,
      width: 120,
      render: (_, record,index) => (
        <Form.Item
          name={["skuWithCustoms", index, "sku", "extraMap", "activityPriceTime"]}
          rules={[{required: true, message: '请选择活动时间'}]}>
          <RangePicker showTime disabledDate={disabledDate}/>
        </Form.Item>
      ),
    },
  ]
  const [batchForm] = Form.useForm()
  const batchInputValue = () => {
    batchForm.validateFields().then((values) => {
      let oldSkuInfo = itemEditStore.form.getFieldValue(skuSalesPriceFormName)
      oldSkuInfo.map(oldSkuInfo => {
        if (values.activitySalesPrice) {
          oldSkuInfo.sku.extraMap.activitySalesPrice = values.activitySalesPrice
        }
        if (values.activityPriceTime) {
          oldSkuInfo.sku.extraMap.activityPriceTime = values.activityPriceTime
        }
        return oldSkuInfo
      })
      itemEditStore.form.setFieldValue(skuSalesPriceFormName, oldSkuInfo)
    })
  }
  const checkSetDisplayBatchSetting = (values) => {
    if (values.activityPriceTime || values.activitySalesPrice) {
      setDisplayBatchSetting(true)
    } else {
      setDisplayBatchSetting(false)
    }
  }
  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };
  return <div>
    {/* 活动销售价 */}
    <Form.Item label="活动销售价" name={activitySalesPriceSwitchFormName}
               valuePropName="checked"
               initialValue={0}
               getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
      <Checkbox>是否开启活动</Checkbox>
    </Form.Item>
    {
      activitySalesPriceSwitch ?
        <Fragment>
          <div className={"batch_wrapper"}>
            <Form form={batchForm}
                  layout={"inline"}
                  onValuesChange={(_, values) => {
                    checkSetDisplayBatchSetting(values)
                  }}>
              <div className={"styles_inputBox"} style={{width: 180,maxWidth: 200}}>
                <Form.Item
                  label=""
                  name={"activitySalesPrice"}
                >
                  <ItemPriceInput addonAfter="元"  placeholder={"活动价"}/>
                </Form.Item>
              </div>
              <div className={"styles_inputBox"} style={{width: 400,maxWidth: 450}}>
                <Form.Item
                  label=""
                  name={"activityPriceTime"}>
                  <RangePicker showTime disabledDate={disabledDate}/>
                </Form.Item>
              </div>
              {
                displayBatchSetting ?
                  <Button type="primary" ghost onClick={() => {
                    batchInputValue()
                  }}>批量设置</Button> :
                  <Tooltip title={"请先输入值"}>
                    <Button type="primary" ghost disabled>批量设置</Button>
                  </Tooltip>
              }
            </Form>
          </div>
          <Table columns={columns}
                 tableLayout="fixed"
                 scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0),y: 500}}
                 dataSource={itemEditStore.processedTableData}
                 pagination={false}
                 rowKey="id"
                 bordered />
        </Fragment> :null
    }

  </div>
})
export default PackageActivityPrice

import itemEditStore from "../ItemEditStore";
import request from "../../../../utils/plugins/axios/request";

const {useState, useEffect, useMemo, Fragment,useRef} = React;
const {List, Input, Button, Modal,Cascader,Tooltip,Space} = antd
const {observer} = mobxReactLite;
const {RightOutlined} = icons


const BackCategoriesSelect = observer((props) => {
  const [recentBackCategories, setRecentBackCategories] = useState([]);
  const [open, setOpen] = useState(false);
  const [categoriesTree, setCategoriesTree] = useState([])
  const [categoriesV1, setCategoriesV1] = useState({loading: true, data: []});
  const [categoriesV2, setCategoriesV2] = useState({loading: true, data: []});
  const [categoriesV3, setCategoriesV3] = useState({loading: true, data: []});
  const [selectedFirst, setSelectedFirst] = useState(null);
  const [selectedSecond, setSelectedSecond] = useState(null);
  const [selectedThird, setSelectedThird] = useState(null);
  const [filterFirst, setFilterFirst] = useState('');
  const [filterSecond, setFilterSecond] = useState('');
  const [filterThird, setFilterThird] = useState('');
  const [showSelectOnForm, setShowSelectOnForm] = useState('');
  const currentCategoriesId = useRef()
  useEffect(()=>{
    if(open){
      request( {
        url: '/mall-admin/api/back/categories/with/children/tree',
        method: 'POST',
        needMask:false,
        success: (res) => {
          setCategoriesTree(res)
        }
      })
    }
  },[open])
  useEffect(() => {
    if (Array.isArray(props.value)) {
      processValue(props.value,true)
    }
  }, [props.value])
  function processValue(value,shouldSetCurrentCategoriesId){
    let first =null, second =null, third = null
    if(value.length>0){
      first = value[0];
      setSelectedFirst(first)
    }else{
      setSelectedFirst(null)
    }
    if(value.length>1){
      second = value[1]
      setSelectedSecond(second)
    }else{
      setSelectedSecond(null)
    }
    if(value.length>2){
      third = value[2]
      setSelectedThird(third)
    }else{
      setSelectedThird(null)
    }
    if(shouldSetCurrentCategoriesId){
      let categoryId
      if(third!=null){
        categoryId =  third.id
      }else if(second!=null){
        categoryId =  second.id
      }else if (first!=null){
        categoryId =  first.id
      }
      currentCategoriesId.current =  categoryId
    }
    let text = value.filter(item => item !== null).map(item => item.name).reduce((acc, curr) => (acc === null ? [curr] : [...acc, ' > ', curr]), null)
    setShowSelectOnForm(text)
  }
  const fetchData = async (selectedFirstId,selectedSecondId) => {
    const resultCategoriesV1 = await getCategoriesChildren(0)
    let resultCategoriesV2={loading: false, data: []}
    if (selectedFirstId) {
      resultCategoriesV2 = await getCategoriesChildren(selectedFirstId)
    }
    let resultCategoriesV3={loading: false, data: []}
    if(selectedSecondId){
      resultCategoriesV3 = await getCategoriesChildren(selectedSecondId)
    }
    setCategoriesV1(resultCategoriesV1)
    setCategoriesV2(resultCategoriesV2)
    setCategoriesV3(resultCategoriesV3)
  };
  useEffect( () => {
    if (open) {
      const saved = localStorage.getItem('recentBackCategories');
      setRecentBackCategories(saved ? JSON.parse(saved) : [])
      fetchData(selectedFirst?selectedFirst.id:null,selectedSecond?selectedSecond.id:null).catch(_ => {
        console.log("BackCategoriesSelect fetchData fail")})
    }
  }, [open])

  function getCategoriesChildren(pid) {

    return new Promise((resolve, reject)=>{
      request({
        url: "/api/backCategories/children/v2",
        method: "GET",
        params: {
          pid: pid
        },
        success: (res) => {
          resolve({loading: false, data: res})
        }
        , fail: (_) => {
          reject({loading: false, data: []})
        }
      })
    })

  }
  function getCategories(childrenId) {
      request({
        url:  `/mall-admin/api/back/categories/current/and/parent/by/${childrenId}`,
        method: "POST",
        needMask:true,
        data: {
        },
        success: (res) => {
          processValue(res.currentBackCategoryList,false)
          const allBackCategoryList = res.allBackCategoryList
          if (allBackCategoryList.length > 0) {
            setCategoriesV1({loading: false, data: allBackCategoryList[0]})
          }else{
            setCategoriesV1({loading: false, data: []})
          }
          if(allBackCategoryList.length > 1){
            setCategoriesV2({loading: false, data: allBackCategoryList[1]})
          }else{
            setCategoriesV2({loading: false, data: []})
          }
          if(allBackCategoryList.length > 2){
            setCategoriesV3({loading: false, data: allBackCategoryList[2]})
          }else{
            setCategoriesV3({loading: false, data: []})
          }
        }
    })

  }

  function resetSelectedSecond() {

    setSelectedSecond(null)
    setSelectedThird(null)
    setFilterSecond("")
    setFilterThird("")
  }

  function resetSelectedThird() {
    setSelectedThird(null)
    setFilterThird("")
  }

  const handleOnCancel = () => {
    const {onClose} = props
    setOpen(false)
    onClose(false)
  }

  function getCategoriesAttr(categoryId) {
    request({
      url: "/api/backCategories/grouped-attribute/v2",
      method: "GET",
      params: {
        id: categoryId
      },
      success: (res) => {
        itemEditStore.setOtherAttrs([])
        itemEditStore.setCategoryAttrs(res)
        confirmSelect(categoryId)
      }
    })
  }

  const handleOnOk = () => {
    let categoryId
    let lastCategory
    if(selectedThird!=null){
      categoryId =  selectedThird.id
      lastCategory = selectedThird
    }else if(selectedSecond!=null){
      categoryId =  selectedSecond.id
      lastCategory = selectedSecond
    }else if (selectedFirst!=null){
      categoryId =  selectedFirst.id
      lastCategory = selectedFirst
    }
    if(itemEditStore.isCreate && !currentCategoriesId.current){
      saveRecentBackCategories(lastCategory)
      getCategoriesAttr(categoryId)
    }else if(currentCategoriesId.current && categoryId!==currentCategoriesId.current){
      Modal.warning({
        title: '是否切换类目',
        content: '切换类目可能将导致已填写内容的丢失？',
        onOk: () => {
          saveRecentBackCategories(lastCategory)
          getCategoriesAttr(categoryId)
        }
      });
    }else{
      saveRecentBackCategories(lastCategory)
      confirmSelect(categoryId)
    }

  }
  function saveRecentBackCategories(lastCategory) {
    setRecentBackCategories(prev => {
      const filtered = prev.filter(item => item.id !== lastCategory.id);
      let newRecentBackCategories = [lastCategory, ...filtered].slice(0, 5)
      localStorage.setItem('recentBackCategories', JSON.stringify(newRecentBackCategories));
      return newRecentBackCategories ;
    });
  }
  function confirmSelect(categoryId) {
    const {onClose} = props
    currentCategoriesId.current = categoryId
    setOpen(false)
    let text = [selectedFirst, selectedSecond, selectedThird].filter(item => item !== null).map(item => item.name).reduce((acc, curr) => (acc === null ? [curr] : [...acc, ' > ', curr]), null)
    setShowSelectOnForm(text)
    onClose(true, [selectedFirst, selectedSecond, selectedThird])
}
  const okButtonDisabled = useMemo(() => {
    if ((categoriesV3.data || []).length > 0) {
      return selectedThird === null
    }
    if ((categoriesV2.data || []).length > 0) {
      return selectedSecond === null
    }
    if ((categoriesV1.data || []).length > 0) {
      return selectedFirst === null
    }
  }, [categoriesV1,
    categoriesV2,
    categoriesV3,
    selectedFirst,
    selectedSecond,
    selectedThird])
  const showSelect = useMemo(() => {
    const filter = [selectedFirst, selectedSecond, selectedThird].filter(item => item);
    return filter.map(item =><Button
      className={"path-node"}
      type={'link'}>{item.name}</Button>).reduce((acc, curr) => (acc === null ? [curr] : [...acc, ' > ', curr]), null)
  }, [selectedFirst, selectedSecond, selectedThird])



  return (<Fragment>
      <div className="component-category-line">
        <div className="info-cate">
          <div className="path-name">{showSelectOnForm}</div>
          <Button type="link" onClick={() => setOpen(true)}>{showSelectOnForm ?"切换":"请选择"}</Button>
        </div>
      </div>
      <Modal
        title={"选择类目"}
        width={900}
        open={open}
        onCancel={handleOnCancel}
        footer={
          <Space>
            <Button  onClick={handleOnCancel}>
              取消
            </Button>
            {
              okButtonDisabled? <Tooltip title={"请选择商品类目至最后一级"}>
                  <Button type="primary" disabled onClick={handleOnOk}>确认</Button>
                </Tooltip>:
                <Button type="primary"  onClick={handleOnOk}>确认</Button>
            }
          </Space>
        }
        wrapClassName={"component-category-select-modal"}>
        <div className={"component-category"}>
          <div className="category-path-wrap category-path">
            <div className="path-info-wrap">

              <div className="path-list"><span className="path-label">已选类目:</span>
                {showSelect}
              </div>
              <div className="path-tips"></div>
            </div>
            <div className="path-help"></div>
          </div>
          <Cascader style={{width: "100%",marginBottom: "12px"}}
                    showSearch={{
                      filter: (inputValue, path) =>{
                       return path.some((option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
                      }
                    }}
                    options={categoriesTree}
                    placeholder="请输入关键词搜索，如：帽子、面霜"
                    onChange={(value, selectedOptions) =>{
                      processValue(selectedOptions,false)
                      fetchData(value.length>1?value[0]:null,value.length>2?value[1]:null)
                    }}
                    fieldNames={{label: 'name', value: 'id',}}/>
          <div className={"recent-select-categories"}>
            <span className="recent-select-categories-title">最近选择：</span>
            {(recentBackCategories || []).map((item, index, array) => (
              <Fragment key={item.id}>
                <a onClick={() => {
                  getCategories(item.id)
                }}>{item.name}</a>
                {index < array.length - 1 && <span className="separator"> | </span>}
              </Fragment>
            ))}
          </div>
          <div className={"category-lists-wrap"}>
            {/* 一级类目 */}
            <div className={"category-list"}>
              <List
                itemLayout="horizontal"
                size="small"
                loading={categoriesV1.loading}
                header={<Input className={"search-wrap"} size="small" placeholder="输入类目名称" allowClear
                               onChange={(e) => setFilterFirst(e.target.value)}/>}
                style={{width: '250px'}}
                virtual
                dataSource={categoriesV1.data.filter(item => item.name.includes(filterFirst))}
                renderItem={item => (
                  <List.Item
                    extra={item.hasChildren ? <RightOutlined/> : ""}
                    className={`category-list-item ${selectedFirst && selectedFirst.name === item.name ? 'selected' : ''}`}
                    onClick={async () => {
                      setSelectedFirst(item)
                      resetSelectedSecond()
                      if(item.hasChildren) {
                        setCategoriesV2({loading: true, data: []})
                        const result = await getCategoriesChildren(item.id)
                        setCategoriesV2(result)
                      }else{
                        setCategoriesV2({loading: false, data: []})
                      }
                    }
                    }>
                    <div className={"category-name"}>{item.name}</div>
                  </List.Item>
                )}
              />
            </div>
            {/* 二级类目 */}
            {selectedFirst && (
              <div className={"category-list"}>
                <List
                  size="small"
                  itemLayout="horizontal"
                  loading={categoriesV2.loading}
                  header={<Input className={"search-wrap"} size="small" placeholder="输入类目名称" allowClear
                                 onChange={(e) => setFilterSecond(e.target.value)}/>}
                  style={{width: '250px'}}
                  dataSource={categoriesV2.data.filter(item => item.name.includes(filterSecond))}
                  renderItem={item => (
                    <List.Item
                      extra={item.hasChildren ? <RightOutlined/> : ""}
                      className={`category-list-item ${selectedSecond && selectedSecond.name === item.name ? 'selected' : ''}`}
                      onClick={async () => {
                        setSelectedSecond(item)
                        resetSelectedThird()
                        if(item.hasChildren){
                          const result = await getCategoriesChildren(item.id)
                          setCategoriesV3(result)
                        }else{
                          setCategoriesV3({loading: false, data: []})
                        }
                      }}>
                      <div className={"category-name"}>{item.name}</div>
                    </List.Item>
                  )}
                />
              </div>
            )}
            {/* 三级类目 */}
            {selectedSecond && (
              <div className={"category-list"}>
                <List
                  size="small"
                  itemLayout="horizontal"
                  loading={categoriesV3.loading}
                  header={<Input className={"search-wrap"} placeholder="输入类目名称" size="small" allowClear
                                 onChange={(e) => {
                                   setFilterThird(e.target.value)
                                 }}/>}
                  style={{width: '250px'}}
                  dataSource={categoriesV3.data.filter(item => item.name.includes(filterThird))}
                  renderItem={item =>
                    <List.Item
                      onClick={() => {
                        setSelectedThird(item)
                      }}
                      className={`category-list-item ${selectedThird && selectedThird.name === item.name ? 'selected' : ''}`}>
                      <span className={"category-name"}>{item.name}</span>
                    </List.Item>}
                />
              </div>
            )}
          </div>
        </div>
      </Modal>
    </Fragment>
  );
})
export default BackCategoriesSelect

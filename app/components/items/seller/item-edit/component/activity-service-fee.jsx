import {ItemPercentageInput} from "./base/item-percentage-input";
import {
  activityCommissionIsCommissionFormName,
  activityCommissionTimeFormName,
  activityCommissionServiceProviderRateFormName,
  activityCommissionFirstRateFormName,
  activityCommissionSecondRateFormName,
  commissionSwitchFormName
} from "../utils/form-name-const";
import itemEditStore from "../ItemEditStore";

const {useEffect,Fragment} = React
const {Form, Checkbox, Row, DatePicker, Col} = antd;
const {RangePicker} = DatePicker;
const {observer} = mobxReactLite;

const ActivityServiceFee = observer((props) => {

  const activityCommissionIsCommission = Form.useWatch(activityCommissionIsCommissionFormName, itemEditStore.form);
  const commissionSwitch = Form.useWatch(commissionSwitchFormName, itemEditStore.form);
  useEffect(() => {
    if (activityCommissionIsCommission === 0) {
      itemEditStore.form.setFieldValue(activityCommissionTimeFormName, null)
      itemEditStore.form.setFieldValue(activityCommissionServiceProviderRateFormName, null)
      itemEditStore.form.setFieldValue(activityCommissionFirstRateFormName, null)
      itemEditStore.form.setFieldValue(activityCommissionSecondRateFormName, null)
    }
  }, [activityCommissionIsCommission])
  return <Fragment> {commissionSwitch === 1 ? (<div>
    {/* 是否开启单品佣金 */}
    <Form.Item label={"活动服务费"}
               name={activityCommissionIsCommissionFormName}
               valuePropName="checked"
               initialValue={0}
               getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
      <Checkbox>是否开启单品佣金</Checkbox>
    </Form.Item>
    {
      activityCommissionIsCommission ? (
        <div className={"common-wrap"}>
          <Form.Item
            label="活动时间"
            name={activityCommissionTimeFormName}
            rules={[{required: true, message: '请选择活动时间'}]}
          >
            <RangePicker showTime/>
          </Form.Item>
          {/* 百分比输入项 */}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="服务商"
                name={activityCommissionServiceProviderRateFormName}
                rules={[{required: true, message: '请输入服务商佣金'}]}
              >
                <ItemPercentageInput placeholder="请输入" addonAfter="%"/>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label="门店"
                name={activityCommissionFirstRateFormName}
                rules={[{required: true, message: '请输入门店佣金'}]}
              >
                <ItemPercentageInput placeholder="请输入" addonAfter="%"/>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label="导购"
                name={activityCommissionSecondRateFormName}
                rules={[{required: true, message: '请输入导购佣金'}]}
              >
                <ItemPercentageInput placeholder="请输入" addonAfter="%"/>
              </Form.Item>
            </Col>
          </Row>
        </div>
      ) : null
    }

  </div>) : null}
  </Fragment>
})
export default ActivityServiceFee


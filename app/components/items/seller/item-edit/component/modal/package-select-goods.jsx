import PackageSelectExpandedTable from "../base/package-select-expanded-table";
import packageSelectGoodsStore from "../../packageSelectGoodsStore";
import request from "../../../../../utils/plugins/axios/request";
import itemEditStore from "../../ItemEditStore";
import {lib} from "../../../../../common/react/utils/lib";
const {<PERSON>er, Alert, Button, Table, Space,Image, Cascader,InputNumber, Select, Input, Row, Col, Tooltip, Switch,Form,message,Modal} = antd;
const {Fragment, useState,useEffect,useRef} = React;
const {PlusOutlined, QuestionCircleOutlined} = icons;
const {observer} = mobxReactLite;
const {useTable} = dtHooks
const PackageSelectGoods = observer(({value,onChange, sortIndex}) => {
  const skuId = Form.useWatch(['skuWithCustoms', sortIndex, 'sku', "skuId"], itemEditStore.form)
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [categoriesTree, setCategoriesTree] = useState([])
  const [showSelect, setShowSelect] = useState(false)
  const [dataSource, setDataSource] = useState([]);
  const [form] = Form.useForm()
  const rowKey = "id"
  const [dataSelected, setDataSelected] = useState([]);
  const isBondedRef = useRef()
  const isBondedFormName = ["item", "isBonded"]
  const isBonded = Form.useWatch(isBondedFormName, itemEditStore.form);
  useEffect(() => {
      if(itemEditStore.isCreate && isBondedRef.current && isBondedRef.current !==isBonded ){
        setDataSource([])
        packageSelectGoodsStore.newSkuSelectList[`${skuId}`] = []
        packageSelectGoodsStore.setNewSkuSelectListSelectList({...packageSelectGoodsStore.newSkuSelectList})
        onChange([])
        itemEditStore.form.setFieldValue(['skuWithCustoms', sortIndex, 'sku', 'stockQuantity'], 0)
      }
      isBondedRef.current = isBonded
  }, [itemEditStore.isEdit,isBonded])
  useEffect(() => {
    if (value && value.length > 0) {
      setDataSource(value)
    }
  }, [value])
  useEffect(() => {
    if (showSelect) {
      request({
        url: '/api/shopCategory/default/tree/v2',
        method: 'POST',
        data: null,
        needMask: false,
        success: (res) => {
          setCategoriesTree(res)
        }
      })
      submit()
      setDataSelected( packageSelectGoodsStore.newSkuSelectList[`${skuId}`] || [])
    }
    return () => {
      if (!showSelect) {
        setDataSelected([])
      }
    }
  }, [showSelect])
  const [tableHeight, setTableHeight] = useState(500);
  const timeOutRef = useRef(null);
  const tableRef = useRef();
  useEffect(() => {
    const resetSize = () => {
      timeOutRef.current && clearTimeout(timeOutRef.current);
      timeOutRef.current = setTimeout(() => {
        if (tableRef.current && tableRef.current.offsetHeight) {
          setTableHeight(tableRef.current.offsetHeight - 72);
        }
      }, 100);
    };
    resetSize();
    window.addEventListener("resize", resetSize);
    return () => {
      window.removeEventListener("resize", resetSize);
    };
  }, []);
  const selectGoods = () => {
    setShowSelect(true)
  }
  const handleOnClose = () => {
    reset()
    setShowSelect(false);
  }
 function removeArrDuplicate(arr, arr2, key) {
    if(!arr2){
      return  [...arr]
    }
    let keyArr = [],
      totalArr = arr;
    arr.map(item => {
      keyArr.push(item[key]);
    });
    arr2.map(item => {
      // 如果数组2和数组1中数据存在重复的  则使用数组2中的数据
      if (keyArr.indexOf(item[key]) !== -1) {
        totalArr[keyArr.indexOf(item[key])] = item;
      }
    });
    return totalArr;
  }
  const handleOnOk = () => {

    const result = dataSelected
    if(result.length > 10){
      message.error("最多不能选择超过10个")
      return
    }
    setDataSource(result)
    packageSelectGoodsStore.newSkuSelectList[`${skuId}`] = removeArrDuplicate(dataSelected, packageSelectGoodsStore.newSkuSelectList[`${skuId}`], "comboSkuId")
    packageSelectGoodsStore.setNewSkuSelectListSelectList({...packageSelectGoodsStore.newSkuSelectList})
    onChange(result)
      itemEditStore.form.setFieldValue(['skuWithCustoms', sortIndex, 'sku', 'stockQuantity'],Array.isArray(result) && result.length>0 ? Math.floor( result.reduce((min, item) => {
      const ratio = item.comboSkuStockQuantity / item.comboSkuQuantity;
      return Math.min(min, ratio);
    }, Infinity)):0)
    setShowSelect(false);
    itemEditStore.setRefreshSkuComboPrice()

  }
  const deleteComboSku = (index) => {

    const newSkuSelectList = packageSelectGoodsStore.newSkuSelectList[`${skuId}`] || []
    newSkuSelectList.splice(index, 1);
    packageSelectGoodsStore.newSkuSelectList[`${skuId}`] = [...newSkuSelectList]
    onChange([...newSkuSelectList]);
    itemEditStore.form.setFieldValue(['skuWithCustoms', sortIndex, 'sku', 'stockQuantity'],Array.isArray(newSkuSelectList) && newSkuSelectList.length>0 ? Math.floor(newSkuSelectList.reduce((min, item) => {
      const ratio = item.comboSkuStockQuantity / item.comboSkuQuantity;
      return Math.min(min, ratio);``
    }, Infinity)):0)
    dataSource.splice(index, 1);
    setDataSource([...dataSource])
    itemEditStore.setRefreshSkuComboPrice()
  }

  const columns = [
    {
      title: '商品',
      width: '360',
      dataIndex: 'product',
      render: (text, record, index) => ({
        children: <div className="style_shortName">
          <Image src={record.comboItemMainImage}/>
          <div style={{flex: 1}}>
            <Form.Item name={['skuWithCustoms', sortIndex, 'sku', 'skuComboList', index, 'comboItemName']}>
              <Input/>
            </Form.Item>
            <div className="style_shortGoodsInfo">
              <Form.Item name={['skuWithCustoms', sortIndex, 'sku', 'skuComboList', index, 'comboSkuId']} hidden={true}>
                <Input/>
              </Form.Item>
              <Form.Item name={['skuWithCustoms', sortIndex, 'sku', 'skuComboList', index, 'comboItemId']}
                         hidden={true}>
                <Input/>
              </Form.Item>
              <div className="style_text">ID：{record.comboItemId}</div>
              <div className="style_text">规格：{record.comboSkuSpecification || '默认'}</div>
            </div>
          </div>
        </div>,
      }),
    },
    {
      title: '销售价',
      width: '100',
      dataIndex: 'comboSkuPrice',
      align: 'right',
      render: (text, record, index) => {
        return <div>{lib.formatPrice(record.comboSkuPrice)}</div>
      },
    },
    {
      title: <Tooltip
        title={" 组合中子品的数量，如设置10，则用户拍下1件组合商品，需发10件子品；组合数量需要小于等于可售库存"}
        arrowPointAtCenter>
        组合数量<QuestionCircleOutlined/>
      </Tooltip>,
      width: '122',
      align: 'center',
      render: (text, record, index) => {
        return <Form.Item name={['skuWithCustoms', sortIndex, 'sku', 'skuComboList', index, 'comboSkuQuantity']}
                          rules={[{required: true, message: '请输入可售库存'}, {
                            pattern: /^\+?[0-9]\d*$/,
                            message: "请输入大于等于零的整数",
                          }, ({getFieldValue}) => ({
                            validator(_, value) {
                              const comboSkuStockQuantity = record.comboSkuStockQuantity
                              if (value && value > comboSkuStockQuantity) {
                                return Promise.reject("组合数量需要小于等于可售库存");
                              }
                              return Promise.resolve();
                            },
                          }),]}
                          wrapperCol={{span: 24}}>
          <InputNumber min={1} step={1} placeholder="组合数量" onChange={(value) => {
            const newSkuSelectList = itemEditStore.form.getFieldValue(['skuWithCustoms', sortIndex, 'sku','skuComboList']);
            itemEditStore.form.setFieldValue(['skuWithCustoms', sortIndex, 'sku', 'stockQuantity'],Array.isArray(newSkuSelectList) && newSkuSelectList.length>0 ?Math.floor(newSkuSelectList.reduce((min, item) => {
              const ratio = item.comboSkuStockQuantity / item.comboSkuQuantity;
              return Math.min(min, ratio);
            }, Infinity)): 0)
            packageSelectGoodsStore.newSkuSelectList[`${skuId}`][index].comboSkuQuantity=value
            console.log("packageSelectGoodsStore.newSkuSelectList[`${skuId}`]",packageSelectGoodsStore.newSkuSelectList[`${skuId}`])
          }}/>
        </Form.Item>
      },
    },
    {
      title: '可售库存',
      width: '122',
      dataIndex: 'stock',
      render: (text, record, index) => {
        return <Input value={record.comboSkuStockQuantity} disabled={true}/>
      },
    },
    {
      title: '关联库存',
      width: '100',
      dataIndex: 'relatedStock',
      align: 'center',
      render: () => {
        return <Switch disabled={true} defaultChecked/>
      },
    },
    {
      title: '操作',
      width: '60',
      render: (text, record, index) => <a onClick={() =>{
        Modal.confirm({
          title: '确定删除？',
          onOk() {
            deleteComboSku(index)
          }})
      }}>删除</a>,
      align: 'center',
    },
  ];
  const modalGoodsColumns = [
    {
      title: '商品/规格',
      dataIndex: 'product',
      width: 360,
      render: (text, record, index) => ({
        children:
          <div className="style_goodsInfo">
            <div style={{marginRight: 10}}>
              <Image className="style_goodsImg" width={56} height={56} src={record.mainImage}/>
            </div>
            <div className="style_goodsContent">
              <div className="style_goodsName">{record.name}</div>
              <div className="style_goodsId">ID:{record.id}</div>
            </div>
          </div>
      }),
    },
    {
      title: '单价',
      width: 134,
      align: 'right',
    },
    {
      title: '贸易类型',
      width: 100,
      align: 'center',
      render: (text, record, index) => {
        return (
          <div>
            {record.isBonded?"保税":"完税"}
          </div>
        )
      }
    },
  ];
  const service = (pagiantion, formData) => {
    return new Promise((resolve, reject) => {
      request({
        url: '/mall-admin/api/items/choose/combo/product/page',
        method: 'POST',
        data: {
          ...pagiantion,
          ...formData,
          shopCategoryId:Array.isArray(formData.shopCategoryId) && value.length > 0 ? formData.shopCategoryId[formData.shopCategoryId.length - 1] : formData.shopCategoryId
        },
        success: resolve,
        fail: reject,
      })
    })
  }
  const {
    tableProps,
    search: {submit, reset},
  } = useTable(service, {
    form,
    manual: true,
    defaultPageSize: 10,
  })
  useEffect(() => {
      if (tableProps.dataSource.length > 0) {
        const keys = tableProps.dataSource.reduce((prev, curr) => [...prev, curr.id], []);
        setExpandedRowKeys(keys);
      }
  },[tableProps.dataSource])
  const expandedRowRender = (record, index) => {
    return <PackageSelectExpandedTable
      dataSource={record.skus}
      isBonded={record.isBonded}
      rowKey={rowKey}
      allSelectedRow={dataSelected}
      onSelectChange={(row, selected) => {
        const selectItem = {
          skuId: skuId,
          comboSkuId: row.id,
          comboItemMainImage: record.mainImage,
          comboItemId: record.id,
          comboItemName: record.name,
          comboSkuQuantity: 1,
          comboSkuPrice: row.price,
          comboSkuSpecification: row.specification,
          comboSkuStockQuantity: row.stockQuantity,
        }
        setDataSelected(selected ? [...dataSelected, selectItem] : dataSelected.filter(item => row[rowKey] !== item.comboSkuId))
      }}
    />;
  }

  return (
    <Fragment>
      {
        dataSource.length === 0 ? <Button type="primary"
                                          ghost
                                          onClick={() => selectGoods()}>选择商品</Button> :
          <div>
            <Table
              className={"package-select-goods-table"}
              dataSource={dataSource}
              columns={columns}
              bordered
              pagination={false}
              summary={() => (
                <Table.Summary>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={8}>
                      <Button type={"link"} icon={<PlusOutlined/>} onClick={() => selectGoods()}>
                        选择商品
                      </Button>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          </div>
      }
      <Drawer
        title={"选择组合商品"}
        open={showSelect}
        width={690}
        onClose={handleOnClose}
        footer={<Fragment>
          <div className="ant-drawer-footer-default">
            <div
              className="ant-drawer-footer-help">已选{(dataSelected|| []).length}/10，最多选择10个商品
            </div>
            <Space>
              <Button onClick={handleOnClose}>取消</Button>
              <Button type="primary" onClick={handleOnOk}>确定</Button>
            </Space>
          </div>

        </Fragment>}
      >
        <div>
          <Alert showIcon
                 icon={<span className="icon-feebas-new icon-feebas-bell" style={{color: "#F69661"}}></span>}
                 message={"子商品的贸易类型需要与组合商品基础信息中的贸易类型一致"} type="info"/>
          <Form layout={"horizontal"} form={form}>
            <Row className={"package-select-goods-search"} gutter={16}>
              <Col span={10}>
                <Input.Group>
                  <Form.Item name="status" noStyle initialValue={"1"}>
                    <Select style={{width: "35%"}}
                            options={[{value: "1", label: "售卖中"}, {value: "-1", label: "已下架"}]}/>
                  </Form.Item>
                  <Form.Item
                    name="shopCategoryId" noStyle >
                    <Cascader style={{width: "65%", marginBottom: "12px"}}
                              changeOnSelect
                              showSearch={{
                                filter: (inputValue, path) => {
                                  return path.some((option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
                                }
                              }}
                              options={categoriesTree}
                              placeholder="请输入关键词搜索"
                              fieldNames={{label: 'name', value: 'id',}}/>
                  </Form.Item>
                </Input.Group>
              </Col>
              <Col span={12}>
                <Form.Item name="keyword" noStyle>
                  <Input.Search
                    placeholder="请输入商品名称/商品ID"
                    allowClear
                    enterButton="搜索"
                    onSearch={(value) => {
                      submit()
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className="table-panel" ref={tableRef}>
          <Table
            className={"package-select-modal-goods-table"}
            columns={modalGoodsColumns}
            bordered={false}
            {...tableProps}
            rowKey={"id"}
            scroll={{ x: "max-content", y: tableHeight }}
            pagination={{
              ...tableProps.pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: total => `共 ${total} 条`,
              position: ['bottomCenter'],
              size: 'default',
            }}
            expandable={{
              expandedRowKeys:expandedRowKeys,
              onExpand: (expanded, record) => {
                let newExpandedRowKeys = expandedRowKeys.length ? [...expandedRowKeys] : [];
                if (expanded) newExpandedRowKeys.push(record.id);
                else newExpandedRowKeys = newExpandedRowKeys.filter(key => key !== record.id);
                setExpandedRowKeys([...newExpandedRowKeys]);
              },
              expandedRowRender,
              defaultExpandAllRows: true,
            }}
          />
        </div>
        </div>
      </Drawer>

    </Fragment>

  )
})

export default PackageSelectGoods;

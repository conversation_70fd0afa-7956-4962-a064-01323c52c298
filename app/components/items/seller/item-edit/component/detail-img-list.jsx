import {customFrontRequest} from "../../../../common/react/upload/custom-front-request";

const {useState,useEffect,Fragment} = React;
const {DndProvider, useDrag, useDrop} = dtDnd;
const {HTML5Backend} = dtDnd;
const {Upload, Button, Row, Col,message} = antd;

const MAX_IMAGES = 50;
const ITEM_TYPE = "IMAGE";

// 单个图片组件（支持拖拽）
const ImageItem = ({image, index, moveImage, removeImage}) => {
  const [{isDragging}, drag] = useDrag({
    type: ITEM_TYPE,
    item: {index},
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: ITEM_TYPE,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveImage(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });
  return (
    <Col span={6}>
      <div
        ref={(node) => drag(drop(node))}
        className={`image-item ${isDragging ? "dragging" : ""}`}
      >
        {image.status==="done"? <Upload listType="picture-card" fileList={[image]} onRemove={()=>{
          removeImage(index)
          return false
        }}/>:null}
        {image.status==="uploading"?
          <Fragment>
            <div className="upload-mask" style={{ height: (100 - image.percent)  + '%' }}></div>
            <img className='upload-img' src='//dante-img.oss-cn-hangzhou.aliyuncs.com/30183475885.svg' />
          </Fragment>:null
        }
      </div>
    </Col>
  );
};

// 图片列表组件
const ImageGrid = ({images, moveImage, removeImage}) => {
  return (
    <Row gutter={[8, 8]} style={{marginTop: 16,overflow: "scroll"}} >
      {images.map((image, index) => (
        <ImageItem key={index} index={index} image={image} moveImage={moveImage} removeImage={removeImage}/>
      ))}
    </Row>
  );
};

// 主组件
function ImageUploader({imgList,onChange}) {
  const [images, setImages] = useState(imgList);
  useEffect(()=>{
    if(Array.isArray(imgList)){
      setImages(imgList)
    }
  },[imgList])

  // 移动图片（拖拽排序）
  const moveImage = (fromIndex, toIndex) => {
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    setImages(updatedImages);
    onChange(updatedImages)
  };

  // 删除图片
  const removeImage = (index) => {
    setImages((prev) =>{
      const imgList = prev.filter((_, i) => i !== index)
      onChange(imgList)
      return imgList
    });
  };

  return (
    <DndProvider backend={HTML5Backend}>
        {/* 图片展示区域 */}
        <ImageGrid images={images} moveImage={moveImage} removeImage={removeImage}/>
    </DndProvider>
  );
}


export default function DetailImgList({value,onChange}) {
  const [imgList, setImgList] = useState([])
  useEffect(() => {
    if(Array.isArray(value)){
      setImgList(value)
    }
  }, [value])

  const innerOnChange = ({file, fileList}) => {
    if (file.status === "uploading") {
      setImgList([...fileList])
    } else if (file.status === "done") {
      setImgList([...fileList])
      onChange(fileList)
    } else if (file.status === "removed") {
      setImgList([...fileList])
      onChange(fileList)
    } else if (file.status === "error") {
      message.error("上传失败")
    }
  }
  return <div className="detail-img-list">
    <div className="detail-img-list-preview">

      <div className="detail-img-list-preview-title">页面预览</div>
        <div className={"viewGroup"}>
          {
            imgList.map((item, index) => {
              return <img key={item} width={375} src={item.url}/>
            })

          }
        </div>
    </div>
    <div className={"detail-img-list-upload-img-list"}>
        <div className={"detail-img-list-upload-img-list-header"}>
           <div className={"title-wrap"}>
             <div className={"title"}>
               详情编辑
             </div>
             <div className={"sub-title"}>
               已上传{(imgList||[]).length}/50张，支持拖拽，单张高度5000px内，≤5M，仅支持jpg/png格式
             </div>
           </div>
           <Upload
             fileList={imgList}
             beforeUpload={(file)=>{
               if (file.size / 1024 / 1024 > 5) {
                 message.error(`对不起，文件大小不能超过${5}M`)
                 return Upload.LIST_IGNORE
               }
               if ((imgList || []).length === 50) {
                 message.error("最多上传50张图片")
                 return Upload.LIST_IGNORE
               } else {
                 return Promise.resolve()
               }
             }
             }
             showUploadList={false}
             customRequest={customFrontRequest}
             accept={".jpg,.png"}
             maxCount={50}
             onChange={innerOnChange}
             multiple>
             <Button>上传图片</Button>
           </Upload>
        </div>
      <ImageUploader imgList={imgList} onChange={onChange}/>
    </div>
  </div>
}

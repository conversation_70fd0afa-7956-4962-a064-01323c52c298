import {TableCellErrorView} from "./sku-spec-price";

const {Fragment, useState, useEffect,useMemo} = React;
import itemEditStore from "../ItemEditStore";
import {cashGiftSwitchFormName, skuSalesPriceFormName, specDetailFormName} from "../utils/form-name-const";
import Schema from "../utils/async-validator";
import {validator, validatorCash} from "../utils/sku-validator";

const {FormRangePicker} = dtComponents;
const {Form, Checkbox, Table, Select, InputNumber, Button, Tooltip} = antd;
const {observer} = mobxReactLite;

const SpecCashGift = observer((props) => {
  const [displayBatchSetting, setDisplayBatchSetting] = useState(false);
  const [dataSource, setDataSource] = useState([])
  useEffect(() => {
    setDataSource([...itemEditStore.processedTableData])
  }, [itemEditStore.processedTableData])

  const selectAfter = (name) => (
    <Form.Item name={name} noStyle initialValue={1}>
      <Select defaultValue={1}>
        <Option value={1}>%</Option>
        <Option value={2}>个</Option>
      </Select>
    </Form.Item>
  );
  const isCashGift = Form.useWatch(cashGiftSwitchFormName, itemEditStore.form);
  const disabledDate = (current) => {
    return current && current < moment().startOf('day');
  };
  const [batchForm] = Form.useForm()
  const batchInputValue = () => {
    batchForm.validateFields().then((values) => {
      let oldSkuInfo = itemEditStore.form.getFieldValue(skuSalesPriceFormName)
      oldSkuInfo.map(item => {
        item.sku.maxDeduction = values.maxDeduction
        item.sku.cashGiftUnit = values.cashGiftUnit
        return oldSkuInfo
      })
      itemEditStore.form.setFieldValue(skuSalesPriceFormName, [...oldSkuInfo])
      setDataSource([...dataSource])
    })
  }
  const checkSetDisplayBatchSetting = (values) => {
    if (values.maxDeduction) {
      setDisplayBatchSetting(true)
    } else {
      setDisplayBatchSetting(false)
    }
  }
  const valitorItems = (values) => {
    let hasError = false

    values.map(async (item, index) => {
      try {

      } catch ({errors, fields}) {
        if (errors.length > 0) {
          values[index].sku.errors = fields
        } else {
          values[index].sku.errors = []
        }
      }
    })
    setTimeout(() => {
      setDataSource([...dataSource])
    },50)

    return hasError ? ["商品福豆活动填写有误，请检查"] : []
  }
  return <div>
    {/* 百分比输入项 */}
    {isCashGift === 1 ? (
      <Fragment>
        <FormRangePicker
          fProps={{
            label: '使用时间',
            name: 'cashGiftUseTimeStart',
            extra: "如不填，默认永久可用"
          }}
          cProps={{
            RangePicker: disabledDate,
            width: 260
          }}
        />
        {
          <div className={"batch_wrapper"}>
            <Form form={batchForm}
                  layout={"inline"}
                  onValuesChange={(_, values) => {
                    checkSetDisplayBatchSetting(values)
                  }}>
              <div className={"styles_inputBox"} style={{width: 180, maxWidth: 180}}>
                <Form.Item
                  className={"styles_inputBox_form_item"}
                  label=""
                  name={"maxDeduction"}
                  rules={[{
                    pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
                    message: "请输入正整数,支持到两位小数"
                  },]}
                >
                  <InputNumber addonAfter={selectAfter("cashGiftUnit")}/>
                </Form.Item>
              </div>
              {
                displayBatchSetting ?
                  <Button type="primary" ghost onClick={() => {
                    batchInputValue()
                  }}>批量设置</Button> :
                  <Tooltip title={"请先输入值"}>
                    <Button type="primary" ghost disabled>批量设置</Button>
                  </Tooltip>
              }
            </Form>
          </div>
        }
        <Form.Item name={skuSalesPriceFormName} noStyle>
          <SpecCashGiftTable dataSource={dataSource}/>
        </Form.Item>
      </Fragment>) : null
    }
  </div>
})
const SpecCashGiftTable = observer((props) => {
  const {dataSource,value,onChange} =props
  const mergeDataSource =useMemo(() => {
    return value
  }, [value ,dataSource])
  const innerChange = async (values,record,index) => {
    if (record.sku.errors) {
      try {
        await validatorCash.validate(record)
        values[index].sku.errors = null
      } catch ({errors, fields}) {
        if (errors.length > 0) {
          record.sku.errors = fields
        } else {
          record.sku.errors = null
        }
      }
    }
    onChange(values)
  }
  const columns = [
    ...itemEditStore.specs.map(spec => ({
      title: spec.name,
      width: 120,
      fixed: "left",
      dataIndex: spec.name,
      render: (value, record, index) => {
        const span = itemEditStore.processedTableData[index][`${spec.name}Span`] || 0;
        return {children: itemEditStore.processedTableData[index][`${spec.name}`], props: {rowSpan: span}};
      }
    })),

    {
      title: <span><span className="styles_required">*</span>最高可抵扣金额</span>,
      width: 120,
      render: (_, record, index) => {
        // <Form.Item
        //   label=""
        //   name={["skuWithCustoms", index, "sku", "maxDeduction"]}
        //   rules={[{required: true, message: '请输入最高可抵扣金额'}, {
        //     pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
        //     message: "请输入正整数,支持到两位小数"
        //   },]}
        // >
        // </Form.Item>
        return <TableCellErrorView record={record} name={"sku.maxDeduction"}>
          <InputNumber
            value={record.sku.maxDeduction}
            addonAfter={
            <Select defaultValue={1}
                    value={record.sku.cashGiftUnit}
                    onChange={(e) => {
                      value[index].sku.cashGiftUnit = e
                      innerChange([...value],record,index)
                    }}>
              <Option value={1}>%</Option>
              <Option value={2}>个</Option>
            </Select>}
                 onChange={(e) => {
                   value[index].sku.maxDeduction =e
                   innerChange([...value],record,index)
                 }}/>
        </TableCellErrorView>
      }
    },
  ];
  return <Table columns={columns}
                scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0), y: 500}}
                dataSource={mergeDataSource}
                pagination={false}
                rowKey="id"
                bordered/>
})
export default SpecCashGift




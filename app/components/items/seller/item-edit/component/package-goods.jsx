import CashGift from "./cash-gift";
import { ItemPriceInput } from "./base/item-price-input";
import ItemText from "./base/item-text";
import { MainImagesFormItem } from "./base/item-image-form-item";
import PackageSelectGoods from "./modal/package-select-goods";
import itemEditStore from "../ItemEditStore";
import { lib } from "../../../../common/react/utils/lib";
import { skuSalesPriceFormName } from "../utils/form-name-const";
import { validatePriceNumber } from "../utils/method";
const {DTInput} = dtComponents
const {useEffect,useState,useMemo} = React;
const {Form, Input, InputNumber, Row, Button, Col, Modal} = antd;
const {observer} = mobxReactLite;
const {PlusOutlined, DeleteOutlined} = icons


const PackageGoodsListItem = observer((props) => {
  const {sortIndex,deleteItem,showDelete} = props
  const  skuComboList =  Form.useWatch(['skuWithCustoms', sortIndex, 'sku', "skuComboList"], itemEditStore.form);


  /**
   * 删除
   */
  const deleteSpecDetail = (index) => {
    Modal.confirm({
      title: '确定删除该规格么？',
      onOk() {
       deleteItem(index)
      },
    })

  }


 const skuComboPrice = useMemo(() => {
    if(!Array.isArray(skuComboList)|| skuComboList.length === 0){
      return 0.00
    }
    return  (skuComboList||[]).reduce((total, item) => {
      return  total + (Number(item.comboSkuPrice)/100 * Number(item.comboSkuQuantity))
    }, 0)
  },[skuComboList,itemEditStore.refreshSkuComboPrice])

  return <div className={"package-select-goods-list-item"}>
    {
      showDelete ? <Button type={"link"}
                              style={{float: "right"}}
                              icon={<DeleteOutlined className={"style_delete"}/>}
                              onClick={() => deleteSpecDetail(sortIndex)}/> : null
    }
    <div style={{display: "flex", justifyContent: "space-between"}}>
      <div className={"item-title"}>规格{showDelete ? `${sortIndex + 1}` : ''}</div>
    </div>
    <div>
      <Form.Item
        hidden
        name={['skuWithCustoms', sortIndex, 'sku', "skuId"]}>

      </Form.Item>
      <Form.Item
        hidden
        name={['skuWithCustoms', sortIndex, 'sku', "version"]}>

      </Form.Item>
      {/* 组合名称 */}
      <Form.Item
        label="组合商品"
        name={['skuWithCustoms', sortIndex, 'sku', "skuComboList"]}
        wrapperCol={{span: 24}}
        extra={<div
          className={"help-container"}>一个组合最多支持添加10个子商品，组合商品贸易类型与子品的贸易类型需一致</div>}
        rules={[
          {
            validator(rule, value) {
              if (!value || value.length === 0) {
                return Promise.reject("请选择组合商品");
              }
              if (value.length < 2) {
                return Promise.reject("组合商品的件数需要大于2");
              }
              if (value.length > 10) {
                return Promise.reject("组合商品最多支持添加10个");
              }
              return Promise.resolve();
            }
          },
        ]}>
        <PackageSelectGoods sortIndex={sortIndex}/>
      </Form.Item>
      {/* 组合名称 */}
      <Form.Item
        label="组合名称"
        name={['skuWithCustoms', sortIndex, 'sku', "name"]}
        rules={[
          {required: true, message: '请输入组合名称'},
          {max: 64, message: '最多输入64个字'},
          ({getFieldValue}) => ({
            validator(_, value) {
              const skuWithCustoms = getFieldValue(['skuWithCustoms']);
              if (Array.isArray(skuWithCustoms) && skuWithCustoms.length > 0) {
                if( skuWithCustoms.find((item,index)=>item.sku.name===value&&sortIndex!==index)){
                  return Promise.reject("组合名称不能重复");
                }
                return Promise.resolve();

              }
              return Promise.resolve();
            },
          }),

        ]}
      >
        <Input showCount maxLength={64} placeholder="请输入最多64个字"/>
      </Form.Item>

      {/* 组合原价（静态展示） */}
      <Form.Item label="组合原价">
        <ItemText value={skuComboPrice.toFixed(2)}/>
      </Form.Item>

      {/* 组合价格 & 划线价 */}
      <Row>
        <Col span={12}>
          <Form.Item
            label="组合价格"
            name={['skuWithCustoms', sortIndex, 'sku', 'price']}
            rules={[{required: true, message: '请输入组合价格'}, { validator: validatePriceNumber }]}
          >
            <ItemPriceInput
              addonAfter="元"
              placeholder="请输入价格"
              style={{width: '100%'}}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="划线价"
            name={['skuWithCustoms', sortIndex, 'sku', 'extraPrice', 'crossedPrice']}
            wrapperCol={{span: 24}}
            rules={[
              {validator: validatePriceNumber},
              ({getFieldValue}) => ({
                validator(_, value) {
                  const price = getFieldValue(['skuWithCustoms', sortIndex, 'sku', 'price']);
                  if (value && price && value < price) {
                    return Promise.reject("划线价不能小于售价价格");
                  }
                  return Promise.resolve();
                },
              }),
            ]}>
            <ItemPriceInput addonAfter="元" placeholder="请输入划线价" allowBlank style={{width: '100%'}}/>
          </Form.Item>
        </Col>
      </Row>

      {/* 商家编码 */}
      <Form.Item label="商家编码"
                 name={['skuWithCustoms', sortIndex, 'sku', 'outerSkuId']}
                 rules={[{
                   pattern: /^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/,
                   message: "仅支持汉字数字英文大小写及符号",
                 },{
                   max: 60,
                   message: '长度不能超过60个字符',
                 },]}
      >
        <DTInput trimMode={"all"} placeholder="请输入"/>
      </Form.Item>

      {/* 库存（静态展示） */}
      <Form.Item label="组合可售库存"
                 name={['skuWithCustoms', sortIndex, 'sku', 'stockQuantity']}
                 extra={<div
                   className={"help-container"}>组合可售库存根据子品可售库存/子品组合数量计算，存在多个子品时，取所有子品结算结果的最小值</div>}>
        <ItemText/>
      </Form.Item>

      {/* 拆单数量 */}
      <Row>
        <Col span={12}>
          <Form.Item
            label="拆单数量"
            name={['skuWithCustoms', sortIndex, "sku", "extraMap", "skuOrderSplitLine"]}
            rules={[
              {
                pattern: RegExp("^[1-9]\\d*$"),
                message: "请输入大于0的整数",
              },]}>
            <InputNumber placeholder="请输入" style={{width: '100%'}}/>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={"限购数量"}
            name={['skuWithCustoms', sortIndex, "sku", "extraMap", "orderQuantityLimit"]}
            wrapperCol={{span: 24}}
            rules={[{
              pattern: RegExp("^[1-9]\\d*$"),
              message: "请输入大于0的整数",
            },]}
          >
            <InputNumber placeholder="请输入"  style={{width: '100%'}}/>
          </Form.Item>
        </Col>
      </Row>
      <Form.Item
        name={['skuWithCustoms', sortIndex, 'sku', "extraMap", 'unitQuantity']}
        initialValue={1}
        hidden
        wrapperCol={{span: 24}}
      >
        <InputNumber  />
      </Form.Item>
      <Form.Item
        name={['skuWithCustoms', sortIndex, 'sku', "tags", 'pushSystem']}
        hidden
        initialValue={itemEditStore.thirdPartyId}
        wrapperCol={{span: 24}}
      >
        <InputNumber />
      </Form.Item>
      {/* 组合图片上传 */}
      <Form.Item
        label="组合图片"
        name={['skuWithCustoms', sortIndex, 'sku', 'image']}>
        <MainImagesFormItem/>
      </Form.Item>
      <CashGift
        cashGiftUnitFormName ={['skuWithCustoms', sortIndex, "sku", "comboCashGiftUnit"]}
        cashGiftSwitchFormName={['skuWithCustoms', sortIndex, "sku", "comboCashGiftSwitch"]}
        maxDeductionFormName={['skuWithCustoms', sortIndex, "sku", 'comboCashGiftMaxDeduction']}
        cashGiftUseTimeStartFormName={['skuWithCustoms', sortIndex, "sku", 'comboCashGiftUseTime']}
      />
    </div>
  </div>
})

const PackageGoodsList = observer((props) => {
  const skuSalesPrice = Form.useWatch(skuSalesPriceFormName, itemEditStore.form);
  const [items, setItems] =useState([])
  useEffect(()=>{
    if((!Array.isArray(skuSalesPrice)|| skuSalesPrice.length === 0) && itemEditStore.form){
      itemEditStore.form.setFieldValue(skuSalesPriceFormName, [{sku:{skuId:lib.generateNumberString(false,18)}}])
    }
  },[skuSalesPrice,itemEditStore.form])
  useEffect(()=> {
   const dataSource = (skuSalesPrice || []).filter(it => it)
   setItems(dataSource)
    itemEditStore.setProcessedTableData(dataSource)
  },[skuSalesPrice])

  const specDetailLength = items.length
  const addSpecDetail = () => {
    const newSpecDetail = [...(items || []), {sku: {skuId: lib.generateNumberString(false, 18)}}];
    setItems(newSpecDetail)
    itemEditStore.setProcessedTableData(newSpecDetail)
    itemEditStore.form.setFieldValue(skuSalesPriceFormName, newSpecDetail);
  }
  return <div className={"package-select-goods-list"}>
    {
      items.map((item, index) => {
        return <PackageGoodsListItem key={item.sku.id} showDelete={items.length>1} sortIndex={index} deleteItem={(index)=>{
          const skuSalesPrice = itemEditStore.form.getFieldValue(skuSalesPriceFormName);
          skuSalesPrice.splice(index, 1);
          const newSpecDetail = [...(skuSalesPrice || [])]
          setItems(newSpecDetail)
          itemEditStore.setProcessedTableData(newSpecDetail)
         setTimeout(()=>{
           itemEditStore.form.setFieldValue(skuSalesPriceFormName, [...newSpecDetail]);
         },50)
        }
        }/>
      })
    }
    {
      specDetailLength < 3 ?
        <Button type="primary"
                icon={<PlusOutlined/>}
                ghost
                onClick={addSpecDetail}>
          <span>添加规格类型（{specDetailLength}/3）</span>
        </Button> : null
    }
  </div>
})
export default PackageGoodsList

import {ItemPriceInput} from "./base/item-price-input";
import SelectAll from "./base/item-select-all";
import itemEditStore from "../ItemEditStore";
import {skuSalesPriceFormName} from "../utils/form-name-const";
import {validatePriceNumber} from "../utils/method";

const {observer} = mobxReactLite;
const {useMemo, useState, Fragment} = React;
const {InputNumber, Form, Button, Tooltip} = antd;
const {DTInput} = dtComponents


const SkuSpecBatchSetPrice = observer((props) => {
  const {specs,batchInputValueCallBack} = props
  const [batchForm] = Form.useForm();
  const [displayBatchSetting, setDisplayBatchSetting] = useState(false);



  const batchSetting = useMemo(() => {
    let value = []
    let node = (specs || []).map((item, index) => {
      value.push(item.itemSpecificationDetailParams.map(item => item.id))
      return <div key={item.id} className={"styles_inputBox"}>
        {
          Array.isArray(item.itemSpecificationDetailParams) && item.itemSpecificationDetailParams.length > 0
            ? <Form.Item
              name={["specificationDetail", index]}
              className={"styles_inputBox_form_item"}
              key={item.id}
              wrapperCol={{span: 24}}
            >
              <SelectAll placeholder={"请选择规格"}
                         list={item.itemSpecificationDetailParams}
                         style={{width: '100%'}}
                         tagAllName={item.name} valueKey={"frontId"}/>
            </Form.Item> : null
        }
      </div>
    })
    batchForm.setFieldValue("specificationDetail", value)
    return node
  }, [specs])
  const checkSetDisplayBatchSetting = (values) => {
    const specificationSelect = ((values.specificationDetail || []).filter((item) => item && item.length > 0) || []).length === specs.length
    if (specificationSelect && (values.price || values.crossedPrice
      || values.stockQuantity||values.stockQuantity==0
      || values.skuOrderSplitLine
      || values.outerSkuId
      || values.orderQuantityLimit)) {
      setDisplayBatchSetting(true)
    } else {
      setDisplayBatchSetting(false)
    }
  }
  const doCombinationSpecSkuInfo = (values) => {
    let combinationSpec = ((values.specificationDetail || []).filter((item) => item && item.length > 0) || []).reduce(
      (acc, curr) => acc.flatMap((a) => curr.map((b) => [...a, b])),
      [[]]
    );
    let combinationSpecSkuInfo = combinationSpec.map(item => {
      let price = values.price
      let crossedPrice = values.crossedPrice
      let stockQuantity = values.stockQuantity
      let skuOrderSplitLine = values.skuOrderSplitLine
      let orderQuantityLimit = values.orderQuantityLimit
      let outerSkuId = values.outerSkuId
      return {
        specDetails: item,
        price,
        crossedPrice,
        stockQuantity,
        skuOrderSplitLine,
        orderQuantityLimit,
        outerSkuId
      }
    })
    return combinationSpecSkuInfo
  }
  const batchInputValue = () => {
    batchForm.validateFields().then((values) => {
      let combinationSpecSkuInfo = doCombinationSpecSkuInfo(values)
      batchInputValueCallBack(combinationSpecSkuInfo)
    })
  }

  const batchClearSkuStockQuantity = () => {
    batchForm.validateFields().then((values) => {
      let combinationSpecSkuInfo = doCombinationSpecSkuInfo(values)
      let oldSkuInfo = itemEditStore.form.getFieldValue(skuSalesPriceFormName)
      oldSkuInfo.map(oldSkuInfo => {
        let findItem = combinationSpecSkuInfo.find(item => [...item.specDetails].sort().join(',') === [...oldSkuInfo.sku.specDetails].sort().join(','))
        if (findItem) {
           oldSkuInfo.sku.stockQuantity = 0
        }
        return oldSkuInfo
      })
      itemEditStore.form.setFieldValue(skuSalesPriceFormName, [...oldSkuInfo])
    })

  }
  return (
    <Fragment>
      {
        specs.length > 0 ? <div className={"batch_wrapper"}>
          <Form form={batchForm}
                layout={"inline"}
                onValuesChange={(_, values) => {
                  checkSetDisplayBatchSetting(values)
                }}>
            {batchSetting}
            <div className={"styles_inputBox styles_inputBox_small"}>
              <Form.Item
                className={"styles_inputBox_form_item"}
                name={'price'}
                rules={[{validator: validatePriceNumber}]}
                wrapperCol={{span: 24}}>
                <ItemPriceInput allowBlank={true} placeholder="销售价" style={{width: '100%'}}/>
              </Form.Item>
            </div>
            <div className={"styles_inputBox styles_inputBox_small"}>
              <Form.Item
                name={'crossedPrice'}
                className={"styles_inputBox_form_item"}
                wrapperCol={{span: 24}}
                rules={[
                  {validator: validatePriceNumber},
                  ({getFieldValue}) => ({
                    validator(_, value) {
                      const price = getFieldValue('price');
                      if (value && price && value < price) {
                        return Promise.reject("划线价不能小于售价价格");
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <ItemPriceInput allowBlank={true} placeholder="划线价" style={{width: '100%'}}/>
              </Form.Item>
            </div>
            <div className={"styles_inputBox styles_inputBox_small"}>
              <Form.Item
                name={'outerSkuId'}
                className={"styles_inputBox_form_item"}
                rules={ [ {
                  pattern: /^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/,
                  message: "仅支持汉字数字英文大小写及符号",
                }, {
                  type: 'string',
                  max: 60,
                  message: '长度不能超过60个字符',
                },]}
                wrapperCol={{span: 24}}>
                <DTInput trimMode={"all"}  placeholder="商家编码" style={{width: '100%'}}/>
              </Form.Item>
            </div>
            <div className={"styles_inputBox styles_inputBox_small"}>
              <Form.Item
                name={'stockQuantity'}
                className={"styles_inputBox_form_item"}
                rules={[{
                  pattern: /^[1-9]\d{0,6}$/,
                  message: "请输入7位正整数",
                }]}
                wrapperCol={{span: 24}}>
                <InputNumber min={0} placeholder="可售库存" style={{width: '100%'}}/>
              </Form.Item>
            </div>
            <div className={"styles_inputBox styles_inputBox_small"}>
              <Form.Item
                name={"skuOrderSplitLine"}
                wrapperCol={{span: 24}}
                className={"styles_inputBox_form_item"}
                rules={[{
                  pattern: RegExp("^[1-9]\\d*$"),
                  message: "请输入大于零的整数",
                },]}
              >
                <InputNumber placeholder="拆单数量" style={{width: '100%'}}/>
              </Form.Item>
            </div>
            <div className={"styles_inputBox styles_inputBox_small"}>
              <Form.Item
                name={"orderQuantityLimit"}
                wrapperCol={{span: 24}}
                className={"styles_inputBox_form_item"}
                rules={[{
                  pattern: RegExp("^[1-9]\\d*$"),
                  message: "请输入大于零的整数",
                },]}
              >
                <InputNumber placeholder="限购数量" style={{width: '100%'}}/>
              </Form.Item>
            </div>
            {
              displayBatchSetting ?
                <Button type="primary" ghost onClick={() => {
                  batchInputValue()
                }}>批量设置</Button> :
                <Tooltip title={"请先选择规格并输入值"}>
                  <Button type="primary" ghost disabled>批量设置</Button>
                </Tooltip>
            }
            <Button type="link" ghost onClick={() => {
              batchClearSkuStockQuantity()
            }}>可售库存清零</Button>
          </Form>
        </div> : null
      }
    </Fragment>
  );
});

export default SkuSpecBatchSetPrice;

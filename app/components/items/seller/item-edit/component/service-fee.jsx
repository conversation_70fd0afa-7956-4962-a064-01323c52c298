import {ItemPercentageInput} from "./base/item-percentage-input";
import {commissionSwitchFormName} from "../utils/form-name-const";
import itemEditStore from "../ItemEditStore";

const {Form, Checkbox, Row, Col,Tooltip} = antd;
const  {QuestionCircleOutlined} = icons
const {observer} = mobxReactLite;

const ServiceFee = observer((props) => {
  const commissionSwitch = Form.useWatch(commissionSwitchFormName, itemEditStore.form);
  return <div>
    {/* 是否开启单品佣金 */}
    <Form.Item name={commissionSwitchFormName} valuePropName="checked"
               getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
      <Checkbox>
        是否开启单品佣金
        <Tooltip placement="topLeft"
                 title={"开启后该商品将以下述比例计算佣金"}
                 arrowPointAtCenter>
          <QuestionCircleOutlined/>
        </Tooltip>
      </Checkbox>
    </Form.Item>
    <Form.Item name="flag" label={"服务费类型"} hidden valuePropName="checked"
               getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
      <Checkbox>是否按比例</Checkbox>
    </Form.Item>
    {/* 百分比输入项 */}
    {commissionSwitch === 1 ? (
      <div className={"common-wrap"}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="服务商"
              name="serviceProviderRate"
              rules={[{required: true, message: '请输入服务商佣金'}]}
            >
              <ItemPercentageInput placeholder="请输入" addonAfter="%"/>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="门店"
              name="firstRate"
              rules={[{required: true, message: '请输入门店佣金'}]}
            >
              <ItemPercentageInput placeholder="请输入" addonAfter="%"/>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="导购"
              name="secondRate"
              rules={[{required: true, message: '请输入导购佣金'}]}
            >
              <ItemPercentageInput placeholder="请输入" addonAfter="%"/>
            </Form.Item>
          </Col>
        </Row>
      </div>) : null
    }
  </div>
})
export default ServiceFee


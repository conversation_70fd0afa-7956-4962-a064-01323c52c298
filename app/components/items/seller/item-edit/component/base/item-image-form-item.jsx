import customRequest from "../../../../../common/react/upload/custom-request";

const {useState, useEffect, Fragment} = React;

const {PlusOutlined} = icons;
const {message} = antd;
const {DTUploaderFile} = dtComponents;
const {observer} = mobxReactLite;

export const picUploadButton = (text) => (
  <div className={"image-empty"}>
    <div className={"placeholder"}>
      <PlusOutlined/>
      <div className={"text"}>{text || '上传'}</div>
    </div>
  </div>
)
export const MainImagesFormItem = observer((props) => {
  /**
   * 主图
   */
  const [mainImages, setImages] = useState(Array(1).fill(null))
  useEffect(() => {
    const value = props.value;
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        mainImages[i] = value[i]
      }
      setImages([...mainImages])
    } else if (value === null) {
      setImages(Array(1).fill(null))
    }
  }, [props.value])

  const innerOnChange = (index, {file, fileList}) => {
    const {onChange} = props
    if (file.status === "uploading") {
      mainImages[index] = file
      setImages([...mainImages])
    } else if (file.status === "done") {
      mainImages[index] = file
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "removed") {
      mainImages[index] = null
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "error") {
      message.error("上传失败")
    }
  }
  return <DTUploaderFile
    maxCount={1}
    size={5}
    accept={[".jpg", ".jpeg", ".png"]}
    listType={"picture-card"}
    fileList={mainImages[0] ? [mainImages[0]] : []}
    uploadButton={picUploadButton("上传主图")}
    customRequest={customRequest}
    onChange={(file) => {
      innerOnChange(0, file)
    }}
  />
})
export const SubImagesFormItem = observer((props) => {
  /**
   * 辅图
   */
  const [subImages, setImages] = useState(Array(5).fill(null))
  useEffect(() => {
    const value = props.value;
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        subImages[i] = value[i]
      }
      setImages([...subImages])
    } else if (value === null) {
      setImages(Array(5).fill(null))
    }
  }, [props.value])
  const innerOnChange = (index, {file, fileList}) => {
    const {onChange} = props
    if (file.status === "uploading") {
      subImages[index] = file
      setImages([...subImages])
    } else if (file.status === "done") {
      subImages[index] = file
      setImages([...subImages])
      onChange(subImages)
    } else if (file.status === "removed") {
      subImages[index] = null
      setImages([...subImages])
      onChange(subImages)
    } else if (file.status === "error") {
      message.error("上传失败")
    }
  }
  return <Fragment>
    <DTUploaderFile
      maxCount={1}
      size={5}
      accept={[".jpg", ".jpeg", ".png"]}
      listType={"picture-card"}
      fileList={subImages[0] ? [subImages[0]] : []}
      uploadButton={picUploadButton("上传辅助图")}
      customRequest={customRequest}
      onChange={(file) => {
        innerOnChange(0, file)
      }}
    />
    <DTUploaderFile
      size={5}
      accept={[".jpg", ".jpeg", ".png"]}
      uploadButton={picUploadButton("上传辅助图")}
      fileList={subImages[1] ? [subImages[1]] : []}
      customRequest={customRequest}
      onChange={(file) => {
        innerOnChange(1, file)
      }}
    />
    <DTUploaderFile
      size={5}
      accept={[".jpg", ".jpeg", ".png"]}
      uploadButton={picUploadButton("上传辅助图")}
      fileList={subImages[2] ? [subImages[2]] : []}
      customRequest={customRequest}
      onChange={(file) => {
        innerOnChange(2, file)
      }}/>
    <DTUploaderFile
      size={5}
      accept={[".jpg", ".jpeg", ".png"]}
      uploadButton={picUploadButton("上传辅助图")}
      fileList={subImages[3] ? [subImages[3]] : []}
      customRequest={customRequest}
      onChange={(file) => {
        innerOnChange(3, file)
      }}/>
  </Fragment>
})

const {useMemo} = React;
const {InputNumber} = antd;
const {observer} = mobxReactLite;


export const ItemPriceInput = observer(({needFormat=true,allowBlank=false ,value, onChange, ...rest}) => {
  const handleBlur = (e) => {
    const value = e.target.value;
    if (!value) return;

    const match = /^\d+(\.\d+)?$/.exec(value);
    if (!match) {
      e.target.value = ''; // 非法输入清空
      return;
    }

    if (value.includes('.')) {
      const [integer, decimal] = value.split('.');
      let value1 = `${integer}.${decimal.slice(0, 2)}`;
      e.target.value = value1;
      needFormat ? onChange(Decimal.mul(value1,100).toNumber()) : onChange(value1)
    }
  };
  const showValue =useMemo(()=>{
    if (allowBlank && (value === '' || value === null)) {
      return needFormat ? null : value
    } else {
      if (!isNaN(value) && value !== null && value !== undefined) {
        return needFormat ? Decimal.div(value,100).toFixed(2) : value
      }else{
        return value
      }
    }
  },[value,needFormat,allowBlank])
  return <InputNumber
    {...rest}
    controls={false}
    value={showValue}
    onBlur={handleBlur}
    onChange={(data) => {
      if(allowBlank && (data === ''||data === null)) return onChange(data)
      needFormat ? onChange(data * 100) : onChange(data)
    }}
  />
})


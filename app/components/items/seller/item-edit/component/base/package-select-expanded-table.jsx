import itemEditStore from "../../ItemEditStore";
import {lib} from "../../../../../common/react/utils/lib";

const { Table,Tooltip,Form} = antd;
const {useEffect,useState} = React;
const {observer} = mobxReactLite;
const PackageSelectExpandedTable = observer(({dataSource,allSelectedRow,isBonded,onSelectChange})=>{
  useEffect(()=>{
    if(Array.isArray(allSelectedRow)){
      setSelectedRowKeys([...allSelectedRow].map(item=>{
        return item.comboSkuId
      }))
    }
  },[allSelectedRow])
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const modalGoodsExpandedColumns = [
    {
      dataIndex: 'product',
      width: 338,
      render: (text, record, index) => ({
        children: <div className="style_skuName">
          {record.specification||"默认"}
        </div>,
      }),
    },
    {
      width: 134,
      dataIndex: 'price',
      align: 'center',
      render: (text, record, index) => ({
        children: <div >
          {lib.formatPrice(record.price)}
        </div>,
      }),
    },
    {
      width: 100,
      dataIndex: 'isBonded',
      align: 'center'
    },
  ];


  const onInnerSelectChange = (record,selected) => {
    onSelectChange(record,selected)

  };
  const onChange = (rowKeys) => {
    setSelectedRowKeys(rowKeys);
  }
  const rowSelection = {
    columnWidth:"70px",
    selectedRowKeys,
    onSelect: onInnerSelectChange,
    onChange:onChange,
    renderCell: (checked, record, index, originNode) => {
      if( isBonded !==  itemEditStore.form.getFieldValue(["item", "isBonded"])){
        return <Tooltip title="贸易类型与组合商品的贸易类型不一致”">{originNode}</Tooltip>;
      }
      return originNode;
    },
    getCheckboxProps: (record) => {
      let checkboxProps = {};
      if( isBonded !==  itemEditStore.form.getFieldValue(["item", "isBonded"])){
        checkboxProps.disabled = true;
      }
      return checkboxProps;
    }
  };
  return (
    <Table
      showHeader={false}
      rowSelection={{
        columnWidth:"70px",
        ...rowSelection,
      }}
      columns={modalGoodsExpandedColumns}
      dataSource={dataSource}
      pagination={false}
      scroll={{
        y: "max-content",
      }}
      rowKey={'id'}/>
  )
})
export default  PackageSelectExpandedTable

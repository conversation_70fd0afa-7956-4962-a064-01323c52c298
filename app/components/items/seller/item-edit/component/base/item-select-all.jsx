const {Fragment,useState,useEffect} = React;
const {Select, Divider, Checkbox} = antd;
const {CloseOutlined} = icons

function SelectAll({value,valueKey="id",labelKey="name",tagAllName, ...rest}) {
  const {list,onChange} = rest
  const [selectedValues, setSelectedValues] = useState([]);
  const [checkAllState, setCheckAllState] = useState({ checked: true, indeterminate: false });
  useEffect(()=>{
    if(Array.isArray(value)){
      setSelectedValues(value)
    }
  },[value])
  useEffect(()=> {
      if (Array.isArray(list)) {
        if(checkAllState.checked){
          const newValue= list.map(item => item[valueKey])
          setSelectedValues(newValue)
          onChange && onChange(newValue)
        }
      }
    }
  ,[list])
  /**
   * 自定义选择标签
   * @param props  CustomTagProps
   * @returns {JSX.Element}
   */
  const tagRender = props => {
    const {label, onClose} = props;
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    return ( <span className="ant-select-selection-item">
                <span className="ant-select-selection-item-content" >
                  {checkAllState.checked? `全选${tagAllName}` : Array.isArray(label)&&label.length ? label[1].props.children:label? label.props.children:""}
                </span>
                <span
                  className="ant-select-selection-item-remove"
                  style={{userSelect: "none", verticalAlign: "0"}}
                  onMouseDown={onPreventMouseDown}
                  onClick={() => {
                    onClose();
                  }}>
                    <CloseOutlined/>
                </span>
            </span>
    );
  };
  const checkAll = e => {
    let checked = e.target.checked;
    setCheckAllState({ checked: checked, indeterminate: false });
    let value;
    if (checked) {
      value = list.map(item => item[valueKey]);
    } else {
      value = [];
    }
    setSelectedValues(value);
    onChange(value);
  };
  const handleChange = value => {
    checkSelectAll(value);
    setSelectedValues(value);
    onChange(value);
  };
  function checkSelectAll(selectedValue) {
      let checked = selectedValue.length === list.length;
      setCheckAllState({ checked: checked, indeterminate: selectedValue.length !== 0 && !checked });
  }
  return <Select
    className={"styles_hiddenRest"}
    style={{width: "100%"}}
    showSearch={false}
    allowClear
    value={selectedValues}
    onChange={handleChange}
    maxTagCount={1}
    showArrow
    mode={"multiple"}
    tagRender={tagRender}
    maxTagPlaceholder={<div></div>}
    menuItemSelectedIcon={null}
    dropdownRender={menu => {
      return (
        <Fragment>
          <div className={"ant-select-item"}>
            <Checkbox
              onChange={checkAll}
              checked={checkAllState.checked}
              indeterminate={checkAllState.indeterminate}>
              全选{tagAllName}
            </Checkbox>
            <Divider style={{margin: "5px 0"}}/>
          </div>
          {menu}
        </Fragment>
      );
    }}
  >
    {list.map((optItem, index) => {
      return <Select.Option key={index} value={optItem[valueKey]} title={optItem[labelKey]} data={optItem}>
        {
          <Checkbox
            checked={selectedValues.includes(optItem[valueKey])}
            style={{marginRight: 6}}>
            {optItem[labelKey]}
          </Checkbox>
        }
      </Select.Option>
    })}
  </Select>
}
export default SelectAll;

import request from "../../../../../utils/plugins/axios/request";

const {Input} = antd
const {useEffect, useRef,useState} = React


function ItemRichEditor({ value = '', onChange }) {
  const { TextArea } = Input;
  const editorRef = useRef(null);
  const [tinymceInit,setTinymceInit] = useState(false);
  const editorInputIng = useRef(false);
  const textAreaId = `editor-${Math.random().toString(36).substr(2, 9)}`;
  useEffect(()=>{

    if(editorRef.current && tinymceInit){
      if(!editorInputIng.current){
        editorRef.current && editorRef.current.setContent(value || '');
      }
    }
  },[value,tinymceInit])
  useEffect(() => {
    // Initialize the editor
    tinymce.init({
      selector: `#${textAreaId}`,
      language: 'zh_CN',
      plugins: "table lists link image",
      toolbar: `formatselect | link image |  bold italic strikethrough forecolor backcolor
       alignleft aligncenter alignright alignjustify
        numlist bullist outdent indent`,
      promotion: false,
      image_dimensions:false,
      file_picker_types: 'file image media',
      branding: false,
      setup: (editor) => {
        editorRef.current = editor;

        // Sync content back to parent
        editor.on('change keyup', () => {
          const content = editor.getContent();
          editorInputIng.current= true
          if (onChange) onChange(content);
        });
      },
      init_instance_callback: (editor) => {
        setTinymceInit(true)
        editor.setContent(value || '');
      },
      images_upload_handler: function (blobInfo) {
        return new Promise((resolve, reject) => {
          var formData = new FormData();
          formData.append('file', blobInfo.blob(), blobInfo.filename());
          request({
            url: '/api/file/upload',
            method: 'post',
            needMask: true,
            data: formData,
            success: function (data) {
              resolve(data);
            },
            fail: function (error, msg) {
              reject(new Error(msg || '上传图片失败'));
            }
          });
        });
      }
    });

    return () => {
      // Clean up the editor
      if (editorRef.current) {
        tinymce.get(editorRef.current.id).remove();
        editorRef.current = null;
      }
    };
  }, []);

  return (
    <div>
      <TextArea id={textAreaId}/>
    </div>
  );
}

export default ItemRichEditor;

const {AutoComplete,Input} = antd;
const {observer} = mobxReactLite;

export const ATTRS_GROUP_BASIC = "BASIC"
export const ATTRS_GROUP_SERVICE = "SERVICE"
export const ATTRS_GROUP_DEFAULT = "DEFAULT"
/**
 *属性组件
 * @param {*} props
 * value ：{
 *            "attrKey": "birthday",//属性key
 *             "attrVal": "2024-11-24", //属性值
 *             group": "BASIC" //组名
 *         }
 *  attrProps：{
 *     "id": 12,
 *     "categoryId": 22,
 *     "attrKey": "主色调",
 *     "group": "DEFAULT",
 *     "index": 1,
 *     "status": 1,
 *     "attrMetas": {
 *         "REQUIRED": "false",
 *         "IMPORTANT": "false",
 *         "SKU_CANDIDATE": "true",
 *         "USER_DEFINED": "true",
 *         "SEARCHABLE": "true",
 *         "VALUE_TYPE": "STRING"
 *     },
 *     "attrMetasForK": {
 *         "SKU_CANDIDATE": "true",
 *         "SEARCHABLE": "true",
 *         "REQUIRED": "false",
 *         "IMPORTANT": "false",
 *         "USER_DEFINED": "true",
 *         "VALUE_TYPE": "STRING"
 *     },
 *     "attrVals": [
 *         "红色",
 *         "黄色"
 *     ],
 *     "attrValsJson": "[\"红色\",\"黄色\"]",
 *     "createdAt": 1459301763000,
 *     "updatedAt": 1483067079000
 * }
 * @returns
 */
 const ItemOtherAttr = observer(({needFormat = true, attrProps, value, onChange, ...rest}) => {

  const innerOnChange = (data) => {
    const {attrKey,group} = attrProps.attributeRule
    onChange({
      attrKey: attrKey,
      attrVal: data,
      group: group //组名
    })
  }
  return <AutoComplete
    {...rest}
    className={"attr-item-select"}
    value={value ? value.attrVal:''}
    options={(attrProps.attributeRule && attrProps.attributeRule.attrVals||[]).map(item => ({value: item,label: item}))}
    onChange={(data) => {
      innerOnChange(data)
    }}
  >  <Input /></AutoComplete>
})
export default ItemOtherAttr

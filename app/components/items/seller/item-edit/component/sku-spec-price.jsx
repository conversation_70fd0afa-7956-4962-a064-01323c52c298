import { ItemPriceInput } from "./base/item-price-input";
import ItemText from "./base/item-text";
import Schema from "../utils/async-validator";
import SkuSpecBatchSetPrice from "./sku-spec-batch-set-price";
import SkuSpecUpload from "./sku-spec-upload";
import itemEditStore from "../ItemEditStore";
import { skuSalesPriceFormName } from "../utils/form-name-const";
import { validatePriceNumber } from "../utils/method";
import {validator} from "../utils/sku-validator";
const {DTInput} = dtComponents
const {observer} = mobxReactLite;
const {useEffect, useState, useRef, useMemo} = React;
const {Table, Input, InputNumber, Form, Switch,Tooltip} = antd;
export const TableCellErrorView = (props) => {
  const {children,record,name, ...rest} = props

  const errorMsg = ()=>{
    if(name){
      if(record.sku.errors && record.sku.errors[name]){
        return (record.sku.errors[name]||[]).map(e=>e.message).join("\n")
      }
    }
    return null
  }
  return <div {...rest} className="ant-form-item-has-success">
    {React.cloneElement(children, {...children.props, status:errorMsg()?"error":""}) }
    <div className="ant-form-item-explain-error">{errorMsg()}</div>
  </div>
}

const SpecTable = observer((props) => {
  const {dataSource,value,onChange,specs} =props

  const innerChange = async (values,record,index) => {
    if (record.sku.errors) {
      try {
        await validator.validate(record)
        values[index].sku.errors = null
      } catch ({errors, fields}) {
        if (errors.length > 0) {
          record.sku.errors = fields
        } else {
          record.sku.errors = null
        }
      }
    }
    onChange(values)
  }
  const mergeDataSource =useMemo(() => {
    return value
  }, [value ,dataSource,itemEditStore.itemsEditorPrivilege.whetherEditor ])


  // 定义表格列
  const columns = [
    ...specs.map(spec => ({
      title: spec.name,
      width: 120,
      fixed: "left",
      dataIndex: spec.name,
      render: (value, record, index) => {
        const span = itemEditStore.processedTableData[index][`${spec.name}Span`] || 0;
        return {children: itemEditStore.processedTableData[index][`${spec.name}`], props: {rowSpan: span}};
      }
    })),
    {
      title: '规格图',
      editable: true,
      align: "center",
      width: 100,
      render: (_, record, index) => (
        // <Form.Item
        //   name={['skuWithCustoms', index, 'sku', 'image']}
        //   wrapperCol={{span: 24}}>
        <TableCellErrorView>
          <SkuSpecUpload value={record.sku.image} onChange={(e) => {
            value[index].sku.image = e
            innerChange([...value],record,index)
          }}/>
        </TableCellErrorView>
        // </Form.Item>
      ),
    },
    {
      title: (<span><span className="styles_required">*</span> 销售价（元）</span>),
      editable: true,
      width: 150,
      render: (_, record, index) => {
        // <Form.Item
        //   name={['skuWithCustoms', index, 'sku', 'price']}
        //   rules={[{required: true, message: '请输入销售价'}, {validator: validatePriceNumber}]}
        //   wrapperCol={{span: 24}}>

        return <TableCellErrorView record={record} name={"sku.price"}>
          <ItemPriceInput addonAfter="元" value={record.sku.price} onChange={(e) => {
            value[index].sku.price = e
            innerChange([...value],record,index )

          }}/>
        </TableCellErrorView>
        // </Form.Item>
      },
    },
    {
      title: "SKUID",
      editable: true,
      width: 150,
      render: (_, record, index) => {
        // return <Form.Item
        //   name={['skuWithCustoms', index, 'sku', 'skuId']}
        //   wrapperCol={{span: 24}}>
        return <ItemText value={record.sku.skuId}/>
        // </Form.Item>
      }
    },
    {
      title: (<span><span className="styles_required">*</span>商家编码</span>),
      editable: true,
      width: 150,
      render: (_, record, index) => (
        // <Form.Item
        //   name={['skuWithCustoms', index, 'sku', 'outerSkuId']}
        //   wrapperCol={{span: 24}}
        //   rules={[{required: true, message: '请输入商家编码'}, {
        //     pattern: /^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/,
        //     message: "仅支持汉字数字英文大小写及符号",
        //   }, {
        //     max: 60,
        //     message: '长度不能超过60个字符',
        //   },]}>
        <TableCellErrorView record={record} name={"sku.outerSkuId"}>
          <DTInput trimMode={"all"} value={record.sku.outerSkuId}
                 onChange={(e) => {
                   value[index].sku.outerSkuId = e.target.value
                   innerChange([...value],record,index)
                 }}/>
        </TableCellErrorView>
        // </Form.Item>
      ),
    },
    {
      title: "划线价（元）",
      editable: true,
      width: 150,
      render: (_, record, index) => {
        return   (// <Form.Item
        //   name={['skuWithCustoms', index, 'sku', 'extraPrice', 'crossedPrice']}//目前没有多规格 name 下标全部为0
        //   wrapperCol={{span: 24}}
        //   rules={[
        //     {validator: validatePriceNumber},
        //     ({getFieldValue}) => ({
        //       validator(_, value) {
        //         const price = getFieldValue(['skuWithCustoms', index, 'sku', 'price']);
        //         if (value && price && value < price) {
        //           return Promise.reject("划线价不能小于售价价格");
        //         }
        //         return Promise.resolve();
        //       },
        //     }),
        //   ]}
        // >
        <TableCellErrorView record={record} name={"sku.extraPrice.crossedPrice"}>
          <ItemPriceInput
            addonAfter="元"
            allowBlank
            value={record.sku.extraPrice ? record.sku.extraPrice.crossedPrice : null}
            onChange={(e) => {
              value[index].sku.extraPrice.crossedPrice = e
             innerChange([...value],record,index)
            }}
          />
        </TableCellErrorView>
        // </Form.Item>
        )
      }
    },
    {
      title: (<span><span className="styles_required">*</span> 可售库存（件）</span>),
      editable: true,
      width: 180,
      render: (_, record, index) => (
        // <Form.Item
        //   name={['skuWithCustoms', index, 'sku', 'stockQuantity']}
        //   rules={[{required: true, message: '请输入可售库存'}, {
        //     pattern: /^\+?[0-9]\d*$/,
        //     message: "请输入大于等于零的整数",
        //   },]}
        //   wrapperCol={{span: 24}}>
        <TableCellErrorView record={record} name={"sku.stockQuantity"}>
          <InputNumber min={0} style={{width: '100%'}} placeholder="可售库存"
                       value={record.sku.stockQuantity}
                       onChange={(e) => {
                         value[index].sku.stockQuantity = e
                        innerChange([...value],record,index)
                       }}/>
        </TableCellErrorView>
        // </Form.Item>
      ),
    },

    // {
    //   title: '',
    //   width: 0,
    //   render: (_, record, index) => (
    //     // <Form.Item
    //     //   name={['skuWithCustoms', index, 'sku', "extraMap", 'unitQuantity']}
    //     //   initialValue={1}
    //     //   hidden
    //     //   wrapperCol={{span: 24}}
    //     // >
    //       <InputNumber/>
    //     // </Form.Item>
    //   ),
    // },
    // {
    //   title: '',
    //   width: 0,
    //   render: (_, record, index) => (
    //     // <Form.Item
    //     //   name={['skuWithCustoms', index, 'sku', "tags", 'pushSystem']}
    //     //   hidden
    //     //   initialValue={itemEditStore.thirdPartyId}
    //     //   wrapperCol={{span: 24}}
    //     // >
    //       <InputNumber/>
    //     // </Form.Item>
    //   ),
    // },
    {
      title: (<span>拆单数量</span>),
      width: 150,
      render: (_, record, index) => (
        // <Form.Item
        //   name={['skuWithCustoms', index, "sku", "extraMap", "skuOrderSplitLine"]}
        //   wrapperCol={{span: 24}}
        //   rules={[{
        //     pattern: RegExp("^[1-9]\\d*$"),
        //     message: "请输入大于0的整数",
        //   },]}
        // >
        <TableCellErrorView record={record} name={"sku.extraMap.skuOrderSplitLine"}>
          <InputNumber placeholder="拆单数量"
                       value={record.sku.extraMap.skuOrderSplitLine}
                       onChange={(e) => {
                         value[index].sku.extraMap.skuOrderSplitLine = e
                        innerChange([...value],record,index)
                       }}/>
        </TableCellErrorView>
        // </Form.Item>
      ),
    },
    {
      title: (<span>限购数量</span>),
      width: 120,
      render: (_, record, index) => (
        // <Form.Item
        //   name={['skuWithCustoms', index, "sku", "extraMap", "orderQuantityLimit"]}
        //   wrapperCol={{span: 24}}
        //   rules={[{
        //     pattern: RegExp("^[1-9]\\d*$"),
        //     message: "请输入大于0的整数",
        //   },]}
        // >
        <TableCellErrorView record={record} name={"sku.extraMap.orderQuantityLimit"}>
          <InputNumber placeholder="限购数量"
                       value={record.sku.extraMap.orderQuantityLimit}
                       onChange={(e) => {
                         value[index].sku.extraMap.orderQuantityLimit = e
                        innerChange([...value],record,index)
                       }}/>
        </TableCellErrorView>
        // </Form.Item>
      ),
    },
    {
      title: <span><span className="styles_required">*</span>sku状态</span>,
      width: 120,
      fixed: "right",
      render: (_, record, index) => (
        // <Form.Item
        //   name={['skuWithCustoms', index, "sku", "status"]}
        //   wrapperCol={{span: 24}}
        //   valuePropName="checked"
        //   initialValue={1}
        //   getValueProps={(value) => ({
        //     checked: value === 1
        //   })}
        //   normalize={(checked) => checked ? 1 : -1}
        // >
        <TableCellErrorView>
          <Switch checkedChildren="已上架"
                         unCheckedChildren="已下架"
                         checked={record.sku.status===1}
                         onChange={(checked) => {
                           value[index].sku.status = checked ? 1 : -1
                          innerChange([...value],record,index)
                         }}
                         defaultChecked={false}
            />
        </TableCellErrorView>
        // </Form.Item>
      ),
    },
  ]
  return (<Table columns={columns}
                 scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0), y: 500}}
                 dataSource={mergeDataSource || []}
                 pagination={false}
                 rowKey="id"
                 bordered/>

  );
});

const SkuSpecTableForm = observer((props) => {
  const [dataSource, setDataSource] = useState([])
  const hasError = useRef(false)
  useEffect(() => {
    setDataSource([...itemEditStore.processedTableData])
  }, [itemEditStore.processedTableData])


  const valitorItems = async (values) => {
    const validator = new Schema({
      sku: {
        type: 'object',
        fields: {
          price: [{required: true, type: 'number', message: '请输入销售价'}, {validator: validatePriceNumber}],
          stockQuantity: [{required: true, type: 'number', message: '请输入可售库存'}, {
          pattern: /^[1-9]\d{0,6}$/,
            message: "请输入7位正整数",
          },],
          outerSkuId: [{type: 'string', required: true, message: '请输入商家编码'}, {
            pattern: /^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/,
            message: "仅支持汉字数字英文大小写及符号",
          }, {
            type: 'string',
            max: 60,
            message: '长度不能超过60个字符',
          },],
          extraPrice: {
            type: 'object',
            fields: {
              crossedPrice: [
                {validator: validatePriceNumber},
                // {
                //   validator: function (_, value)  {
                //      const price =itemEditStore.form.getFieldValue(['skuWithCustoms', index, 'sku', 'price']);
                //      if (value && price && value < price) {
                //        return Promise.reject("划线价不能小于售价价格");
                //      }
                //     return Promise.resolve();
                //   }
                // }
              ],
            }
          },
          extraMap: {
            type: 'object',
            fields: {
              orderQuantityLimit: [{
                pattern: RegExp("^[1-9]\\d*$"),
                message: "请输入大于0的整数",
              }],
              skuOrderSplitLine: [{
                pattern: RegExp("^[1-9]\\d*$"),
                message: "请输入大于0的整数",
              }],
            }
          }
        }
      }
    })
    hasError.current = false
   return new Promise(async (resolve, reject) => {
     await Promise.all(
       values.map(async (item, index) => {
         try {
           const price = item.sku.price;
           if (item.sku.extraPrice && item.sku.extraPrice.crossedPrice
             && price && item.sku.extraPrice.crossedPrice < price) {
             values[index].sku.errors = {"sku.extraPrice.crossedPrice": [new Error("划线价不能小于售价价格")]}
           } else {
             values[index].sku.errors = {}
           }
           await validator.validate(item)
         } catch ({errors, fields}) {
           if (errors.length > 0) {
             hasError.current = true
             values[index].sku.errors = fields
           } else {
             values[index].sku.errors = {}
           }
         }
       })
     )
     console.log("校验错误", hasError.current)
     if (hasError.current){
       reject(["商品销售信息填写有误，请检查"])
     } else{
       resolve()
     }
   })

  }
    return <div>
      <SkuSpecBatchSetPrice specs={itemEditStore.specs} batchInputValueCallBack={(combinationSpecSkuInfo)=> {

        let oldSkuInfo = itemEditStore.form.getFieldValue(skuSalesPriceFormName)
        oldSkuInfo.map(item => {
          let findItem = combinationSpecSkuInfo.find(combinationSpecSkuInfoItem => [...combinationSpecSkuInfoItem.specDetails].sort().join(',') === [...item.sku.specDetails].sort().join(','))
          if (findItem) {
            if (findItem.price) {
              item.sku.price = findItem.price
            }
            if (findItem.crossedPrice) {
              if(item.sku.extraPrice === undefined){
                item.sku.extraPrice ={}
                item.sku.extraPrice.crossedPrice = findItem.crossedPrice
              }else{
                item.sku.extraPrice.crossedPrice = findItem.crossedPrice
              }

            }
            if (findItem.outerSkuId) {
              item.sku.outerSkuId = findItem.outerSkuId
            }
            if (findItem.stockQuantity||findItem.stockQuantity==0) {
              item.sku.stockQuantity = findItem.stockQuantity
            }
            if (findItem.skuOrderSplitLine) {
              item.sku.extraMap.skuOrderSplitLine = findItem.skuOrderSplitLine
            }
            if (findItem.orderQuantityLimit) {
              item.sku.extraMap.orderQuantityLimit = findItem.orderQuantityLimit
            }
          }
          return oldSkuInfo
        })
        itemEditStore.form.setFieldValue(skuSalesPriceFormName, oldSkuInfo)
        setDataSource([...dataSource])

      }
      }/>
      <Form.Item name={skuSalesPriceFormName} noStyle
                 // rules={[{
                 //   validator(rule,value){
                 //     if(!value){
                 //       return Promise.resolve("")
                 //     }
                 //     return valitorItems(value);
                 //   },
                 // }
                 // ]}
      >
        <SpecTable  specs={itemEditStore.specs} dataSource={dataSource} />
      </Form.Item>
    </div>
  })

  export default SkuSpecTableForm;

import ItemOtherAttr, {ATTRS_GROUP_BASIC, ATTRS_GROUP_DEFAULT, ATTRS_GROUP_SERVICE} from "./base/Item-other-attr";
import itemEditStore from "../ItemEditStore";
const {toJS} = mobx;
const {useMemo} = React;

const {Form, Input, Row, Col} = antd;
const {DTInput} = dtComponents

const {observer} = mobxReactLite;

const NormalAttrs = observer((props) => {

  const commonAttrsFormItmProps = {labelAlign: "right", labelCol: {flex: "120px"}}

  const mergedOtherAttrs = useMemo(() => {
    let otherAttrs = []
    if (itemEditStore.otherAttrs.length !== 0) {
      let default_attr = itemEditStore.otherAttrs.find(item => item.group === ATTRS_GROUP_DEFAULT);
      let default_service = itemEditStore.otherAttrs.find(item => item.group === ATTRS_GROUP_SERVICE);
      if (default_attr) {
        otherAttrs = [...otherAttrs, ...default_attr.otherAttributeWithRules]
      }
      if (default_service) {
        otherAttrs = [...otherAttrs, ...default_service.otherAttributeWithRules]
      }
    }
    if(itemEditStore.categoryAttrs.length !== 0){
      let default_attr = itemEditStore.categoryAttrs.find(item => item.group === ATTRS_GROUP_DEFAULT);
      let default_service = itemEditStore.categoryAttrs.find(item => item.group === ATTRS_GROUP_SERVICE);
      if (default_attr) {
        otherAttrs = [...otherAttrs, ...default_attr.categoryAttributes]
      }
      if (default_service) {
        otherAttrs = [...otherAttrs, ...default_service.categoryAttributes]
      }
    }
    return otherAttrs
  }, [itemEditStore.otherAttrs,itemEditStore.categoryAttrs])

  return <div>
    <Form.Item label="类目属性" wrapperCol={{span: 22}}>
      <div className={"common-wrap"}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="商品条码"
              name={["item","barCode"]}
              {...commonAttrsFormItmProps}
              labelAlign={"right"}
              rules={[
                {max: 15, message: '计量单位最多15个字符'},
              ]}
            >
              <DTInput placeholder="请输入" className={"attr-item-input"}  trimMode={"all"}/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="计量单位"
              name={["item", "extra", "unit"]}
              {...commonAttrsFormItmProps}
              labelAlign={"right"}
              rules={[
                {max: 30, message: '计量单位最多30个字符'},
              ]}
            >
              <Input placeholder="请输入" className={"attr-item-input"} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="产地"
              name={["otherAttributeWithRules", "origin"]}
              {...commonAttrsFormItmProps}
              rules={ [{
                validator: (_, value) => {
                  if (value && value.attrVal) {
                    let length = value.attrVal.length;
                    if (length > 30) {
                      return Promise.reject(new Error("字数不能超过30个"));
                    }
                  }
                  return Promise.resolve();
                },
              }]}
            >
              <ItemOtherAttr placeholder="请输入"
                             attrProps={{attributeRule: {attrKey: "origin", group: ATTRS_GROUP_BASIC}}}/>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="重量"
              name={[`otherAttributeWithRules`, "weight"]}
              {...commonAttrsFormItmProps}
              rules={[ {
                validator: (_, value) => {
                  if (value && value.attrVal) {
                    let length = value.attrVal.length;
                    if (length > 30) {
                      return Promise.reject(new Error("字数不能超过30个"));
                    }
                  }
                  return Promise.resolve();
                },
              }]}
            >
              <ItemOtherAttr placeholder="请输入"
                             attrProps={{attributeRule: {attrKey: "weight", group: ATTRS_GROUP_BASIC}}}/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="型号"
              name={["item", "specification"]}
              {...commonAttrsFormItmProps}
              rules={[
                {max: 30, message: '型号最多30个字符'},
              ]}
            >
              <Input placeholder="请输入"  className={"attr-item-input"}/>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="发货地"
              name={["item", "extra", "countryOfDeparture"]}
              {...commonAttrsFormItmProps}
              rules={[
                {max: 30, message: '发货地最多30个字符'},
              ]}
            >
              <Input placeholder="请输入" className={"attr-item-input"}/>
            </Form.Item>
          </Col>
          {/*放入动态属性*/}
          {
            mergedOtherAttrs && mergedOtherAttrs.length > 0 && mergedOtherAttrs.map((item, index) => {
              const attributeRule=item.attributeRule? item.attributeRule: item
              const required=attributeRule.attrMetasForK.REQUIRED === "true"
              return <Col span={12} key={index}>
                <Form.Item
                  label={attributeRule.attrKey}
                  name={["otherAttributeWithRules", attributeRule.attrKey]}
                  {...commonAttrsFormItmProps}
                  rules={[{
                    required: required,
                  } ,
                    {
                    validator(rules, value) {
                      if (required && value && (!value.attrVal||value.attrVal==='')) {
                        return  Promise.reject(new Error(`请填写${attributeRule.attrKey}`));
                      }
                      return Promise.resolve();
                    },
                  }]}
                >
                  <ItemOtherAttr attrProps={{attributeRule}}/>
                </Form.Item>
              </Col>
            })
          }
        </Row>
      </div>
    </Form.Item>
  </div>
})
export default NormalAttrs


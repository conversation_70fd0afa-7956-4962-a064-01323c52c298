import { ItemPriceInput } from "./base/item-price-input";
import ItemText from "./base/item-text";
import itemEditStore from "../ItemEditStore";
import { lib } from "../../../../common/react/utils/lib";
import { validatePriceNumber } from "../utils/method";

const {DTInput} = dtComponents
const {observer} = mobxReactLite;

const {useState} = React;
const {Table, Input, InputNumber, Form, Tooltip, Col} = antd;
const {QuestionCircleOutlined} = icons;

const SkuPrice = observer((props) => {
  let isThirdPartyItem = lib.getParam("isThirdPartyItem");
  const [dataSource, setDataSource] = useState([{}]);
  // Table columns definition

  const columns = [
    {
      title: (<span><span className="styles_required">*</span> 销售价（元）</span>),
      editable: true,
      width: 150,
      render: (_, record) => (<Form.Item
          name={['skuWithCustoms', 0, 'sku', 'price']}//目前没有多规格 name 下标全部为0
          rules={[{required: true, message: '请输入销售价'}, { validator: validatePriceNumber }]}
          wrapperCol={{span: 24}}>
          <ItemPriceInput addonAfter="元" />
        </Form.Item>
      ),
    },
    {
      title: (<Tooltip placement="topLeft" title={"划线价小于等于销售价"} arrowPointAtCenter>
        划线价（元）<QuestionCircleOutlined/>
      </Tooltip>),
      editable: true,
      width: 150,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, 'sku', 'extraPrice', 'crossedPrice']}//目前没有多规格 name 下标全部为0
          wrapperCol={{span: 24}}
          rules={[
            { validator: validatePriceNumber },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const price = getFieldValue(['skuWithCustoms', 0, 'sku', 'price']);
                if (value  && price && value < price  ) {
                  return Promise.reject("划线价不能小于售价价格");
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <ItemPriceInput addonAfter="元" allowBlank/>
        </Form.Item>
      ),
    },
    {
      title: "SKUID",
      editable: true,
      width: 150,
      render: (_, record,index) => {
        return  <Form.Item
          name={['skuWithCustoms', index, 'sku', 'skuId']}
          wrapperCol={{span: 24}}>
          <ItemText/>
        </Form.Item>
      }
    },
    {
      title: (<span><span className="styles_required">*</span>商家编码</span>),
      editable: true,
      // className: 'hidden-column',
      width: 150,
      render: (_, record) => {
        return <Form.Item
          name={['skuWithCustoms', 0, 'sku', 'outerSkuId']}
          wrapperCol={{span: 24}}
          rules={isThirdPartyItem==0?[{required: true, message: '请输入商家编码'},{
            pattern: /^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/,
            message: "仅支持汉字数字英文大小写及符号",
          },{
            max: 60,
            message: '长度不能超过60个字符',
          },]:[]}
        >
          <DTInput trimMode={"all"} disabled={isThirdPartyItem==1}/>
        </Form.Item>
      },
    },
    {
      title: (
        <span ><span className="styles_required">*</span> 可售库存(件）</span>),
      editable: true,
      width: 180,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, 'sku', 'stockQuantity']}
          rules={[{required: true, message: '请输入可售库存'}, {
          pattern: /^[1-9]\d{0,6}$/,
            message: "请输入7位正整数",
          },]}
          wrapperCol={{span: 24}}>
          <InputNumber min={0} style={{ width: '100%'}} placeholder="可售库存" />
        </Form.Item>
      ),
    },

    {
      title: '',
      width: 0,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, 'sku', "extraMap", 'unitQuantity']}
          initialValue={1}
          hidden
          wrapperCol={{span: 24}}
        >
          <InputNumber />
        </Form.Item>
      ),
    },
    {
      title: '',
      width: 0,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, 'sku', "version"]}
          hidden
          wrapperCol={{span: 24}}
        >
          <InputNumber />
        </Form.Item>
      ),
    },
    {
      title: '',
      width: 0,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, 'sku', "tags", 'pushSystem']}
          hidden
          initialValue={itemEditStore.thirdPartyId}
          wrapperCol={{span: 24}}
        >
          <InputNumber />
        </Form.Item>
      ),
    },
    {
      title: (<span>拆单数量</span>),
      width: 150,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, "sku", "extraMap", "skuOrderSplitLine"]}
          wrapperCol={{span: 24}}
          rules={[{
            pattern: RegExp("^[1-9]\\d*$"),
            message: "请输入大于0的整数",
          },]}
        >
          <InputNumber placeholder="拆单数量"/>
        </Form.Item>
      ),
    },
    {
      title: (<span>限购数量</span>),
      width: 120,
      render: (_, record) => (
        <Form.Item
          name={['skuWithCustoms', 0, "sku", "extraMap", "orderQuantityLimit"]}
          wrapperCol={{span: 24}}
          rules={[{
            pattern: RegExp("^[1-9]\\d*$"),
            message: "请输入大于0的整数",
          },]}
        >
          <InputNumber placeholder="限购数量"/>
        </Form.Item>
      ),
    },
  ];

  return (<Table
      className="sku-price-table"
      size={"small"}
      tableLayout={"fixed"}
      bordered
      dataSource={dataSource}
      columns={columns}
      scroll={{x: columns.reduce((acc, cur) => acc + cur.width, 0)}}
      pagination={false}
    />
  );
})
export default SkuPrice;

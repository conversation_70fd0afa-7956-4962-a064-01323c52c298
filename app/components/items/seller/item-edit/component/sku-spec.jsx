import itemEditStore from "../ItemEditStore";
import {
  skuSalesPriceFormName,
  specDetailFormName,
  specDetailValuesFormName
} from "../utils/form-name-const";
import {lib} from "../../../../common/react/utils/lib";
import request from "../../../../utils/plugins/axios/request";
import SkuSortSpec from "./sku-sort-spec";
import {generateCombinations, handleGenerateTable, parseSpecData, reorderSkus} from "../hooks/useSpecColumn";
import useDebounce from "../hooks/useDebounce";
const { DTInput } = dtComponents
const {useState, Fragment, useRef, useEffect,useCallback,useMemo} = React
const {Input, Select, Space, Button, Divider, Form,Tooltip,Modal,message} = antd;
const {observer} = mobxReactLite;
const {CheckOutlined, CloseOutlined,DeleteOutlined,PlusOutlined,ExclamationCircleFilled} = icons

const SkuSelect = observer((props) => {
  const specDetail = Form.useWatch(specDetailFormName, itemEditStore.form);
  const [items, setItems] = useState()
  const [isEdit, setIsEdit] = useState(false)
  const [form] = Form.useForm()
  const inputRef = useRef()
  const {value} = props
  useEffect(() => {
    //过滤可选
    setItems(lib.clone(itemEditStore.skuSelectList).map(item=>{
      if (value && item.id === value.id){
        item.disabled = false
      }else if(Array.isArray(specDetail)) {
        item.disabled = specDetail.filter(it=>it).some((item1)=>item1.id === item.id)
      }
      return item
    }))
  },[itemEditStore.skuSelectList,specDetail])


  useEffect(() => {
    if (isEdit) {
      inputRef.current.focus({
        cursor: 'end',
      });
    }
  }, [isEdit])

  const submitSkuName=()=> {
    form.submit()
  }

  const onFinish=(values)=> {
    request({
      url: "/mall-admin/api/item/specification",
      method: "post",
      data: {
        name: values.name
      },
      success: (res) => {
          setIsEdit(false)
          itemEditStore.requestSkuSelectList()
      }
    })
  }
  const notEditModeView = ()=> {
    return <div className="add-sku-name">
                <span className="new-sku">
                <span>无合适选项？</span>
                <a onClick={() => {
                  setIsEdit(true)
                }}>创建类型</a></span>
    </div>
  }
  const editModeView = ()=> {
    return  <Fragment>
      <Form form={form} onFinish={onFinish}>
        <Form.Item name={"name"}
                   style={{marginBottom: 0}}
                   rules={[
                     {
                       required: true,
                       message: "请输入规格类型",
                     },
                     {
                       pattern: /^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/,
                       message: "仅支持汉字数字英文大小写及符号",
                     },{
                       max: 8,
                       message: '规格类型不能超过8个字符',
                     },
                     {
                     //  pattern: /^([\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]|\w)+$/,
                      // message: '只能包含大小写字母、数字以及英文符号'
                     }]}>
          <Input
            ref={inputRef}
            placeholder={"请输入规格类型"}
          />
        </Form.Item>
      </Form>
      <CheckOutlined style={{color: '#3cc781', margin: "0px 8px", cursor: "pointer"}} onClick={() => {
        submitSkuName()
      }}/>
      <CloseOutlined style={{color: '#ff4050', cursor: "pointer"}} onClick={() => {
        setIsEdit(false)
      }}/>
    </Fragment>
  }
 const innerOnChange =(e,data)=>{
   props.onChange && props.onChange({ id: data.id, name: data.name })
 }
 const footer = useMemo(()=>{
   return  itemEditStore.itemsEditorPrivilege.whetherEditor?
     <Fragment>
       <Divider style={{margin: '8px 0',}}/>
       <div className={"popup-footer"}>
         {isEdit ? editModeView() : notEditModeView()}
       </div>
   </Fragment>:null
 },[itemEditStore.itemsEditorPrivilege.whetherEditor,isEdit])
  return   (itemEditStore.isThirdPartyItem===1||!itemEditStore.itemsEditorPrivilege.whetherEditor) ?
    <Tooltip title={!itemEditStore.itemsEditorPrivilege.whetherEditor?itemEditStore.itemsEditorPrivilege.reason:"仓内同步货品不支持自定义规格类型"} >
      <Select
        value={value && value.id}
        placeholder={"请选择规格类型"}
        style={{width: '168px'}}
        optionLable={"name"}
        disabled
        onChange={innerOnChange}
        fieldNames={{label: "name", value: "id"}}
        dropdownMatchSelectWidth={256}
        dropdownRender={(menu) => (
          <Fragment>
            {menu}
            {footer}
          </Fragment>
        )}
        options={items}
      />
    </Tooltip>: <Select
    value={value && value.id}
    placeholder={"请选择规格类型"}
    style={{width: '168px'}}
    optionLable={"name"}
    onChange={innerOnChange}
    fieldNames={{label: "name", value: "id"}}
    dropdownMatchSelectWidth={256}
    dropdownRender={(menu) => (
      <Fragment>
        {menu}
        {footer}
      </Fragment>
    )}
    options={items}
  />
})

const SpecValuesCreateInput = observer((props) => {

  const {value,onChange,specDetail,onNewAddSpec,isLast,deleteSpecValue} = props
  let [inputValue, setInputValue] = useState(value)
  const [showDelete, setShowDelete] = useState(false)
  useEffect(() => {
    if (value && value.name) {
      setShowDelete(true)
    }
  }, [value])
  const debouncedValue = useDebounce(inputValue, 500);

  useEffect(() => {
    onChange && onChange(inputValue)
    checkToAdd(inputValue.name)
  }, [debouncedValue]);
  const innerOnChange =(e)=>{
    const input = e.target.value
    inputValue ={ ...inputValue, name: input }
    setInputValue(inputValue)

  }
  function checkToAdd(input) {
    const specs= itemEditStore.form.getFieldValue(specDetailFormName)
    const skuSalesPriceLegth=   generateCombinations(parseSpecData(specs))
    if(skuSalesPriceLegth.length>125){
      message.error("最多添加125个规格")
      inputValue ={ ...inputValue, name: "",state:"add" }
      onChange && onChange(inputValue)
      setShowDelete(false)
      setInputValue(inputValue)
    }else {
      isLast && Boolean(input) && onNewAddSpec && onNewAddSpec()
    }
  }
  const onBlur =useCallback((e)=>{
   const specs= itemEditStore.form.getFieldValue(specDetailFormName)
    if(inputValue.name){
     const skuSalesPriceLegth= generateCombinations(parseSpecData(specs))
      if(skuSalesPriceLegth.length>125){
        message.error("最多添加125个规格")
        inputValue ={ ...inputValue, name: "",state:"add" }
        onChange && onChange(inputValue)
        setInputValue(inputValue)
      }else{
        inputValue.state="edit"
        setShowDelete(true)
      }
    }
  },[inputValue])
  const onDeleteSpecValue =(e)=>{
    Modal.confirm({
      title: '确定删除该规格么？',
      onOk() {
        deleteSpecValue()
      },
    })


  }
  return  <Fragment>
    { (!showDelete && itemEditStore.itemsEditorPrivilege.whetherEditor||showDelete)?
      <div className={"style_helperWrapper"}>
          <div className="style_skuValueInput">
            {
              (itemEditStore.isThirdPartyItem===1||!itemEditStore.itemsEditorPrivilege.whetherEditor) ?
                <Tooltip title={!itemEditStore.itemsEditorPrivilege.whetherEditor?itemEditStore.itemsEditorPrivilege.reason:"仓内同步货品不支持自定义规格类型"} >
                  <DTInput trimMode={"all"}
                           value={inputValue.name}
                           disabled
                           placeholder={`请输入${specDetail.name}`}/>
                </Tooltip>:<DTInput trimMode={"all"}
                                    value={inputValue.name}
                                    placeholder={`请输入${specDetail.name}`}
                                    onChange={innerOnChange}
                                    onBlur={onBlur} />
            }

            {
              showDelete && itemEditStore.itemsEditorPrivilege.whetherEditor ?  <div className="style_specOperations">
                <div className="style_delete" onClick={onDeleteSpecValue}><DeleteOutlined /></div>
              </div> :null
            }
          </div>
        </div>:null
    }
  </Fragment>
})
const SpecValues = observer((props) => {

  const {sortIndex} = props
  const specDetail = Form.useWatch([specDetailFormName,sortIndex], itemEditStore.form);
  const specValueFormName  = [specDetailFormName, sortIndex,specDetailValuesFormName]
  useEffect(()=>{
    if(specDetail&&specDetail.id && !specDetail[`${specDetailValuesFormName}`]){
      itemEditStore.form.setFieldValue(specValueFormName,[{state:"add",frontId:lib.generateNumberString(true,18)}] );
    }
  },[specDetail])
  const onNewAddSpec =useCallback( ()=>{
     const specValue= itemEditStore.form.getFieldValue(specValueFormName)

      const newSpecDetail = [...specValue,{state:"add",frontId:lib.generateNumberString(true,18)}];

      itemEditStore.form.setFieldValue(specValueFormName,newSpecDetail );
  },[specDetail])
  const groupValue =  (specDetail&& specDetail.id && specDetail[`${specDetailValuesFormName}`]||[]).filter(item=>item)||[]
  const deleteSpecValue = useCallback((id)=>{
    const specValue=  itemEditStore.form.getFieldValue(specValueFormName)
    const newItemSpecificationDetailParams = specValue.filter((item)=>item.frontId!==id)
    itemEditStore.form.setFieldValue(specValueFormName,[...newItemSpecificationDetailParams]);
  },[specDetail])

  const onSorted =useCallback( (sortResult)=>{
    const specValue=  itemEditStore.form.getFieldValue(specValueFormName)
    const newSpecDetail = [...sortResult,specValue.length >0 ? specValue[specValue.length-1]:{state:"add",frontId:lib.generateNumberString(true,18)}];
    itemEditStore.form.setFieldValue(specValueFormName,newSpecDetail );
  },[specDetail])


  return <Fragment>
    {groupValue.length>0 ? <div className="style_specValueBox">
      <div className="style_skuValueBox">
        { groupValue.map((item,index)=>{
          const dependencies = []
          for (let i = 0; i < groupValue.length; i++) {
            if (i !== index) {
              dependencies.push([...specValueFormName, i])
            }
          }
            return  <div style={{display: "flex", flexDirection: "column", width: "33%"}} key={item.frontId}>
              <Form.Item name={[...specValueFormName,index]}
                         style={{marginBottom: 0}}
                         label={""}
                         // dependencies={dependencies}
                         rules={[
                           ({ getFieldValue}) => ({
                             validator(_, value) {
                               if (value && value.name) {
                                 if(!/^[\u4E00-\u9FFFa-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|\\;:'",.<>/?\s]*$/.test(value.name)){
                                   return Promise.reject(new Error('仅支持汉字数字英文大小写及符号'));
                                 }
                                 if(value.name.length>20){
                                   return Promise.reject(new Error('规格值不能超过20个字符'));
                                 }

                                 let specValueFormValue = getFieldValue(specValueFormName)
                                 let findCount = 0;
                                 specValueFormValue.forEach((item,index)=>{
                                   if(value && value.name === item.name){
                                     findCount++
                                   }
                                 })
                                 if(findCount>1){
                                   return Promise.reject(new Error('规格值不能重复'));
                                 }
                                 return Promise.resolve();
                               }else{
                                 if(value && value.state === "add"){
                                   return Promise.resolve();
                                 }
                                 return Promise.reject(new Error('请输入规格值'));
                               }

                             },
                           })
                         ]}>
                <SpecValuesCreateInput onNewAddSpec={onNewAddSpec}
                                       specDetail={specDetail}
                                       isLast={index === groupValue.length - 1}
                                       specValueFormName={specValueFormName}
                                       deleteSpecValue={()=>deleteSpecValue(item.frontId)}/>
              </Form.Item>
            </div>
          })
        }

      </div>
      {itemEditStore.itemsEditorPrivilege.whetherEditor ?
      <div style={{display: "flex", alignItems: "center", marginTop: "12px"}}>
        <SkuSortSpec specGroupValue={groupValue} onSorted={onSorted}/>
      </div>:null}
    </div>:null}

  </Fragment>

})
const SkuSelectGroup = observer((props) => {
  const specDetail = props.value;
  useEffect(() => {
    if(Array.isArray(specDetail)){
      const {parsedSpecs ,processedData}=  handleGenerateTable(specDetail)
      if(Array.isArray(parsedSpecs)&& parsedSpecs.length>0){
        itemEditStore.setParsedSpecs(parsedSpecs)
        itemEditStore.setProcessedTableData(processedData)
        itemEditStore.setShowSpecDetail((processedData || []).length > 0)
        const newSkus = reorderSkus(itemEditStore.form.getFieldValue(skuSalesPriceFormName),processedData)
        itemEditStore.form.setFieldValue(skuSalesPriceFormName,newSkus );
      }else{
        itemEditStore.setParsedSpecs([])
        itemEditStore.setProcessedTableData([])
        itemEditStore.setShowSpecDetail(false)
      }
    }
  }, [specDetail])
  const addSpecDetail=()=>{
    const newSpecDetail = [...(specDetail||[]),{}];
    itemEditStore.form.setFieldValue(specDetailFormName,newSpecDetail );
  }

  /**
   * 上移
   */
  const upSpecDetail=(index)=>{
    if(index===0){
      return
    }
    [specDetail[index - 1], specDetail[index]] = [specDetail[index], specDetail[index - 1]];
    itemEditStore.form.setFieldValue(specDetailFormName,[...specDetail] );

  }
  /**
   * 下移
   */
  const downSpecDetail=(index)=>{
    if(index===(specDetail||[]).length-1){
      return
    }
    [specDetail[index + 1], specDetail[index]] = [specDetail[index], specDetail[index + 1]];
    itemEditStore.form.setFieldValue(specDetailFormName,[...specDetail] );
  }
  /**
   * 删除
   */
  const deleteSpecDetail=(index)=>{
    Modal.confirm({
      title: '确定删除该规格类型么？',
      onOk() {
        specDetail.splice(index, 1);
        itemEditStore.form.setFieldValue(specDetailFormName,specDetail.length>0?[...specDetail]:[] );
        if(specDetail.length===0){
          itemEditStore.form.setFieldValue(skuSalesPriceFormName,[])
        }
      },
    })

  }
  const items = useMemo(() => {
    if(!Array.isArray(specDetail)){
      return []
    }
    return (specDetail||[]).filter(it => it)
  }, [specDetail])
  const specDetailLength = items.length
  return <div className="style_helperWrapper">
    <div style={{flex: 1}}>
      <Space direction={"vertical"} style={{width: '100%'}}>
        {
          (items||[]).map((item, index) => {
            return <div className="style_helperWrapper" key={item.frontId}>
              <div className="style_contentBox">
                <div className="style_skuNameBox">
                  <div>
                    <Form.Item name={[specDetailFormName, index]}
                               style={{marginBottom: 0}}
                               label={""}
                               rules={[
                                 {
                                   required: true,
                                   message: "请选择规格类型",
                                 },
                               ]}>
                      <SkuSelect/>
                    </Form.Item>
                  </div>
                  <div style={{display: "flex", alignItems: "center"}}>
                    <div className="style_specOperations">
                      {itemEditStore.itemsEditorPrivilege.whetherEditor ?
                        <div className={`style_item ${index === (specDetail || []).length - 1 ? "style_disabled" : ""}`}
                             onClick={() => downSpecDetail(index)}>下移</div>:
                        <Tooltip title={itemEditStore.itemsEditorPrivilege.reason}>
                          <div className={`style_item style_disabled`}>下移</div>
                        </Tooltip>
                      }
                      {itemEditStore.itemsEditorPrivilege.whetherEditor ?
                        <div className={`style_item ${index === 0 ? "style_disabled" : ""}`}
                             onClick={() => upSpecDetail(index)}>上移</div> :
                        <Tooltip title={itemEditStore.itemsEditorPrivilege.reason}>
                          <div className={`style_item style_disabled`}>上移</div>
                        </Tooltip>
                      }
                      {itemEditStore.itemsEditorPrivilege.whetherEditor ?
                        <div className="style_item" onClick={() => deleteSpecDetail(index)}><DeleteOutlined/></div>
                        :   <Tooltip title={itemEditStore.itemsEditorPrivilege.reason}>
                          <div className="style_item style_disabled"><DeleteOutlined/></div>
                        </Tooltip>
                      }
                    </div>
                  </div>
                </div>
                 <SpecValues sortIndex={index}/>
              </div>
            </div>
          })
        }
      </Space>
      {
        specDetailLength<3? <div style={specDetailLength > 0?{ marginTop: '12px'}:{}}>
          {
            (itemEditStore.isThirdPartyItem===1||!itemEditStore.itemsEditorPrivilege.whetherEditor) ?
              <Tooltip title={!itemEditStore.itemsEditorPrivilege.whetherEditor?itemEditStore.itemsEditorPrivilege.reason:"仓内同步货品不支持自定义规格类型"} >
                <Button type="primary"
                        icon={<PlusOutlined />}
                        ghost
                        disabled>
                  <span>添加规格类型（{specDetailLength}/3）</span>
                </Button>
              </Tooltip>:
              <Button type="primary"
                      icon={<PlusOutlined />}
                      ghost
                      onClick={addSpecDetail}>
              <span>添加规格类型（{specDetailLength}/3）</span>
            </Button>
          }

        </div> :null
      }
      {
        !itemEditStore.itemsEditorPrivilege.whetherEditor ? <div className={"help-container"}>
          <ExclamationCircleFilled style={{color: "#faad14",marginRight: "8px"}}/>{itemEditStore.itemsEditorPrivilege.reason}</div> : null
      }
    </div>
  </div>
})
const SkuSpec = observer((props) => {
  return <div className={"sku-spec"} >
    <Form.Item label="商品规格" wrapperCol={{span: 22}} name={specDetailFormName}>
        <SkuSelectGroup/>
    </Form.Item>
  </div>
})
export default SkuSpec

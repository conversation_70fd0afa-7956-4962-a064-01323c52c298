import itemEditStore from "../ItemEditStore";
import SpecCashGift from "./cash-gift-sepec";
const {Fragment} = React;
const { Form, Checkbox, Input, Select ,DatePicker,InputNumber} = antd;
const {RangePicker} = DatePicker;
const { observer } = mobxReactLite;
const CashGift = observer((props) => {
  const {cashGiftSwitchFormName,cashGiftUseTimeStartFormName,maxDeductionFormName,cashGiftUnitFormName} =props
	const isCashGift = Form.useWatch(cashGiftSwitchFormName, itemEditStore.form);
	const disabledDate = (current) => {
		return current && current < moment().startOf('day');
	};


	return <div>
		{/* 是否可用福豆 */}
		<Form.Item name={cashGiftSwitchFormName} valuePropName="checked"
               initialValue={0}
			getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}>
			<Checkbox>
				是否可用福豆
			</Checkbox>
		</Form.Item>
		{/* 百分比输入项 */}
		{isCashGift === 1 ? (
      <Fragment>
        {
          itemEditStore.showSpecDetail ? <SpecCashGift/>:<div className={"common-wrap"}>
            <Form.Item
              label="最高可抵扣金额"
              name={maxDeductionFormName}
              //	name="maxDeduction"
              rules={[
                { required: true, message: '请输入最高可抵扣金额' },
                { pattern: /^[1-9]\d*$/, message: "请输入正整数" },
              ]}
            >
              <InputNumber addonAfter={<Form.Item name={cashGiftUnitFormName}  noStyle initialValue={1}>
                <Select defaultValue={1}>
                  <Option value={1}>%</Option>
                  <Option value={2}>个</Option>
                </Select>
              </Form.Item>} />
            </Form.Item>
            <Form.Item
              label="使用时间"
              name={cashGiftUseTimeStartFormName}
              extra={"如不填，默认永久可用"}
            >
              <RangePicker disabledDate={disabledDate}/>
            </Form.Item>

          </div>
        }
      </Fragment>
			) : null
		}
	</div>
})
export default CashGift


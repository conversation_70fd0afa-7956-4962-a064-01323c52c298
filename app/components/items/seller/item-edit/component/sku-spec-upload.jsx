import ImagePreviewButton from "../../../../common/react/ImagePreviewButton";
import { customFrontRequest } from "../../../../common/react/upload/custom-front-request";
const {Popover,Upload,Modal} = antd
const {PictureFilled,EyeOutlined,DeleteOutlined,FormOutlined} = icons
const {observer} = mobxReactLite;
const {useState,useEffect,useRef} = React;
const {DTUploaderFile} = dtComponents;

const SkuSpecUpload = observer((props) => {
  /**
   * 规格图
   */
  const [mainImages, setImages] = useState(Array(1).fill(null))
  const [replaceImages, setReplaceImages] = useState(Array(1).fill(null))
  const {onChange} = props
  useEffect(() => {
    const value = props.value;
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        value[i].status="done"
        mainImages[i] = value[i]
      }
      setImages([...mainImages])
    } else if (value === null) {
      setImages(Array(1).fill(null))
    }
  }, [props.value])

  const innerOnChange = (index, {file, fileList}) => {
    if (file.status === "uploading") {
      mainImages[index] = file
      setImages([...mainImages])
    } else if (file.status === "done") {
      mainImages[index] = file
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "removed") {
      mainImages[index] = null
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "error") {
      message.error("上传失败")
    }
  }
  const innerReplaceImagesOnChange = (index, {file, fileList}) => {

    if (file.status === "uploading") {
      replaceImages[index] = file
      setReplaceImages([...replaceImages])
    } else if (file.status === "done") {
      replaceImages[index] = file
      setReplaceImages([...replaceImages])
      mainImages[index] = file
      setImages([...mainImages])
      onChange(mainImages)
    } else if (file.status === "removed") {
      replaceImages[index] = null
      setReplaceImages([...replaceImages])
    } else if (file.status === "error") {
      message.error("上传失败")
    }
  }
  const picUploadButton = () => (
    <div className={"image-sku-pic"}>
        <PictureFilled/>
    </div>
  )
  const actionLayout =( )=> {
    return <div className={"upload-popover-inner-content"}>
      <ImagePreviewButton src={mainImages[0].url}
                          customButton={true}
                          buttonText={ <div className="upload-action" onClick={() => {}}>
        <EyeOutlined className="upload-action-icon"/>
        <div className="upload-action-text">预览</div>
      </div>}/>
      <div className="upload-action">
        <Upload
          style={{ margin: "0 10px"}}
          maxCount={1}
          size={5}
          accept={[".jpg", ".jpeg", ".png"]}
          listType={"text"}
          showUploadList={false}
          fileList={replaceImages[0] ? [replaceImages[0]] : []}
          customRequest={customFrontRequest}
          onChange={(file) => {
            innerReplaceImagesOnChange(0, file)
          }}
        >
          <div className="upload-action" style={{ margin: 0}}>
            <FormOutlined className="upload-action-icon"/>
            <div className="upload-action-text">替换</div>
          </div>
        </Upload>
      </div>
      <div className="upload-action"  onClick={() => {
        Modal.confirm({
          title: '确定删除该规格图么？',
          onOk() {
            setImages(Array(1).fill(null))
            onChange([])
          },
        })

      }}>
        <DeleteOutlined className="upload-action-icon"/>
        <div className="upload-action-text">删除</div>
      </div>
    </div>
  }
  return (
    <div className={"table-upload-pic"}>
      {
        mainImages[0] && mainImages[0].status === "done"? <Popover placement="top"  content={actionLayout()}>
            <img src={mainImages[0].url} style={{width: "32px", height: "32px"}}/>
          </Popover>:<DTUploaderFile
          className={"table-upload-pic-select-picture-card"}
          maxCount={1}
          size={5}
          accept={[".jpg", ".jpeg", ".png"]}
          listType={"picture-card"}
          fileList={mainImages[0] ? [mainImages[0]] : []}
          uploadButton={picUploadButton()}
          customRequest={customFrontRequest}
          onChange={(file) => {
            innerOnChange(0, file)
          }}
        />
      }
    </div>)
})

export default SkuSpecUpload;

// eslint-disable-next-line no-undef
import request from "../../../utils/plugins/axios/request"

const { observable, action } = mobx
const itemEditStore = observable({
  isCreate: true,
  isEdit: false,
  isThirdPartyItem: undefined,
  itemId: -1,
  thirdPartyId: 3, // 目前只有apiv3
  sourceType: 1, // 商品来源 1 代塔 2京东云交易
  outerSkuName: "",
  goodsType: -1,
  form: null,
  /**
   * 可售模版
   */
  salesAreaTemplateOption: [],
  listCountryOption: [],
  customsTownOption: [],
  deliveryFeeTemplateOption: [],
  otherAttrs: [], // 编辑时候才有
  categoryAttrs: [], // 创建时候才有
  skuSelectList: [],
  isPackage: false,
  showSpecDetail: false,
  specs: [],
  processedTableData: [],
  skuComboPrice: {},
  refreshSkuComboPrice:0,
  itemsEditorPrivilege: {
    whetherEditor: true,
    reason: ""
  },
  setIsEdit(isEdit) {
    this.isEdit = isEdit
    this.isCreate = !isEdit
  },
  setIsPackage(isPackage) {
    this.isPackage = isPackage
  },
  setRefreshSkuComboPrice() {
    this.refreshSkuComboPrice++
  },
  setItemId(itemId) {
    this.itemId = itemId
  },
  setIsThirdPartyItem(itemId) {
    this.isThirdPartyItem = itemId
  },
  setSourceType(sourceType) {
    this.sourceType = sourceType
  },
  setCategoryAttrs(attrs) {
    this.categoryAttrs = attrs
  },

  setOtherAttrs(attrs) {
    this.otherAttrs = attrs
  },
  setForm(form) {
    this.form = form
  },
  setItemData(data) {
  },
  setParsedSpecs(data) {
    this.specs = data
  },
  setProcessedTableData(data) {
    this.processedTableData = data
  },
  setSalesAreaTemplateOption(data) {
    this.salesAreaTemplateOption = data
  },
  setListCountryOption(data) {
    this.listCountryOption = data
  },
  setCustomsTownOption(data) {
    this.customsTownOption = data
  },
  setDeliveryFeeTemplateOption(data) {
    this.deliveryFeeTemplateOption = data
  },
  setShowSpecDetail(showSpecDetail) {
    this.showSpecDetail = showSpecDetail
  },
  setItemsEditorPrivilege(itemsEditorPrivilege) {
    this.itemsEditorPrivilege = itemsEditorPrivilege
  },
  requestSkuSelectList() {
    request({
      url: "/mall-admin/api/item/specification/list",
      method: "Post",
      success: (res) => {
        this.skuSelectList = res
      }
    })
  },
})
export default itemEditStore

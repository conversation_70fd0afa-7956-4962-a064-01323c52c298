import { DEFAULT_UNIT } from "./form-name-const"
import { SourceType } from "./enum"
import itemEditStore from "../ItemEditStore"
import { lib } from "../../../../common/react/utils/lib"
const transformBackCategorieId = (backCategories = []) => {
  let first = null
  let second = null
  let third = null
  if (backCategories.length > 0) {
    first = backCategories[0]
  }
  if (backCategories.length > 1) {
    second = backCategories[1]
  }
  if (backCategories.length > 2) {
    third = backCategories[2]
  }
  let categoryId
  if (third != null) {
    categoryId = third.id
  } else if (second != null) {
    categoryId = second.id
  } else if (first != null) {
    categoryId = first.id
  }
  return categoryId
}

/**
 * 转换编辑值
 * @param {Object} detail - 包含详细信息的对象
 * @returns {Object} 返回一个包含转换后的编辑值的对象
 */
export const transformEditValues = (detail) => {
  if (detail.item.mainImage) {
    detail.item.mainImageFileList = [{ url: detail.item.mainImage }]
  }
  const itemExtra = detail.item.extra
  // 活动主图处理
  if (itemExtra && itemExtra.activityStartTime && itemExtra.activityEndTime) {
    // eslint-disable-next-line no-undef,max-len
    itemExtra.activityImageTime = [moment(itemExtra.activityStartTime), moment(itemExtra.activityEndTime)]
  }
  if (itemExtra && itemExtra.activityMainImage) {
    itemExtra.activityMainImageFileList = [{ url: itemExtra.activityMainImage }]
  }
  if (itemExtra && itemExtra.activityDetailImages) {
    itemExtra.activitySubImageFileList =
      itemExtra.activityDetailImages.split(",").map(item => ({ url: item }))
  }
  // 活动价格 ,目前没有多规格这个均为1个
  detail.skuWithCustoms.forEach((skuWithCustomsItem, index) => {
    if (index === 0) {
      detail.skuCustom = skuWithCustomsItem.skuCustom
      detail.activitySalesPriceSwitch = skuWithCustomsItem.sku.activitySalesPriceSwitch
    }
    if (skuWithCustomsItem.sku.extraMap && skuWithCustomsItem.sku.extraMap.activityStartTime
      && skuWithCustomsItem.sku.extraMap.activityEndTime) {
      // eslint-disable-next-line no-undef,max-len
      detail.skuWithCustoms[index].sku.extraMap.activityPriceTime = [moment(skuWithCustomsItem.sku.extraMap.activityStartTime), moment(skuWithCustomsItem.sku.extraMap.activityEndTime)]
    }
    if (skuWithCustomsItem.sku.extraMap && skuWithCustomsItem.sku.extraMap.activitySalesPrice) {
      detail.skuWithCustoms[index].sku.extraMap.activitySalesPrice = Number(skuWithCustomsItem.sku.extraMap.activitySalesPrice)
    }
    if (skuWithCustomsItem.sku.image) {
      skuWithCustomsItem.sku.image = [{ url: skuWithCustomsItem.sku.image }]
    }
    if (skuWithCustomsItem.sku.comboCashGiftUseTimeStart) {
      skuWithCustomsItem.sku.comboCashGiftUseTime = [moment(skuWithCustomsItem.sku.comboCashGiftUseTimeStart), moment(skuWithCustomsItem.sku.comboCashGiftUseTimeEnd)]
    }
  })

  if (detail.activityCommissionConfig &&
    detail.activityCommissionConfig.matchingStartTimeString
    && detail.activityCommissionConfig.matchingEndTimeString) {
    detail.activityCommissionConfig.activityCommissionTime = [
      // eslint-disable-next-line no-undef
      moment(detail.activityCommissionConfig.matchingStartTimeString),
      // eslint-disable-next-line no-undef
      moment(detail.activityCommissionConfig.matchingEndTimeString)]
  }
  // 属性
  if (detail.otherAttrs && detail.otherAttrs.length) {
    for (const attrsElement of detail.otherAttrs) {
      for (const attrsRule of attrsElement.otherAttributeWithRules) {
        if (!detail[`otherAttributeWithRules`]) {
          detail[`otherAttributeWithRules`] = {}
        }
        detail[`otherAttributeWithRules`][`${attrsRule.attributeRule.attrKey}`] = {
          attrKey: attrsRule.attributeRule.attrKey,
          attrVal: attrsRule.attrVal || null,
          group: attrsElement.group
        }
      }
    }
  }
  if (detail.itemDetail.videoUrl) {
    detail.itemDetail.mainVideo = [{ url: detail.itemDetail.videoUrl, thumbUrl: detail.itemDetail.videoThumbnailUrl }]
  }
  if (detail.itemDetail.imageList && detail.itemDetail.imageList.length > 0) {
    detail.itemDetail.imageList = detail.itemDetail.imageList.map(item => ({ url: item, status: "done" }))
  }
  if (Array.isArray(detail.itemSpecificationParams)) {
    detail.itemSpecificationParams.map(item => {
      if (Array.isArray(item.itemSpecificationDetailParams)) {
        item.itemSpecificationDetailParams.push({
          state: "add", frontId: lib.generateNumberString(true, 18)
        })
      }
    })
  }
  return detail
}

export const transformSubmitValues = ({
                                        values,
                                        isEdit,
                                        isThirdPartyItem,
                                        pushSystem,
                                        isPackage
                                      }) => {
  values.item.type = isPackage ? 2 : 1
  if (values.item.brandId === undefined) {
    values.item.brandId = null
  }
  if (values.backCategoryList && values.backCategoryList.length) {
    values.item.categoryId = transformBackCategorieId(values.backCategoryList)
    delete values.backCategoryList
  }
  if (values.shopCategoryList && values.shopCategoryList.length) {
    values.item.shopCategoryIds = values.shopCategoryList.filter(item => item).map(item => item.id)
    delete values.shopCategoryList
  }
  if (Array.isArray(values.itemSpecificationParams)) {
    values.itemSpecificationParams = values.itemSpecificationParams.filter(item => item.itemSpecificationDetailParams)
  }
  if (isThirdPartyItem === "1") {
    values.item.isThirdPartyItem = 1 // 1表示是第三方平台商品
    values.item.pushSystem = pushSystem // pushSystem表示第三方平台id
  } else if (!isEdit) {
    // 修改时不修改是否第三方平台，会出bug跨境保税保存后变成一般贸易
    values.item.isThirdPartyItem = 0 // 1表示是第三方平台商品
  }
  // 主图，附图
  if (values.item.mainImageFileList
    && values.item.mainImageFileList.length
    && values.item.mainImageFileList[0]
  ) {
    values.item.mainImage = values.item.mainImageFileList[0].url
    delete values.item.mainImageFileList
  }
  if (values.itemDetail.images && values.itemDetail.images.length) {
    values.itemDetail.images = values.itemDetail.images.filter(item => item).map(item => ({
      name: item.name,
      url: item.url
    }))
  }
  if (values.itemDetail.mainVideo) {
    const mainVideo = values.itemDetail.mainVideo.filter(item => item)
    if (mainVideo.length > 0) {
      values.itemDetail.videoUrl = values.itemDetail.mainVideo[0].url
      values.itemDetail.videoThumbnailUrl = values.itemDetail.mainVideo[0].thumbUrl
    } else {
      values.itemDetail.videoUrl = null
      values.itemDetail.videoThumbnailUrl = null
    }
    delete values.itemDetail.mainVideo
  }
  if (values.itemDetail.imageList && values.itemDetail.imageList.length > 0) {
    values.itemDetail.imageList = values.itemDetail.imageList.filter(item => item && item.url).map(item => item.url) || []
  } else {
    values.itemDetail.imageList = []
  }
  // 活动图文
  const itemExtra = values.item.extra
  if (!itemExtra.unit) {
    itemExtra.unit = DEFAULT_UNIT
  }
  if (itemExtra.activityMainImageFileList && itemExtra.activityMainImageFileList.length) {
    const activityMainImageFileList = itemExtra.activityMainImageFileList.filter(item => item)
    itemExtra.activityMainImage = activityMainImageFileList.length ? activityMainImageFileList[0].url : ""
    delete values.item.extra.activityMainImageFileList
  }
  if (itemExtra.activitySubImageFileList && itemExtra.activitySubImageFileList.length) {
    itemExtra.activityDetailImages = itemExtra.activitySubImageFileList.filter(item => item).map(item => item.url).join(",")
    delete values.item.extra.activitySubImageFileList
  }
  if (itemExtra.activityImageTime && itemExtra.activityImageTime.length) {
    itemExtra.activityStartTime = itemExtra.activityImageTime[0].format("YYYY-MM-DD HH:mm:ss")
    itemExtra.activityEndTime = itemExtra.activityImageTime[1].format("YYYY-MM-DD HH:mm:ss")
    delete itemExtra.activityImageTime
  }
  // 活动价格
  if (values.skuWithCustoms) {
    const skuCustom = values.skuCustom
    values.skuWithCustoms.forEach((skuWithCustomsItem, index) => {
      const sku = skuWithCustomsItem.sku
      if (index === 0) {
        // 放入拆单和限制购数量 ,重构前是这样放的
        itemExtra.skuOrderSplitLine = sku.extraMap.skuOrderSplitLine
        itemExtra.unitQuantity = sku.extraMap.unitQuantity
        itemExtra.orderQuantityLimit = sku.extraMap.orderQuantityLimit
      }
      skuWithCustomsItem.skuCustom = skuCustom
      sku.activitySalesPriceSwitch = values.activitySalesPriceSwitch
      if (!values.activitySalesPriceSwitch) {
        sku.extraMap.activitySalesPrice = null
        sku.extraMap.activityStartTime = null
        sku.extraMap.activityEndTime = null
      } else {
        sku.extraMap.activitySalesPrice = Number(sku.extraMap.activitySalesPrice)
      }
      if (sku.extraMap.activityPriceTime && sku.extraMap.activityPriceTime.length) {
        sku.extraMap.activityStartTime = sku.extraMap.activityPriceTime[0].format("YYYY-MM-DD HH:mm:ss")
        sku.extraMap.activityEndTime = sku.extraMap.activityPriceTime[1].format("YYYY-MM-DD HH:mm:ss")
        //delete sku.extraMap.activityPriceTime
      }
      if (sku.image && Array.isArray(sku.image) && sku.image.length > 0) {
        const image = sku.image.filter(item => item && item.url)
        if (Array.isArray(image) && image.length > 0) {
          sku.image = image[0].url
        } else {
          sku.image = null
        }
      } else {
        sku.image = null
      }
      if (sku.comboCashGiftUseTime) {
        const [comboCashGiftUseTimeStart, comboCashGiftUseTimeEnd] = sku.comboCashGiftUseTime
        sku.comboCashGiftUseTimeStart	= comboCashGiftUseTimeStart ? moment(comboCashGiftUseTimeStart).format("x") : null
        sku.comboCashGiftUseTimeEnd = comboCashGiftUseTimeEnd ? moment(comboCashGiftUseTimeEnd).format("x") : null
        delete sku.comboCashGiftUseTime
      }
    })
  }
  // 活动佣金
  if (values.activityCommissionConfig && values.activityCommissionConfig.isCommission === 0) {
    delete values.activityCommissionConfig
    values.deleteActivityCommissionConfig = true
  }
  // flag 1 按比例 0 固定
  if (values.activityCommissionConfig) {
    if (values.activityCommissionConfig.activityCommissionTime && values.activityCommissionConfig.activityCommissionTime.length) {
      values.activityCommissionConfig.matchingStartTimeString = values.activityCommissionConfig.activityCommissionTime[0].format("YYYY-MM-DD HH:mm:ss")
      values.activityCommissionConfig.matchingEndTimeString = values.activityCommissionConfig.activityCommissionTime[1].format("YYYY-MM-DD HH:mm:ss")
      delete values.activityCommissionConfig.activityCommissionTime
    }
    values.activityCommissionConfig.serviceProviderFee = 0
    values.activityCommissionConfig.firstFee = 0
    values.activityCommissionConfig.secondFee = 0
  }

  const otherAttributeArr = Object.values(values.otherAttributeWithRules).filter(item => item)
  if (otherAttributeArr.length) {
    values.groupedOtherAttributes = Object.values(
      otherAttributeArr.reduce((acc, item) => {
        const group = item.group // 获取 group 名称
        // 如果分组不存在，则初始化
        if (!acc[group]) {
          acc[group] = {
            group,
            otherAttributes: []
          }
        }
        // 将当前对象加入对应分组的 otherAttributes 中
        if (item.attrVal !== null) {
          acc[group].otherAttributes.push(item)
        }
        return acc
      }, {})
    )
  }

  return values
}
/**
 *  是否时京东云交易商品
 * @param sourceType
 * @return {boolean}
 */
// eslint-disable-next-line eqeqeq
export const isJDItem = (sourceType) => sourceType == SourceType.GOODS_SOURCE_TYPE_JD
/**
 * 是否自建商品
 * @param sourceType
 * @return {boolean}
 */
export const isSelfItem = (sourceType) => sourceType == SourceType.GOODS_SOURCE_TYPE_SELF
export const handleDTApiV3Goods = (data) => ({
  item: {
    name: data.outerSkuName,
    isBonded: data.type === 1 ? 1 : 0,
    extra: {
      unit: data.unit,
    }
  },
  skuWithCustoms: [{
    sku: {
      outerSkuId: data.outerSkuId,
      extraMap: {
        unitQuantity: 1,
      },
      tags: {
        pushSystem: itemEditStore.thirdPartyId
      }
    }
  }],
})
export const handleJdApiV3Goods = (data) => {
  const skuDetails = data.skuDetails
  const skuDetailImages = data.skuDetailImages
  const mainImageList = []
  const subImageList = []
  if (Array.isArray(skuDetails)) {
    skuDetails.forEach(item => {
      if (item.isPrimary) { // 只
        mainImageList.push(item.path)
      } else {
        subImageList.push(item)
      }
    })
  }
  // ["item", "mainImageFileList"]
  // ["itemDetail", "images"]

  const item = {
    name: data.outerSkuName,
    sourceType: data.sourceType,
    isBonded: data.type === 1 ? 1 : 0,
    extra: {
      unit: data.unit,
    }
  }
  const detailData = {
    item,
    skuCustom: {
      customTaxHolder: 2
    },
    skuWithCustoms: [{
      sku: {
        outerSkuId: data.outerSkuId,
        extraMap: {
          unitQuantity: 1,
        },
        tags: {
          pushSystem: itemEditStore.thirdPartyId
        }
      }
    }],
  }
  if (mainImageList.length > 0) {
    item.mainImageFileList = [{ url: mainImageList[0] }]
  }
  if (subImageList.length > 0) {
    const itemDetail = {}
    itemDetail.images = subImageList.map(img => ({ url: img.path }))
    detailData.itemDetail = itemDetail
  }
  if (Array.isArray(skuDetailImages) && skuDetailImages.length > 0) {
    detailData.richDetail = skuDetailImages.map(detailImage => `<img src="${detailImage.path}">`).join("")
  }
  return detailData
}


export const handleApiV3Goods = (data) => {
  itemEditStore.setSourceType(data.sourceType)
  if (isJDItem(data.sourceType)) {
    return handleJdApiV3Goods(data)
  }
  return handleDTApiV3Goods(data)
}


 /**
 * 检查组合列表是否包含重复的子商品组合
 * @param {Array} allCombos 所有待提交的组合列表（包含新增和已有组合）
 * @returns {boolean} 是否通过校验（true=无重复）
 */
export function validateCombosOnSubmit(allCombos) {
  const seenCombos = new Set()

  for (const combo of allCombos) {
    if (combo.sku.skuComboList) {
      // 生成唯一组合特征字符串（排序后保证顺序无关）
      const comboSignature = combo.sku.skuComboList
        .map(item => `${item.comboSkuId}:${item.comboSkuQuantity}`)
        .sort() // 排序确保不同顺序的组合被视为相同
        .join("|")

      if (seenCombos.has(comboSignature)) {
        return false
      }
      seenCombos.add(comboSignature)
    }
  }
  return true
}
export const validatePriceNumber = (_, value) => {
  if (!value && value != 0) {
    return Promise.resolve()
  }
  if (value === 0 || value < 0) {
    return Promise.reject(new Error("请输入大于0的数字"))
  }
  const regex = /^(?:0|[1-9]\d{0,4})(?:\.\d{1,2})?$/ // 整数5 位 小数 2
  if (regex.test(`${value/100}`)) {
    return Promise.resolve()
  }
  return Promise.reject(new Error("请输入整数5位，小数2位的数字"))
}


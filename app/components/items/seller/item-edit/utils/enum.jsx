const SourceType = {
  /**
   * 代塔仓自有
   */
  GOODS_SOURCE_TYPE_DT: 1,
  /**
   * 京东云交易
   */
  GOODS_SOURCE_TYPE_JD: 2,
  /**
   * 自建商品
   */
  GOODS_SOURCE_TYPE_SELF: 3,
}
const JdCloudTransactionReceiptStatus = {
  PENDING: 1,            // 未回执
  RECEIVED_SUCCESS: 2,   // 已回执，回执成功
  RECEIVED_FAILED: 3     // 已回执，回执失败
}
const RefundType = {
  /**
   * 售中仅退款
   */
  ON_SALE_REFUND: 1,
  /**
   * 售后仅退款
   */
  AFTER_SALE_REFUND: 2,
  /**
   * 售后退货退款
   */
  AFTER_SALE_RETURN: 3
}
export { SourceType, JdCloudTransactionReceiptStatus, RefundType }

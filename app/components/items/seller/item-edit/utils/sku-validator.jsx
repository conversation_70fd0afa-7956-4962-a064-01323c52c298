import Schema from "./async-validator"
import { validatePriceNumber } from "./method"

export const validator = new Schema({
  sku: {
    type: "object",
    fields: {
      price: [{ required: true, type: "number", message: "请输入销售价" }, { validator: validatePriceNumber }],
      stockQuantity: [{ required: true, type: "number", message: "请输入可售库存" }, {
        pattern: /^[1-9]\d{0,6}$/,
        message: "请输入7位正整数",
      },],
      outerSkuId: [{
        type: "string",
        max: 60,
        message: "长度不能超过60个字符",
      }],
      extraPrice: {
        type: "object",
        fields: {
          crossedPrice: [
            { validator: validatePriceNumber },
            // {
            //   validator: function (_, value)  {
            //      const price =itemEditStore.form.getFieldValue(['skuWithCustoms', index, 'sku', 'price']);
            //      if (value && price && value < price) {
            //        return Promise.reject("划线价不能小于售价价格");
            //      }
            //     return Promise.resolve();
            //   }
            // }
          ],
        }
      },
      extraMap: {
        type: "object",
        fields: {
          orderQuantityLimit: [{
            pattern: RegExp("^[1-9]\\d*$"),
            message: "请输入大于0的整数",
          }],
          skuOrderSplitLine: [{
            pattern: RegExp("^[1-9]\\d*$"),
            message: "请输入大于0的整数",
          }],
        }
      }
    }
  }
})
export const validatorCash = new Schema({
  sku: {
    type: "object",
    fields: {
      maxDeduction: [{ type: "number", required: true, message: "请输入最高可抵扣金额" }, {
        pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
        message: "请输入正整数,支持到两位小数"
      }]
    }
  }
})


export const activityValidator = new Schema({
  sku: {
    type: "object",
    fields: {

      extraMap: {
        type: "object",
        fields: {

          activitySalesPrice: [{ type: "number", required: true, message: "请输入活动价格" },{ validator: validatePriceNumber }],
          activityPriceTime: [{ type: "array", required: true, message: "请选择活动时间" }]
        }
      }
    }
  }
})

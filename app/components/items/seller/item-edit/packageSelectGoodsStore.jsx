// eslint-disable-next-line no-undef

const { observable } = mobx
const packageSelectGoodsStore = observable({

  oldSkuSelectList: {},
  newSkuSelectList: {},
  setOldSkuSelectList(packAgeSkuId, data) {
    this.oldSkuSelectList = data
    this.newSkuSelectList = data
  },
  setNewSkuSelectListSelectList(packAgeSkuId, data) {
    this.skuSelectList = data
  },
})
export default packageSelectGoodsStore

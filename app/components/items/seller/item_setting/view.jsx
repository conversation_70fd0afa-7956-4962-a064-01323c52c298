import request from "../../../utils/plugins/axios/request";
import SaleBaseQuantitySetting from "./component/sale-base-quantity-setting";

const { useState } = React;
const {  Tabs } = antd;
const {  Spa, SpaConfigProvider } = dtComponents;
const ItemSetting = () => {
  const [activeKey, setActiveKey] = useState("1");
  

  const tabsItems = [
    {
      key: "1",
      label: "销售基数设置",
      children: <div style={{height: '100%'}}><SaleBaseQuantitySetting/> </div>
    }
  ]


  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      items={tabsItems}
    />
  )
}

ReactDOM.render(<SpaConfigProvider spa={new Spa({
  _openPage: () => {
  }, request: {
    request: (params) => {
      params.url = params.url + "?shopId=" + sessionStorage.shopId
      const obj = Object.assign(params, {})
      request(obj);
    }
  }
})}>
  <ItemSetting />
</SpaConfigProvider>, document.getElementById("item-setting")
);


import request from "../../../../utils/plugins/axios/request";
import AddGoodsModal from "./add-goods-modal";
import UpdateBaseQuantityModal from "./update-base-quantity-modal";

const { useRef, useState, Fragment, useEffect } = React;
const { Space, Form, message, Modal, Button, InputNumber } = antd;
const { SearchList } = dtComponents;

const SaleBaseQuantitySetting = () => {
  const searchListRef = useRef()
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState(null);

  const getConfig = async function () {
    let url = "https://maria.yang800.com/api/data/v2/1014/858";
    const data = await axios.get(url)
    return data.data.data;
  };

  const handleAdjustBase = (row) => {
    setCurrentRow(row);
    setUpdateModalVisible(true);
  };

  // 处理添加商品
  const handleAddGoods = (selectedGoods) => {
    if (!selectedGoods || selectedGoods.length === 0) {
      return;
    }

    request({
      url: `/mall-admin/api/sku/extension/property/allow/item`,
      method: "POST",
      data: {
        itemIds: selectedGoods.map(item => item.id)
      },
      success: () => {
        message.success("添加成功");
        setAddModalVisible(false);
        searchListRef.current.load();
      },
      error: (error) => {
        // message.error("添加失败：" + error);
      }
    });
  };

  // 删除活动
  const delActivity = (id) => {
    Modal.confirm({
      title: '删除',
      content: '删除后所有SKU设置都将清空，请确认是否删除？',
      onOk: () => {
        request({
          url: `/mall-admin/api/sku/extension/property/batch/delete/item`,
          method: "POST",
          data: {
            itemIds: [id]
          },
          success: () => {
            message.success('删除成功')
            searchListRef.current.load(); 
          }
        })
      }
    })
  }

  return (
    <div style={{height:"100%"}}>
      <SearchList
        ref={searchListRef}
        scrollMode={"tableScroll"}
        paginationConfig={{ size: "small", showPosition: 'bottom' }}
        searchConditionConfig={{
          size: "middle",
        }}
        getConfig={getConfig}
        renderLeftOperation={() => {
          return <Button type="primary" onClick={() => {
            setAddModalVisible(true)
          }}>添加</Button>
        }}
        onLoadBeforeHandleParams={(obj,type)=>{
            if(obj.shopCategoryId){
              let arr = obj.shopCategoryId.split(",");
              let last = arr[arr.length - 1];
              obj.shopCategoryId = last;
            }
            return obj;
          }
        }
        tableCustomFun={{
          operateFn: (row) => {
            return <Space wrap>
              <a onClick={() => handleAdjustBase(row)}>调整基数</a>
              <a onClick={() => delActivity(row.id)}>删除</a>
            </Space>
          },
          showQuanityFn: (row) => {
              return (row.saleQuantity || 0) + (row.saleBaseQuantity || 0)
            }
        }}
        
      />

      {/* 调整基数弹窗 */}
      <UpdateBaseQuantityModal
        open={updateModalVisible}
        onClose={(success) => {
          setUpdateModalVisible(false);
          if (success) {
            searchListRef.current.load();
          }
        }}
        rowItem={currentRow || {}}
      />

      {/* 添加商品弹窗 */}
      <AddGoodsModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onConfirm={handleAddGoods}
      />
    </div>
  );
};

export default SaleBaseQuantitySetting;


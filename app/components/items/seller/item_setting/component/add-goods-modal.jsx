import request from "../../../../utils/plugins/axios/request";

const { useRef, useState, useEffect, Fragment } = React;
const { Modal, Button, Form, Input, Select, Table, Space, Cascader, message, Row, Col } = antd;
const { Option } = Select;

const AddGoodsModal = ({ visible, onCancel, onConfirm }) => {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  // 品牌列表
  const [brandList, setBrandList] = useState([])
  // 类目列表
  const [categoryList, setCategoryList] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
  });

  // 商品状态选项
  const statusOptions = [
    { label: '上架', value: 1 },
    { label: '下架', value: -1 },
  ];

  // 表格列配置
  const columns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '类目',
      dataIndex: 'shopCategoryName',
      key: 'shopCategoryName',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '实际销量',
      dataIndex: 'saleQuantity',
      key: 'saleQuantity',
      width: 100,
      render: (text) => text || 0,
    },
  ];

  // 请求品牌列表
  const queryBrandList = () => {
    request({
      url: `/mall-admin/api/item/brand/list/dropdown`,
      method: 'POST',
      success(res) {
        if (!res) return
        setBrandList(res.map(item => {
          return {
            value: item.id,
            label: item.name
          }
        }))
      }
    })
  }

  // 请求类目列表
  const queryCategoryList = () => {
    request({
      url: `/api/shopCategory/default/tree/v2`,
      method: 'POST',
      data: null,
      success(res) {
        setCategoryList(res);
      },
      error(err) {
        console.error('获取类目列表失败:', err);
      }
    });
  };

  // 获取商品列表数据
  const fetchGoodsList = async (params = {}) => {
    setLoading(true);
    try {
      const formValues = form.getFieldsValue();
      let shopCategoryId = null;
      if(formValues.shopCategoryId && formValues.shopCategoryId.length){
        shopCategoryId = formValues.shopCategoryId[formValues.shopCategoryId.length - 1];
      }
      const requestData = {
        currentPage: params.currentPage || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        shopId: sessionStorage.shopId,
        ...formValues,
        ...params,
        shopCategoryId,
        name: (formValues.name || '').trim(),
      };

      const response = await request({
        url: '/mall-admin/api/sku/extension/property/page',
        method: 'POST',
        data: requestData,
      });

      if (response) {
        console.log(response)
        const { dataList, page } = response;
        setDataSource(dataList);
        setPagination(prev => ({
          ...prev,
          current: page.currentPage || 1,
          total: page.totalCount || 0,
          pageSize: response.pageSize || 10,
        }));
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
    } finally {
      setLoading(false);
    }
  };


  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchGoodsList({ currentPage: 1 });
  };

  // 重置
  const handleReset = () => {
    form.resetFields();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchGoodsList({ currentPage: 1 });
  };

  // 表格分页变化
  const handleTableChange = (paginationConfig) => {
    const newPagination = {
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    };
    setPagination(newPagination);
    fetchGoodsList({
      currentPage: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    });
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.extensionPropertySwitch,
    }),
  };

  // 确认选择
  const handleConfirm = () => {
    if (selectedRows.length === 0) {
      message.warning('请选择商品');
      return;
    }
    onConfirm && onConfirm(selectedRows);
    handleCancel();
  };

  // 取消
  const handleCancel = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    form.resetFields();
    onCancel && onCancel();
  };

  // 弹窗打开时获取数据
  useEffect(() => {
    if (visible) {
      fetchGoodsList();
      // 查询品牌列表
      queryBrandList();
      // 查询类目列表
      queryCategoryList();
    }
  }, [visible]);

  return (
    <Modal
      title="选择商品"
      open={visible}
      onCancel={handleCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={handleConfirm}>
          确定
        </Button>,
      ]}
    >
      {/* 搜索表单 */}
      <Form
        form={form}
        style={{ marginBottom: 16 }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item name="name" label="商品名称">
              <Input placeholder="请输入商品名称" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="shopCategoryId" label="类目">
              <Cascader
                changeOnSelect
                showSearch={{
                  filter: (inputValue, path) => {
                    return path.some((option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
                  }
                }}
                options={categoryList}
                placeholder="请选择类目"
                fieldNames={{ label: 'name', value: 'id', }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="status" label="商品状态">
              <Select placeholder="请选择状态" allowClear>
                {statusOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="品牌"
              name='brandId'
             
            >
              <Select
                placeholder="请选择品牌"
                allowClear
                options={brandList}
              ></Select>
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      {/* 商品表格 */}
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowKey="id"
        scroll={{ y: 400 }}
        size="small"
      />
    </Modal>
  );
};

export default AddGoodsModal;






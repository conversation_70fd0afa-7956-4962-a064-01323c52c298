import request from "../../../../utils/plugins/axios/request";
import { useSpecColumn } from "../../item-edit/hooks/useSpecColumn";

const { useState, useEffect } = React;
const { Modal, Form, InputNumber, message, Table } = antd;

function UpdateBaseQuantityModal({ open, onClose, rowItem = {} }) {

  const [isRequest, setIsRequest] = useState(false);
  const [form] = Form.useForm();
  const [itemDetail, setItemDetail] = useState({ specDetail: [], skuDetail: [] });

  useEffect(() => {
    if (open && rowItem) {
      // 获取商品的规格信息
      request({
        url: `/mall-admin/api/sku/extension/property/get/by/item/id?itemId=${rowItem.id}`,
        method: 'GET',
        needMask: true,
        data: {},
        success: (res) => {
          setIsRequest(true)
          const specDetail = res.itemSpecificationParams || [];
          const skuDetail = (res.skuExtensionPropertyDetailList || []).map(item => ({
            sku: {
              ...item,
              saleQuantity: item.saleQuantity || 0,
              saleBaseNumber: item.saleBaseNumber || 0
            }
          }));

          setItemDetail({ specDetail, skuDetail });

          // 如果有规格，设置表单初始值
          if (specDetail.length > 0) {
            // 有规格商品，设置SKU数据
            form.setFieldsValue({
              skuWithCustoms: skuDetail
            });
          } else {
            // 无规格商品，直接设置单个销量基数
            form.setFieldsValue({
              skuId: res.skuExtensionPropertyDetailList[0].skuId || '',
              saleBaseNumber: res.skuExtensionPropertyDetailList[0].saleBaseNumber || 0
            });
          }
        }
      });
    }
    return () => {
      if (!open) {
        setItemDetail({ specDetail: [], skuDetail: [] });
        form.resetFields();
        setIsRequest(false)
      }
    }
  }, [open, rowItem]);

  const onFinish = (values) => {
    let skuData = [];
    console.log('values', values)
    if (itemDetail.specDetail.length > 0) {
      // 多规格商品，批量更新SKU的销量基数
      skuData = values.skuWithCustoms.map(item => ({
        skuId: item.sku.skuId,
        saleBaseNumber: item.sku.saleBaseNumber
      }));


    } else {
      // 无规格商品，更新单个商品的销量基数
      skuData = [{
        skuId: values.skuId,
        saleBaseNumber: values.saleBaseNumber
      }];
    }
    request({
      url: `/mall-admin/api/sku/extension/property/batch/create/or/update`,
      method: 'POST',
      contentType: "application/json",
      data: {
        skuExtensionProperties: skuData
      },
      success: () => {
        message.success('修改成功');
        onClose(true);
      },
      error: (error) => {
        // message.error("修改失败：" + error);
      }
    });
  };

  // 批量表格组件
  const BatchTable = React.memo(({ form, specDetail, skuDetail, rowItem }) => {
    const [dataSource, setDataSource] = useState([]);
    const hasSpecs = specDetail && specDetail.length > 0;

    const { specs } = useSpecColumn({
      specDetail: hasSpecs ? specDetail : [],
      skuDetail: hasSpecs ? skuDetail : [],
      afterReorderSkus: (newSkuDetail, newDataSource) => {
        if (hasSpecs) {
          form.setFieldValue('skuWithCustoms', newSkuDetail);
          setDataSource([...newDataSource]);
        } else {
          setDataSource([{
            id: 'no-spec-item',
            actualSales: (rowItem && rowItem.actualSales) || (rowItem && rowItem.totalSales) || 0,
            saleBaseQuantity: form.getFieldValue('saleBaseNumber') || 0
          }]);
        }
      }
    });

    // 为无规格商品创建数据源
    React.useEffect(() => {
      if (!hasSpecs) {
        setDataSource([{
          id: 'no-spec-item',
          actualSales: (rowItem && rowItem.saleQuantity) || 0,
          saleBaseNumber: form.getFieldValue('saleBaseNumber') || 0
        }]);
      }
    }, [hasSpecs, rowItem, form]);

    const actualSalesColumn = {
      title: "实际销量",
      width: 100,
      render: (_, record, index) => {
        if (hasSpecs) {
          const skuData = form.getFieldValue(['skuWithCustoms', index, 'sku']) || {};
          return skuData.saleQuantity || 0;
        } else {
          return (rowItem && rowItem.saleQuantity) || 0;
        }
      },
    };

    const inputColumn = {
      title: "销量基数",
      editable: true,
      width: 150,
      render: (_, record, index) => (
        <Form.Item
          name={hasSpecs ? ['skuWithCustoms', index, 'sku', 'saleBaseNumber'] : 'saleBaseNumber'}
          rules={[
            { required: true, message: '请输入销量基数' },
          ]}
          style={{marginTop: '24px'}}
          wrapperCol={{ span: 24 }}
        >
          <InputNumber
            min={0}
            precision={0}
            style={{ width: '100%' }}
            placeholder="请输入销量基数"
          />
        </Form.Item>
      ),
    };

    const columns = [
      // 只有在有规格时才显示规格列
      ...(hasSpecs ? specs.map(spec => ({
        title: spec.name,
        width: 120,
        fixed: "left",
        dataIndex: spec.name,
        render: (value, _, index) => {
          const span = dataSource[index] ? (dataSource[index][`${spec.name}Span`] || 0) : 0;
          return { children: value, props: { rowSpan: span } };
        }
      })) : []),
      actualSalesColumn,
      {
        title: '',
        width: 0,
        render: (_, record, index) => (
          <Form.Item
            name={hasSpecs ? ['skuWithCustoms', index, 'sku', 'skuId'] : 'skuId'}
            hidden
            wrapperCol={{ span: 24 }}
          >
            <InputNumber />
          </Form.Item>
        ),
      },
      inputColumn
    ];

    // 监听表单值变化以实时更新统计信息
    const skuWithCustoms = Form.useWatch('skuWithCustoms', form) || [];
    const saleBaseNumber = Form.useWatch('saleBaseNumber', form) || 0;

    // 计算统计信息
    const calculateSummary = () => {
      let totalBaseQuantity = 0;
      let totalDisplaySales = 0;
      if (hasSpecs) {
        // 有规格商品的统计
        skuWithCustoms.forEach(item => {
          let sku = (item || {}).sku || {}
          const baseQuantity = sku.saleBaseNumber || 0;
          const actualSales = sku.saleQuantity || 0;
          totalBaseQuantity += baseQuantity;
          totalDisplaySales += (actualSales + baseQuantity);
        });
      } else {
        // 无规格商品的统计
        const baseQuantity = saleBaseNumber;
        const actualSales = (rowItem && rowItem.saleQuantity) || 0;
        totalBaseQuantity = baseQuantity;
        totalDisplaySales = actualSales + baseQuantity;
      }

      return { totalBaseQuantity, totalDisplaySales };
    };

    const { totalBaseQuantity, totalDisplaySales } = calculateSummary();

    return (
      <div>
        <Table
          columns={columns}
          scroll={{ x: columns.reduce((acc, cur) => acc + (cur.width || 0), 0), y: 450 }}
          dataSource={dataSource}
          pagination={false}
          rowKey={(record, index) => index}
          bordered
        />
        {
          hasSpecs && <div style={{ marginTop: 16, color: '#666', fontSize: '14px' }}>
            共计：商品【{rowItem.name || ''}】基数为{totalBaseQuantity}，前台展示销量为{totalDisplaySales}
          </div>
        }

      </div>
    );
  });

  return isRequest && <Modal
  className="update-base-quantity-modal"
    open={open}
    title="调整销量基数"
    width={itemDetail.specDetail.length > 0 ? 800 : 520}
    onOk={() => {
      form.validateFields()
        .then(values => {
          onFinish(values);
        })
        .catch(info => {
          console.log('验证失败:', info);
        });
    }}
    onCancel={() => onClose(false)}
    destroyOnClose
  >
    <Form
      form={form}
      onFinish={onFinish}
    >
      <BatchTable
        form={form}
        specDetail={itemDetail.specDetail}
        skuDetail={itemDetail.skuDetail}
        rowItem={rowItem}
      />
    </Form>

  </Modal>;
}

export default UpdateBaseQuantityModal;


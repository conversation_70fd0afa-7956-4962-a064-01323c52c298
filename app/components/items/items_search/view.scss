@import "compass/css3/inline-block";
@import "pokeball/theme";

.component-item-search {
  .cutwords {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  a {
    color: inherit;
  }

  .items-total{
    line-height: 12px;
    float: right;
  }

  .current-location-dark{
    padding: 12px 0;
    font-size: 12px;
  }

  .current-location-span{
    i {

      &.chosen-close{
        @include inline-block(bottom);
      }
    }

    .bread-selector {
      @include inline-block();
      border: 1px solid $color-border;
      padding: 4px;
      margin-right: 6px;
    }
  }

  .brand-list {
    padding: 1px 0 0 10px;
    height: 99px;
    overflow: hidden;
  }

  .selected-brands-list {
    padding-left: 10px;

    .selected-brand {
      margin: 0 10px 0 5px;
    }

    i {
      font-size: 14px;
      color: $color-primary;
      vertical-align: baseline;
    }
  }

  .brand-li {
    width: 100px;
    height: 50px;
    padding: 10px;
    float: left;
    border: 1px solid $color-border;
    margin-right: -1px;
    margin-top: -1px;
    overflow: hidden;
    cursor: pointer;

    .check-icon {
      display: none;
      position: absolute;
      bottom: -12px;
      right: -1px;
      font-size: 16px;
    }

    &:hover,
    &.active {
      position: relative;
      border-color: $color-primary;
      position: relative;
    }

    &.active {
      .check-icon {
        color: $color-primary;
        @include inline-block(middle);
      }
    }
    &.noLogo {
      .brand-item {
        .brand-name {
          display: block;
          text-align: center;
          height: 100%;
          font-size: 12px;
          white-space: nowrap;
          line-height: 30px;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }

  .brand-item {
    width: 100%;
    height: 100%;

    &:hover {
      .brand-name {
        display: block;
        text-align: center;
        color: $color-primary;
        height: 100%;
        font-size: 12px;
        white-space: nowrap;
        line-height: 30px;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .brand-img {
        display: none;
      }
    }
  }

  .brand-img {
    max-width: 80px;
    max-height: 30px;
  }

  .brand-name {
   /* display: none;*/
  }

  .nav-group.shop-search {
    border: none;
    padding: 0;
    dl {
      border: 1px solid #dfdfdf;
      border-top: none;
      padding: 0;
      float: left;
      &:first-child {
        border-top: 1px solid #dfdfdf;
      }
      dt {
        width: 120px;
        padding: 0 10px;
        float: left;
        height: 100%;
        background: #f5f5f5;
      }
      dd {
        margin: 0;
        padding-left: 10px;
        &.attr-list {
          width: 928px;
          float: left;
        }
        &.more-dd {
          width: 150px;
          float: right;
        }
      }
    }
  }

  .search-attrs {
    border: none;
    padding: 0;
    tr {
      border: 1px solid #dfdfdf;
      border-top: none;
      padding: 0;
      &:first-child {
        border-top: 1px solid #dfdfdf;
      }
      &.nbb {
        border-bottom: none;
      }
      &.active {
        border: 1px solid $color-primary;
        border-top: 1px solid $color-primary;
        th {
          background-color: #f3fafd;
        }
      }
    }

    th {
      width: 120px;
      padding: 8px 10px;
      background-color: #f5f5f5;
      line-height: 18px;
      /*vertical-align: top;*/
      text-align: left;
      font-size: 12px;
      color: $color-text-assist;
      font-weight: normal;
    }

    .attr-group {
      @include inline-block(top);
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    td {
      margin: 0;
      padding: 4px 10px;
      line-height: 36px;
      text-align: left;
      &.attr-container,
      &.brand-container {
        width: 928px;

        .attr-list {
          width: 100%;
          height: 32px;
          overflow-y: hidden;
          /*display: flex;*/
          justify-content: center;
          align-items: center;

          .attr-dd {
            width: 170px;
            float: left;
            line-height: 18px;
            padding: 5px 23px 5px 0;
            margin-right: 10px;
          }

          .attr {
            padding-left: 9px;
            position: relative;
            cursor: pointer;
            color: $color-text-note;
            max-width: 140px;
            line-height: 18px;
            height: 24px;
            border: 1px solid $color-border-white;

            &.checked {
              border: 1px solid #dfdfdf;
              padding-right: 30px;
              display: inline-block;
              color: $color-text-assist;
              line-height: 22px;

              i {
                display: block;
              }
            }

            i {
              display: none;
              position: absolute;
              right: 8px;
              top: 6px;
              height: 12px;
              width: 12px;
              line-height: 12px;
              color: $color-text-assist;
            }
          }
        }

        &.extend {
          .attr-list {
            max-height: 128px;
            overflow-y: visible;
          }

          .attr-dd {
            padding: 5px 23px 5px 0;
          }

          .brand-list {
            height: 150px;
            overflow-y: scroll;
          }
        }

        a {
          color: $color-text-note;
          max-width: 140px;
          padding-left: 10px;
          display: inline-block;
          float: left;
          &:hover {
            color: $color-text-assist;
          }
        }
      }

      .brand-buttons {
        margin: 20px 0 0 0;
        text-align: center;
        width: 100%;
        float: left;
      }

      &.more-dd {
        width: 150px;
        vertical-align: top;
        text-align: right;
        padding-left: 0;
        font-size: 0;
        padding: 4px 0;

        button {
          margin-right: 10px;
          height: 24px;
          background-color: #ffffff;
          color: $color-text-assist;
          border: 1px solid #dfdfdf;
          padding: 5px 7px;
        }

        .js-elects i {
          vertical-align: top;
          color: $color-primary;
        }
      }
    }
    .show-more {
      height: 28px;
      width: 100%;
      border-top: 3px solid #dfdfdf;
      position: relative;
      margin-bottom: 30px;

      .toggle-more {
        position: absolute;
        right: 0;
        top: -3px;
        height: 28px;
        line-height: 28px;
        padding: 0 20px;
        background-color: #dfdfdf;
        cursor: pointer;
      }
    }
  }

  .shop-search-filter {
    background-color: #f5f5f5;
    border: 1px solid #dfdfdf;
    margin-top: -5px;
    width: 100%;
    height: 42px;
    .filter-banner {
      display: inline-block;
      float: left;
      .js-item-sort {
        padding: 0 10px;
        display: inline-block;
        line-height: 38px;
        cursor: pointer;
        border-left: 1px solid #f5f5f5;
        border-right: 1px solid #f5f5f5;
        color:$color-text-note;
        &:first-child {
          border-left: none;
        }
        &.active {
          background: #ffffff;
          color: $btn-hover-background-color;
          border-color: #dfdfdf;
          i {
            color: $btn-hover-background-color;
          }
        }
        i {
          color: $color-text-note;
        }
      }
    }
    .filter-form {
      display: inline-block;
      padding-left: 10px;
      float: left;
      fieldset {
        padding: 4px 0;

        .input-group {
          display: inline-block;
          vertical-align: middle;
        }
        .line {
          line-height: 42px;
          vertical-align: top;
          font-size: 12px;
        }
        .operations {
          vertical-align: middle;
        }
      }
    }
    .displayChange {
      float: right;
      display: inline-block;
      .js-list-display {
        padding: 0 10px;
        display: inline-block;
        line-height: 38px;
        cursor: pointer;
        border-left: 1px solid #f5f5f5;
        border-right: 1px solid #f5f5f5;
        &:last-child {
          border-right: none;
        }
        &.active {
          background: #ffffff;
          color: #197aff;
          border-color: #dfdfdf;
          i {
            color: #197aff;
          }
        }
        i {
          color: $color-text-note;
          font-size: 16px;
        }
      }
    }
  }
  .list {
    .shop-search-items-img-type {
      li.product {
        width: 232px;
        height: 355px;
        padding: 10px;
        margin: 0 10px 10px 0;
        border: 1px solid $color-border;
        &.no-mr {
          margin-right: 0;
        }
        &:hover {
          border: 1px solid $color-primary;
        }
        .product-image {
          width: 212px;
          height: 212px;
        }
        .product-price {
          font-size: 16px;
          line-height: 16px;
          color: $color-currency;
          font-weight: bold;
          .saleQuantity {
            font-size: 12px;
            color: $color-text-note;
            font-weight: normal;
            float: right;
            line-height: 16px;
          }
        }
        .product-desc {
          color: $color-text-assist;
          line-height: 18px;
          margin: 7px 0 8px;
          em {
            color: $color-primary;
            font-weight: bold;
          }
        }

        .shop-name {
          margin: 8px 0;
          line-height: 16px;
          color: $color-text-note;
          i {
            font-size: 16px;
          }
          a {
            color: $color-text-note;
            &:hover {
              color: $color-primary;
            }
          }
        }
      }
    }
    .shop-search-items-list-type {
      margin-top: 20px;

      tr {
        &.last {
          td {
            border-bottom: 1px solid $color-border;
          }
        }

        &:hover {
          td {
            background-color: #f5f5f5;
          }
        }
      }

      td {
        padding: 20px 0;
        vertical-align: top;
        border-top: 1px solid $color-border;
      }
      .promotion-type {
        display: inline-block;
        width: 50px;
        height: 20px;
        line-height: 20px;
        margin-left: 10px;
        color: $color-text-white;
        text-align: center;
      }
      .promotion-type-3 {
        background-color: #fbc543;
      }
      .product-image {
        width: 80px;
        height: 80px;
        border: 1px solid rgba(216,216,216,0.4);
        margin-right: 20px;
        display: inline-block;
      }
      .descAshop {
        display: inline-block;
        width: 330px;
        vertical-align: top;
        p {
          margin: 0;
          line-height: 18px;
          a {
            color: $color-text-assist;
            &:hover {
              color: $color-primary;
            }
          }
        }
        .product-desc {
          color: $color-text-assist;
          line-height: 18px;
        }
        .wan_tax {
          font-weight: bold;
          width: 30px;
          height: 15.4px;
          color: #e38573;
          border: 1px solid #e38573;
          border-radius: 2px;
          text-align: center;
          line-height: 15.4px;
        }
        .shop-name {
          margin: 10px 0;
          line-height: 16px;
          color: $color-text-note;
          a {
            color: $color-text-note;
          }
          i {
            font-size: 16px;
            color: $color-text-note;
          }
        }
      }
      .product-price {
        font-size: 16px;
        line-height: 20px;
        color: #ff7900;
        font-weight: bold;
      }
      .saleQuantity {
        font-size: 12px;
        color: $color-text-note;
        line-height: 16px;
      }
    }
  }

  .no-results {
    padding: 80px 0;

    .img {
      @include inline-block(middle);
      width: 80px;
      height: 80px;
      background-color: $color-background;
      border-radius: 100%;
      padding-top: 29px;
    }

    .good-icon {
      font-size: 40px;
      color: $color-text-disabled;
    }
  }

}

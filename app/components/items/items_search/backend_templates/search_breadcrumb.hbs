<div class="current-location-dark clearfix">
  <span class="items-total" data-total="{{_DATA_.entities.total}}">
    {{i18n "Total" bundle="design"}}{{#if _DATA_.entities.total}}{{_DATA_.entities.total}}{{else}}0{{/if}}{{i18n "x item(s)" bundle="design"}}
  </span>
  <span class="current-location-span">
    {{#each _DATA_.breadCrumbs}}
      {{#if ../fcid}}
        {{#equals id 0}}
          <a class="font-14 js-bread-category-selector" data-id="{{id}}">{{name}}</a>&nbsp;&nbsp;<i class="font-14 icon-feebas icon-feebas-next"></i>&nbsp;
        {{/equals}}
      {{else}}
        <a class="font-14 js-bread-category-selector" data-id="{{id}}">{{name}}</a>&nbsp;&nbsp;<i class="font-14 icon-feebas icon-feebas-next"></i>&nbsp;
      {{/if}}
    {{/each}}
    {{#each _DATA_.chosen}}
      <a  data-id="{{key}}" data-selector="{{key}}:{{name}}" class="bread-selector js-bread-selector font-12
        {{#equals type 1}}
          js-bread-brand-selector
        {{else}}
          {{#equals type 2}}
            js-bread-property-selector
          {{else}}
            js-bread-front-selector
          {{/equals}}
        {{/equals}}">
        {{#equals type 2}}
          {{key}}:{{name}}
        {{else}}
          {{#equals type 1}}
            {{i18n "Brand" bundle="design"}}:{{name}}
          {{else}}
            {{name}}
          {{/equals}}
        {{/equals}}
        <i class="font-12 icon-feebas icon-feebas-close"></i>
      </a>
    {{/each}}
  </span>
</div>

<div class="search-attrs js-search-attrs">
  <table width="100%">
    {{#if _DATA_.brands}}
      <tr class="">
        <th>{{i18n "Brand" bundle="design"}}</th>
        <td class="brand-container js-filter-container" id="js-brand-container" data-length="{{size _DATA_.brands}}">
          <ul class="brand-list clearfix">
          {{#each _DATA_.brands}}
            <li class="brand-li js-brand-selector {{#equals ../showBandLogo 0}}noLogo{{/equals}}" data-id="{{key}}" data-name="{{name}}">
              <div class="brand-item cutwords" data-value="{{key}}" title="{{name}}">
                <span class="brand-name">{{name}}</span>
                {{#equals ../showBandLogo 1}}
                <img class="brand-img" src="{{cdnPath extra.img "30"}}" alt="{{name}}">
                {{/equals}}
              </div>
              <i class="icon-feebas icon-feebas-xuanzhong check-icon"></i>
            </li>
          {{/each}}
          </ul>
          <p class="js-selected-brands-list selected-brands-list hide">
            <span class="">{{i18n "Selected brands" bundle="design"}}:</span>
          </p>
          <div class="brand-buttons hide">
            <button class="btn js-brand-confirm btn-medium btn-primary" type="button">{{i18n "Confirm" bundle="design"}}</button>
            <button class="btn js-brand-cancel btn-medium btn-secondary" type="button">{{i18n "Cancel" bundle="design"}}</button>
          </div>
        </td>
        <td class="more-dd">
          {{#gt (size _DATA_.brands) 1}}
            <button class="btn btn-minor btn-small js-brand-elects">
              <i class="icon-feebas icon-feebas-tianjia"></i> <span>{{i18n "Options" bundle="design"}}</span>
            </button>
          {{/gt}}
          {{#gt (size _DATA_.brands) 17}}
            <button class="btn btn-minor btn-small js-unfold">
              <span>{{i18n "unfold" bundle="design"}}</span> <i class="icon-feebas icon-feebas-xiangxiazhedie"></i>
            </button>
            <button class="btn btn-minor btn-small js-fold hide">
              <span>{{i18n "fold" bundle="design"}}</span> <i class="icon-feebas icon-feebas-xiangshangzhedie"></i>
            </button>
          {{/gt}}
        </td>
      </tr>
    {{/if}}
    {{#if _DATA_.backCategories}}
      <tr class="{{#gt @index 4}}hide{{/gt}}">
        <th>分类</th>
        <td class="list-more attr-container js-filter-container">
          <ul class="attr-list clearfix">
          {{#each _DATA_.backCategories}}
            <li class="attr-dd dd-cancel">
              <a class="js-category-selector cutwords" title="{{name}}" href="javascript:;" data-key="{{key}}" data-attr="{{name}}">{{name}}</a>
            </li>
          {{/each}}
          </ul>
        </td>
        <td class="more-dd">
          {{#gt (size _DATA_.backCategories) 5}}
            <button class="btn btn-minor btn-small js-unfold">
              <span>{{i18n "unfold" bundle="shop_design"}}</span> <i class="icon-feebas icon-feebas-xiangxiazhedie"></i>
            </button>
            <button class="btn btn-minor btn-small js-fold hide">
              <span>{{i18n "fold" bundle="shop_design"}}</span> <i class="icon-feebas icon-feebas-xiangshangzhedie"></i>
            </button>
          {{/gt}}
        </td>
      </tr>
    {{/if}}
    {{#each _DATA_.attributes}}
    <tr class="{{#ifCond @index ">=" 4}}hide{{/ifCond}}">
      <th><span class="attr-group">{{group}}</span></th>
      <td class="attr-container js-filter-container" data-length="{{size nameAndCounts}}">
        <ul class="attr-list clearfix">
        {{#each nameAndCounts}}
          <li class="attr-dd dd-cancel">
            <div class="attr hide cutwords" data-value="{{name}}" title="{{name}}">
              {{name}}
              <i class="icon-feebas icon-feebas-guanbi"></i>
            </div>
            <a class="js-property-selector cutwords" title="{{name}}" href="javascript:;" data-attr="{{name}}">{{name}}</a>
            {{!-- <span>({{count}})</span> --}}
          </li>
        {{/each}}
        </ul>
        <div class="brand-buttons hide">
          <button class="btn js-attrs-confirm btn-medium btn-primary" type="button">{{i18n "Confirm" bundle="design"}}</button>
          <button class="btn js-brand-cancel btn-medium btn-secondary" type="button">{{i18n "Cancel" bundle="design"}}</button>
        </div>
      </td>

      <td class="more-dd">
        {{#gt (size nameAndCounts) 1}}
          <button class="btn btn-minor btn-small js-attr-elects"><i class="icon-feebas icon-feebas-tianjia"></i> <span>{{i18n "Options" bundle="design"}}</span></button>
        {{/gt}}
        {{#gt (size nameAndCounts) 5}}
          <button class="btn btn-minor btn-small js-unfold">
            <span>{{i18n "unfold" bundle="shop_design"}}</span> <i class="icon-feebas icon-feebas-xiangxiazhedie"></i>
          </button>
          <button class="btn btn-minor btn-small js-fold hide">
            <span>{{i18n "fold" bundle="shop_design"}}</span> <i class="icon-feebas icon-feebas-xiangshangzhedie"></i>
          </button>
        {{/gt}}
      </td>
    </tr>
    {{/each}}
  </table>
  <div class="show-more">
    {{#gt (size _DATA_.attributes) 5}}
      <span class="toggle-more js-toggle-more" data-length="{{size _DATA_.attributes}}">
        <span>
          {{i18n "More options" bundle="design"}}
          <i class="icon-feebas icon-feebas-xiangxiazhedie"></i>
        </span>
        <span class="hide">
          {{i18n "fold" bundle="design"}}
          <i class="icon-feebas icon-feebas-xiangshangzhedie"></i>
        </span>
      </span>
    {{/gt}}
  </div>
</div>

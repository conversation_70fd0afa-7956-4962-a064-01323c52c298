<div class="list list-squared list-condensed clearfix">
  {{#if _DATA_.entities.total}}
    {{#equals display "1"}}
    {{!-- 列表展示 --}}
      <table class="shop-search-items-list-type" width="100%">
        <tbody>
        {{#each _DATA_.entities.data}}
          <tr>
            <td width="520">
              <div class="product-image">
                <a href="/items/{{id}}" target="_blank">
                  <img data-original="{{cdnPath mainImage "78"}}" alt="{{{name}}}" style="width:78px; height:78px;">
                </a>
              </div>
              <div class="descAshop">
                <p class="product-desc">
                  <a href="/items/{{id}}" target="_blank">{{{name}}}</a>
                </p>
                <p style="width: 30px;height:  17px;text-align: center;">
                  {{#equals isBonded "1"}}<b  style="width: 26px;height:  17px;font-family: PingFangSC-Medium;font-size: 12px;color: #FFFFFF;letter-spacing: 0.05px;background: #6868E8;border-radius: 2px;float: left;">保税</b>{{/equals}}
                  {{#equals isBonded "0"}}<b  style="height:  17px;font-family: PingFangSC-Medium;font-size: 12px;color: #FFD166;letter-spacing: 0.05px;border-radius: 2px;border: 1px solid #FFD166;float: left;">完税</b>{{/equals}}
                  {{#equals isBonded "2"}}<b  style="width: 26px;height:  17px;font-family: PingFangSC-Medium;font-size: 12px;color: #FFFFFF;letter-spacing: 0.05px;background: #6868E8;border-radius: 2px;float: left;">直邮</b>{{/equals}}
                </p>
                <p style="width: 52px;height:  17px;text-align: center;">

                  <span class="saleQuantity">销量：{{#if saleQuantity}}{{saleQuantity}}{{else}}0{{/if}}</span>
                </p>

              </div>
            </td>
            <td width="175" class="left-text"><span class="product-price">￥ {{formatPrice price}}</span></td>
            <td width="170" class="left-text">{{#if originUrl}}<img data-original="{{cdnPath originUrl "20"}}" style="width: 20px;height:20px;" alt="{{origin}}" >{{/if}} </td>
            <td class="right-text">
              {{#each promotionTypes}}
                <span class="promotion-type promotion-type-{{this}}">{{#equals this 3}}折扣{{/equals}}</span>
              {{/each}}
            </td>
          </tr>
        {{/each}}
        </tbody>
      </table>
    {{else}}
    {{!-- 大图展示 --}}
      <ul class="shop-search-items-img-type">
        {{#each _DATA_.entities.data}}
          <li class="product {{#mod @index 5}}no-mr{{/mod}}">
            <div class="product-image">
              <a href="/items/{{id}}" target="_blank">
                <img data-original="{{cdnPath mainImage "212"}}" alt="{{{name}}}" style="width:212px; height:212px;">
              </a>
            </div>
            <p class="product-price">
              ￥ {{formatPrice price}}
              <span style="float: right;">{{#if originUrl}}<img data-original="{{cdnPath originUrl "20"}}" style="width: 20px;height:20px;" alt="{{origin}}" >{{/if}}</span>
            </p>
            <p class="product-desc">
              <a href="/items/{{id}}" target="_blank">{{{name}}}</a>
            </p>
            <p style="height:  17px;text-align: center;">
              {{#equals isBonded "1"}}<b  style="width: 26px;height:  17px;font-family: PingFangSC-Medium;font-size: 12px;color: #FFFFFF;letter-spacing: 0.05px;background: #6868E8;border-radius: 2px;float: left;">保税</b>{{/equals}}
              {{#equals isBonded "0"}}<b  style="height:  17px;font-family: PingFangSC-Medium;font-size: 12px;color: #FFD166;letter-spacing: 0.05px;border-radius: 2px;border: 1px solid #FFD166;float: left;">完税</b>{{/equals}}
              {{#equals isBonded "2"}}<b  style="width: 26px;height:  17px;font-family: PingFangSC-Medium;font-size: 12px;color: #FFFFFF;letter-spacing: 0.05px;background: #6868E8;border-radius: 2px;float: left;">直邮</b>{{/equals}}
              <span class="saleQuantity" style="float: right;">销量：{{#if saleQuantity}}{{saleQuantity}}{{else}}0{{/if}}</span>
            </p>

          </li>
        {{/each}}
      </ul>
    {{/equals}}
  {{else}}
    <div class="no-results center-text">
      <span class="img"><i class="icon-feebas icon-feebas-goods good-icon"></i></span>
      <p class="desc">暂无商品</p>
    </div>
  {{/if}}
</div>

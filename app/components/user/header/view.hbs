{{#component "header js-comp"}}
<div class="header-logo">

  {{#if isBuyerCenter}}
  <a href="/buyer/index">
    <div class="logo-wraper user-center">
      <div class="logo">
        <img id="logoId" src="/assets/images/other-images/buyer-logo.png" width="180" height="60" alt=""
          style="display: none;">
      </div>
    </div>
  </a>
  {{else}}
  <a href="javascript:jump()">
    <div class="logo-wraper">
      <div class="logo">
        <img id="logoId" src="#" width="180" height="60" alt="" style="display: none;">
      </div>
    </div>
  </a>
  {{/if}}

</div>
<div class="top-nav-wrapper">
  {{#if isBuyerCenter}}
  <ul class="top-nav top-nav-left">
    <li>
      <a href="/buyer/index">首页</a>
    </li>
    <li>
      <a href="/user/profile">账户设置</a>
    </li>
  </ul>
  {{/if}} {{!--
  <ul class="top-nav top-nav-left">
    <li>
      <a href="javascript:;" class="nav-toggle-btn">
        应用中心
        <i class="icon-feebas icon-feebas-xiangxiazhedie toggle-tool"></i>
      </a>
      <ul class="dropdown-menu">
        <li>
          <a href="{{_HREF_.mainHref}}">商城首页</a>
        </li>
      </ul>
    </li>
  </ul> --}}
  <ul class="top-nav top-nav-right">

    {{!-- <li>
      <a href="/dashboard/todo">
        <i class="icon-feebas icon-feebas-daibanshixiang"></i>
        待办事项 {{#if _TODO_CNT_}} {{#ifCond _TODO_CNT_ "!=" 0}}
        <label class="label label-danger pull-right">{{_TODO_CNT_}}</label>
        {{/ifCond}} {{/if}}
      </a>
    </li> --}}
    <li class="user-info-nav-item message">
      <a href="javascript:;" id="msg" class="nav-toggle-btn" style="padding: 18px 16px;height:60px">
        {{!-- <i class="icon-feebas icon-feebas-xiaoxi"></i> --}}
        <img id="logoId" src="/assets/images/other-images/isNews.png" alt="" width="22" height="22">
        <div class=""></div>
      </a>
      <ul class="dropdown-menu">
        <div class="cont-head">
          <div class="cont-font">消息盒子</div>
        </div>
        <div class="cont-bot">
          <div class="aside">
          </div>
          <div class="cont-mid">

          </div>
        </div>
      </ul>
    </li>
    {{!-- <li>
      <a href="javascript:;">
        <i class="icon-feebas icon-feebas-bangzhu"></i>
        帮助
      </a>
    </li> --}}
    <li class="user-info-nav-item">
      <a href="javascript:;" class="nav-toggle-btn">
        <i class="icon-feebas icon-feebas-gerenxinxi pull-left"></i>
        <div class="user-info pull-left">
          <div class="user-name">{{_DATA_.name}}</div>
          <div class="user-position">{{_DATA_.employee.orgName}}{{#if
            _DATA_.employee.depName}}-{{_DATA_.employee.depName}}{{/if}}</div>
        </div>
        {{_USER_.name}}
        <i class="icon-feebas icon-feebas-xiangxiazhedie toggle-tool"></i>
      </a>
      <ul class="dropdown-menu">
        <li>
          <a href="/user/profile" id="profile">账号信息</a>
        </li>
        <li>
          <a href="javascript:;" id="js-user-logout">退出</a>
        </li>
      </ul>
    </li>
  </ul>
  {{#if isBuyerCenter}}
  {{else}}
<!--  <ul class="top-nav switch-system" style="display:none;margin-right: 12px;float: right;">-->
<!--    <li class="user-info-nav-item">-->
<!--      <div href="javascript:;" class="system-a">-->
<!--        小程序商城<i class="icon-feebas icon-feebas-xiangxiazhedie toggle-tool" style="font-size: 12px;"></i>-->
<!--      </div>-->
<!--      <ul class="dropdown-menu systems" style="display: none;">-->
<!--        <li>-->
<!--          <a href="javascript:;" id="js-oms">oms</a>-->
<!--        </li>-->
<!--        <li>-->
<!--          <a href="javascript:;" id="js-gx">供销平台</a>-->
<!--        </li>-->

<!--      </ul>-->
<!--    </li>-->
<!--  </ul>-->
  {{/if}}
</div>
{{/component}}
<script>
  function Base64() {

    // private property
    _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

    // public method for encoding
    this.encode = function (input) {
      var output = "";
      var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
      var i = 0;
      input = _utf8_encode(input);
      while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
          enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
          enc4 = 64;
        }
        output = output +
          _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
          _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
      }
      return output;
    }

    // public method for decoding
    this.decode = function (input) {
      var output = "";
      var chr1, chr2, chr3;
      var enc1, enc2, enc3, enc4;
      var i = 0;
      input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
      while (i < input.length) {
        enc1 = _keyStr.indexOf(input.charAt(i++));
        enc2 = _keyStr.indexOf(input.charAt(i++));
        enc3 = _keyStr.indexOf(input.charAt(i++));
        enc4 = _keyStr.indexOf(input.charAt(i++));
        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;
        output = output + String.fromCharCode(chr1);
        if (enc3 != 64) {
          output = output + String.fromCharCode(chr2);
        }
        if (enc4 != 64) {
          output = output + String.fromCharCode(chr3);
        }
      }
      output = _utf8_decode(output);
      return output;
    }

    // private method for UTF-8 encoding
    _utf8_encode = function (string) {
      string = string.replace(/\r\n/g, "\n");
      var utftext = "";
      for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
        if (c < 128) {
          utftext += String.fromCharCode(c);
        } else if ((c > 127) && (c < 2048)) {
          utftext += String.fromCharCode((c >> 6) | 192);
          utftext += String.fromCharCode((c & 63) | 128);
        } else {
          utftext += String.fromCharCode((c >> 12) | 224);
          utftext += String.fromCharCode(((c >> 6) & 63) | 128);
          utftext += String.fromCharCode((c & 63) | 128);
        }

      }
      return utftext;
    }

    // private method for UTF-8 decoding
    _utf8_decode = function (utftext) {
      var string = "";
      var i = 0;
      var c = c1 = c2 = 0;
      while (i < utftext.length) {
        c = utftext.charCodeAt(i);
        if (c < 128) {
          string += String.fromCharCode(c);
          i++;
        } else if ((c > 191) && (c < 224)) {
          c2 = utftext.charCodeAt(i + 1);
          string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
          i += 2;
        } else {
          c2 = utftext.charCodeAt(i + 1);
          c3 = utftext.charCodeAt(i + 2);
          string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
          i += 3;
        }
      }
      return string;
    }
  }
    function guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
  function jump() {
      var b = new Base64();
      var str = b.encode('{{_USER_.shopId}}');
      if ('{{_USER_.shopId}}' == '33' || '{{_USER_.shopId}}' == '82') {
        window.location.href = '#'
      } else {
        window.location.href = '/fw/index?id=' + guid() + str
      }

    }
</script>
<script src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/30461944621.js"></script>
<script>
  $(function () {
    setTimeout(function () {
      /* if(sessionStorage.ymg =='1')
      {*/
      if (sessionStorage.shopLogo) {
        $("#logoId").attr("src", sessionStorage.shopLogo)
        $(".logo-wraper").css("background-color", "white")
        $(".logo-wraper .logo").css("display", "flex")
        $(".logo-wraper .logo").css("align-items", "center")
        $(".logo-wraper .logo").css("justify-content", "center")

      }/*else
        {
          $("#logoId").attr("src","/assets/images/other-images/buyer-logo-ymg.png")
        }
      }*/
      $("#logoId").show();
    }, 100)
    
  })
</script>

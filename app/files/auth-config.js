module.exports = {
    enable: true,
    strict: false,
    service: "getRolesByUserId",
    loginRedirect: "/login",
    roles: {
        GLOBAL: {
            requests: [
                "GET: /(index)?",
                "GET: /login",
                "GET: /bellamy-login",
                "GET: /buyer/pre-return",
                "GET: /item/snapshot",
                "GET: /search",
                "GET: /forget-password",
                "GET: /act/.*",
                "GET: /shops/\\d+",
                "GET: /shops/\\d+/list",
                "GET: /items/\\d+",
                "GET: /shops",
                "GET: /seller/index",                               // 嘉涛提供的子账号进入商家中心的解决方案
                "POST: /api/user/login",
                "POST: /api/user/logout",
                "GET: /api/user/(mobile|email|username)-available",
                "GET: /api/user/captcha",
                "POST: /api/user/register-by-mobile/send-sms",
                "POST: /api/user/register-by-mobile",
                "POST: /api/user/reset-password-by-mobile/send-sms",
                "POST: /api/user/reset-password-by-mobile",
                "GET: /forget-password-success",
                "ALL: /api/refund/notify/.*",
                "POST: /api/order/paid/.*",
                "GET: /api/items",
                "GET: /api/carts/count",
                "GET: /api/coupon/item/\\d+",
                "GET: /api/item/\\d+/promotion",
                "GET: /api/item/\\d+/full-promotion",
                "GET: /api/delivery-fee-charge/.*",
                "GET: /api/address/ip-info",
                "GET: /api/address/\\d+/children",
                "GET: /register",
                "GET: /api/comment/item-detail/paging",
                "GET: /api/comment/item-detail/replies",
                "ALL: /api/gateway",
                "GET: /api/item/\\d+/sku-price-with-default-promotion",
                "GET: /api/mobile/item/5008",
                "GET: /api/item/5008/detail-info",
                "GET: /api/user/current",
                "GET: /api/design/article/list",
                "GET: /api/design/article/category",
                "GET: /api/design/article",
                "GET: /api/design/article/\\d+",
                "GET: /api/design/article/listCategory",
                "GET: /articles/list",
                "GET: /articles/detail",
                "GET: /api/user/mobile-check",
                "GET: /api/shop/\\d+",
                "GET: /api/agreement/*",
                "ALL: /api/buyer/receiver-infos",
                "ALL: /api/buyer/receiver-infos/.*",
                "GET: /agreement/return-policy",
            ]
        },
        LOGIN: {
            requests: [
                "GET: /user/index",
                "GET: /user/update-password"
            ]
        },
        SELLER: {
            requests: [
                "GET: /seller/.*",
                "GET: /system/sites",
                "GET: /system/sites/.*",
                "GET: /user/member-price-list",
                "ALL:/api/membershipPrice/.*",
                "ALL:/api/weShop/.*",
                "ALL:/api/subStore/.*"
            ],
        },
        SUB_SELLER: {
            requests: [
                "GET: /seller/.*"
            ]
        },
        BUYER: {
            requests: [
                "GET: /buyer/.*", "ALL: /api/.*"
            ]
        },
        ADMIN: {
            requests: [
                "GET: /system/.*"
            ],
            resources: [
                "manage_site_design", "manage_site_shop_templet", "manage_site_article"
            ]
        }
    },
    trees: {
        SUB_SELLER: {
            orders: {
                name: "订单管理",
                children: {
                    order: {
                        name: "订单管理",
                        resources: ["manage_order"],
                        children: {
                            order_export: {
                                name: "导出",
                                resources: ["manage_order"],
                            },
                            order_audit: {
                                name: "审核订单",
                                resources: ["manage_order"],
                            },
                            order_cancel: {
                                name: "取消订单",
                                resources: ["manage_order"],
                            },
                            order_ship: {
                                name: "发货",
                                resources: ["manage_order"],
                            },
                            order_refund_apply: {
                                name: "申请退款",
                                resources: ["manage_order"],
                            },
                            order_edit_identity: {
                                name: "修改身份证",
                                resources: ["manage_order"],
                            },
                            order_push: {
                                name: "推送",
                                resources: ["manage_order"],
                            }
                        }
                    },
                    evaluation_manage: {
                        name: "评价管理",
                        resources: ["evaluation_manage"]
                    },
                    sensitive_word: {
                        name: "敏感词配置",
                        resources: ["sensitive_word"]
                    },
                    refund: {
                        name: "售后列表",
                        resources: ["manage_refund"],
                        children: {
                            refund_export: {
                                name: "导出",
                                resources: ["manage_refund"],
                            },
                            refund_agree: {
                                name: "审核",
                                resources: ["manage_refund"],
                            },
                            /*  refund_refuse: {
                                name: "拒绝",
                                resources: ["manage_refund"],
                              },*/
                            refund_confirm: {
                                name: "退款",
                                resources: ["manage_refund"],
                            }
                        }
                    },
                    invoice_management: {
                        name: "发票管理",
                        resources: ["invoice_management"]
                    }/* ,

          review: {
            name: "评价管理",
            resources: ["manage_review"]
          }*/
                }
            },
            items: {
                name: "商品管理",
                children: {
                    item_create: {
                        name: "创建商品",
                        resources: ["create_item"]
                    },
                    item_on_shelf: {
                        name: "上下架商品",
                        resources: ["list_item"],
                        children: {
                            item_on_shelf_export: {
                                name: "导出",
                                resources: ["list_item"],
                            },
                            item_on_shelf_off: {
                                name: "下架",
                                resources: ["list_item"],
                            },
                            item_off_shelf_on: {
                                name: "上架",
                                resources: ["delist_item"],
                            },
                            item_on_shelf_edit: {
                                name: "编辑",
                                resources: ["list_item"],
                            },
                            item_on_shelf_del: {
                                name: "删除",
                                resources: ["list_item"],
                            }
                        }
                    },
                    item_setting: {
                        name: "商品配置",
                        resources: ["item_setting"]
                    },
                    /*
          item_off_shelf: {
            name: "下架商品",
            resources: ["delist_item"],
            children: {
              item_off_shelf_export: {
                name: "导出",
                resources: ["delist_item"],
              },
              item_off_shelf_on: {
                name: "上架",
                resources: ["delist_item"],
              },
              item_off_shelf_edit: {
                name: "编辑",
                resources: ["delist_item"],
              },
              item_off_shelf_del: {
                name: "删除",
                resources: ["delist_item"],
              }
            }
          },*/ /* ,          select_item_on_shelf_list: {
            name: "售卖第三方商品",
            resources: ["select_item_on_shelf_list"]
          }*/
                    shop_category: {
                        name: "类目管理",
                        resources: ["manage_shop_category"]
                    },
                    item_sort: {
                        name: "商品分类",
                        resources: ["sort_item"]
                    },
                    good_display: {
                        name: "商品分类",
                        resources: ["good_display"]
                    },
                    third_party_stock: {
                        name: "商品库存中心",
                        resources: ["third_party_stock"]
                    },
                    goods_tags: {
                        name: "商品标签",
                        resources: ["goods_tags"]
                    },
                    address_module: {
                        name: "可售地址模版",
                        resources: ["address_module"]
                    },
                  brand_manage: {
                    name: "品牌管理",
                    resources: ["brand_manage"]
                  }
                    /* ,
          distribution_price_list: {
            name: "微分销价管理",
            resources: ["distribution_price_list"]
          }*/
                }
            },
            ladderDistribution: {
                name: "分销管理",
                children: {
                    ladder_commission_rate_setting: {
                        name: "服务费管理",
                        resources: ["ladder_commission_rate_setting"]
                    },
                    service_provider_info: {
                        name: "服务商信息",
                        resources: ["service_provider_info"]
                    },
                    distribution_shop: {
                        name: "分销店铺",
                        resources: ["distribution_shop"]
                    },
                    we_distribution_apply_audit: {
                        name: "分销审核",
                        resources: ["we_distribution_apply_audit"]
                    },
                    brand_speak_audit: {/* --按钮权限命名用页面名字+按钮名字，方便判断控制按钮，也避免按钮权限重复--*/
                        name: "品牌代言审核",
                        resources: ["brand_speak_audit"],
                        children: {
                            brand_speak_audit_export: {
                                name: "导出",
                                resources: ["brand_speak_audit"],
                            },
                            brand_speak_audit_check: {
                                name: "审核",
                                resources: ["brand_speak_audit"],
                            }
                        }
                    },
                    distribution_user_list: {
                        name: "分销用户",
                        resources: ["ladder_distribution_user"],
                        children: {
                            distribution_user_list_export: {
                                name: "导出",
                                resources: ["ladder_distribution_user"],
                            }
                        }
                    },
                    shop_user_list: {
                        name: "用户管理",
                        resources: ["shop_user_list"]
                    },
                    user_tag_list: {
                        name: "用户标签",
                        resources: ["user_tag_list"]
                    }
                }
            },
            ladderDistribution_member: {
                name: "会员管理",
                children: {
                    manage_member_list: {
                        name: "会员信息",
                        resources: ["manage_member_list"],
                        children: {
                            manage_member_list_add: {
                                name: "新增",
                                resources: ["manage_member_list"],
                            },
                            manage_member_list_edit: {
                                name: "编辑",
                                resources: ["manage_member_list"],
                            },
                            manage_member_list_bady_info: {
                                name: "宝宝信息",
                                resources: ["manage_member_list"],
                            }
                        }
                    }
                }
            },
            IntegralManagement: {
                name: "积分管理",
                children: {
                    add_integral_product: {
                        name: "创建积分商品",
                        resources: ["add_integral_product"]
                    },
                    integral_products_on_shelf: {
                        name: "上架积分商品",
                        resources: ["integral_products_on_shelf"]
                    },
                    integral_products_off_shelf: {
                        name: "下架积分商品",
                        resources: ["integral_products_off_shelf"]
                    },
                    integral_order_manage: {
                        name: "积分订单管理",
                        resources: ["integral_order_manage"],
                        children: {
                            integral_order_export: {
                                name: "导出",
                                resources: ["integral_order_manage"],
                            },
                            integral_order_ship: {
                                name: "发货",
                                resources: ["integral_order_manage"],
                            }
                        }
                    },
                    integral_account_detail: {
                        name: "积分账户明细",
                        resources: ["integral_account_detail"],
                        children: {
                            integral_account_detail_edit_integral: {
                                name: "修改积分",
                                resources: ["integral_account_detail"],
                            },
                        }
                    },
                    integral_detail_record: {
                        name: "积分明细记录",
                        resources: ["integral_detail_record"]
                    },
                    shop_code_list: {
                        name: "商品码管理",
                        resources: ["shop_code_list"]
                    },
                    item_code_list: {
                        name: "历史商品码管理",
                        resources: ["history_item_code_list"]
                    },
                    integral_rule_management: {
                        name: "积分管理",
                        resources: ["integral_rule_management"]
                    },
                    /*integral_rule_set: {
                      name: "积分规则",
                      resources: ["integral_rule_set"]
                    },
                    ladderDistribution_integral_risk_mgt_rule_set: {
                      name: "积分风控规则",
                      resources: ["integral_risk_mgt_rule_set"]
                    },*/
                }
            },
          communityOperationUserManager: {
            name: "用户管理",
            children: {
              community_operation_user_list :{
                name: "用户列表",
                resources: ["community_operation_user_list"],
              }
            }
          },
            subStoreDistribution: {
                name: "门店管理",
                children: {
                    distribution_store: {
                        name: "分销门店列表",
                        resources: ["distribution_store_manage"],
                        children: {
                            distribution_store_change_store: {
                                name: "换绑门店",
                                resources: ["distribution_store_manage"],
                            },
                            distribution_store_export: {
                                name: "导出",
                                resources: ["distribution_store_manage"],
                            },
                            distribution_store_frozen: {
                                name: "冻结解冻",
                                resources: ["distribution_store_manage"],
                            }
                        }
                    },
                    store_distribution_guider: {
                        name: "导购管理",
                        resources: ["store_distribution_guider"],
                        children: {
                            store_distribution_guider_export: {
                                name: "导出",
                                resources: ["store_distribution_guider"],
                            },
                            store_distribution_guider_import: {
                                name: "导入",
                                resources: ["store_distribution_guider"],
                            },
                            store_distribution_guider_edit: {
                                name: "修改导购名称",
                                resources: ["store_distribution_guider"],
                            }
                        }
                    },
                    store_distribution_apply_audit: {
                        name: "门店分销申请",
                        resources: ["store_distribution_apply_audit"],
                        children: {
                            store_distribution_apply_audit_import: {
                                name: "导入",
                                resources: ["store_distribution_apply_audit"],
                            },
                            store_distribution_apply_audit_export: {
                                name: "导出",
                                resources: ["store_distribution_apply_audit"],
                            },
                            store_distribution_apply_audit_check: {
                                name: "审核",
                                resources: ["store_distribution_apply_audit"],
                            },
                            store_distribution_apply_audit_edit: {
                                name: "编辑",
                                resources: ["store_distribution_apply_audit"],
                            }
                        }
                    },
                    store_shop_user_list: {
                        name: "用户管理",
                        resources: ["store_shop_user_list"],
                        children: {
                            shop_user_list_set_guider: {
                                name: "设置导购",
                                resources: ["store_shop_user_list"],
                            },
                            shop_user_list_bind_store: {
                                name: "绑定门店",
                                resources: ["store_shop_user_list"],
                            }
                        }
                    },
                    store_commission_rate_setting: {
                        name: "服务费管理",
                        resources: ["store_commission_rate_setting"]
                    }
                }
            },
            finance_management: {
                name: "财务管理",
                children: {
                    withdrawal_real_name_auth: {
                        name: "提现实名认证",
                        resources: ["withdrawal_real_name_auth"]
                    },
                    withdraw_deposit_apply: {
                        name: "提现申请",
                        resources: ["withdraw_deposit_apply"],
                        children: {
                            withdraw_deposit_apply_export: {
                                name: "导出",
                                resources: ["withdraw_deposit_apply"],
                            },
                            withdraw_deposit_apply_audit: {
                                name: "审核",
                                resources: ["withdraw_deposit_apply"],
                            },
                            /* withdraw_deposit_apply_refuse: {
                               name: "拒绝",
                               resources: ["withdraw_deposit_apply"],
                             },*/
                            withdraw_deposit_apply_offline_transfer: {
                                name: "线下转账",
                                resources: ["withdraw_deposit_apply"],
                            }
                        }
                    },
                    new_withdraw_deposit_apply: {
                        name: "提现申请(新)",
                        resources: ["new_withdraw_deposit_apply"],
                    },
                    enterprise_withdrawal: {
                        name: "付汇与提现",
                        resources: ["enterprise_withdrawal"],
                    },
                    bill_query: {
                        name: "账单查询",
                        resources: ["bill_query"]
                    },
                    withdrawal_rules: {
                        name: "提现规则",
                        resources: ["withdrawal_rules"]
                    },
                    withdrawal_settings: {
                        name: "提现设置",
                        resources: ["withdrawal_settings"]
                    }
                }
            },
            /*  member: {
                name: "会员管理",
                children: {
                  member_price: {
                    name: "会员价管理",
                    resources: ["manage_member_price"]
                  }
                }
              },*/
          offsite_promotion: {
            name: "站外推广",
            children: {
              event_promotion: {
                name: "营销活动管理",
                resources: ["event_promotion"]
              }
            }
          },
            marketing: {
                name: "营销管理",
                children: {
                    activity_creaet: {
                        name: "营销活动管理",
                        resources: ["create_activity"]
                    },
                  gift_manage: {
                    name: "营销活动管理",
                    resources: ["gift_manage"]
                  },
                  marketing_tools: {
                    name: "营销工具",
                    resources: ["marketing_tools"]
                  },
                  reach_management: {
                    name: "触达管理",
                    resources: ["reach_management"]
                  },

                }
            },
            enterprise_wechat_management: {
                name: "企微管理",
                children: {
                  external_member_manage: {
                    name: "小程序客户管理",
                    resources: ["external_member_manage"]
                  },
                  external_crowd_manage: {
                    name: "企微群管理",
                    resources: ["external_crowd_manage"]
                  },
                  external_contacts_manage: {
                    name: "员工管理",
                    resources: ["external_contacts_manage"]
                  },
                  external_qrcode_manage: {
                    name: "二维码管理",
                    resources: ["external_qrcode_manage"]
                  },
                  label_community_operation_user_set: {
                    name: "标签管理",
                    resources: ["label_community_operation_user_set"]
                  },
                  enterprise_wechat_member_management: {
                    name: "会员管理",
                    resources: ["enterprise_wechat_member_management"]
                  },

                }
            },
            member_management: {
                name: "会员管理",
                children: {
                    member_list: {
                        name: "会员列表",
                        resources: ["member_list"]
                    },
                    member_level_management: {
                        name: "会员等级管理",
                        resources: ["member_level_management"]
                    }
                }
            },
            data_management: {
                name: "数据管理",
                children: {
                    user_analysis: {
                        name: "用户分析",
                        resources: ["user_analysis"]
                    },
                    transaction_analysis: {
                        name: "交易分析",
                        resources: ["transaction_analysis"]
                    }
                }
            },
            shops: {
                name: "店铺管理",
                children: {
                    /* shop_design: {
                      name: "店铺装修",
                      resources: ["manage_site_shop_design"]
                    },*/
                    sho_profile: {
                        name: "店铺详情",
                        resources: ["view_shop_profile"]
                    },
                  view_shop_attorney: {
                        name: "授权书管理",
                        resources: ["view_shop_attorney"]
                    },
                    sho_pay: {
                        name: "店铺支付信息",
                        resources: ["sho_pay"]
                    },
                    view_shop_decoration: {
                        name: "小程序装修",
                        resources: ["view_shop_decoration"]
                    },
                  view_weapp_decoration_new: {
                        name: "小程序装修-新",
                        resources: ["view_weapp_decoration_new"]
                    },
                    view_shop_clause_set: {
                        name: "条款设置",
                        resources: ["view_shop_clause_set"]
                    },
                    material_management: {
                        name: "素材管理",
                        resources: ["material_management"]
                    },
                    /*           ,
                              sho_adv: {
                                name: '店铺广告',
                                resources: ['view_shop_adv']
                              } */
                    applet_information: {
                        name: "微信小程序信息",
                        resources: ["wechat_applet_information"]
                    },
                    label_setting: {
                        name: "标签设置",
                        resources: ["label_set"]
                    },
                    order_audit_rules: {
                        name: "订单审核规则",
                        resources: ["order_audit_rules"]
                    }
                }
            },
            shipping: {
                name: "运费管理",
                children: {
                    manage_freight: {
                        name: "运费模版",
                        resources: ["manage_freight"]
                    }
                }
            },
            content_community: {
                name: "内容社区",
                children: {
                    live_community: {
                        name: "直播社区",
                        resources: ["live_community"]
                    },
                    content_material_library: {
                        name: "内容素材库",
                        resources: ["content_material_library"]
                    },
                    wish_list: {
                        name: "心愿单",
                        resources: ["wish_list"]
                    }
                }
            },
            auth: {
                name: "权限管理",
                children: {
                    seller_role: {
                        name: "角色管理",
                        resources: ["manage_seller_role"]
                    },
                    seller_sub: {
                        name: "子账号管理",
                        resources: ["manage_seller_sub"]
                    }
                }
            },
            log_manage: {
                name: "日志管理",
                children: {
                    operation_log: {
                        name: "操作日志",
                        resources: ["operation_log"]
                    },
                    login_log: {
                        name: "登录日志",
                        resources: ["login_log"]
                    }
                }
            },
            // others: {
            //   name: '其他管理',
            //   children: {
            //     seller_pictures: {
            //       name: '图片管理',
            //       resources: ['manage_seller_pictures']
            //     }
            //   }
            // },
            settlements: {
                name: "结算管理",
                children: {
                    order_sum: {
                        name: "订单日汇总",
                        resources: ["manage_settle_order_sum"]
                    },
                    manage_settle_data_board: {
                        name: "数据看板",
                        resources: ["manage_settle_data_board"]
                    },
                    manage_settle_mail_reception: {
                        name: "邮件接收",
                        resources: ["manage_settle_mail_reception"]
                    },
                    order_detail: {
                        name: "订单明细",
                        resources: ["manage_settle_order_detail"]
                    },
                    refund_detail: {
                        name: "退款单明细",
                        resources: ["manage_settle_refund_detail"]
                    }
                }
            }
        },
        SELLER: {
            orders: {
                name: "订单管理",
                children: {
                    order: {
                        name: "订单管理",
                        resources: ["manage_order"],
                        children: {
                            order_export: {
                                name: "导出",
                                resources: ["manage_order"],
                            },
                            order_audit: {
                                name: "审核订单",
                                resources: ["manage_order"],
                            },
                            order_cancel: {
                                name: "取消订单",
                                resources: ["manage_order"],
                            },
                            order_ship: {
                                name: "发货",
                                resources: ["manage_order"],
                            },
                            order_refund_apply: {
                                name: "申请退款",
                                resources: ["manage_order"],
                            },
                            order_edit_identity: {
                                name: "修改身份证",
                                resources: ["manage_order"],
                            },
                            order_push: {
                                name: "推送",
                                resources: ["manage_order"],
                            }
                        }
                    },
                    evaluation_manage: {
                        name: "评价管理",
                        resources: ["evaluation_manage"]
                    },
                    sensitive_word: {
                        name: "敏感词配置",
                        resources: ["sensitive_word"]
                    },
                    refund: {
                        name: "售后列表",
                        resources: ["manage_refund"],
                        children: {
                            refund_export: {
                                name: "导出",
                                resources: ["manage_refund"],
                            },
                            refund_agree: {
                                name: "审核",
                                resources: ["manage_refund"],
                            },
                            /*  refund_refuse: {
                                name: "拒绝",
                                resources: ["manage_refund"],
                              },*/
                            refund_confirm: {
                                name: "退款",
                                resources: ["manage_refund"],
                            }
                        }
                    },
                    invoice_management: {
                        name: "发票管理",
                        resources: ["invoice_management"]
                    }/* ,


          review: {
            name: "评价管理",
            resources: ["manage_review"]
          }*/
                }
            },
            items: {
                name: "商品管理",
                children: {
                    item_create: {
                        name: "创建商品",
                        resources: ["create_item"]
                    },
                    item_on_shelf: {
                        name: "上下架商品",
                        resources: ["list_item"],
                        children: {
                            item_on_shelf_export: {
                                name: "导出",
                                resources: ["list_item"],
                            },
                            item_on_shelf_off: {
                                name: "下架",
                                resources: ["list_item"],
                            },
                            item_off_shelf_on: {
                                name: "上架",
                                resources: ["delist_item"],
                            },
                            item_on_shelf_edit: {
                                name: "编辑",
                                resources: ["list_item"],
                            },
                            item_on_shelf_del: {
                                name: "删除",
                                resources: ["list_item"],
                            }
                        }
                    },
                    item_setting: {
                        name: "商品配置",
                        resources: ["item_setting"]
                    },
                    /*
                    item_off_shelf: {
                      name: "下架商品",
                      resources: ["delist_item"],
                      children: {
                        item_off_shelf_export: {
                          name: "导出",
                          resources: ["delist_item"],
                        },
                        item_off_shelf_on: {
                          name: "上架",
                          resources: ["delist_item"],
                        },
                        item_off_shelf_edit: {
                          name: "编辑",
                          resources: ["delist_item"],
                        },
                        item_off_shelf_del: {
                          name: "删除",
                          resources: ["delist_item"],
                        }
                      }
                    },*/ /* ,          select_item_on_shelf_list: {
                      name: "售卖第三方商品",
                      resources: ["select_item_on_shelf_list"]
                    }*/
                    shop_category: {
                        name: "类目管理",
                        resources: ["manage_shop_category"]
                    },
                    item_sort: {
                        name: "商品分类",
                        resources: ["sort_item"]
                    },
                    good_display: {
                        name: "商品展示",
                        resources: ["good_display"]
                    },
                    third_party_stock: {
                        name: "商品库存中心",
                        resources: ["third_party_stock"]
                    },
                    address_module: {
                        name: "可售地址模版",
                        resources: ["address_module"]
                    },
                  brand_manage: {
                    name: "品牌管理",
                    resources: ["brand_manage"]
                  },
                    goods_tags: {
                        name: "商品标签",
                        resources: ["goods_tags"]
                    }/* ,
          distribution_price_list: {
            name: "微分销价管理",
            resources: ["distribution_price_list"]
          }*/
                }
            },
            /* we_distribution: {
               name: "微分销管理",
               children: {
                 distribution_shop: {
                   name: "分销店铺",
                   resources: ["distribution_shop"]
                 },
                 we_distribution_apply_audit: {
                   name: "分销申请",
                   resources: ["we_distribution_apply_audit"]
                 },
                 withdraw_deposit_apply: {
                   name: "提现申请",
                   resources: ["withdraw_deposit_apply"]
                 },
                 we_shop_set: {
                   name: "店铺设置",
                   resources: ["view_shop_adv"]
                 }
               }
             },*/
            ladderDistribution: {
                name: "分销管理",
                children: {
                    ladder_commission_rate_setting: {
                        name: "服务费管理",
                        resources: ["ladder_commission_rate_setting"]
                    },
                    service_provider_info: {
                        name: "服务商信息",
                        resources: ["service_provider_info"]
                    },
                    distribution_shop: {
                        name: "分销店铺",
                        resources: ["distribution_shop"]
                    },
                    we_distribution_apply_audit: {
                        name: "分销审核",
                        resources: ["we_distribution_apply_audit"]
                    },
                    brand_speak_audit: {/* --按钮权限命名用页面名字+按钮名字，方便判断控制按钮，也避免按钮权限重复--*/
                        name: "品牌代言审核",
                        resources: ["brand_speak_audit"],
                        children: {
                            brand_speak_audit_export: {
                                name: "导出",
                                resources: ["brand_speak_audit"],
                            },
                            brand_speak_audit_check: {
                                name: "审核",
                                resources: ["brand_speak_audit"],
                            }
                        }
                    },
                    distribution_user_list: {
                        name: "分销用户",
                        resources: ["ladder_distribution_user"],
                        children: {
                            distribution_user_list_export: {
                                name: "导出",
                                resources: ["ladder_distribution_user"],
                            }
                        }
                    },
                    shop_user_list: {
                        name: "用户管理",
                        resources: ["shop_user_list"]
                    },
                    user_tag_list: {
                        name: "用户标签",
                        resources: ["user_tag_list"]
                    }
                }
            },
            ladderDistribution_member: {
                name: "会员管理",
                children: {
                    manage_member_list: {
                        name: "会员信息",
                        resources: ["manage_member_list"],
                        children: {
                            manage_member_list_add: {
                                name: "新增",
                                resources: ["manage_member_list"],
                            },
                            manage_member_list_edit: {
                                name: "编辑",
                                resources: ["manage_member_list"],
                            },
                            manage_member_list_bady_info: {
                                name: "宝宝信息",
                                resources: ["manage_member_list"],
                            }
                        }
                    }
                }
            },
            IntegralManagement: {
                name: "积分管理",
                children: {
                    add_integral_product: {
                        name: "创建积分商品",
                        resources: ["add_integral_product"]
                    },
                    integral_products_on_shelf: {
                        name: "上架积分商品",
                        resources: ["integral_products_on_shelf"]
                    },
                    integral_products_off_shelf: {
                        name: "下架积分商品",
                        resources: ["integral_products_off_shelf"]
                    },
                    integral_order_manage: {
                        name: "积分订单管理",
                        resources: ["integral_order_manage"],
                        children: {
                            integral_order_export: {
                                name: "导出",
                                resources: ["integral_order_manage"],
                            },
                            integral_order_ship: {
                                name: "发货",
                                resources: ["integral_order_manage"],
                            }
                        }
                    },
                    integral_account_detail: {
                        name: "积分账户明细",
                        resources: ["integral_account_detail"],
                        children: {
                            integral_account_detail_edit_integral: {
                                name: "修改积分",
                                resources: ["integral_account_detail"],
                            },
                        }
                    },
                    integral_detail_record: {
                        name: "积分明细记录",
                        resources: ["integral_detail_record"]
                    },
                    shop_code_list: {
                        name: "商品码管理",
                        resources: ["shop_code_list"]
                    },
                    item_code_list: {
                        name: "历史商品码管理",
                        resources: ["history_item_code_list"]
                    },
                    integral_rule_management: {
                        name: "积分管理",
                        resources: ["integral_rule_management"]
                    },
                    /*integral_rule_set: {
                      name: "积分规则",
                      resources: ["integral_rule_set"]
                    },
                    ladderDistribution_integral_risk_mgt_rule_set: {
                      name: "积分风控规则",
                      resources: ["integral_risk_mgt_rule_set"]
                    },*/
                }
            },
          communityOperationUserManager: {
            name: "用户管理",
            children: {
              community_operation_user_list :{
                name: "用户列表",
                resources: ["community_operation_user_list"],
              }
            }
          },
            subStoreDistribution: {
                name: "门店管理",
                children: {
                    distribution_store: {
                        name: "分销门店列表",
                        resources: ["distribution_store_manage"],
                        children: {
                            distribution_store_change_store: {
                                name: "换绑门店",
                                resources: ["distribution_store_manage"],
                            },
                            distribution_store_export: {
                                name: "导出",
                                resources: ["distribution_store_manage"],
                            },
                            distribution_store_frozen: {
                                name: "冻结解冻",
                                resources: ["distribution_store_manage"],
                            }
                        }
                    },
                    store_distribution_guider: {
                        name: "导购管理",
                        resources: ["store_distribution_guider"],
                        children: {
                            store_distribution_guider_export: {
                                name: "导出",
                                resources: ["store_distribution_guider"],
                            },
                            store_distribution_guider_import: {
                                name: "导入",
                                resources: ["store_distribution_guider"],
                            },
                            store_distribution_guider_edit: {
                                name: "修改导购名称",
                                resources: ["store_distribution_guider"],
                            }
                        }
                    },
                    store_distribution_apply_audit: {
                        name: "门店分销申请",
                        resources: ["store_distribution_apply_audit"],
                        children: {
                            store_distribution_apply_audit_export: {
                                name: "导出",
                                resources: ["store_distribution_apply_audit"],
                            },
                            store_distribution_apply_audit_import: {
                                name: "导入",
                                resources: ["store_distribution_apply_audit"],
                            },
                            store_distribution_apply_audit_check: {
                                name: "审核",
                                resources: ["store_distribution_apply_audit"],
                            },
                            store_distribution_apply_audit_edit: {
                                name: "编辑",
                                resources: ["store_distribution_apply_audit"],
                            }
                        }
                    },
                    store_shop_user_list: {
                        name: "用户管理",
                        resources: ["store_shop_user_list"],
                        children: {
                            shop_user_list_set_guider: {
                                name: "设置导购",
                                resources: ["store_shop_user_list"],
                            },
                            shop_user_list_bind_store: {
                                name: "绑定门店",
                                resources: ["store_shop_user_list"],
                            }
                        }
                    },
                    store_commission_rate_setting: {
                        name: "服务费管理",
                        resources: ["store_commission_rate_setting"]
                    }
                }
            },
            finance_management: {
                name: "财务管理",
                children: {
                    withdrawal_real_name_auth: {
                        name: "提现实名认证",
                        resources: ["withdrawal_real_name_auth"]
                    },
                    withdraw_deposit_apply: {
                        name: "提现申请",
                        resources: ["withdraw_deposit_apply"],
                        children: {
                            withdraw_deposit_apply_export: {
                                name: "导出",
                                resources: ["withdraw_deposit_apply"],
                            },
                            withdraw_deposit_apply_audit: {
                                name: "审核",
                                resources: ["withdraw_deposit_apply"],
                            },
                            /* withdraw_deposit_apply_refuse: {
                               name: "拒绝",
                               resources: ["withdraw_deposit_apply"],
                             },*/
                            withdraw_deposit_apply_offline_transfer: {
                                name: "线下转账",
                                resources: ["withdraw_deposit_apply"],
                            }
                        }
                    },
                    new_withdraw_deposit_apply: {
                        name: "提现申请(新)",
                        resources: ["new_withdraw_deposit_apply"],
                    },
                    enterprise_withdrawal: {
                        name: "付汇与提现",
                        resources: ["enterprise_withdrawal"],
                    },
                    bill_query: {
                        name: "账单查询",
                        resources: ["bill_query"]
                    },
                    withdrawal_rules: {
                        name: "提现规则",
                        resources: ["withdrawal_rules"]
                    },
                    withdrawal_settings: {
                        name: "提现设置",
                        resources: ["withdrawal_settings"]
                    }
                }
            },
            member: {
                name: "会员管理",
                children: {
                    member_price: {
                        name: "会员价管理",
                        resources: ["manage_member_price"]
                    }
                }
            },
          offsite_promotion: {
            name: "站外推广",
            children: {
              event_promotion: {
                name: "营销活动管理",
                resources: ["event_promotion"]
              }
            }
            },
            marketing: {
                name: "营销管理",
                children: {
                    activity_creaet: {
                        name: "营销活动管理",
                        resources: ["create_activity"]
                    },
                  coupon_main: {
                    name: "二维码管理",
                    resources: ["coupon_main"]
                  },
                  gift_manage: {
                    name: "账号储值管理",
                    resources: ["gift_manage"]
                  },
                  marketing_tools: {
                    name: "营销工具",
                    resources: ["marketing_tools"]
                  },
                  reach_management: {
                    name: "触达管理",
                    resources: ["reach_management"]
                  },

                }
            },
            enterprise_wechat_management: {
                name: "企微管理",
                children: {
                  external_member_manage: {
                    name: "小程序客户管理",
                    resources: ["external_member_manage"]
                  },
                  external_crowd_manage: {
                    name: "企微群管理",
                    resources: ["external_crowd_manage"]
                  },
                  external_contacts_manage: {
                    name: "员工管理",
                    resources: ["external_contacts_manage"]
                  },
                  external_qrcode_manage: {
                    name: "二维码管理",
                    resources: ["external_qrcode_manage"]
                  },
                  label_setting: {
                    name: "标签设置",
                    resources: ["label_delete_set"]
                  },
                  label_community_operation_user_set: {
                    name: "标签管理",
                    resources: ["label_community_operation_user_set"]
                  },
                  enterprise_wechat_member_management: {
                    name: "会员管理",
                    resources: ["enterprise_wechat_member_management"]
                  },

                }
            },
            member_management: {
                name: "会员管理",
                children: {
                    member_list: {
                        name: "会员列表",
                        resources: ["member_list"]
                    },
                    member_level_management: {
                        name: "会员等级管理",
                        resources: ["member_level_management"]
                    }
                }
            },
            data_management: {
                name: "数据管理",
                children: {
                    user_analysis: {
                        name: "用户分析",
                        resources: ["user_analysis"]
                    },
                    transaction_analysis: {
                        name: "交易分析",
                        resources: ["transaction_analysis"]
                    }
                }
            },
            shops: {
                name: "店铺管理",
                children: {
                    /* shop_design: {
                      name: "店铺装修",
                      resources: ["manage_site_shop_design"]
                    },*/
                    sho_profile: {
                        name: "店铺详情",
                        resources: ["view_shop_profile"]
                    },
                    view_shop_attorney: {
                        name: "授权书管理",
                        resources: ["view_shop_attorney"]
                    },
                  sho_pay: {
                        name: "店铺支付信息",
                        resources: ["sho_pay"]
                    },
                    view_shop_decoration: {
                        name: "小程序装修",
                        resources: ["view_shop_decoration"]
                    },
                  view_weapp_decoration_new: {
                        name: "小程序装修-新",
                        resources: ["view_weapp_decoration_new"]
                    },
                    view_shop_clause_set: {
                        name: "条款设置",
                        resources: ["view_shop_clause_set"]
                    },
                    material_management: {
                        name: "素材管理",
                        resources: ["material_management"]
                    },
                    /*           ,
                              sho_adv: {
                                name: '店铺广告',
                                resources: ['view_shop_adv']
                              } */
                    applet_information: {
                        name: "微信小程序信息",
                        resources: ["wechat_applet_information"]
                    },
                    order_audit_rules: {
                        name: "订单审核规则",
                        resources: ["order_audit_rules"]
                    }
                }
            }, shipping: {
                name: "运费管理",
                children: {
                    manage_freight: {
                        name: "运费模版",
                        resources: ["manage_freight"]
                    }
                }
            },
            content_community: {
                name: "内容社区",
                children: {
                    live_community: {
                        name: "直播社区",
                        resources: ["live_community"]
                    },
                    content_material_library: {
                        name: "内容素材库",
                        resources: ["content_material_library"]
                    },
                    wish_list: {
                        name: "心愿单",
                        resources: ["wish_list"]
                    }
                }
            },
            auth: {
                name: "权限管理",
                children: {
                    seller_role: {
                        name: "角色管理",
                        resources: ["manage_seller_role"]
                    },
                    seller_sub: {
                        name: "子账号管理",
                        resources: ["manage_seller_sub"]
                    }
                }
            },
            log_manage: {
                name: "日志管理",
                children: {
                    operation_log: {
                        name: "操作日志",
                        resources: ["operation_log"]
                    },
                    login_log: {
                        name: "登录日志",
                        resources: ["login_log"]
                    }
                }
            },
            // others: {
            //   name: '其他管理',
            //   children: {
            //     seller_pictures: {
            //       name: '图片管理',
            //       resources: ['manage_seller_pictures']
            //     }
            //   }
            // },
            settlements: {
                name: "结算管理",
                children: {
                    order_sum: {
                        name: "订单日汇总",
                        resources: ["manage_settle_order_sum"]
                    },
                    manage_settle_data_board: {
                        name: "数据看板",
                        resources: ["manage_settle_data_board"]
                    },
                    manage_settle_mail_reception: {
                        name: "邮件接收",
                        resources: ["manage_settle_mail_reception"]
                    },
                    order_detail: {
                        name: "订单明细",
                        resources: ["manage_settle_order_detail"]
                    },
                    refund_detail: {
                        name: "退款单明细",
                        resources: ["manage_settle_refund_detail"]
                    }
                }
            }
        },
        OPERATOR: {
            sitemanager: {
                name: "站点管理",
                children: {
                    site_design: {
                        name: "站点装修管理",
                        resources: ["manage_site_design"]
                    },
                    site_shop_templet: {
                        name: "店铺模版管理",
                        resources: ["manage_site_shop_templet"]
                    },
                    site_article: {
                        name: "文章管理",
                        resources: ["manage_site_article"]
                    },
                    site_shop_design: {
                        name: "店铺装修管理",
                        resources: ["manage_site_shop_design"]
                    }
                }
            }
        }
    },
    resources: {
        manage_order: {
            requests: ["GET: /seller/order", "GET: /seller/order-detail", "GET: /seller/pre-return", "ALL: /api/.*"]
        },
        evaluation_manage:{
            requests: ["GET: /seller/evaluation-manage"]
        },
        invoice_management: {
            requests: ["GET: /seller/invoice-management"]
        },
        sensitive_word: {
            requests: ["GET: /seller/sensitive-word"]
        },
        manage_refund: {
            requests: ["GET: /seller/sku-return", "GET: /seller/sku-return-detail"]
        },
        manage_review: {
            requests: ["GET: /seller/comments", "GET: /seller/comment-detail"]
        },
        create_item: {
            requests: ["GET: /seller/release-items", "GET: /seller/item-publish"]
        },
        list_item: {
            requests: ["GET: /seller/on-shelf", "GET: /seller/item-publish"]
        },
        goods_tags: {
            requests: ["GET: /seller/goods_tags"]
        },
        delist_item: {
            requests: ["GET: /seller/off-shelf", "GET: /seller/item-publish"]
        },
        manage_shop_category: {
            requests: ["GET: /seller/shop-category"]
        },
        sort_item: {
            requests: ["GET: /seller/item-sort"]
        },
        third_party_stock: {
            requests: ["GET: /seller/third-party-stock"]
        },
        address_module: {
            requests: ["GET: /seller/address-module", "ALL: /api/.*"]
        },
        distribution_price_list: {
            requests: ["GET: /api/distributionPrice/paging"]
        },
        select_item_on_shelf_list: {
            requests: ["GET: /api/seller/items/paging"]
        },
        create_activity: {
            requests: ["GET: /seller/marketing/activity-index",
                "GET: /seller/marketing/activity-management",
                "GET: /seller/marketing/coupon-create",
                "GET: /seller/marketing/limit-create",
                "GET: /seller/marketing/discount-create"]
        },
        design_shop: {
            requests: [
                "GET: /system/sites(|/styles/.*|/scripts/.*|/fonts/.*|/images/.*)",
                "ALL: /api/design/.*",
                "ALL: /api/images/.*",
                "ALL: /design/.*",
            ]
        },
        design_mshop: {
            requests: [
                "GET: /system/sites(|/styles/.*|/scripts/.*|/fonts/.*|/images/.*)",
                "ALL: /api/design/.*",
                "ALL: /api/images/.*",
                "ALL: /design/.*",
            ]
        },
        view_shop_profile: {
            requests: ["GET: /seller/profile"]
        },
      view_shop_attorney: {
            requests: ["GET: /seller/letter-manage"]
        },
        sho_pay: {
            requests: ["GET: /seller/new-shop-pay-list"]
        },
        view_shop_decoration: {
            requests: ["GET: /seller/weapp-decoration"]
        },
        view_shop_decoration_new: {
            requests: ["GET: /seller/weapp-decoration-new"]
        },
        view_shop_clause_set: {
            requests: ["GET: /seller/store/mini-app-set"]
        },
        material_management: {
            requests: ["GET: /seller/material-management"]
        },
        wechat_applet_information: {
            requests: ["GET: /seller/wechat-applet-information"]
        },
        label_delete_set: {
            requests: ["GET: /seller/label-set"]
        },
        label_community_operation_user_set: {
            requests: ["GET: /seller/label-community-operation-user-set"]
        },
        enterprise_wechat_member_management: {
            requests: ["GET: /seller/enterprise-wechat/member-management"]
        },
        manage_freight: {
            requests: ["GET: /seller/freight"]
        },
        manage_seller_role: {
            requests: ["GET: /seller/auth", "GET: /seller/auth-create"]
        },
        manage_seller_sub: {
            requests: ["GET: /seller/accounts"]
        },
        manage_seller_pictures: {
            requests: ["GET: /seller/pictures"]
        },
        manage_settle_order_sum: {
            requests: ["GET: /seller/settlement/order-sum"]
        },
        manage_settle_data_board: {
            requests: ["GET: /seller/settlement/data-board"]
        },
        manage_settle_mail_reception: {
            requests: ["GET: /seller/settlement/mail-reception"]
        },
        manage_settle_order_detail: {
            requests: ["GET: /seller/settlement/order-detail", "GET: /seller/settlement/order-detail-info"]
        },
        manage_settle_refund_detail: {
            requests: ["GET: /seller/settlement/refund-detail"]
        },
        manage_site_design: {
            requests: [
                "GET: /system/sites(|/styles/.*|/scripts/.*|/fonts/.*|/images/.*)",
                "ALL: /api/design/.*",
                "ALL: /api/images/.*",
                "ALL: /design/.*",
            ]
        },
        manage_site_shop_templet: {
            requests: [
                "GET: /system/sites(|/styles/.*|/scripts/.*|/fonts/.*|/images/.*)",
                "ALL: /api/design/.*",
                "ALL: /api/images/.*",
                "ALL: /design/.*",
            ]
        },
        manage_site_article: {
            requests: [
                "GET: /system/sites(|/styles/.*|/scripts/.*|/fonts/.*|/images/.*)",
                "ALL: /api/design/.*",
                "ALL: /api/images/.*",
                "ALL: /design/.*",
            ]
        },
        manage_site_shop_design: {
            requests: [
                "GET: /system/sites(|/styles/.*|/scripts/.*|/fonts/.*|/images/.*)",
                "ALL: /api/design/.*",
                "ALL: /api/images/.*",
                "ALL: /design/.*",
            ]
        },
        distribution_shop: {
            requests: ["GET: /seller/distribution-shop-manage", "GET: /api/weShop/paging", "PUT: /api/weDistributionApplication/seller/\\d+/agree", "PUT:/api/weDistributionApplication/seller/\\d+/reject"]
        },
        profit_change_record: {
            requests: ["GET: /api/weShop/profitChangeRecord/paging"]
        },
        we_distribution_apply_audit: {
            requests: ["GET: /seller/we-distribution-apply-audit", "GET: /api/weDistributionApplication/paging", "PUT: /api/weDistributionApplication/seller/\\d+/agree", "PUT:/api/weDistributionApplication/seller/\\d+/reject"]
        },
        withdraw_deposit_apply: {
            requests: ["GET: /seller/store/store_withdraw-deposit-apply",
                "GET: /api/weWithdrawDetail/seller/paging",
                "PUT: /api/weWithdrawDetail/seller/\\d+/agree",
                "PUT:/api/weWithdrawDetail/seller/\\d+/reject",
                "GET: /seller/store/store-new-withdraw-deposit-apply",
                "GET: /api/withdrawProfitApply/findPage",
                "GET: /seller/store/bill-query",
                "GET: /api/accountStatement/findPage",
                "GET: /seller/store/withdrawal-settings",
                "GET: /api/withdrawProfitApply/findWithdrawPeriod",
                "GET: /seller/store/enterprise-withdrawal",
                "GET: /seller/store/enterprise-withdrawal-record"
            ]
        },
        new_withdraw_deposit_apply: {
            requests: ["GET: /seller/store/store-new-withdraw-deposit-apply", "GET: /api/withdrawProfitApply/findPage"]
        },
        enterprise_withdrawal: {
            requests: ["GET: /seller/store/enterprise-withdrawal", "GET: /api/enterpriseWithdraw/waitingWithdrawOrder/find"]
        },
        bill_query: {
            requests: ["GET: /seller/store/bill-query", "GET: /api/accountStatement/findPage"]
        },
        withdrawal_settings: {
            requests: ["GET: /seller/store/withdrawal-settings", "GET: /api/withdrawProfitApply/findWithdrawPeriod"]
        },
        withdrawal_rules: {
            requests: ["GET: /seller/store/withdrawal-rules", "ALL: /api/.*"]
        },
        withdrawal_real_name_auth: {
            requests: ["GET: /seller/store/withdrawal-real-name-auth", "ALL: /api/.*"]
        },
        manage_member_price: {
            requests: ["GET: /api/membershipPrice/shop", "PUT: /api/membershipPrice/\\d+", "GET:/api/membershipPrice/\\d+"]
        },
        view_shop_adv: {
            requests: ["GET: /seller/advert-position-list"]
        },
        store_distribution: {
            requests: [
                "ALL: /api/subStore/.*"
            ]
        },
        distribution_store_manage: {
            requests: ["GET: /seller/store/distribution-store-manage"]
        },
        store_distribution_guider: {
            requests: ["GET: /seller/store/store-guider-list"]
        },
        store_distribution_apply_audit: {
            requests: ["GET: /seller/store/store_distribution-apply-audit"]
        },
        store_commission_rate_setting: {
            requests: ["GET: /seller/store/setting-commission-rate"]
        },
        ladder_commission_rate_setting: {
            requests: ["GET: /seller/ladder-distribution/setting-commission-rate", "GET: /seller/ladder-distribution/new-customer-cms-set", "ALL: /api/.*"]
        },
        service_provider_info: {
            requests: ["GET: /seller/store/service-provider-info", "GET: /seller/ladder-distribution/setting-commission-rate", "GET: /seller/ladder-distribution/new-customer-cms-set", "ALL: /api/.*"]
        },
        brand_speak_audit: {
            requests: ["GET: /api/subStore/service-provider/paging", "ALL: /api/.*"]
        },
        shop_user_list: {
            requests: ["GET: /seller/users", "ALL: /api/.*"]
        },
        store_shop_user_list: {
            requests: ["GET: /seller/users", "ALL: /api/.*"]
        },
        user_tag_list: {
            requests: ["GET: /seller/user-tags", "ALL: /api/.*"]
        },
        ladder_distribution_user: {
            requests: ["GET: /seller/ladder-distribution/distribution-user-list", "GET: /seller/ladder-distribution/distribution-subordinate-user-list"]
        },
        integral_detail_record: {
            requests: ["GET: /seller/ladder-distribution/integral-detail-record", "ALL: /api/.*"]
        },
        shop_code_list: {
            requests: ["GET: /seller/ladder-distribution/shop-code-list", "ALL: /api/.*"]
        },
        history_item_code_list: {
            requests: ["GET: /seller/ladder-distribution/history-item-code-list", "ALL: /api/.*"]
        },
        integral_account_detail: {
            requests: ["GET: /seller/ladder-distribution/integral-account-detail", "GET: /seller/ladder-distribution/integral-record", "GET: /seller/ladder-distribution/exchange-history", "ALL: /api/.*"]
        },
        integral_rule_set: {
            requests: ["GET: /seller/ladder-distribution/integral-rule-set", "ALL: /api/.*"]
        },
        integral_rule_management: {
            requests: ["GET: /seller/ladder-distribution/integral-rule-management", "GET: /seller/ladder-distribution/.*", "ALL: /api/.*"]
        },
        integral_risk_mgt_rule_set: {
            requests: ["GET: /seller/ladder-distribution/integral-risk-mgt-rule-set", "ALL: /api/.*"]
        },
        add_integral_product: {
            requests: ["GET: /seller/ladder-distribution/release-items", "ALL: /api/.*"]
        },
        integral_products_on_shelf: {
            requests: ["GET: /seller/ladder-distribution/on-shelf", "ALL: /api/.*"]
        },
        integral_products_off_shelf: {
            requests: ["GET: /seller/ladder-distribution/off-shelf", "ALL: /api/.*"]
        },
        integral_order_manage: {
            requests: ["GET: /seller/ladder-distribution/order", "GET: /seller/ladder-distribution/order-detail", "ALL: /api/.*"]
        },
        operation_log: {
            requests: ["GET: /seller/log-manage/operation-log"]
        },
        login_log: {
            requests: ["GET: /seller/log-manage/ligin-log"]
        },
        manage_member_list: {
            requests: ["GET: /seller/member/member-manage", "GET: /seller/member/baby-list", "GET: /seller/member/integral-record", "GET: /seller/member/exchange-history", "GET: /seller/member/member-info-edit", "ALL: /api/.*"]
        },
        history_item_code_list_detail: {
            requests: ["GET: /seller/ladder-distribution/history-item-code-list", "GET: /seller/ladder-distribution/history-item-code-list-detail"]
        },
        order_audit_rules: {
            requests: ["GET: /seller/set-order-audit-rules"]
        },
        good_display: {
            requests: ["GET: /seller/item-display"]
        },
         external_member_manage:{
           requests: ["GET: /seller/marketing/member-manage"]
        },
      external_crowd_manage:{
        requests: ["GET: /seller/marketing/external-crowd-manage"]
      },
      external_contacts_manage:{
        requests: ["GET: /seller/marketing/external-contacts-manage"]
      },
      external_qrcode_manage:{
        requests: ["GET: /seller/marketing/qrcode-manage"]
      },
      coupon_main:{
        requests: ["GET: /seller/marketing/coupon-main"]
      },
      gift_manage:{
        requests: ["GET: /seller/gift-manage"]
      },
      marketing_tools:{
        requests: ["GET: /seller/marketing/marketing-tools"]
      },
      brand_manage:{
          requests: ["GET: /seller/brand-manage"]
      },
      community_operation_user_list:{
        requests: ["GET: /seller/users"]
      },
      event_promotion:{
        requests: ["GET: /seller/offsite-promotion-activity"]
        },
      view_weapp_decoration_new:{
        requests: ["GET: /seller/design-new"]
      },
      member_list:{
        requests: ["GET: /seller/member-list"]
      },
      member_level_management:{
        requests: ["GET: /seller/member-level-management"]
      },
      reach_management:{
        requests: ["GET: /seller/marketing/reach-management"]
      },
      user_analysis:{
        requests: ["GET: /seller/data/user-analysis"]
      },
      transaction_analysis:{
        requests: ["GET: /seller/data/transaction-analysis"]
      },
      item_setting: {
        requests: ["GET: /seller/item-setting"]
      }

    }
}

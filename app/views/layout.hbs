<!DOCTYPE html>
<html lang="zh-CN">
<!-- Powered by Terminus.io -->
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  {{!-- {{#block "meta"}}
  <title>{{#if seo.title}}{{seo.title}}{{else}}{{#block "title"}}{{/block}}{{/if}}</title>--}}
  <title>跨境电商,保税仓,挑选进口商品,分享全球好货,正品保税仓,海外代购专家,高品质进口商品,品牌方,跨境保税服务,国际买手达人,专业的国际买手服务商</title>
  {{!--<meta name="keywords" content="{{seo.keywords}}">
 <meta name="description" content="{{seo.description}}">--}}
  <meta name="keywords" content="跨境电商,保税仓,挑选进口商品,分享全球好货,正品保税仓,海外代购专家,高品质进口商品,品牌方,跨境保税服务,国际买手达人,专业的国际买手服务商">
  <meta name="description" content="一家专注于跨境电商保税仓的服务商，职责是做您的正品保税仓，您只需负责挑选进口商品和分享全球好货，我拥有强大的商品、物流团队，让您安心做海外代购专家。洋800拥有各种高品质的进口商品和品牌方，多年来为各大平台提供优质的跨境保税服务，让洋800可以更从容的为您服务，为您带来全新的跨境电商体验，让您瞬间成为国际买手达人。洋800，专业的国际买手服务商。">
  {{!-- {{/block}} --}}
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="/assets/images/other-images/favicon.ico" type="image/x-icon" rel="shortcut icon">
  <meta name="baidu-site-verification" content="P7Xr6eyWnw" />
  <!-- pokeball iconfont ,修改请找 -->
  <link rel="stylesheet" href="//at.alicdn.com/t/font_1468400228_1677577.css" />
  <!-- feebas 前台的iconfont，请修改这个链接 -->
  <link rel="stylesheet" href="//at.alicdn.com/t/font_kn0nyurl0zjb57b9.css" />
  <link rel="stylesheet" href="//at.alicdn.com/t/c/font_2869305_s6jlttxasoh.css" />
  <link rel="stylesheet" href="/assets/styles/eevee-layout.css">
  <link rel="stylesheet" href="/assets/styles/vendor.css">
  <link rel="stylesheet" href="/assets/styles/app-back.css">
  <!--    <script>
      !function(e,t,n,s,a,c,i){e[a]=e[a]||function(){(e[a].q=e[a].q||[]).push(arguments)},c=t.createElement(n),i=t.getElementsByTagName(n)[0],c.async=1,c.src=s,i.parentNode.insertBefore(c,i)}(window,document,"script","//static.terminus.io/ta.js","$ta");
      $ta('start',{ udata: { uid: {{#if _USER_}}{{_USER_.id}}{{else}}0{{/if}} }{{#if _TERMINUS_KEY_}}, ak: '{{_TERMINUS_KEY_}}'{{/if}} })
    </script>-->
  {{!-- <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script> --}}
  <script src="https://dcdn.yang800.com/lib/tinymce/tinymce.min.js" referrerpolicy="origin"></script>

</head>
<body data-base="{{_HREF_.base}}" data-user-type="{{_USER_.type}}" class="{{block "bodyClass"}}">
{{#block "designable"}}{{/block}}
<!--[if lt IE 7]>
<p class="browsehappy">您正使用<strong>过时</strong>的浏览器，因此您可能体验不到最好的效果,请点击<a href="http://browsehappy.com/">这里</a> 去升级您的浏览器</p>
<![endif]-->
{{> tool}}
{{#block "body"}}
{{/block}}
<script src="https://dcdn.yang800.com/lib/axios/1.2.1/axios.min.js"></script>
<script src="/assets/scripts/vendor.js"></script>
<!--[if lt IE 9]>
<script src="http://cdn.staticfile.org/jquery-placeholder/2.0.8/jquery.placeholder.js"></script>
<![endif]-->
<script src="/assets/scripts/templates-back.js"></script>
<script src="/assets/scripts/app-back.js"></script>
<script>
  moment.locale("zh-cn");
  require("common/react/utils/lib");
    {{i18nEnv}}
  i18nIniter({
    cache: { enable: false },
    resourceFormat: 'json',
    callback: function () {
      require("app")();
    }
  });
</script>
<!-- <script src="https://cdn.bootcss.com/jquery/1.10.2/jquery.min.js"></script>-->
<script>
  $(function () {
    let desc;
    let reg = /^.*.yang800.*/;
    if (reg.test(window.location.hostname)){
      $('title').html("洋800,跨境电商,保税仓,挑选进口商品,分享全球好货,正品保税仓,海外代购专家,高品质进口商品,品牌方,跨境保税服务,国际买手达人,专业的国际买手服务商");
      desc = "洋800,跨境电商,保税仓,挑选进口商品,分享全球好货,正品保税仓,海外代购专家,高品质进口商品,品牌方,跨境保税服务,国际买手达人,专业的国际买手服务商";
      $("meta[name='description']").attr('content', desc);
      $("meta[name='keywords']").attr('content', desc);
    }
     reg=/(^webdev.parana.yang800.cn*$)|(^.*bellamy.shop$)/;
    if(reg.test(window.location.hostname)) {
      $('title').html("贝拉米商城");
      desc = "贝拉米商城";
      $("meta[name='description']").attr('content', desc);
      $("meta[name='keywords']").attr('content', desc);
    }
    reg=/(^webdev.parana.yang800.cn$)|(^.*youshuntong.cn$)/;
    if(reg.test(window.location.hostname)) {
      $('title')
        .html("邮瞬通");
      desc = "邮瞬通商城平台";
      $("meta[name='description']")
        .attr('content', desc);
      $("meta[name='keywords']")
        .attr('content', desc);
    }
    reg=/(^webdev.parana.yang800.cn$)|(^.*aolipet.com$)/;
    if(reg.test(window.location.hostname)) {
      $('title')
              .html("奥利派");
      desc = "奥利派商城平台";
      $("meta[name='description']")
              .attr('content', desc);
      $("meta[name='keywords']")
              .attr('content', desc);
    }
  })
</script>
{{#block "tool"}}{{/block}}
</body>
<!-- Powered by Terminus.io -->
</html>

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{title}}</title>
    <meta name="description" content="{{description}}">
    <meta name="keywords" content="{{keywords}}" />
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="stylesheet" href="/assets/styles/vendor.css">
    <link rel="stylesheet" href="/assets/styles/eevee-layout.css">
    {{#if _DESIGN_MODE_}}
      <link rel="stylesheet" href="/assets/fonts/iconfont.css" />
      <link rel="stylesheet" href="/assets/styles/eevee-editing.css">
      <link rel="stylesheet" href="/assets/styles/eevee-components-vendor.css">
    {{/if}}
    <!-- pokeball iconfont ,修改请找端点王凯凯 -->
    <link rel="stylesheet" href="//at.alicdn.com/t/font_1466485617_047616.css" />
    <!-- feebas 前台的iconfont，请修改这个链接 -->
    <link rel="stylesheet" href="//at.alicdn.com/t/font_kn0nyurl0zjb57b9.css" />
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_2869305_s6jlttxasoh.css" />
    <link rel="stylesheet" href="/assets/styles/app-front.css" />
    <script src="https://dcdn.yang800.com/lib/axios/1.2.1/axios.min.js"></script>
    <!--   <script>
      !function(e,t,n,s,a,c,i){e[a]=e[a]||function(){(e[a].q=e[a].q||[]).push(arguments)},c=t.createElement(n),i=t.getElementsByTagName(n)[0],c.async=1,c.src=s,i.parentNode.insertBefore(c,i)}(window,document,"script","//static.terminus.io/ta.js","$ta");
      $ta('start',{ udata: { uid: {{#if _USER_}}{{_USER_.id}}{{else}}0{{/if}} }{{#if _TERMINUS_KEY_}}, ak: '{{_TERMINUS_KEY_}}'{{/if}} })
    </script>-->
  </head>
  <body data-design="{{_DESIGN_MODE_}}" style="background-color: #ffffff;">
    {{> tool}}
    {{{block "body"}}}
    <script src="/assets/scripts/vendor.js"></script>
    <script src="/assets/scripts/templates-front.js"></script>
    <script src="/assets/scripts/app-front.js"></script>
<!-- 注释客服   <script src="https://qiyukf.com/script/6ca792e0c81e77d5c65db9893f288e3c.js" defer async></script>-->
    <script>
      $(function () {
        // 修改版权
        let copeRight=$(".eve-page-part p:last")[0].innerHTML;
        let reg=/(^.*.yangmaigu.com$)/;
        if(reg.test(window.location.hostname))
        {
          copeRight=copeRight.replace('浙ICP备17034299号-1','浙ICP备19020738号-1').replace('杭州','金华')

        }
        reg=/(^.*.youshuntong.cn$)/;
        if(reg.test(window.location.hostname))
        {
          copeRight=copeRight.replace('浙ICP备17034299号-1','粤ICP备18119769号-1').replace('杭州但丁云科技有限公司','广州顺晋供应链管理有限公司').replace('浙B2-20180827','粤B2-20191551')
        }
        reg=/(^webdev.parana.yang800.cn$)|(^.*aolipet.com$)/;
        if(reg.test(window.location.hostname))
        {
          copeRight=copeRight.replace('浙ICP备17034299号-1','浙ICP备20019926号').replace('杭州但丁云科技有限公司','浙江奥利派科技有限公司').replace('浙B2-20180827','').replace('增值业务电信经营许可证','')
        }
        $(".eve-page-part")[2].innerHTML="<p style='text-align: center; font-size: 14px;  height: 146px;background: #F4F4F4;color: #373737;font-family: 微软雅黑,Microsoft Yahei;letter-spacing: .5px;border-top: 1px solid #c9c9c9;'>"+copeRight+"</p>";

      })
      {{i18nEnv}}
      i18nIniter({
        cache: { enable: false },
        resourceFormat: 'json',
        callback: function() {
          require("app")()
        }
      });
    </script>
    <span><script type="text/javascript">var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_1274687325'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "s19.cnzz.com/stat.php%3Fid%3D1274687325%26show%3Dpic' type='text/javascript'%3E%3C/script%3E"));</script></span>
  </body>
</html>

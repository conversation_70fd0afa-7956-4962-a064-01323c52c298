{{#partial "realTitle"}}会员列表{{/partial}}
{{#partial "realBody"}}
  {{#inject "common/breadcrumb"}}{"_DATA_": {"breadcrumb": [{"url": "#", "crumb": "会员管理"},{"url": "#", "crumb": "会员列表"}]}}{{/inject}}
  <div class="member-list-container">
    <div class="page-header">
      <h2>会员列表</h2>
      <p>查看和管理所有会员信息，了解会员活跃度和消费情况</p>
    </div>
    
    <div class="content-placeholder">
      <div class="empty-state">
        <div class="empty-icon">👥</div>
        <h3>会员列表功能开发中</h3>
        <p>此页面正在开发中，敬请期待...</p>
      </div>
    </div>
  </div>
  
  <style>
    .member-list-container {
      padding: 20px;
      background: #fff;
      min-height: 500px;
    }
    
    .page-header {
      margin-bottom: 30px;
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 20px;
    }
    
    .page-header h2 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 24px;
    }
    
    .page-header p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
    
    .content-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }
    
    .empty-state {
      text-align: center;
    }
    
    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }
    
    .empty-state h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 18px;
    }
    
    .empty-state p {
      margin: 0;
      color: #999;
      font-size: 14px;
    }
  </style>
{{/partial}}
{{> seller/layout}}

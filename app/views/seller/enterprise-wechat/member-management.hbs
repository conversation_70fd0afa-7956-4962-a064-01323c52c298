{{#partial "realTitle"}}会员管理{{/partial}}
{{#partial "realBody"}}
  {{#inject "common/breadcrumb"}}{"_DATA_": {"breadcrumb": [{"url": "#", "crumb": "用户管理"},{"url": "#", "crumb": "会员管理"}]}}{{/inject}}
  <div class="enterprise-wechat-member-management-container">
    <div class="page-header">
      <h2>会员管理</h2>
    </div>

    <div class="content-placeholder">
      <div class="empty-state">
        <div class="empty-icon">👥</div>
        <h3>会员管理功能开发中</h3>
      </div>
    </div>
  </div>

  <style>
    .enterprise-wechat-member-management-container {
      padding: 20px;
      background: #fff;
      min-height: 600px;
    }

    .page-header {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #e8e8e8;
    }

    .page-header h2 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 24px;
      font-weight: 500;
    }

    .page-header p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    .content-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }

    .empty-state {
      text-align: center;
      max-width: 500px;
    }

    .empty-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.6;
    }

    .empty-state h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 20px;
      font-weight: 500;
    }

    .empty-state p {
      margin: 0 0 30px 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }

    .feature-preview {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 6px;
      text-align: left;
    }

    .feature-preview h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }

    .feature-preview ul {
      margin: 0;
      padding-left: 20px;
      color: #666;
    }

    .feature-preview li {
      margin-bottom: 8px;
      font-size: 14px;
    }
  </style>
{{/partial}}
{{> seller/layout}}

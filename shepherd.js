const fs = require("fs-extra")
const i18nlint = require("./lib/shepherd/i18nlint")
const priority = require("./lib/shepherd/priority")
const yaml2json = require("./lib/shepherd/yaml2json")
const backyaml = require("./lib/shepherd/backyaml")
const _concat = require("./lib/shepherd/concat")
const i18n = require("./lib/shepherd/i18n")
const colorMigration = require("./lib/shepherd/colorMigration")
const repleaceExpression = require("./lib/shepherd/repleace-expression")
const argv = require("minimist")(process.argv.slice(2))
console.log("argv", argv)

process.settings = {
    paths: {
        app: "app",
        test: "test",
        vendor: "vendor",
        public: "public"
    },
    bases: [
        "app/{components_vendor,components}/**/images/other-images/",
        "app/{components_vendor,components}/**/images/",
        "app/images/other-images/",
        "app/styles/",
        "app/components/items/styles/",
        "app/components/common/styles/",
        "app/components/shop/styles/",
        "app/components/settlement/styles/",
        "app/components/design/styles/",
        "app/components/user/styles/",
        "app/components/trade/styles/",
        "app/components/pay/styles/",
        "app/components_vendor/items/styles/",
        "app/components_vendor/common/styles/",
        "app/components_vendor/shop/styles/",
        "app/components_vendor/settlement/styles/",
        "app/components_vendor/design/styles/",
        "app/components_vendor/user/styles/",
        "app/components_vendor/trade/styles/",
        "app/components_vendor/pay/styles/",
        "app/components/",
        "app/components_vendor/",
        "app/components_eevee/",
        "vendor/eevee/",
        "vendor/ta/",
        "vendor/",
        "app/*/",
        "{app,vendor}/{,eevee/,views/eevee/,*/}",
        "./node_modules/@terminus/eevee/dist/components/eevee/",
        "./node_modules/@terminus/eevee/dist/",
        "public/",
        "./",
    ],
    scriptsOrder: [
        "vendor/require.js",
        "vendor/advanced.js",
        "vendor/es5-shim.js",
        // 会乱码暂注释"vendor/es5-sham.js",
        "vendor/pokeball.js",
        "vendor/react.js",
        "vendor/react-dom.js",
        "vendor/react-is.js",
        "vendor/moment-with-locales.js",
        "vendor/cropper.js",
        "vendor/antd.js",
        "vendor/ant-design-icons.js",
        "vendor/Intl.js",
        "vendor/mobx.js",
        "vendor/mobx-react-lite.js",
        "vendor/dt.util.js",
        "vendor/dt.components.js",
        "vendor/dt.dnd.js",
        "vendor/qrcode.js",
        "...",
        "app/scripts/app.coffee"
    ],
    stylesOrder: [
        "vendor/pokeball.scss",
        "vendor/antd.sss",
        "vendor/dt.components.css",
        "vendor/cropper.min.css",
        "..."
    ],
    bundles: [
        {
            name: "pokeball.js",
            version: "master",
            url: "http://registry.terminus.io/packages/pokeball/2.0.10/pokeball.js"
        }, {
            name: "pokeball.scss",
            version: "master",
            url: "http://registry.terminus.io/packages/pokeball/2.0.10/pokeball.scss"
        }, {
            name: "../app/styles/pokeball/_variables.scss",
            version: "master",
            url: "http://registry.terminus.io/packages/pokeball/2.0.10/variables.scss"
        }, {
            name: "components.tar.gz",
            version: "master",
            url: "http://registry.terminus.io/packages/parana-front-base/1.1.5/components.tar.gz"
        }, {
            name: "i18n-frontend.js",
            version: "master",
            url: "http://registry.terminus.io/packages/i18n-frontend/0.1.5/browser.js"
        }, {
            name: "i18n-helpers.js",
            version: "master",
            url: "http://registry.terminus.io/packages/i18n-helpers/0.1.0/index.js"
        }, {
            name: "jquery.lazyload.js",
            version: "1.9.3",
            url: "http://registry.terminus.io/packages/jquery.lazyload.js/1.9.3/jquery.lazyload.js"
        }, {
            name: "wysihtml5.js",
            version: "0.3.0",
            url: "http://registry.terminus.io/packages/wysihtml5/0.3.0/wysihtml5.js"
        }, {
            name: "advanced.js",
            version: "0.5.2",
            url: "http://registry.terminus.io/packages/wysihtml5/0.5.2/wysihtml5-advanced.js"
        }, {
            name: "es5-shim.js",
            version: "4.5.12",
            url: "https://dcdn.yang800.com/lib/es5-shim/4.5.12/es5-shim.min.js"
        }, /* , {
      name: "es5-sham.js",
      version: "4.5.9",
      url: "http://cdnjs.cloudflare.com/ajax/libs/es5-shim/4.5.9/es5-sham.js"
    }*/ {
            name: "require.js",
            version: "1.0.0",
            url: "http://registry.terminus.io/packages/require_definition/1.0.0/require.js"
        }, {
            name: "ta/ta.js",
            version: "master",
            url: "http://registry.terminus.io/packages/ta/0.1.0/ta-min.js"
        },
        {
            name: "react.js",
            version: "16.9.0",
            url: "https://dcdn.yang800.com/lib/react/16.14.0/umd/react.production.min.js"
        },
        {
            name: "react-dom.js",
            version: "16.9.0",
            url: "https://dcdn.yang800.com/lib/react-dom/16.14.0/umd/react-dom.production.min.js"
        },
        {
            name: "react-is.js",
            version: "16.9.0",
            url: "https://dcdn.yang800.com/lib/react-is/16.9.0/umd/react-is.production.min.js"
        },
        {
            name: "antd.js",
            version: "4.24.8",
            url: "https://dcdn.yang800.com/lib/antd/4.24.8/antd-with-locales.min.js"
        },
        {
            name: "ant-design-icons.js",
            version: "4.7.0",
            url: "https://dcdn.yang800.com/lib/ant-design-icons/4.7.0/index.umd.min.js"
        },

        {
            name: "antd.css",
            version: "4.24.8",
            url: "https://dcdn.yang800.com/lib/antd/4.24.8/antd.variable.min.css"
        },
        {
            name: "echarts.js",
            version: "5.2.2",
            url: "https://daita-front.oss-cn-shanghai.aliyuncs.com/lib/echarts/5.2.2/echarts.min.js"
        },
        {
            name: "Intl.js",
            version: "1.2.5",
            url: "https://dcdn.yang800.com/lib/intl/1.2.5/intl.js"

        },
        {
            name: "mobx.js",
            version: "6.13.5",
            url: "https://dcdn.yang800.com/lib/mobx/6.13.5/dist/mobx.umd.production.min.js"
        },
        {
            name: "mobx-react-lite.js",
            version: "3.4.3",
            //  url: "https://dcdn.yang800.com/lib/mobx-react-lite/4.0.7/mobxreactlite.umd.production.min.js"
            url: "https://dcdn.yang800.com/lib/mobx-react-lite/3.4.3/mobxreactlite.umd.production.min.js"
        },
        {
            name: "dt.util.js",
            version: "2.0.18-alpha.79",
            url: "https://daita-front.oss-cn-shanghai.aliyuncs.com/lib/%40dt/util/2.0.18-alpha.79/dt.util.umd.production.js"
        },
        {
            name: "dt.networks.js",
            version: "2.0.18-alpha.84",
            url: "https://dcdn.yang800.com/lib/%40dt/networks/2.0.18-alpha.84/dt.networks.umd.production.js"
        },
        {
            name: "dt.hooks.js",
            version: "2.0.18-alpha.84",
            url: "https://dcdn.yang800.com/lib/%40dt/hooks/2.0.18-alpha.84/dt.hooks.umd.production.js"
        },
        {
            name: "dt.components.js",
            version: "2.0.18-alpha.84",
            url: "https://daita-front.oss-cn-shanghai.aliyuncs.com/lib/%40dt/components/2.0.18-alpha.84/dt.components.umd.production.js"

        },
        {
            name: "dt.components.css",
            version: "2.0.18-alpha.84",
            url: "https://daita-front.oss-cn-shanghai.aliyuncs.com/lib/%40dt/components/2.0.18-alpha.84/dt.components.css"
        },
        {
            name: "dt.dnd.js",
            version: "2.0.18-alpha.78",
            url: "https://daita-front.oss-cn-shanghai.aliyuncs.com/lib/%40dt/dnd/2.0.18-alpha.84/dt.dnd.umd.production.js"

        },
         {
            name: "cropper.js",
            version: "1.6.2",
            url: "https://dcdn.yang800.com/lib/cropper/1.6.2/cropper.min.js"
        },
        {
            name: "cropper.css",
            version: "1.6.2",
            url: "https://dcdn.yang800.com/lib/cropper/1.6.2/cropper.min.css"
        },
        {
            name: "moment-with-locales.js",
            version: "2.24.0",
            url: "https://momentjs.cn/downloads/moment-with-locales.js"

        },
        {
            name: "qrcode.js",
            version: "1.8.0",
            url: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/50255651664.js"
        },
        {
            name: "decimal.js",
            version: "10.5.0",
            url: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/39696192721.js"
        }
    ]
}

module.exports = (shepherd) => {
    const log = shepherd.log
    log.debug("进入shepherd.js的exports")
    const { coffee, compass, copy, concat, cmd, babel, sprite, handlebars, jst, rev, uglifier, cleancss } = shepherd.chains
    const { repo, targz } = shepherd.plugins

    const copyResource = (src, dest) => shepherd.src(src)
        .then(copy(dest))
        .then(shepherd.dest())

    const locale = (src, dest) => shepherd.src(src)
        .then(priority({ priority: ["app/files/", "app/components_vendor/", "app/components/"], override: false }))
        .then(i18n({}, shepherd))
        .then(concat(dest))
        .then(shepherd.dest())

    const config = (src, dest) => shepherd.src(src)
        .then(priority({ priority: ["app/files/", "app/components_vendor/", "app/components/"], override: false }))
        .then(concat(dest))
        .then(yaml2json())
        .then(shepherd.dest())

    const styles = (src, dest) => shepherd.src(src)
        .then(compass())
        .then(concat(dest, { order: process.settings.stylesOrder }))
        .then(cleancss())
        .then(shepherd.dest())

    const scripts = (src, dest) => shepherd.src(src)
        .then(priority())
        .then(coffee())
        .then(babel({
            perferSyntax: [".babel", ".es", ".jsx", ".es6"],
            compileOptions: {
                presets: ["es2015", "react"],
                plugins: [
                    "transform-object-rest-spread",
                    "transform-class-properties",
                    "transform-es2015-modules-commonjs",
                    "transform-es2015-destructuring",
                    "transform-es2015-parameters",
                    "transform-es2015-template-literals",
                    "transform-es2015-spread",
                    "transform-es2015-block-scoping",
                    ["transform-define", {
                        "process.env.BUILD_ENV": argv.e,
                    }],
                ]
            }
        }))
        .then(cmd())
        .then(_concat(dest, { order: process.settings.scriptsOrder }))
        .then(uglifier({
            compileOptions: {
                compress: { screw_ie8: false },
                output: { ascii_only: true, screw_ie8: false },
                mangle: { screw_ie8: false }
            }
        }))
        .then(shepherd.dest())

    const templates = (src, dest) => shepherd.src(src)
        .then(priority())
        .then(handlebars())
        .then(concat(dest))
        .then(jst())
        .then(uglifier({ compileOptions: { compress: { screw_ie8: false }, output: { ascii_only: true, screw_ie8: false }, mangle: { screw_ie8: false } } }))
        .then(shepherd.dest())

    const srcPath = {
        frontScriptPath: "app/{{scripts,components_eevee},{components,components_vendor}/{common,design,shop_design,member_design,utils}}/**/{!(config), *}.{js,coffee,jsx,es6,es}",
        backScriptPath: "app/{scripts,{components,components_vendor,components_eevee}/{shop_design/item_detail,design/lottery_*,!(design|shop_design), *}}/**/{!(config), *}.{js,coffee,jsx,es6,es}",
        frontTemplatePath: "app/{components,components_vendor}/{common,design,shop_design,member_design,utils}/**/{frontend_templates,all_templates}/*.hbs",
        backTemplatePath: "app/{components,components_vendor}/{design/lottery_*,!(design|shop_design|member_design), *}/**/{frontend_templates,all_templates}/*.hbs",
        frontStylePath: "app/{{styles,components_eevee},{components,components_vendor}/{common,design,shop_design,member_design,utils}}/**/[a-z]*.{css,scss,sass}",
        backStylePath: "app/{styles,{components,components_vendor,components_eevee}/{shop_design/item_detail,design/lottery_*,!(design|shop_design), *}}/**/[a-z]*.{css,scss,sass}",
        vendorScriptPath: "vendor/*.js",
        configScriptPath: "app/{scripts,components,components_vendor,components_eevee}/{common,design,shop_design,member_design,eevee}/**/config.{js,coffee,es6}",
        designAssetPath: "vendor/eevee/{scripts,styles,fonts}/*",
        designViewPath: "vendor/eevee/views/**/*",
        templatePath: "{app/views/**/templates/*.hbs,app/{components,components_vendor}/**/{frontend_templates,all_templates}/*.hbs}",
        spritePath: "app/{images,components,components_vendor}/**/images/*.png",
        imagePath: "app/{images,components,components_vendor}/**/other-images/*.{png,jpg,gif,ico,htc,svg,ttf,xls}",
        vendorStylePath: "vendor/[a-z]*.{css,scss,sass}",
        componentPath: "{app/{components_eevee,components_vendor,components}/**/{view.hbs,thumbnail.png},app/{components_eevee,components_vendor,components}/**/{backend_templates,all_templates}/*.hbs}",
        viewPath: "app/views/**/*.hbs",
        configPath: "./{{ecosystem,package}.json,{Pampasfile,shepherd}.js}",
        filePath: "app/files/{front_href,render_config,auth-config,MP_verify_g4NzNz3C4lLqFmua,MP_verify_G8Ymb1QRHbSLzzem,Hkd92WwtjP}.{js,yaml,txt}",
        frontConfigPath: "{app/files/front_config.yaml,app/{components,components_vendor}/**/front_config.yaml}",
        backConfigPath: "{app/files/back_config.yaml,app/{components,components_vendor}/**/back_config.yaml}",
        frontMappingPath: "app/files/front_mappings.yaml",
        enLocalesPath: "app/{files,components,components_vendor}/**/en_US.yaml",
        zhLocalesPath: "app/{files,components,components_vendor}/**/zh_CN.yaml"
    }

    shepherd.task("clean", () => Promise.resolve(
        fs.emptyDirSync(process.settings.paths.public),
        fs.emptyDirSync("app/components_vendor"),
        fs.emptyDirSync("app/components_eevee"),
        fs.emptyDirSync("vendor/eevee")
    ))

    shepherd.task("repo", ["clean"], () => repo({ bundles: process.settings.bundles }))

    shepherd.task("componentsTar", ["repo"], () => targz.extract("vendor/components.tar.gz", "app/components_vendor"))

    shepherd.task("eevee", ["clean"], () => copyResource("./node_modules/@terminus/eevee/dist/{fonts,scripts,styles,views}/**/*", "vendor/eevee"))

    shepherd.task("eeveeComponents", ["clean"], () => copyResource("./node_modules/@terminus/eevee/dist/components/eevee/**/*", "app/components_eevee/eevee"))

    shepherd.task("eeveeViews", () => copyResource("{vendor/eevee/views/**/*,app/views/eevee/**/*}", "public/views/eevee"))

    shepherd.task("frontScripts", () => scripts(srcPath.frontScriptPath, "public/assets/scripts/app-front.js"))

    shepherd.task("backScripts", () => scripts(srcPath.backScriptPath, "public/assets/scripts/app-back.js"))

    shepherd.task("scriptsVendor", () => shepherd.src(srcPath.vendorScriptPath)
        .then(babel({
            perferSyntax: [".js"],
            compileOptions: { plugins: [[repleaceExpression, { "process.env.NODE_ENV": "\"development\"" }]] }
        }))
        .then(_concat("public/assets/scripts/vendor.js", { order: process.settings.scriptsOrder }))
        .then(uglifier({ compileOptions: { compress: { screw_ie8: false }, output: { ascii_only: true, screw_ie8: false }, mangle: { screw_ie8: false } } }))
        .then(shepherd.dest()))

    shepherd.task("ta", () => copyResource("vendor/ta/ta.js", "public/assets/scripts/"))

    shepherd.task("scriptsConfig", () => scripts(srcPath.configScriptPath, "public/assets/scripts/config.js"))

    shepherd.task("assetsDesigner", () => copyResource(srcPath.designAssetPath, "public/assets"))

    shepherd.task("viewsDesigner", () => copyResource(srcPath.designViewPath, "public/views"))

    shepherd.task("sprite", () => shepherd.src(srcPath.spritePath)
        .then(priority())
        .then(sprite({
            spritesheet_url: "/assets/images/icons.png",
            spritesheet_image: "public/assets/images/icons.png",
            spritesheet_css: "app/styles/_icons.scss",
            format: "css"
        }))
        .then(shepherd.dest()))

    shepherd.task("imagesCopy", () => copyResource(srcPath.imagePath, "public/assets/images/other-images"))

    shepherd.task("frontStyles", ["sprite"], () => styles(srcPath.frontStylePath, "public/assets/styles/app-front.css"))

    shepherd.task("backStyles", ["sprite"], () => styles(srcPath.backStylePath, "public/assets/styles/app-back.css"))

    shepherd.task("stylesVendor", () => shepherd.src(srcPath.vendorStylePath)
        .then(priority({ priority: ["vendor/pokeball.scss", "vendor/"], override: true }))
        .then((assets) => {
            assets[0].content = `@import "pokeball/theme";\n${assets[0].content}`
            return Promise.resolve(assets)
        })
        .then(compass())
        .then(concat("public/assets/styles/vendor.css", { order: process.settings.stylesOrder }))
        .then(cleancss())
        .then(shepherd.dest()))

    shepherd.task("componentsView", () => shepherd.src(srcPath.componentPath)
        .then(priority())
        .then(copy("public/components"))
        .then(shepherd.dest()))

    shepherd.task("views", () => copyResource(srcPath.viewPath, "public/views"))

    shepherd.task("frontTemplates", () => templates(srcPath.frontTemplatePath, "public/assets/scripts/templates-front.js"))

    shepherd.task("backTemplates", () => templates(srcPath.backTemplatePath, "public/assets/scripts/templates-back.js"))

    shepherd.task("configFile", () => copyResource(srcPath.configPath, "public/"))

    shepherd.task("file", () => copyResource(srcPath.filePath, "public/"))

    shepherd.task("frontConfig", ["file"], () => config(srcPath.frontConfigPath, "public/front_config.json"))

    shepherd.task("front_mapping", ["file"], () => config(srcPath.frontMappingPath, "public/front_mappings.json"))
    shepherd.task("backConfig", ["file"], () => shepherd.src(srcPath.backConfigPath)
        .then(priority({ priority: ["app/files/", "app/components_vendor/", "app/components/"], override: false }))
        .then(concat("public/back_config.json"))
        .then(backyaml())
        .then(shepherd.dest()))

    shepherd.task("enLocales", () => locale(srcPath.enLocalesPath, "public/resources/all_mix/en-US.json"))

    shepherd.task("zhLocales", () => locale(srcPath.zhLocalesPath, "public/resources/all_mix/zh-CN.json"))

    shepherd.task("init", ["repo", "eevee", "eeveeComponents", "componentsTar"])

    shepherd.task("app", [
        "server",
        "views", "frontStyles", "backStyles", "frontScripts", "backScripts", "frontTemplates", "backTemplates", "componentsView", "imagesCopy",
        "backConfig", "frontConfig", "front_mapping", "enLocales", "zhLocales",
        "assetsDesigner", "scriptsConfig",
        "scriptsVendor", "stylesVendor", "ta"
    ])

    shepherd.task("server", () => shepherd.src("server/**/*.{js,coffee}")
        .then(babel({ perferSyntax: [".js"], compileOptions: { plugins: ["transform-async-to-generator"] } }))
        .then(coffee())
        .then(copy("lib"))
        .then(shepherd.dest()))

    shepherd.task("revision", ["app"], () => shepherd.src("public/**/*")
        .then(rev({
            rootPath: `${process.cwd()}/public`,
            manifest: "manifest.json"
        }))
        .then(copy("public/", { bases: ["public/"] }))
        .then(shepherd.dest()))

    shepherd.task("zhI18nlint", () => i18nlint({ locale: "zh_CN" }, shepherd))

    shepherd.task("enI18nlint", () => i18nlint({ locale: "en_US" }, shepherd))

    shepherd.task("default", ["init"], () => {
        shepherd.watch(srcPath.componentPath, ["componentsView"])
        shepherd.watch(srcPath.viewPath, ["views"])
        shepherd.watch(srcPath.spritePath, ["sprite"])
        shepherd.watch(srcPath.frontStylePath, ["frontStyles"])
        shepherd.watch(srcPath.backStylePath, ["backStyles"])
        shepherd.watch(srcPath.frontScriptPath, ["frontScripts"])
        shepherd.watch(srcPath.backScriptPath, ["backScripts"])
        shepherd.watch(srcPath.backConfigPath, ["backConfig"])
        shepherd.watch(srcPath.frontConfigPath, ["frontConfig"])
        shepherd.watch(srcPath.frontTemplatePath, ["frontTemplates"])
        shepherd.watch(srcPath.backTemplatePath, ["backTemplates"])
        shepherd.watch(srcPath.configScriptPath, ["scriptsConfig"])
        shepherd.watch(srcPath.filePath, ["file"])
        shepherd.watch(srcPath.frontMappingPath, ["front_mapping"])
        shepherd.watch(srcPath.imagePath, ["imagesCopy"])
        shepherd.watch(srcPath.vendorScriptPath, ["scriptsVendor"])
        shepherd.watch(srcPath.vendorStylePath, ["stylesVendor"])
        shepherd.watch("vendor/eevee/*", ["assetsDesigner"])
        shepherd.watch(srcPath.enLocalesPath, ["enLocales"])
        shepherd.watch(srcPath.zhLocalesPath, ["zhLocales"])
        shepherd.watch("vendor/ta/ta.js", ["ta"])
        shepherd.watch("server/**/*", ["server"])
        shepherd.watch("app/{components,components_vendor}/**/*", ["zhI18nlint", "componentsView", "backScripts", "frontStyles"])
        shepherd.watch("app/{components,components_vendor}/**/*", ["enI18nlint", "componentsView", "backScripts", "frontStyles"])
    })

    shepherd.task("build", ["init"], () => shepherd.run("revision"))

    shepherd.task("prepareConfig", () => Promise.resolve(fs.copy("Pampasfile-prod.js", "Pampasfile.js")))

    shepherd.task("migration", () => shepherd.src("app/components/**/*.{sass,css,scss}")
        .then(colorMigration())
        .then(shepherd.dest()))
}
